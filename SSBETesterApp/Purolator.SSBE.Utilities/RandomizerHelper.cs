﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SSBE.Utilities
{
    public static class RandomizerHelper
    {
        #region Public Static Methods
        public static string GetRandomIntegerString(int digitCount)
        {
            Random random = new Random(Guid.NewGuid().GetHashCode());            
            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < digitCount; i++)
            { 
                sb.Append(random.Next( i != 0 ? 0 : 1, 9));
            }

            return sb.ToString();
        }

        public static long GetRandomLong(long min, long max)
        {
            byte[] buf = new byte[8];
            Random random = new Random(Guid.NewGuid().GetHashCode());            
            random.NextBytes(buf);
            
            long longRand = BitConverter.ToInt64(buf, 0);

            return (Math.Abs(longRand % (max - min)) + min);
        }

        public static string GetRandomMessageGuid()
        {
            return "SSBET" + Guid.NewGuid().ToString("N").ToUpper();
        }
        #endregion
    }
}
