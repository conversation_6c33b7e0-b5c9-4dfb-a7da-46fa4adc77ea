﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SSBE.Utilities
{
    public static class ConverterHelper
    {
        #region Public Methods
        public static bool CanCovert(object value, Type type)
        {
            TypeConverter converter = TypeDescriptor.GetConverter(type);
            return converter.IsValid(value);
        }
        #endregion
    }
}
