﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SSBE.Utilities
{
    public static class FileHelper
    {

        [DllImport("kernel32.dll", CharSet = CharSet.Auto)]
        private static extern int GetShortPathName(
            [MarshalAs(UnmanagedType.LPTStr)]
         string path,
            [MarshalAs(UnmanagedType.LPTStr)]
         StringBuilder shortPath,
            int shortPathLength
            );

        #region Public Static Methods
        public static string GetShortPath(string path)
        {
            var shortPath = new StringBuilder(255);
            GetShortPathName(path, shortPath, 255);
            return shortPath.ToString();
        }
        #endregion
    }
}
