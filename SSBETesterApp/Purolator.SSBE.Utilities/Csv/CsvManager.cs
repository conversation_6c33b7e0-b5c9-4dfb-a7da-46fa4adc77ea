﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SSBE.Utilities.Csv
{
    public class CsvManager<T> where T : new()
    {
        #region Public Properties
        public char DefaultSeparator { get; set; }
        public List<CsvFieldTarget> CsvFieldsToMap { get; set; }
        public string FilePath { get; set; }
        public CsvReader Reader { get; set; }
        public bool IsFirstLineColumnName { get; private set; }
        #endregion

        #region Public Constructors
        public CsvManager(string filePath, bool isFirstLineColumnName, char separator)
        {
            CsvFieldsToMap = new List<CsvFieldTarget>();
            DefaultSeparator = separator;
            IsFirstLineColumnName = isFirstLineColumnName;
            FilePath = filePath;
        }

        public CsvManager(string filePath, bool isFirstLineColumnName) : this(filePath, isFirstLineColumnName, ',') 
        { 
        }
        #endregion

        #region Public Methods
        public void SetField(Expression<Func<T, string>> expression, int position)
        {
            GetAndSetFieldTarget(expression, position);
        }

        public void SetField(Expression<Func<T, ValueType>> expression, int position)
        {
            GetAndSetFieldTarget(expression, position);
        }

        public void SetField(Expression<Func<T, DateTime>> expression, int position)
        {
            GetAndSetFieldTarget(expression, position);
        }

        public List<T> GetObjectList()
        {
            Reader = new CsvReader(FilePath, CsvFieldsToMap, DefaultSeparator, IsFirstLineColumnName);
            var csvRows = GetRowsFromFile();
            var resultList = new List<T>(csvRows.Count);
            foreach (var csvRow in csvRows)
            {
                var destinationObject = new T();
                var createdObj = SetPropertyViaReflection(destinationObject, csvRow);
                resultList.Add(createdObj);
            }
            return resultList;
        }
        #endregion

        #region Private Methods
        private void GetAndSetFieldTarget(Expression expression, int position)
        {
            var property = GetMemberInfo(expression);
            CsvFieldsToMap.Add(new CsvFieldTarget()
            {
                FieldName = property.Name,
                Position = position
            });
        }

        private List<CsvRow> GetRowsFromFile()
        {
            var csvRows = Reader.ReadCsvRows();
            return csvRows;

        }

        private T SetPropertyViaReflection(T destinationObj, CsvRow csvRow)
        {
            Type type = destinationObj.GetType();
            foreach (var csvFieldResult in csvRow.CsvFieldsResult)
            {
                PropertyInfo prop = type.GetProperty(csvFieldResult.FieldName);
                var propertyType = prop.PropertyType;

                if (ConverterHelper.CanCovert(csvFieldResult.FieldValue, propertyType))
                {
                    var convertedValue = Convert.ChangeType(csvFieldResult.FieldValue, propertyType);
                    prop.SetValue(destinationObj, convertedValue, null);
                }
            }
            return destinationObj;
        }

        private static MemberInfo GetMemberInfo(Expression method)
        {
            var lambda = method as LambdaExpression;
            if (lambda == null)
                throw new InvalidCastException("Invalid lambda expression");

            MemberExpression memberExpression = null;

            switch (lambda.Body.NodeType)
            {
                case ExpressionType.Convert:
                    memberExpression =
                        ((UnaryExpression)lambda.Body).Operand as MemberExpression;
                    break;
                case ExpressionType.MemberAccess:
                    memberExpression = lambda.Body as MemberExpression;
                    break;
            }

            if (memberExpression == null)
                throw new ArgumentException("Invalid expression");

            return memberExpression.Member;
        }
        #endregion
    }
}
