﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SSBE.Utilities.Csv
{
    public class CsvRow
    {
        #region Public Properties
        public List<CsvFieldResult> CsvFieldsResult { get; set; }
        #endregion

        #region Public Constructors
        public CsvRow(int numberOfColumns)
        {
            CsvFieldsResult = new List<CsvFieldResult>(numberOfColumns);
        }
        public CsvRow()
        {
            CsvFieldsResult = new List<CsvFieldResult>();
        }
        #endregion
    }

    public class CsvFieldTarget
    {
        #region Public Properties
        public string FieldName { get; set; }
        public int Position { get; set; }
        #endregion
    }

    public class CsvFieldResult : CsvFieldTarget
    {
        #region Public Properties
        public object FieldValue { get; set; }
        #endregion
    }
}
