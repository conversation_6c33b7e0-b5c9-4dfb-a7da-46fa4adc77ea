﻿using Purolator.SSBE.Business.Entities;
using Purolator.SSBE.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SSBE.DAL
{
    public static class StoredProcedures
    {
        #region Public Constants
        public enum Environment
        {
            Dev,
            Cert
        }
        #endregion

        #region Private Constants
        private const string CertSqlAppConfigKey = "CertSqlConnection";
        private const string DevSqlAppConfigKey = "DevSqlConnection";
        #endregion

        #region Private Static Properties
        private static string ConnectionStringAppConfigKey { get; set; }

        private static string ConnectionString
        {
            get { return ConfigurationManager.ConnectionStrings[ConnectionStringAppConfigKey].ConnectionString; }
        }
        #endregion

        #region Public Static Methods
        public static void ConfigureEnvironment(Environment environment)
        {
            switch (environment)
            {
                case Environment.Dev:
                    ConnectionStringAppConfigKey = DevSqlAppConfigKey;
                    break;
                case Environment.Cert:
                    ConnectionStringAppConfigKey = CertSqlAppConfigKey;
                    break;
                default:
                    throw new NotSupportedException("Environment not supported!");
            }
        }
        
        public static void InsertSAPMessageSTG(ShipmentEventStaging shipmentEventStaging)
        {
            if (shipmentEventStaging != null)
            {
                using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("[dbo].[InsertSAPMessageSTG]", sqlcon) { CommandType = CommandType.StoredProcedure })
                    {
                        cmd.Parameters.AddWithValue("@doc", shipmentEventStaging.ToXml());
                        sqlcon.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
            }
        }

        public static void LoadSAPPinInfo(int batchSize = 100)
        {
            using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
            {
                using (SqlCommand cmd = new SqlCommand("[dbo].[LoadSAPPinInfo_Carlo2]", sqlcon) { CommandType = CommandType.StoredProcedure })
                {
                    cmd.Parameters.AddWithValue("@BatchSize", batchSize);
                    sqlcon.Open();
                    cmd.ExecuteNonQuery();
                }
            }
        }

        public static bool IsPiecePinExists(string piecePin)
        {
            DataSet dataSet = new DataSet();
            using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
            {
                string cmdText = String.Format("SELECT 1 AS IsFound FROM SAPMessageStg WHERE PiecePin='{0}' UNION SELECT 1 FROM SAPPinInfo WHERE PiecePin='{0}'", piecePin);
                using (SqlDataAdapter sqlDataAdapter = new SqlDataAdapter())
                {
                    using (SqlCommand cmd = sqlcon.CreateCommand())
                    {
                        cmd.CommandText = cmdText;
                        sqlDataAdapter.SelectCommand = cmd;
                        sqlDataAdapter.Fill(dataSet);
                    }
                }
            }
            return dataSet != null && dataSet.Tables.Count > 0 && dataSet.Tables[0].Rows.Count > 0;
        }

        public static void StartShift(string terminal)
        {
            if (!String.IsNullOrWhiteSpace(terminal))
            {
                using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
                {
                    using (SqlCommand cmd = new SqlCommand("[dbo].[InitiateAMShift]", sqlcon) { CommandType = CommandType.StoredProcedure })
                    {
                        cmd.Parameters.AddWithValue("@Terminal", terminal);
                        cmd.Parameters.AddWithValue("@Date", DateTimeOffset.Now);
                        cmd.Parameters.AddWithValue("@CurrentUser", "SSBE_TESTER");
                        cmd.Parameters.AddWithValue("@ShiftDate", DateTime.Now);
                        cmd.Parameters.AddWithValue("@Success", null).Value = ParameterDirection.Output;

                        sqlcon.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
            }
        }

        public static void StopShift(string terminal)
        {
            if (!String.IsNullOrWhiteSpace(terminal))
            {
                using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
                {
                    string cmdText = String.Format("UPDATE [dbo].[SortPlanCalendar] SET ActualShiftStartTime = NULL WHERE DATE IN (SELECT dbo.[supParameterValue] (null,'Terminal', {0},'WorkingDate')) AND TERMINAL = '{0}'", terminal);
                    using (SqlCommand cmd = new SqlCommand(cmdText, sqlcon) { CommandType = CommandType.Text })
                    {
                        sqlcon.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
            }
        }

        public static void MatchShipmentsAndRoutes()
        {
            using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
            {
                using (SqlCommand cmd = new SqlCommand("[dbo].[MatchShipmentsandRoutes]", sqlcon) { CommandType = CommandType.StoredProcedure })
                {
                    sqlcon.Open();
                    cmd.ExecuteNonQuery();
                }
            }
        }

        public static DataSet GetPinMasterInfo(string piecePin)
        {
            DataSet dataSet = new DataSet();
            using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
            {
                string cmdText = String.Format("SELECT * FROM [dbo].[PinMaster] WHERE PiecePin = '{0}'", piecePin);
                using (SqlDataAdapter sqlDataAdapter = new SqlDataAdapter())
                {
                    using (SqlCommand cmd = sqlcon.CreateCommand())
                    {
                        cmd.CommandText = cmdText;
                        sqlDataAdapter.SelectCommand = cmd;
                        sqlDataAdapter.Fill(dataSet);
                    } 
                }
            }
            if (dataSet.Tables != null && dataSet.Tables.Count > 0 && dataSet.Tables[0].Rows.Count == 0)
            {
                dataSet.Tables[0].Rows.Add(dataSet.Tables[0].NewRow());
            }
            return dataSet;
        }

        public static DataSet GetSAPPinInfo(string piecePin)
        {
            DataSet dataSet = new DataSet();
            using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
            {
                string cmdText = String.Format("SELECT * FROM [dbo].[SAPPinInfo] WHERE PiecePin = '{0}'", piecePin);
                using (SqlDataAdapter sqlDataAdapter = new SqlDataAdapter())
                {
                    using (SqlCommand cmd = sqlcon.CreateCommand())
                    {
                        cmd.CommandText = cmdText;
                        sqlDataAdapter.SelectCommand = cmd;
                        sqlDataAdapter.Fill(dataSet);
                    }
                }
            }
            if (dataSet.Tables != null && dataSet.Tables.Count > 0 && dataSet.Tables[0].Rows.Count == 0)
            {
                dataSet.Tables[0].Rows.Add(dataSet.Tables[0].NewRow());
            }
            return dataSet;
        }

        public static DataSet GetSAPPinInfoLogs(DateTime startDateTime)
        {
            DataSet dataSet = new DataSet();
            using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
            {
                string cmdText = String.Format("SELECT * FROM [dbo].[supLogMain] WHERE ProcName = 'LoadSAPPinInfo_Carlo2' AND Date >= '{0}' AND Date <= '{1}'", startDateTime, DateTime.Now.AddMinutes(2));
                using (SqlDataAdapter sqlDataAdapter = new SqlDataAdapter())
                {
                    using (SqlCommand cmd = sqlcon.CreateCommand())
                    {
                        cmd.CommandText = cmdText;
                        sqlDataAdapter.SelectCommand = cmd;
                        sqlDataAdapter.Fill(dataSet);
                    }
                }
            }
            return dataSet;
        }

        public static void DeleteSAPPinInfoLogs(DateTime startDateTime)
        {
            using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
            {
                string cmdText = String.Format("DELETE FROM [dbo].[supLogMain] WHERE ProcName = 'LoadSAPPinInfo_Carlo2' AND Date >= '{0}' AND Date <= '{1}'", startDateTime, DateTime.Now.AddMinutes(2));
                using (SqlCommand cmd = new SqlCommand(cmdText, sqlcon) { CommandType = CommandType.Text })
                {
                    sqlcon.Open();
                    cmd.ExecuteNonQuery();
                }
            }
        }

        public static void UpdatePinMasterStateCode(string piecePin, string stateCode)
        {
            using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
            {
                string cmdText = String.Format("UPDATE [dbo].[PinMaster] SET PinMasterStateCode = '{0}' WHERE PiecePin = '{1}'", stateCode, piecePin);
                using (SqlCommand cmd = new SqlCommand(cmdText, sqlcon) { CommandType = CommandType.Text })
                {
                    sqlcon.Open();
                    cmd.ExecuteNonQuery();
                }
            }
        }

        public static void UpdatePinMasterPostalCode(string piecePin, string postalCode)
        {
            using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
            {
                string cmdText = String.Format("UPDATE [dbo].[PinMaster] SET PostalCode = '{0}' WHERE PiecePin = '{1}'", postalCode, piecePin);
                using (SqlCommand cmd = new SqlCommand(cmdText, sqlcon) { CommandType = CommandType.Text })
                {
                    sqlcon.Open();
                    cmd.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteRecordFromSAPMessageStg(string piecePin)
        {
            using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
            {
                string cmdText = String.Format("DELETE FROM [dbo].[SAPMessageStg] WHERE PiecePin = '{0}'", piecePin);
                using (SqlCommand cmd = new SqlCommand(cmdText, sqlcon) { CommandType = CommandType.Text })
                {
                    sqlcon.Open();
                    cmd.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteRecordFromSAPPinInfo(string piecePin)
        {
            using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
            {
                string cmdText = String.Format("DELETE FROM [dbo].[SAPPinInfo] WHERE PiecePin = '{0}'", piecePin);
                using (SqlCommand cmd = new SqlCommand(cmdText, sqlcon) { CommandType = CommandType.Text })
                {
                    sqlcon.Open();
                    cmd.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteRecordFromPinMaster(string piecePin)
        {
            using (SqlConnection sqlcon = new SqlConnection(ConnectionString))
            {
                string cmdText = String.Format("DELETE FROM [dbo].[PinMaster] WHERE PiecePin = '{0}'", piecePin);
                using (SqlCommand cmd = new SqlCommand(cmdText, sqlcon) { CommandType = CommandType.Text })
                {
                    sqlcon.Open();
                    cmd.ExecuteNonQuery();
                }
            }
        }
        #endregion
    }
}
