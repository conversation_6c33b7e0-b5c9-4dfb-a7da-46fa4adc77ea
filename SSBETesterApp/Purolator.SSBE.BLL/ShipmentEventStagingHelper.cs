﻿using Purolator.SSBE.Business.Entities;
using Purolator.SSBE.DAL;
using Purolator.SSBE.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SSBE.BLL
{
    //public static class ShipmentEventStagingHelper
    //{
    //    public static void LoadShipmentEvent(ShipmentEventStaging shipmentEventStagingItem, string pinMasterStateCode)
    //    {
    //        string piecePin = shipmentEventStagingItem.Message.PiecePin;

    //        StoredProcedures.InsertSAPMessageSTG(shipmentEventStagingItem);
    //        StoredProcedures.LoadSAPPinInfo();

    //        StoredProcedures.UpdatePinMasterStateCode(piecePin, pinMasterStateCode);
    //        StoredProcedures.DeletePiecePinFromSAPInfo(piecePin);

    //        shipmentEventStagingItem.Message.MessageGUID = RandomizerHelper.GetRandomMessageGuid();
    //        shipmentEventStagingItem.Message.FWODocumentNo = RandomizerHelper.GetRandomLong(90210, long.MaxValue);

    //        StoredProcedures.InsertSAPMessageSTG(shipmentEventStagingItem);
    //        StoredProcedures.LoadSAPPinInfo();
    //    }
    //}
}
