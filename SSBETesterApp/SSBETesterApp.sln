﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2012
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SSBE.TesterUI", "Purolator.SSBE.TesterUI\Purolator.SSBE.TesterUI.csproj", "{D6CC5DF1-37C6-4152-988E-E87263911125}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SSBE.Business.Entities", "Purolator.SSBE.Business.Entities\Purolator.SSBE.Business.Entities.csproj", "{F734F9AC-FC96-4F2F-A943-BA914A26114B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SSBE.DAL", "Purolator.SSBE.DAL\Purolator.SSBE.DAL.csproj", "{9DD8979F-0615-41E8-AB5F-8B0D777419C4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SSBE.Utilities", "Purolator.SSBE.Utilities\Purolator.SSBE.Utilities.csproj", "{94C3C93A-6ADA-4200-8299-950E5024A36B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SSBE.BLL", "Purolator.SSBE.BLL\Purolator.SSBE.BLL.csproj", "{44518C6D-3505-4EBC-BC57-68BF24D925D7}"
EndProject
Global
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 6
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://dv3530atfs.purolator.com:8080/
		SccLocalPath0 = .
		SccProjectUniqueName1 = Purolator.SSBE.Business.Entities\\Purolator.SSBE.Business.Entities.csproj
		SccProjectName1 = Purolator.SSBE.Business.Entities
		SccLocalPath1 = Purolator.SSBE.Business.Entities
		SccProjectUniqueName2 = Purolator.SSBE.DAL\\Purolator.SSBE.DAL.csproj
		SccProjectName2 = Purolator.SSBE.DAL
		SccLocalPath2 = Purolator.SSBE.DAL
		SccProjectUniqueName3 = Purolator.SSBE.TesterUI\\Purolator.SSBE.TesterUI.csproj
		SccProjectName3 = Purolator.SSBE.TesterUI
		SccLocalPath3 = Purolator.SSBE.TesterUI
		SccProjectUniqueName4 = Purolator.SSBE.Utilities\\Purolator.SSBE.Utilities.csproj
		SccProjectName4 = Purolator.SSBE.Utilities
		SccLocalPath4 = Purolator.SSBE.Utilities
		SccProjectUniqueName5 = Purolator.SSBE.BLL\\Purolator.SSBE.BLL.csproj
		SccProjectName5 = Purolator.SSBE.BLL
		SccLocalPath5 = Purolator.SSBE.BLL
	EndGlobalSection
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D6CC5DF1-37C6-4152-988E-E87263911125}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D6CC5DF1-37C6-4152-988E-E87263911125}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D6CC5DF1-37C6-4152-988E-E87263911125}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D6CC5DF1-37C6-4152-988E-E87263911125}.Release|Any CPU.Build.0 = Release|Any CPU
		{F734F9AC-FC96-4F2F-A943-BA914A26114B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F734F9AC-FC96-4F2F-A943-BA914A26114B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F734F9AC-FC96-4F2F-A943-BA914A26114B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F734F9AC-FC96-4F2F-A943-BA914A26114B}.Release|Any CPU.Build.0 = Release|Any CPU
		{9DD8979F-0615-41E8-AB5F-8B0D777419C4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9DD8979F-0615-41E8-AB5F-8B0D777419C4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9DD8979F-0615-41E8-AB5F-8B0D777419C4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9DD8979F-0615-41E8-AB5F-8B0D777419C4}.Release|Any CPU.Build.0 = Release|Any CPU
		{94C3C93A-6ADA-4200-8299-950E5024A36B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{94C3C93A-6ADA-4200-8299-950E5024A36B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{94C3C93A-6ADA-4200-8299-950E5024A36B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{94C3C93A-6ADA-4200-8299-950E5024A36B}.Release|Any CPU.Build.0 = Release|Any CPU
		{44518C6D-3505-4EBC-BC57-68BF24D925D7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44518C6D-3505-4EBC-BC57-68BF24D925D7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44518C6D-3505-4EBC-BC57-68BF24D925D7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44518C6D-3505-4EBC-BC57-68BF24D925D7}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
