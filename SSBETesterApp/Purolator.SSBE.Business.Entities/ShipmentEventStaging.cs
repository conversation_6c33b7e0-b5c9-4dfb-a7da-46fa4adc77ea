﻿using System;
using System.Data;
using System.Configuration;
using System.Xml.Serialization;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Globalization;

/// <PERSON>'s code
namespace Purolator.SSBE.Business.Entities
{
    [Serializable()]
    [XmlRoot("SAPMessage")]
    public class ShipmentEventStaging
    {
        [XmlElement("Message")]
        public ShipmentEventStagingMessage Message { get; set; }

        [XmlArray("PINServiceArray")]
        [XmlArrayItem(typeof(ShipmentService), ElementName = "PINService")]
        public List<ShipmentService> ShipmentServices { get; set; }
    }

    [Serializable()]
    public class ShipmentEventStagingMessage
    {
        public string MessageGUID { get; set; }
        public long FWODocumentNo { get; set; }
        public string EventExternalCode { get; set; }

        [XmlElement("EventDateTimeTZ")]
        public string LastUpdatedTime // format: 2011-11-11T15:05:46.473+01:00
        {
            get { return EventDateTimeTZ.ToString("yyyy-MM-ddTHH:mm:ss.fffzzz"); }
            set 
            {
                DateTimeOffset dateTimeOffset = DateTimeOffset.Now;
                if (DateTimeOffset.TryParse(value, out dateTimeOffset))
                {
                    EventDateTimeTZ = dateTimeOffset;
                }
            }
        }

        [XmlIgnore]
        public DateTimeOffset EventDateTimeTZ { get; set; }

        public string SenderCodeSet { get; set; }
        public string SenderID { get; set; }
        public string SenderName { get; set; }
        public long FreightUnit { get; set; }
        public string PiecePin { get; set; }
        public string ShipmentPin { get; set; }
        public string ServiceLevel { get; set; }
        public string TransportMode { get; set; }
        public string ServicePriority { get; set; }
        public string PackageType { get; set; }
        public string MaterialHandlingType { get; set; }
        public string ReturnType { get; set; }
        public string ProductNumber { get; set; }
        public string ShipmentDate { get; set; }
        public Decimal Weight { get; set; }
        public string WeightUnit { get; set; }
        public string OriginTerminal { get; set; }
        public string OriginSiteID { get; set; }
        public string DestinationTerminal { get; set; }
        public string DestinationSiteID { get; set; }
        public string ExpectedDeliveryDate { get; set; }
        public string DeclareAddressLine1 { get; set; }
        public string DeclareAddressLine2 { get; set; }
        public string DeclareAddressLine3 { get; set; }
        public string DeclareCompanyName { get; set; }
        public string DeclareReceiverAttnName { get; set; }
        public string DeclareCity { get; set; }
        public string DeclareProvince { get; set; }
        public string DeclarePostalCode { get; set; }
        public string DeclareCountry { get; set; }
        public string CurrentSuiteNum { get; set; }
        public string CurrentStreetNum { get; set; }
        public string CurrentStreetNumSuf { get; set; }
        public string CurrentStreetName { get; set; }
        public string CurrentStreetType { get; set; }
        public string CurrentStreetDir { get; set; }
        public string CurrentCity { get; set; }
        public string CurrentProvince { get; set; }
        public string CurrentPostalCode { get; set; }
        public string CurrentCountry { get; set; }
        public string CurrentCompanyName { get; set; }
        public string CurrentAddressLine2 { get; set; }
        public string CurrentAddressLine3 { get; set; }
        public string AddressType { get; set; }
        public string PurolatorAVInfo { get; set; }
        public string PurolatorAVStatus { get; set; }
    }

    [Serializable()]
    public class ShipmentService
    {
        public string ServiceScopeInd { get; set; }
        public string ServiceType { get; set; }
        public string ServiceTypeValue { get; set; }
    }
}
