﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Purolator.SSBE.TesterUI.Controls
{
    public partial class EnhancedCheckedListBox : CheckedListBox
    {
        public override int ItemHeight { get; set; }

        public EnhancedCheckedListBox()
        {
            ItemHeight = Font.Height + 6;
        }

        protected override void OnDrawItem(DrawItemEventArgs e)
        {
            Color foreColor = this.ForeColor;
            Color backColor = this.BackColor;

            DrawItemState s2 = e.State;

            if ((s2 & DrawItemState.Focus) == DrawItemState.Focus)
            {
                s2 &= DrawItemState.NoFocusRect;
            }

            if ((s2 & DrawItemState.Selected) == DrawItemState.Selected)
            {
                s2 &= ~DrawItemState.Selected;

            }

            DrawItemEventArgs e2 = new DrawItemEventArgs(e.Graphics, e.Font, e.<PERSON>, e.Index, s2, foreColor, backColor);
            base.OnDrawItem(e2);
        }
    }
}
