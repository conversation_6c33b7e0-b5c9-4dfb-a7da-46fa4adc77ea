﻿using ClosedXML.Excel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Purolator.SSBE.TesterUI.Helpers
{
    public static class DataGridViewHelper
    {
        #region Public Static Methods
        public static void StretchLastColumn(this DataGridView dataGridView)
        {
            var lastColIndex = dataGridView.Columns.Count - 1;
            var lastCol = dataGridView.Columns[lastColIndex];
            lastCol.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
        }

        public static void ExportToExcelFile(string fileName, Dictionary<string, string> columnFormatMapping, params DataGridView[] dataGridViews)
        {
            XLWorkbook workbook = new XLWorkbook();

            foreach (DataGridView dataGridView in dataGridViews)
            {
                DataTable dataTable = dataGridView.DataSource as DataTable;

                if (dataTable != null)
                {
                    string worksheetName = !String.IsNullOrWhiteSpace(dataTable.TableName) ? dataTable.TableName : workbook.Worksheets.Count.ToString();
                    var worksheet = workbook.Worksheets.Add(dataTable, worksheetName);
                    
                    for (int i = 0; i < dataGridView.Rows.Count; i++)
                    {
                        for (int j = 0; j < dataGridView.Columns.Count; j++)
                        {
                            string columnName = worksheet.Cell(1, j + 1).Value.ToString();
                            Color cellBackColor = dataGridView.Rows[i].Cells[j].Style.BackColor == Color.Empty ? Color.White : dataGridView.Rows[i].Cells[j].Style.BackColor;
                            var cell = worksheet.Cell(i + 2, j + 1);

                            if (columnFormatMapping != null && columnFormatMapping.ContainsKey(columnName))
                            {
                                cell.Style.NumberFormat.Format = columnFormatMapping[columnName];
                            }

                            cell.Style.Fill.BackgroundColor = XLColor.FromColor(cellBackColor);
                            cell.Style.Border.LeftBorder = cell.Style.Border.RightBorder =  XLBorderStyleValues.Thin;
                            cell.Style.Border.LeftBorderColor = cell.Style.Border.RightBorderColor = XLColor.DarkGray;
                        }
                    }

                    worksheet.Columns().AdjustToContents();
                }
            }

            workbook.SaveAs(fileName);
            workbook.Dispose();
        }
        #endregion
    }
}
