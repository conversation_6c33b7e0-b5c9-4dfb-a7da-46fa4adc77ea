﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SSBE.TesterUI.Helpers
{
    public static class BitmapHelper
    {
        #region Public Static Methods
        public static void CaptureView(Rectangle bounds, string fileName)
        {
            try
            {
                using (Bitmap bitmap = new Bitmap(bounds.Width, bounds.Height))
                {
                    using (Graphics g = Graphics.FromImage(bitmap))
                    {
                        g.CopyFrom<PERSON>creen(new Point(bounds.Left, bounds.Top), Point.Empty, bounds.Size);
                    }
                    bitmap.Save(fileName, ImageFormat.Jpeg);
                }
            }
            catch
            {
            }
        }
        #endregion
    }
}
