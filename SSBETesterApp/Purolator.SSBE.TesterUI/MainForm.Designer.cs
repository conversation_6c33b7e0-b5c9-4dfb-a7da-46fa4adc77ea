﻿namespace Purolator.SSBE.TesterUI
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnLoadShipment = new System.Windows.Forms.Button();
            this.groupBoxSAPNoRecordsPinMasterState = new System.Windows.Forms.GroupBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.checkBoxSAPNoRecordPinMasterNoRecord = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.checkBoxSAPRecordExistsPinMasterNoRecord = new System.Windows.Forms.CheckBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.textBoxPreStagingShipmentSource = new System.Windows.Forms.TextBox();
            this.buttonPreStagingShipmentSourceBrowse = new System.Windows.Forms.Button();
            this.checkBoxPreserveShipmentData = new System.Windows.Forms.CheckBox();
            this.buttonPostStagingShipmentSourceBrowse = new System.Windows.Forms.Button();
            this.textBoxPostStagingShipmentSource = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.checkBoxStartStopShiftPreStaging = new System.Windows.Forms.CheckBox();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.radioButtonCert = new System.Windows.Forms.RadioButton();
            this.radioButtonDev = new System.Windows.Forms.RadioButton();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.checkBoxCaptureScreen = new System.Windows.Forms.CheckBox();
            this.checkBoxStartStopShiftPostStaging = new System.Windows.Forms.CheckBox();
            this.checkBoxUniquePrePostStagingFWO = new System.Windows.Forms.CheckBox();
            this.buttonSAPNoRecordAll = new System.Windows.Forms.Button();
            this.buttonSAPNoRecordNone = new System.Windows.Forms.Button();
            this.buttonSAPRecordExistsNone = new System.Windows.Forms.Button();
            this.buttonSAPRecordExistsAll = new System.Windows.Forms.Button();
            this.checkedListBoxEventExternalCode = new Purolator.SSBE.TesterUI.Controls.EnhancedCheckedListBox();
            this.checkedListBoxSAPRecordExistsPinMasterState = new Purolator.SSBE.TesterUI.Controls.EnhancedCheckedListBox();
            this.checkedListBoxSAPNoRecordPinMasterState = new Purolator.SSBE.TesterUI.Controls.EnhancedCheckedListBox();
            this.groupBoxSAPNoRecordsPinMasterState.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnLoadShipment
            // 
            this.btnLoadShipment.Enabled = false;
            this.btnLoadShipment.Location = new System.Drawing.Point(356, 619);
            this.btnLoadShipment.Name = "btnLoadShipment";
            this.btnLoadShipment.Size = new System.Drawing.Size(105, 23);
            this.btnLoadShipment.TabIndex = 0;
            this.btnLoadShipment.Text = "Load Shipment";
            this.btnLoadShipment.UseVisualStyleBackColor = true;
            this.btnLoadShipment.Click += new System.EventHandler(this.btnLoadShipment_Click);
            // 
            // groupBoxSAPNoRecordsPinMasterState
            // 
            this.groupBoxSAPNoRecordsPinMasterState.Controls.Add(this.checkedListBoxSAPNoRecordPinMasterState);
            this.groupBoxSAPNoRecordsPinMasterState.Location = new System.Drawing.Point(15, 67);
            this.groupBoxSAPNoRecordsPinMasterState.Name = "groupBoxSAPNoRecordsPinMasterState";
            this.groupBoxSAPNoRecordsPinMasterState.Size = new System.Drawing.Size(336, 152);
            this.groupBoxSAPNoRecordsPinMasterState.TabIndex = 1;
            this.groupBoxSAPNoRecordsPinMasterState.TabStop = false;
            this.groupBoxSAPNoRecordsPinMasterState.Text = "PinMaster State Code";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.buttonSAPNoRecordNone);
            this.groupBox1.Controls.Add(this.buttonSAPNoRecordAll);
            this.groupBox1.Controls.Add(this.checkBoxSAPNoRecordPinMasterNoRecord);
            this.groupBox1.Controls.Add(this.groupBoxSAPNoRecordsPinMasterState);
            this.groupBox1.Location = new System.Drawing.Point(21, 229);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(367, 239);
            this.groupBox1.TabIndex = 2;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "SAPPinInfo - No Record";
            // 
            // checkBoxSAPNoRecordPinMasterNoRecord
            // 
            this.checkBoxSAPNoRecordPinMasterNoRecord.AutoSize = true;
            this.checkBoxSAPNoRecordPinMasterNoRecord.Location = new System.Drawing.Point(15, 31);
            this.checkBoxSAPNoRecordPinMasterNoRecord.Name = "checkBoxSAPNoRecordPinMasterNoRecord";
            this.checkBoxSAPNoRecordPinMasterNoRecord.Size = new System.Drawing.Size(134, 17);
            this.checkBoxSAPNoRecordPinMasterNoRecord.TabIndex = 2;
            this.checkBoxSAPNoRecordPinMasterNoRecord.Text = "PinMaster - No Record";
            this.checkBoxSAPNoRecordPinMasterNoRecord.UseVisualStyleBackColor = true;
            this.checkBoxSAPNoRecordPinMasterNoRecord.CheckedChanged += new System.EventHandler(this.checkBoxSAPNoRecordPinMasterNoRecord_CheckedChanged);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.buttonSAPRecordExistsNone);
            this.groupBox2.Controls.Add(this.buttonSAPRecordExistsAll);
            this.groupBox2.Controls.Add(this.checkBoxSAPRecordExistsPinMasterNoRecord);
            this.groupBox2.Controls.Add(this.groupBox3);
            this.groupBox2.Location = new System.Drawing.Point(415, 229);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(367, 239);
            this.groupBox2.TabIndex = 3;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "SAPPinInfo - Record Exists";
            // 
            // checkBoxSAPRecordExistsPinMasterNoRecord
            // 
            this.checkBoxSAPRecordExistsPinMasterNoRecord.AutoSize = true;
            this.checkBoxSAPRecordExistsPinMasterNoRecord.Location = new System.Drawing.Point(15, 31);
            this.checkBoxSAPRecordExistsPinMasterNoRecord.Name = "checkBoxSAPRecordExistsPinMasterNoRecord";
            this.checkBoxSAPRecordExistsPinMasterNoRecord.Size = new System.Drawing.Size(134, 17);
            this.checkBoxSAPRecordExistsPinMasterNoRecord.TabIndex = 3;
            this.checkBoxSAPRecordExistsPinMasterNoRecord.Text = "PinMaster - No Record";
            this.checkBoxSAPRecordExistsPinMasterNoRecord.UseVisualStyleBackColor = true;
            this.checkBoxSAPRecordExistsPinMasterNoRecord.CheckedChanged += new System.EventHandler(this.checkBoxSAPRecordExistsPinMasterNoRecord_CheckedChanged);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.checkedListBoxSAPRecordExistsPinMasterState);
            this.groupBox3.Location = new System.Drawing.Point(15, 67);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(336, 152);
            this.groupBox3.TabIndex = 1;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "PinMaster State Code";
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.checkedListBoxEventExternalCode);
            this.groupBox4.Location = new System.Drawing.Point(21, 107);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(761, 97);
            this.groupBox4.TabIndex = 4;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "Post-Staging Event External Code (Filter)";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(18, 22);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(149, 13);
            this.label1.TabIndex = 5;
            this.label1.Text = "Pre-Staging Shipment Source:";
            // 
            // textBoxPreStagingShipmentSource
            // 
            this.textBoxPreStagingShipmentSource.HideSelection = false;
            this.textBoxPreStagingShipmentSource.Location = new System.Drawing.Point(187, 20);
            this.textBoxPreStagingShipmentSource.Name = "textBoxPreStagingShipmentSource";
            this.textBoxPreStagingShipmentSource.ReadOnly = true;
            this.textBoxPreStagingShipmentSource.Size = new System.Drawing.Size(502, 20);
            this.textBoxPreStagingShipmentSource.TabIndex = 6;
            // 
            // buttonPreStagingShipmentSourceBrowse
            // 
            this.buttonPreStagingShipmentSourceBrowse.Location = new System.Drawing.Point(707, 18);
            this.buttonPreStagingShipmentSourceBrowse.Name = "buttonPreStagingShipmentSourceBrowse";
            this.buttonPreStagingShipmentSourceBrowse.Size = new System.Drawing.Size(75, 23);
            this.buttonPreStagingShipmentSourceBrowse.TabIndex = 7;
            this.buttonPreStagingShipmentSourceBrowse.Text = "Browse...";
            this.buttonPreStagingShipmentSourceBrowse.UseVisualStyleBackColor = true;
            this.buttonPreStagingShipmentSourceBrowse.Click += new System.EventHandler(this.buttonPreStagingShipmentSourceBrowse_Click);
            // 
            // checkBoxPreserveShipmentData
            // 
            this.checkBoxPreserveShipmentData.AutoSize = true;
            this.checkBoxPreserveShipmentData.Location = new System.Drawing.Point(227, 31);
            this.checkBoxPreserveShipmentData.Name = "checkBoxPreserveShipmentData";
            this.checkBoxPreserveShipmentData.Size = new System.Drawing.Size(133, 17);
            this.checkBoxPreserveShipmentData.TabIndex = 8;
            this.checkBoxPreserveShipmentData.Text = "Preserve Staging Data";
            this.checkBoxPreserveShipmentData.UseVisualStyleBackColor = true;
            // 
            // buttonPostStagingShipmentSourceBrowse
            // 
            this.buttonPostStagingShipmentSourceBrowse.Location = new System.Drawing.Point(707, 58);
            this.buttonPostStagingShipmentSourceBrowse.Name = "buttonPostStagingShipmentSourceBrowse";
            this.buttonPostStagingShipmentSourceBrowse.Size = new System.Drawing.Size(75, 23);
            this.buttonPostStagingShipmentSourceBrowse.TabIndex = 11;
            this.buttonPostStagingShipmentSourceBrowse.Text = "Browse...";
            this.buttonPostStagingShipmentSourceBrowse.UseVisualStyleBackColor = true;
            this.buttonPostStagingShipmentSourceBrowse.Click += new System.EventHandler(this.buttonPostStagingShipmentSourceBrowse_Click);
            // 
            // textBoxPostStagingShipmentSource
            // 
            this.textBoxPostStagingShipmentSource.HideSelection = false;
            this.textBoxPostStagingShipmentSource.Location = new System.Drawing.Point(187, 60);
            this.textBoxPostStagingShipmentSource.Name = "textBoxPostStagingShipmentSource";
            this.textBoxPostStagingShipmentSource.ReadOnly = true;
            this.textBoxPostStagingShipmentSource.Size = new System.Drawing.Size(502, 20);
            this.textBoxPostStagingShipmentSource.TabIndex = 10;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(18, 62);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(154, 13);
            this.label2.TabIndex = 9;
            this.label2.Text = "Post-Staging Shipment Source:";
            // 
            // checkBoxStartStopShiftPreStaging
            // 
            this.checkBoxStartStopShiftPreStaging.AutoSize = true;
            this.checkBoxStartStopShiftPreStaging.Location = new System.Drawing.Point(15, 54);
            this.checkBoxStartStopShiftPreStaging.Name = "checkBoxStartStopShiftPreStaging";
            this.checkBoxStartStopShiftPreStaging.Size = new System.Drawing.Size(170, 17);
            this.checkBoxStartStopShiftPreStaging.TabIndex = 15;
            this.checkBoxStartStopShiftPreStaging.Text = "Start && Stop Shift (Pre-Staging)";
            this.checkBoxStartStopShiftPreStaging.UseVisualStyleBackColor = true;
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.radioButtonCert);
            this.groupBox5.Controls.Add(this.radioButtonDev);
            this.groupBox5.Location = new System.Drawing.Point(21, 489);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(367, 112);
            this.groupBox5.TabIndex = 16;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "Staging Environment";
            // 
            // radioButtonCert
            // 
            this.radioButtonCert.AutoSize = true;
            this.radioButtonCert.Location = new System.Drawing.Point(137, 31);
            this.radioButtonCert.Name = "radioButtonCert";
            this.radioButtonCert.Size = new System.Drawing.Size(80, 17);
            this.radioButtonCert.TabIndex = 1;
            this.radioButtonCert.Text = "Certification";
            this.radioButtonCert.UseVisualStyleBackColor = true;
            this.radioButtonCert.CheckedChanged += new System.EventHandler(this.radioButtonCert_CheckedChanged);
            // 
            // radioButtonDev
            // 
            this.radioButtonDev.AutoSize = true;
            this.radioButtonDev.Checked = true;
            this.radioButtonDev.Location = new System.Drawing.Point(15, 31);
            this.radioButtonDev.Name = "radioButtonDev";
            this.radioButtonDev.Size = new System.Drawing.Size(88, 17);
            this.radioButtonDev.TabIndex = 0;
            this.radioButtonDev.TabStop = true;
            this.radioButtonDev.Text = "Development";
            this.radioButtonDev.UseVisualStyleBackColor = true;
            this.radioButtonDev.CheckedChanged += new System.EventHandler(this.radioButtonDev_CheckedChanged);
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.checkBoxUniquePrePostStagingFWO);
            this.groupBox6.Controls.Add(this.checkBoxCaptureScreen);
            this.groupBox6.Controls.Add(this.checkBoxStartStopShiftPostStaging);
            this.groupBox6.Controls.Add(this.checkBoxPreserveShipmentData);
            this.groupBox6.Controls.Add(this.checkBoxStartStopShiftPreStaging);
            this.groupBox6.Location = new System.Drawing.Point(415, 489);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(367, 112);
            this.groupBox6.TabIndex = 17;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "Actions";
            // 
            // checkBoxCaptureScreen
            // 
            this.checkBoxCaptureScreen.AutoSize = true;
            this.checkBoxCaptureScreen.Checked = true;
            this.checkBoxCaptureScreen.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxCaptureScreen.Location = new System.Drawing.Point(227, 54);
            this.checkBoxCaptureScreen.Name = "checkBoxCaptureScreen";
            this.checkBoxCaptureScreen.Size = new System.Drawing.Size(100, 17);
            this.checkBoxCaptureScreen.TabIndex = 17;
            this.checkBoxCaptureScreen.Text = "Capture Screen";
            this.checkBoxCaptureScreen.UseVisualStyleBackColor = true;
            // 
            // checkBoxStartStopShiftPostStaging
            // 
            this.checkBoxStartStopShiftPostStaging.AutoSize = true;
            this.checkBoxStartStopShiftPostStaging.Location = new System.Drawing.Point(15, 81);
            this.checkBoxStartStopShiftPostStaging.Name = "checkBoxStartStopShiftPostStaging";
            this.checkBoxStartStopShiftPostStaging.Size = new System.Drawing.Size(175, 17);
            this.checkBoxStartStopShiftPostStaging.TabIndex = 16;
            this.checkBoxStartStopShiftPostStaging.Text = "Start && Stop Shift (Post-Staging)";
            this.checkBoxStartStopShiftPostStaging.UseVisualStyleBackColor = true;
            // 
            // checkBoxUniquePrePostStagingFWO
            // 
            this.checkBoxUniquePrePostStagingFWO.AutoSize = true;
            this.checkBoxUniquePrePostStagingFWO.Checked = true;
            this.checkBoxUniquePrePostStagingFWO.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxUniquePrePostStagingFWO.Location = new System.Drawing.Point(15, 31);
            this.checkBoxUniquePrePostStagingFWO.Name = "checkBoxUniquePrePostStagingFWO";
            this.checkBoxUniquePrePostStagingFWO.Size = new System.Drawing.Size(179, 17);
            this.checkBoxUniquePrePostStagingFWO.TabIndex = 18;
            this.checkBoxUniquePrePostStagingFWO.Text = "Unique Pre && Post Staging FWO";
            this.checkBoxUniquePrePostStagingFWO.UseVisualStyleBackColor = true;
            // 
            // buttonSAPNoRecordAll
            // 
            this.buttonSAPNoRecordAll.Location = new System.Drawing.Point(221, 27);
            this.buttonSAPNoRecordAll.Name = "buttonSAPNoRecordAll";
            this.buttonSAPNoRecordAll.Size = new System.Drawing.Size(62, 23);
            this.buttonSAPNoRecordAll.TabIndex = 3;
            this.buttonSAPNoRecordAll.Text = "All";
            this.buttonSAPNoRecordAll.UseVisualStyleBackColor = true;
            this.buttonSAPNoRecordAll.Click += new System.EventHandler(this.buttonSAPNoRecordAll_Click);
            // 
            // buttonSAPNoRecordNone
            // 
            this.buttonSAPNoRecordNone.Location = new System.Drawing.Point(289, 27);
            this.buttonSAPNoRecordNone.Name = "buttonSAPNoRecordNone";
            this.buttonSAPNoRecordNone.Size = new System.Drawing.Size(62, 23);
            this.buttonSAPNoRecordNone.TabIndex = 4;
            this.buttonSAPNoRecordNone.Text = "None";
            this.buttonSAPNoRecordNone.UseVisualStyleBackColor = true;
            this.buttonSAPNoRecordNone.Click += new System.EventHandler(this.buttonSAPNoRecordNone_Click);
            // 
            // buttonSAPRecordExistsNone
            // 
            this.buttonSAPRecordExistsNone.Location = new System.Drawing.Point(289, 27);
            this.buttonSAPRecordExistsNone.Name = "buttonSAPRecordExistsNone";
            this.buttonSAPRecordExistsNone.Size = new System.Drawing.Size(62, 23);
            this.buttonSAPRecordExistsNone.TabIndex = 6;
            this.buttonSAPRecordExistsNone.Text = "None";
            this.buttonSAPRecordExistsNone.UseVisualStyleBackColor = true;
            this.buttonSAPRecordExistsNone.Click += new System.EventHandler(this.buttonSAPRecordExistsNone_Click);
            // 
            // buttonSAPRecordExistsAll
            // 
            this.buttonSAPRecordExistsAll.Location = new System.Drawing.Point(221, 27);
            this.buttonSAPRecordExistsAll.Name = "buttonSAPRecordExistsAll";
            this.buttonSAPRecordExistsAll.Size = new System.Drawing.Size(62, 23);
            this.buttonSAPRecordExistsAll.TabIndex = 5;
            this.buttonSAPRecordExistsAll.Text = "All";
            this.buttonSAPRecordExistsAll.UseVisualStyleBackColor = true;
            this.buttonSAPRecordExistsAll.Click += new System.EventHandler(this.buttonSAPRecordExistsAll_Click);
            // 
            // checkedListBoxEventExternalCode
            // 
            this.checkedListBoxEventExternalCode.BackColor = System.Drawing.SystemColors.Control;
            this.checkedListBoxEventExternalCode.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.checkedListBoxEventExternalCode.CheckOnClick = true;
            this.checkedListBoxEventExternalCode.ColumnWidth = 100;
            this.checkedListBoxEventExternalCode.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkedListBoxEventExternalCode.FormattingEnabled = true;
            this.checkedListBoxEventExternalCode.Items.AddRange(new object[] {
            "3000",
            "3010",
            "HVOID",
            "HRET",
            "3050",
            "3070",
            "4010",
            "4030",
            "6010",
            "6030",
            "6050",
            "6060",
            "9010"});
            this.checkedListBoxEventExternalCode.Location = new System.Drawing.Point(12, 29);
            this.checkedListBoxEventExternalCode.MultiColumn = true;
            this.checkedListBoxEventExternalCode.Name = "checkedListBoxEventExternalCode";
            this.checkedListBoxEventExternalCode.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.checkedListBoxEventExternalCode.Size = new System.Drawing.Size(517, 57);
            this.checkedListBoxEventExternalCode.TabIndex = 1;
            this.checkedListBoxEventExternalCode.SelectedIndexChanged += new System.EventHandler(this.checkedListBoxEventExternalCode_SelectedIndexChanged);
            // 
            // checkedListBoxSAPRecordExistsPinMasterState
            // 
            this.checkedListBoxSAPRecordExistsPinMasterState.BackColor = System.Drawing.SystemColors.Control;
            this.checkedListBoxSAPRecordExistsPinMasterState.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.checkedListBoxSAPRecordExistsPinMasterState.CheckOnClick = true;
            this.checkedListBoxSAPRecordExistsPinMasterState.ColumnWidth = 100;
            this.checkedListBoxSAPRecordExistsPinMasterState.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkedListBoxSAPRecordExistsPinMasterState.FormattingEnabled = true;
            this.checkedListBoxSAPRecordExistsPinMasterState.Items.AddRange(new object[] {
            "RAM",
            "ATQ",
            "RER",
            "RES",
            "EXR",
            "EXS",
            "SSR",
            "SSS",
            "SSC",
            "RTC",
            "CM",
            "ULT",
            "LDT",
            "RPT",
            "LWS",
            "PR"});
            this.checkedListBoxSAPRecordExistsPinMasterState.Location = new System.Drawing.Point(13, 28);
            this.checkedListBoxSAPRecordExistsPinMasterState.MultiColumn = true;
            this.checkedListBoxSAPRecordExistsPinMasterState.Name = "checkedListBoxSAPRecordExistsPinMasterState";
            this.checkedListBoxSAPRecordExistsPinMasterState.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.checkedListBoxSAPRecordExistsPinMasterState.Size = new System.Drawing.Size(317, 114);
            this.checkedListBoxSAPRecordExistsPinMasterState.TabIndex = 0;
            this.checkedListBoxSAPRecordExistsPinMasterState.SelectedIndexChanged += new System.EventHandler(this.checkedListBoxSAPRecordExistsPinMasterState_SelectedIndexChanged);
            // 
            // checkedListBoxSAPNoRecordPinMasterState
            // 
            this.checkedListBoxSAPNoRecordPinMasterState.BackColor = System.Drawing.SystemColors.Control;
            this.checkedListBoxSAPNoRecordPinMasterState.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.checkedListBoxSAPNoRecordPinMasterState.CausesValidation = false;
            this.checkedListBoxSAPNoRecordPinMasterState.CheckOnClick = true;
            this.checkedListBoxSAPNoRecordPinMasterState.ColumnWidth = 100;
            this.checkedListBoxSAPNoRecordPinMasterState.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkedListBoxSAPNoRecordPinMasterState.FormattingEnabled = true;
            this.checkedListBoxSAPNoRecordPinMasterState.Items.AddRange(new object[] {
            "SSR",
            "SSS",
            "SSC",
            "RTC",
            "CM",
            "ULT",
            "LDT",
            "RPT",
            "LWS",
            "PR"});
            this.checkedListBoxSAPNoRecordPinMasterState.Location = new System.Drawing.Point(12, 28);
            this.checkedListBoxSAPNoRecordPinMasterState.MultiColumn = true;
            this.checkedListBoxSAPNoRecordPinMasterState.Name = "checkedListBoxSAPNoRecordPinMasterState";
            this.checkedListBoxSAPNoRecordPinMasterState.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.checkedListBoxSAPNoRecordPinMasterState.Size = new System.Drawing.Size(200, 114);
            this.checkedListBoxSAPNoRecordPinMasterState.TabIndex = 0;
            this.checkedListBoxSAPNoRecordPinMasterState.SelectedIndexChanged += new System.EventHandler(this.checkedListBoxSAPNoRecordPinMasterState_SelectedIndexChanged);
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(804, 654);
            this.Controls.Add(this.groupBox5);
            this.Controls.Add(this.buttonPostStagingShipmentSourceBrowse);
            this.Controls.Add(this.textBoxPostStagingShipmentSource);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.buttonPreStagingShipmentSourceBrowse);
            this.Controls.Add(this.textBoxPreStagingShipmentSource);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.btnLoadShipment);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.groupBox6);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Name = "MainForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "SSBE Tester";
            this.groupBoxSAPNoRecordsPinMasterState.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox4.ResumeLayout(false);
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button btnLoadShipment;
        private System.Windows.Forms.GroupBox groupBoxSAPNoRecordsPinMasterState;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private Purolator.SSBE.TesterUI.Controls.EnhancedCheckedListBox checkedListBoxSAPRecordExistsPinMasterState;
        private Purolator.SSBE.TesterUI.Controls.EnhancedCheckedListBox checkedListBoxSAPNoRecordPinMasterState;
        private System.Windows.Forms.CheckBox checkBoxSAPNoRecordPinMasterNoRecord;
        private System.Windows.Forms.CheckBox checkBoxSAPRecordExistsPinMasterNoRecord;
        private System.Windows.Forms.GroupBox groupBox4;
        private Controls.EnhancedCheckedListBox checkedListBoxEventExternalCode;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox textBoxPreStagingShipmentSource;
        private System.Windows.Forms.Button buttonPreStagingShipmentSourceBrowse;
        private System.Windows.Forms.CheckBox checkBoxPreserveShipmentData;
        private System.Windows.Forms.Button buttonPostStagingShipmentSourceBrowse;
        private System.Windows.Forms.TextBox textBoxPostStagingShipmentSource;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckBox checkBoxStartStopShiftPreStaging;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.RadioButton radioButtonCert;
        private System.Windows.Forms.RadioButton radioButtonDev;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.CheckBox checkBoxStartStopShiftPostStaging;
        private System.Windows.Forms.CheckBox checkBoxCaptureScreen;
        private System.Windows.Forms.CheckBox checkBoxUniquePrePostStagingFWO;
        private System.Windows.Forms.Button buttonSAPNoRecordNone;
        private System.Windows.Forms.Button buttonSAPNoRecordAll;
        private System.Windows.Forms.Button buttonSAPRecordExistsNone;
        private System.Windows.Forms.Button buttonSAPRecordExistsAll;
    }
}

