﻿namespace Purolator.SSBE.TesterUI
{
    partial class ShipmentResultsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle9 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle10 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle11 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle12 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle13 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle14 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle15 = new System.Windows.Forms.DataGridViewCellStyle();
            this.dataGridViewSAPPinInfoBefore = new System.Windows.Forms.DataGridView();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.tableLayoutPanelTop = new System.Windows.Forms.TableLayoutPanel();
            this.dataGridViewPinMasterAfter = new System.Windows.Forms.DataGridView();
            this.dataGridViewSAPPinInfoAfter = new System.Windows.Forms.DataGridView();
            this.dataGridViewPinMasterBefore = new System.Windows.Forms.DataGridView();
            this.dataGridViewLogs = new System.Windows.Forms.DataGridView();
            this.label5 = new System.Windows.Forms.Label();
            this.buttonExportResults = new System.Windows.Forms.Button();
            this.tableLayoutPanelCore = new System.Windows.Forms.TableLayoutPanel();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewSAPPinInfoBefore)).BeginInit();
            this.tableLayoutPanelTop.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewPinMasterAfter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewSAPPinInfoAfter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewPinMasterBefore)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewLogs)).BeginInit();
            this.tableLayoutPanelCore.SuspendLayout();
            this.SuspendLayout();
            // 
            // dataGridViewSAPPinInfoBefore
            // 
            this.dataGridViewSAPPinInfoBefore.AllowUserToAddRows = false;
            this.dataGridViewSAPPinInfoBefore.AllowUserToDeleteRows = false;
            this.dataGridViewSAPPinInfoBefore.AllowUserToResizeRows = false;
            this.dataGridViewSAPPinInfoBefore.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.AllCells;
            this.dataGridViewSAPPinInfoBefore.AutoSizeRowsMode = System.Windows.Forms.DataGridViewAutoSizeRowsMode.AllCells;
            this.dataGridViewSAPPinInfoBefore.BackgroundColor = System.Drawing.SystemColors.Control;
            this.dataGridViewSAPPinInfoBefore.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dataGridViewSAPPinInfoBefore.CausesValidation = false;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewSAPPinInfoBefore.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dataGridViewSAPPinInfoBefore.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.Format = "G";
            dataGridViewCellStyle2.NullValue = "NULL";
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewSAPPinInfoBefore.DefaultCellStyle = dataGridViewCellStyle2;
            this.dataGridViewSAPPinInfoBefore.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewSAPPinInfoBefore.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
            this.dataGridViewSAPPinInfoBefore.Location = new System.Drawing.Point(3, 43);
            this.dataGridViewSAPPinInfoBefore.MinimumSize = new System.Drawing.Size(0, 112);
            this.dataGridViewSAPPinInfoBefore.Name = "dataGridViewSAPPinInfoBefore";
            this.dataGridViewSAPPinInfoBefore.ReadOnly = true;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewSAPPinInfoBefore.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dataGridViewSAPPinInfoBefore.RowHeadersVisible = false;
            this.dataGridViewSAPPinInfoBefore.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect;
            this.dataGridViewSAPPinInfoBefore.ShowCellErrors = false;
            this.dataGridViewSAPPinInfoBefore.ShowCellToolTips = false;
            this.dataGridViewSAPPinInfoBefore.ShowEditingIcon = false;
            this.dataGridViewSAPPinInfoBefore.ShowRowErrors = false;
            this.dataGridViewSAPPinInfoBefore.Size = new System.Drawing.Size(861, 145);
            this.dataGridViewSAPPinInfoBefore.TabIndex = 0;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(3, 20);
            this.label1.MinimumSize = new System.Drawing.Size(0, 20);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(250, 20);
            this.label1.TabIndex = 4;
            this.label1.Text = "SAPPinInfo (Before - LoadSAPPinInfo_Carlo)";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(3, 216);
            this.label2.MinimumSize = new System.Drawing.Size(0, 20);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(238, 20);
            this.label2.TabIndex = 5;
            this.label2.Text = "SAPPinInfo (After - LoadSAPPinInfo_Carlo)";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.Location = new System.Drawing.Point(3, 412);
            this.label3.MinimumSize = new System.Drawing.Size(0, 20);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(245, 20);
            this.label3.TabIndex = 6;
            this.label3.Text = "PinMaster (Before - LoadSAPPinInfo_Carlo)";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(3, 608);
            this.label4.MinimumSize = new System.Drawing.Size(20, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(233, 15);
            this.label4.TabIndex = 7;
            this.label4.Text = "PinMaster (After - LoadSAPPinInfo_Carlo)";
            // 
            // tableLayoutPanelTop
            // 
            this.tableLayoutPanelTop.AutoScroll = true;
            this.tableLayoutPanelTop.ColumnCount = 1;
            this.tableLayoutPanelTop.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanelTop.Controls.Add(this.label4, 0, 11);
            this.tableLayoutPanelTop.Controls.Add(this.label3, 0, 8);
            this.tableLayoutPanelTop.Controls.Add(this.dataGridViewPinMasterAfter, 0, 12);
            this.tableLayoutPanelTop.Controls.Add(this.label2, 0, 5);
            this.tableLayoutPanelTop.Controls.Add(this.dataGridViewSAPPinInfoBefore, 0, 3);
            this.tableLayoutPanelTop.Controls.Add(this.label1, 0, 2);
            this.tableLayoutPanelTop.Controls.Add(this.dataGridViewSAPPinInfoAfter, 0, 6);
            this.tableLayoutPanelTop.Controls.Add(this.dataGridViewPinMasterBefore, 0, 9);
            this.tableLayoutPanelTop.Controls.Add(this.dataGridViewLogs, 0, 15);
            this.tableLayoutPanelTop.Controls.Add(this.label5, 0, 14);
            this.tableLayoutPanelTop.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanelTop.GrowStyle = System.Windows.Forms.TableLayoutPanelGrowStyle.FixedSize;
            this.tableLayoutPanelTop.Location = new System.Drawing.Point(3, 3);
            this.tableLayoutPanelTop.Name = "tableLayoutPanelTop";
            this.tableLayoutPanelTop.RowCount = 16;
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 10F));
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 10F));
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 25F));
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 25F));
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 25F));
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 25F));
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanelTop.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanelTop.Size = new System.Drawing.Size(867, 813);
            this.tableLayoutPanelTop.TabIndex = 9;
            // 
            // dataGridViewPinMasterAfter
            // 
            this.dataGridViewPinMasterAfter.AllowUserToAddRows = false;
            this.dataGridViewPinMasterAfter.AllowUserToDeleteRows = false;
            this.dataGridViewPinMasterAfter.AllowUserToResizeRows = false;
            this.dataGridViewPinMasterAfter.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.AllCells;
            this.dataGridViewPinMasterAfter.AutoSizeRowsMode = System.Windows.Forms.DataGridViewAutoSizeRowsMode.AllCells;
            this.dataGridViewPinMasterAfter.BackgroundColor = System.Drawing.SystemColors.Control;
            this.dataGridViewPinMasterAfter.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dataGridViewPinMasterAfter.CausesValidation = false;
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewPinMasterAfter.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle4;
            this.dataGridViewPinMasterAfter.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle5.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle5.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle5.Format = "G";
            dataGridViewCellStyle5.NullValue = "NULL";
            dataGridViewCellStyle5.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle5.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewPinMasterAfter.DefaultCellStyle = dataGridViewCellStyle5;
            this.dataGridViewPinMasterAfter.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewPinMasterAfter.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
            this.dataGridViewPinMasterAfter.Location = new System.Drawing.Point(3, 626);
            this.dataGridViewPinMasterAfter.MinimumSize = new System.Drawing.Size(0, 112);
            this.dataGridViewPinMasterAfter.Name = "dataGridViewPinMasterAfter";
            this.dataGridViewPinMasterAfter.ReadOnly = true;
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle6.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle6.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle6.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewPinMasterAfter.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            this.dataGridViewPinMasterAfter.RowHeadersVisible = false;
            this.dataGridViewPinMasterAfter.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect;
            this.dataGridViewPinMasterAfter.ShowCellErrors = false;
            this.dataGridViewPinMasterAfter.ShowCellToolTips = false;
            this.dataGridViewPinMasterAfter.ShowEditingIcon = false;
            this.dataGridViewPinMasterAfter.ShowRowErrors = false;
            this.dataGridViewPinMasterAfter.Size = new System.Drawing.Size(861, 145);
            this.dataGridViewPinMasterAfter.TabIndex = 3;
            // 
            // dataGridViewSAPPinInfoAfter
            // 
            this.dataGridViewSAPPinInfoAfter.AllowUserToAddRows = false;
            this.dataGridViewSAPPinInfoAfter.AllowUserToDeleteRows = false;
            this.dataGridViewSAPPinInfoAfter.AllowUserToResizeRows = false;
            this.dataGridViewSAPPinInfoAfter.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.AllCells;
            this.dataGridViewSAPPinInfoAfter.AutoSizeRowsMode = System.Windows.Forms.DataGridViewAutoSizeRowsMode.AllCells;
            this.dataGridViewSAPPinInfoAfter.BackgroundColor = System.Drawing.SystemColors.Control;
            this.dataGridViewSAPPinInfoAfter.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dataGridViewSAPPinInfoAfter.CausesValidation = false;
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle7.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle7.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle7.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle7.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewSAPPinInfoAfter.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle7;
            this.dataGridViewSAPPinInfoAfter.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle8.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle8.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle8.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle8.Format = "G";
            dataGridViewCellStyle8.NullValue = "NULL";
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle8.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewSAPPinInfoAfter.DefaultCellStyle = dataGridViewCellStyle8;
            this.dataGridViewSAPPinInfoAfter.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewSAPPinInfoAfter.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
            this.dataGridViewSAPPinInfoAfter.Location = new System.Drawing.Point(3, 239);
            this.dataGridViewSAPPinInfoAfter.MinimumSize = new System.Drawing.Size(0, 112);
            this.dataGridViewSAPPinInfoAfter.Name = "dataGridViewSAPPinInfoAfter";
            this.dataGridViewSAPPinInfoAfter.ReadOnly = true;
            dataGridViewCellStyle9.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle9.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle9.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle9.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle9.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle9.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle9.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewSAPPinInfoAfter.RowHeadersDefaultCellStyle = dataGridViewCellStyle9;
            this.dataGridViewSAPPinInfoAfter.RowHeadersVisible = false;
            this.dataGridViewSAPPinInfoAfter.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect;
            this.dataGridViewSAPPinInfoAfter.ShowCellErrors = false;
            this.dataGridViewSAPPinInfoAfter.ShowCellToolTips = false;
            this.dataGridViewSAPPinInfoAfter.ShowEditingIcon = false;
            this.dataGridViewSAPPinInfoAfter.ShowRowErrors = false;
            this.dataGridViewSAPPinInfoAfter.Size = new System.Drawing.Size(861, 145);
            this.dataGridViewSAPPinInfoAfter.TabIndex = 2;

            // 
            // dataGridViewPinMasterBefore
            // 
            this.dataGridViewPinMasterBefore.AllowUserToAddRows = false;
            this.dataGridViewPinMasterBefore.AllowUserToDeleteRows = false;
            this.dataGridViewPinMasterBefore.AllowUserToResizeRows = false;
            this.dataGridViewPinMasterBefore.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.AllCells;
            this.dataGridViewPinMasterBefore.AutoSizeRowsMode = System.Windows.Forms.DataGridViewAutoSizeRowsMode.AllCells;
            this.dataGridViewPinMasterBefore.BackgroundColor = System.Drawing.SystemColors.Control;
            this.dataGridViewPinMasterBefore.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dataGridViewPinMasterBefore.CausesValidation = false;
            dataGridViewCellStyle10.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle10.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle10.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle10.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle10.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle10.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle10.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewPinMasterBefore.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle10;
            this.dataGridViewPinMasterBefore.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle11.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle11.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle11.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle11.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle11.Format = "G";
            dataGridViewCellStyle11.NullValue = "NULL";
            dataGridViewCellStyle11.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle11.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle11.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewPinMasterBefore.DefaultCellStyle = dataGridViewCellStyle11;
            this.dataGridViewPinMasterBefore.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewPinMasterBefore.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
            this.dataGridViewPinMasterBefore.Location = new System.Drawing.Point(3, 435);
            this.dataGridViewPinMasterBefore.MinimumSize = new System.Drawing.Size(0, 112);
            this.dataGridViewPinMasterBefore.Name = "dataGridViewPinMasterBefore";
            this.dataGridViewPinMasterBefore.ReadOnly = true;
            dataGridViewCellStyle12.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle12.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle12.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle12.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle12.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle12.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle12.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewPinMasterBefore.RowHeadersDefaultCellStyle = dataGridViewCellStyle12;
            this.dataGridViewPinMasterBefore.RowHeadersVisible = false;
            this.dataGridViewPinMasterBefore.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect;
            this.dataGridViewPinMasterBefore.ShowCellErrors = false;
            this.dataGridViewPinMasterBefore.ShowCellToolTips = false;
            this.dataGridViewPinMasterBefore.ShowEditingIcon = false;
            this.dataGridViewPinMasterBefore.ShowRowErrors = false;
            this.dataGridViewPinMasterBefore.Size = new System.Drawing.Size(861, 145);
            this.dataGridViewPinMasterBefore.TabIndex = 1;
            // 
            // dataGridViewLogs
            // 
            this.dataGridViewLogs.AllowUserToAddRows = false;
            this.dataGridViewLogs.AllowUserToDeleteRows = false;
            this.dataGridViewLogs.AllowUserToResizeRows = false;
            this.dataGridViewLogs.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.DisplayedCells;
            this.dataGridViewLogs.BackgroundColor = System.Drawing.SystemColors.Control;
            this.dataGridViewLogs.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dataGridViewLogs.CausesValidation = false;
            dataGridViewCellStyle13.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle13.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle13.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle13.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle13.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle13.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle13.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewLogs.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle13;
            this.dataGridViewLogs.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle14.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle14.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle14.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle14.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle14.Format = "G";
            dataGridViewCellStyle14.NullValue = "NULL";
            dataGridViewCellStyle14.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle14.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle14.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridViewLogs.DefaultCellStyle = dataGridViewCellStyle14;
            this.dataGridViewLogs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewLogs.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
            this.dataGridViewLogs.Location = new System.Drawing.Point(3, 822);
            this.dataGridViewLogs.MinimumSize = new System.Drawing.Size(0, 112);
            this.dataGridViewLogs.Name = "dataGridViewLogs";
            this.dataGridViewLogs.ReadOnly = true;
            dataGridViewCellStyle15.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle15.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle15.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle15.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle15.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle15.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle15.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridViewLogs.RowHeadersDefaultCellStyle = dataGridViewCellStyle15;
            this.dataGridViewLogs.RowHeadersVisible = false;
            this.dataGridViewLogs.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect;
            this.dataGridViewLogs.ShowCellErrors = false;
            this.dataGridViewLogs.ShowCellToolTips = false;
            this.dataGridViewLogs.ShowEditingIcon = false;
            this.dataGridViewLogs.ShowRowErrors = false;
            this.dataGridViewLogs.Size = new System.Drawing.Size(861, 145);
            this.dataGridViewLogs.TabIndex = 8;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(3, 799);
            this.label5.MinimumSize = new System.Drawing.Size(0, 20);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(213, 20);
            this.label5.TabIndex = 9;
            this.label5.Text = "supLogMain (After - LoadSAPPinInfo_Carlo)";
            // 
            // buttonExportResults
            // 
            this.buttonExportResults.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.buttonExportResults.Location = new System.Drawing.Point(384, 827);
            this.buttonExportResults.Name = "buttonExportResults";
            this.buttonExportResults.Size = new System.Drawing.Size(105, 23);
            this.buttonExportResults.TabIndex = 8;
            this.buttonExportResults.Text = "Export Results";
            this.buttonExportResults.UseVisualStyleBackColor = true;
            this.buttonExportResults.Click += new System.EventHandler(this.buttonExportResults_Click);
            // 
            // tableLayoutPanelCore
            // 
            this.tableLayoutPanelCore.AutoScroll = true;
            this.tableLayoutPanelCore.ColumnCount = 1;
            this.tableLayoutPanelCore.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanelCore.Controls.Add(this.tableLayoutPanelTop, 0, 0);
            this.tableLayoutPanelCore.Controls.Add(this.buttonExportResults, 0, 1);
            this.tableLayoutPanelCore.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanelCore.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanelCore.Name = "tableLayoutPanelCore";
            this.tableLayoutPanelCore.RowCount = 2;
            this.tableLayoutPanelCore.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanelCore.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40F));
            this.tableLayoutPanelCore.Size = new System.Drawing.Size(873, 859);
            this.tableLayoutPanelCore.TabIndex = 9;
            // 
            // ShipmentResultsForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(873, 859);
            this.Controls.Add(this.tableLayoutPanelCore);
            this.Name = "ShipmentResultsForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Shipment - Results View";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewSAPPinInfoBefore)).EndInit();
            this.tableLayoutPanelTop.ResumeLayout(false);
            this.tableLayoutPanelTop.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewPinMasterAfter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewSAPPinInfoAfter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewPinMasterBefore)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewLogs)).EndInit();
            this.tableLayoutPanelCore.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        public System.Windows.Forms.DataGridView dataGridViewSAPPinInfoBefore;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanelTop;
        public System.Windows.Forms.DataGridView dataGridViewPinMasterBefore;
        public System.Windows.Forms.DataGridView dataGridViewSAPPinInfoAfter;
        public System.Windows.Forms.DataGridView dataGridViewPinMasterAfter;
        private System.Windows.Forms.Button buttonExportResults;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanelCore;
        public System.Windows.Forms.DataGridView dataGridViewLogs;
        private System.Windows.Forms.Label label5;

    }
}