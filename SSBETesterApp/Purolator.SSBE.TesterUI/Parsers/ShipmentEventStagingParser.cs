﻿using Purolator.SSBE.Business.Entities;
using Purolator.SSBE.Utilities;
using Purolator.SSBE.Utilities.Csv;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SSBE.TesterUI.Parsers
{
    public static class ShipmentEventStagingParser
    { 
        #region Public Static Methods
        public static void InitializeShipmentEventStagingMessage(ShipmentEventStagingMessage item, bool isForce = false)
        {
            if (String.IsNullOrWhiteSpace(item.MessageGUID) || isForce)
            {
                item.MessageGUID = RandomizerHelper.GetRandomMessageGuid();
            }
            if (item.FWODocumentNo == 0 || isForce)
            {
                item.FWODocumentNo = RandomizerHelper.GetRandomLong(90210, long.MaxValue);
            }
            if (item.EventDateTimeTZ == DateTimeOffset.MinValue || isForce)
            {
               item.EventDateTimeTZ = DateTimeOffset.Now;
            }
            if (String.IsNullOrWhiteSpace(item.ShipmentDate) || isForce)
            {
                item.ShipmentDate = DateTime.Now.ToString();
            }
            if (String.IsNullOrWhiteSpace(item.ExpectedDeliveryDate) || isForce)
            {
                item.ExpectedDeliveryDate = DateTime.Now.ToString();
            }
            if (String.IsNullOrWhiteSpace(item.PiecePin) || isForce)
            {
                item.PiecePin = RandomizerHelper.GetRandomIntegerString(35);
            }
            if (String.IsNullOrWhiteSpace(item.ShipmentPin) || isForce)
            {
                item.ShipmentPin = item.PiecePin;
            }
        }

        public static List<ShipmentEventStaging> Parse(string fileName, bool isDefaultInitialziation)
        {
            CsvManager<ShipmentEventStagingMessage> shipmentEventStagingMessageManager = new CsvManager<ShipmentEventStagingMessage>(fileName, true);
            List<ShipmentEventStaging> shipmentEventStagingItems = new List<ShipmentEventStaging>();
            List<ShipmentEventStagingMessage> shipmentEventStagingMessages = null;

            shipmentEventStagingMessageManager.SetField(x => x.MessageGUID, 0);
            shipmentEventStagingMessageManager.SetField(x => x.FWODocumentNo, 1);
            shipmentEventStagingMessageManager.SetField(x => x.EventExternalCode, 2);
            shipmentEventStagingMessageManager.SetField(x => x.LastUpdatedTime, 3);
            shipmentEventStagingMessageManager.SetField(x => x.SenderCodeSet, 4);
            shipmentEventStagingMessageManager.SetField(x => x.SenderID, 5);
            shipmentEventStagingMessageManager.SetField(x => x.SenderName, 6);
            shipmentEventStagingMessageManager.SetField(x => x.FreightUnit, 7);
            shipmentEventStagingMessageManager.SetField(x => x.PiecePin, 8);
            shipmentEventStagingMessageManager.SetField(x => x.ShipmentPin, 9);
            shipmentEventStagingMessageManager.SetField(x => x.ServiceLevel, 10);
            shipmentEventStagingMessageManager.SetField(x => x.TransportMode, 11);
            shipmentEventStagingMessageManager.SetField(x => x.ServicePriority, 12);
            shipmentEventStagingMessageManager.SetField(x => x.PackageType, 13);
            shipmentEventStagingMessageManager.SetField(x => x.MaterialHandlingType, 14);
            shipmentEventStagingMessageManager.SetField(x => x.ReturnType, 15);
            shipmentEventStagingMessageManager.SetField(x => x.ProductNumber, 16);
            shipmentEventStagingMessageManager.SetField(x => x.ShipmentDate, 17);
            shipmentEventStagingMessageManager.SetField(x => x.Weight, 18);
            shipmentEventStagingMessageManager.SetField(x => x.WeightUnit, 19);
            shipmentEventStagingMessageManager.SetField(x => x.OriginTerminal, 20);
            shipmentEventStagingMessageManager.SetField(x => x.OriginSiteID, 21);
            shipmentEventStagingMessageManager.SetField(x => x.DestinationTerminal, 22);
            shipmentEventStagingMessageManager.SetField(x => x.DestinationSiteID, 23);
            shipmentEventStagingMessageManager.SetField(x => x.ExpectedDeliveryDate, 24);
            shipmentEventStagingMessageManager.SetField(x => x.DeclareAddressLine1, 25);
            shipmentEventStagingMessageManager.SetField(x => x.DeclareAddressLine2, 26);
            shipmentEventStagingMessageManager.SetField(x => x.DeclareAddressLine3, 27);
            shipmentEventStagingMessageManager.SetField(x => x.DeclareCompanyName, 28);
            shipmentEventStagingMessageManager.SetField(x => x.DeclareReceiverAttnName, 29);
            shipmentEventStagingMessageManager.SetField(x => x.DeclareCity, 30);
            shipmentEventStagingMessageManager.SetField(x => x.DeclareProvince, 31);
            shipmentEventStagingMessageManager.SetField(x => x.DeclarePostalCode, 32);
            shipmentEventStagingMessageManager.SetField(x => x.DeclareCountry, 33);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentSuiteNum, 34);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentStreetNum, 35);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentStreetNumSuf, 36);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentStreetName, 37);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentStreetType, 38);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentStreetDir, 39);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentCity, 40);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentProvince, 41);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentPostalCode, 42);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentCountry, 43);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentCompanyName, 44);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentAddressLine2, 45);
            shipmentEventStagingMessageManager.SetField(x => x.CurrentAddressLine3, 46);
            shipmentEventStagingMessageManager.SetField(x => x.AddressType, 47);
            shipmentEventStagingMessageManager.SetField(x => x.PurolatorAVInfo, 48);
            shipmentEventStagingMessageManager.SetField(x => x.PurolatorAVStatus, 49);

            shipmentEventStagingMessages = shipmentEventStagingMessageManager.GetObjectList();
            shipmentEventStagingMessages.ForEach((item) =>
                {
                    ShipmentEventStaging shipmentEventStagingItem = new ShipmentEventStaging()
                    {
                        Message = item
                    };

                    if (isDefaultInitialziation)
                    {
                        InitializeShipmentEventStagingMessage(item);
                    }
                    shipmentEventStagingItems.Add(shipmentEventStagingItem);
                });

            return shipmentEventStagingItems;
        }
        #endregion
    }
}
