﻿using Purolator.SSBE.Business.Entities;
using Purolator.SSBE.DAL;
using Purolator.SSBE.TesterUI.Helpers;
using Purolator.SSBE.TesterUI.Parsers;
using Purolator.SSBE.Utilities;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Purolator.SSBE.TesterUI
{
    public partial class MainForm : Form
    {
        #region Private Constants
        private const string ScreenshotsFolderName = "Screenshots";
        private const string PreStagingShipmentSourceFileName = "PreStagingShipmentSource.csv";
        private const string PostStagingShipmentSourceFileName = "PostStagingShipmentSource.csv";
        #endregion

        #region Private Properties
        private bool IsShipmentLoaded { get; set; }
        private bool IsPinMasterBeforeBound { get; set; }
        private bool IsPinMasterAfterBound { get; set; }
        private bool IsSAPInfoBeforeBound { get; set; }
        private bool IsSAPInfoAfterBound { get; set; }

        private ShipmentResultsForm ShipmentResultsForm { get; set; }
        private bool IsInputValid
        {
            get
            {
                return File.Exists(textBoxPreStagingShipmentSource.Text) && File.Exists(textBoxPostStagingShipmentSource.Text) && checkedListBoxEventExternalCode.CheckedItems.Count > 0 && (checkedListBoxSAPNoRecordPinMasterState.CheckedItems.Count > 0 || checkedListBoxSAPRecordExistsPinMasterState.CheckedItems.Count > 0 ||
                    checkBoxSAPNoRecordPinMasterNoRecord.Checked || checkBoxSAPRecordExistsPinMasterNoRecord.Checked);
            }
        }

        private bool IsPreserveStagingData
        {
            get { return checkBoxPreserveShipmentData.Checked; }
        }

        private bool IsStartStopShiftPreStaging
        {
            get { return checkBoxStartStopShiftPreStaging.Checked; }
        }

        private bool IsStartStopShiftPostStaging
        {
            get { return checkBoxStartStopShiftPostStaging.Checked; }
        }

        private bool IsGenerateUniquePrePostStagingFWO
        {
            get { return checkBoxUniquePrePostStagingFWO.Checked; }
        }

        private string ScreenshotsPath { get; set; }
        #endregion

        #region Public Constructors
        public MainForm()
        { 
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            Application.ThreadException += Application_ThreadException;
            CultureInfo.DefaultThreadCurrentCulture = new CultureInfo("en-US");

            InitializeComponent();
            InitializeForm();
        }
        #endregion

        #region Event Handlers
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            ShowErrorMessageBox(((Exception)e.ExceptionObject).Message);
        }

        private void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            ShowErrorMessageBox(e.Exception.Message);
        }

        private void btnLoadShipment_Click(object sender, EventArgs e)
        {
            LoadShipment();
        }

        private void buttonPreStagingShipmentSourceBrowse_Click(object sender, EventArgs e)
        {
            ShowOpenFileDialog(true);
        }

        private void buttonPostStagingShipmentSourceBrowse_Click(object sender, EventArgs e)
        {
            ShowOpenFileDialog(false);
        }

        private void dataGridViewSAPPinInfoBefore_DataBindingComplete(object sender, DataGridViewBindingCompleteEventArgs e)
        {
            IsSAPInfoBeforeBound = true;

            if (IsSAPInfoAfterBound && IsSAPInfoBeforeBound)
            {
                ApplySAPPinInfoAfterDataGridViewStyles();
            }
        }

        private void dataGridViewSAPPinInfoAfter_DataBindingComplete(object sender, DataGridViewBindingCompleteEventArgs e)
        {
            IsSAPInfoAfterBound = true;

            if (IsSAPInfoAfterBound && IsSAPInfoBeforeBound)
            {
                ApplySAPPinInfoAfterDataGridViewStyles();             
            }
        }


        private void dataGridViewPinMasterBefore_DataBindingComplete(object sender, DataGridViewBindingCompleteEventArgs e)
        {
            IsPinMasterBeforeBound = true;

            if (IsPinMasterAfterBound && IsPinMasterBeforeBound)
            {
                ApplyPinMasterAfterDataGridViewStyles();
            }
        }

        private void dataGridViewPinMasterAfter_DataBindingComplete(object sender, DataGridViewBindingCompleteEventArgs e)
        {
            IsPinMasterAfterBound = true;

            if (IsPinMasterAfterBound && IsPinMasterBeforeBound)
            {
                ApplyPinMasterAfterDataGridViewStyles();
            }
        }

        private void radioButtonDev_CheckedChanged(object sender, EventArgs e)
        {
            StoredProcedures.ConfigureEnvironment(StoredProcedures.Environment.Dev);
        }

        private void radioButtonCert_CheckedChanged(object sender, EventArgs e)
        {
            StoredProcedures.ConfigureEnvironment(StoredProcedures.Environment.Cert);
        }

        private void checkedListBoxEventExternalCode_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnLoadShipment.Enabled = IsInputValid;
        }

        private void checkedListBoxSAPNoRecordPinMasterState_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnLoadShipment.Enabled = IsInputValid;
        }

        private void checkedListBoxSAPRecordExistsPinMasterState_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnLoadShipment.Enabled = IsInputValid;
        }

        private void checkBoxSAPRecordExistsPinMasterNoRecord_CheckedChanged(object sender, EventArgs e)
        {
            btnLoadShipment.Enabled = IsInputValid;
        }

        private void checkBoxSAPNoRecordPinMasterNoRecord_CheckedChanged(object sender, EventArgs e)
        {
            btnLoadShipment.Enabled = IsInputValid;
        }

        private void buttonSAPNoRecordAll_Click(object sender, EventArgs e)
        {
            SetSAPNoRecordCheckedState(true);
        }

        private void buttonSAPNoRecordNone_Click(object sender, EventArgs e)
        {
            SetSAPNoRecordCheckedState(false);
        }

        private void buttonSAPRecordExistsAll_Click(object sender, EventArgs e)
        {
            SetSAPRecordExistsCheckedState(true);
        }

        private void buttonSAPRecordExistsNone_Click(object sender, EventArgs e)
        {
            SetSAPRecordExistsCheckedState(false);
        }

        private void dataGridViewSAPPinInfoAfter_Sorted(object sender, EventArgs e)
        {
            if (IsSAPInfoAfterBound && IsSAPInfoBeforeBound)
            {
               // ApplySAPPinInfoAfterDataGridViewStyles();
            }
        }

        private void dataGridViewPinMasterAfter_Sorted(object sender, EventArgs e)
        {
            if (IsPinMasterAfterBound && IsPinMasterBeforeBound)
            {
               // ApplyPinMasterAfterDataGridViewStyles();
            }
        }
        #endregion

        #region Private Methods
        private void ShowErrorMessageBox(string errorMessage)
        {
            MessageBox.Show("The following error occurred:\n\n" + errorMessage, "Application Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void InitializeForm()
        {
            string defaultPreStagingShipmentSourceFileName = Path.Combine(Environment.CurrentDirectory, PreStagingShipmentSourceFileName);
            string defaultPostStagingShipmentSourceFileName = Path.Combine(Environment.CurrentDirectory, PostStagingShipmentSourceFileName);

            ScreenshotsPath = Path.Combine(Environment.CurrentDirectory, ScreenshotsFolderName);

            if (File.Exists(defaultPreStagingShipmentSourceFileName))
            {
                textBoxPreStagingShipmentSource.Text = defaultPreStagingShipmentSourceFileName;
                btnLoadShipment.Enabled = IsInputValid;
            }
            else
            {
                textBoxPreStagingShipmentSource.Text = String.Empty;
                btnLoadShipment.Enabled = false;
            }


            if (File.Exists(defaultPostStagingShipmentSourceFileName))
            {
                textBoxPostStagingShipmentSource.Text = defaultPostStagingShipmentSourceFileName;
                btnLoadShipment.Enabled = IsInputValid;
            }
            else
            {
                textBoxPostStagingShipmentSource.Text = String.Empty;
                btnLoadShipment.Enabled = false;
            }

            StoredProcedures.ConfigureEnvironment(StoredProcedures.Environment.Dev);
        }

        private void SetSAPNoRecordCheckedState(bool isChecked)
        {
            checkBoxSAPNoRecordPinMasterNoRecord.Checked = isChecked;

            for (int i = 0; i < checkedListBoxSAPNoRecordPinMasterState.Items.Count; i++)
            {
                checkedListBoxSAPNoRecordPinMasterState.SetItemChecked(i, isChecked);
            } 
        }

        private void SetSAPRecordExistsCheckedState(bool isChecked)
        {
            checkBoxSAPRecordExistsPinMasterNoRecord.Checked = isChecked;

            for (int i = 0; i < checkedListBoxSAPRecordExistsPinMasterState.Items.Count; i++)
            {
                checkedListBoxSAPRecordExistsPinMasterState.SetItemChecked(i, isChecked);
            } 
        }

        private void ShowOpenFileDialog(bool isPreStagingShipmentSource)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();

            openFileDialog.InitialDirectory = Environment.CurrentDirectory;
            openFileDialog.Title = "Browse Shipment Source Files";

            openFileDialog.CheckFileExists = true;
            openFileDialog.CheckPathExists = true;

            openFileDialog.DefaultExt = "csv";
            openFileDialog.Filter = "Comma delimited (*.csv)|*.csv";
            openFileDialog.FilterIndex = 2;
            openFileDialog.RestoreDirectory = true;

            openFileDialog.ReadOnlyChecked = true;
            openFileDialog.ShowReadOnly = true;

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                if (isPreStagingShipmentSource)
                {
                    textBoxPreStagingShipmentSource.Text = openFileDialog.FileName;
                }
                else
                {
                    textBoxPostStagingShipmentSource.Text = openFileDialog.FileName;

                }

                btnLoadShipment.Enabled = IsInputValid;
            }
        }

        private List<DataSet> LoadShipment(int shipmentId, ShipmentEventStaging preShipmentEventStagingItem, ShipmentEventStaging postShipmentEventStagingItem, string pinMasterStateCode, bool isSAPRecordExists, bool isPinMasterRecordExists)
        {
            ShipmentEventStaging _preShipmentEventStagingItem = preShipmentEventStagingItem.DeepClone<ShipmentEventStaging>();
            ShipmentEventStaging _postShipmentEventStagingItem = postShipmentEventStagingItem.DeepClone<ShipmentEventStaging>();

            ShipmentEventStagingParser.InitializeShipmentEventStagingMessage(_preShipmentEventStagingItem.Message);

            List<DataSet> dataSets = new List<DataSet>();
            string messageGuid = _preShipmentEventStagingItem.Message.MessageGUID.ToString();
            string piecePin = _preShipmentEventStagingItem.Message.PiecePin;
            string postalCode = _preShipmentEventStagingItem.Message.DeclarePostalCode;
            long fwoDocumentNumber = _preShipmentEventStagingItem.Message.FWODocumentNo;
            string preShipmentEventStagingTerminal = _preShipmentEventStagingItem.Message.DestinationTerminal;
            string postShipmentEventStagingTerminal = _postShipmentEventStagingItem.Message.DestinationTerminal;

            ///MessageBox.Show(_preShipmentEventStagingItem.ToXml());

            DateTimeOffset eventDateTimeTZ = _preShipmentEventStagingItem.Message.EventDateTimeTZ;
            StringBuilder sbShipmentDescription = new StringBuilder();

            if (isSAPRecordExists)
            {
                sbShipmentDescription.Append("SI_RECORD_EXISTS | ");
            }
            else
            {
                sbShipmentDescription.Append("SI_NO_RECORD | ");
            }
            if (isPinMasterRecordExists)
            {
                sbShipmentDescription.AppendFormat("PM_RECORD_EXISTS | {0} | ", pinMasterStateCode);
            }
            else
            {
                sbShipmentDescription.Append("PM_NO_RECORD | ");
            }

            if (!String.IsNullOrWhiteSpace(_postShipmentEventStagingItem.Message.ReturnType))
            {
                sbShipmentDescription.AppendFormat("{0} | ", _postShipmentEventStagingItem.Message.ReturnType);
            }

            sbShipmentDescription.Append(_postShipmentEventStagingItem.Message.EventExternalCode);

            if (isSAPRecordExists && !isPinMasterRecordExists)
            {
                StoredProcedures.InsertSAPMessageSTG(_preShipmentEventStagingItem);

                if (IsStartStopShiftPreStaging)
                {
                    StoredProcedures.StartShift(preShipmentEventStagingTerminal);
                }

                StoredProcedures.LoadSAPPinInfo();
                StoredProcedures.DeleteRecordFromPinMaster(piecePin);

                if (IsStartStopShiftPreStaging)
                {
                    StoredProcedures.StopShift(preShipmentEventStagingTerminal);
                }
            }
            else if (isPinMasterRecordExists)
            {
                StoredProcedures.InsertSAPMessageSTG(_preShipmentEventStagingItem);

                if (IsStartStopShiftPreStaging)
                {
                    StoredProcedures.StartShift(preShipmentEventStagingTerminal);
                }

                StoredProcedures.LoadSAPPinInfo();
                StoredProcedures.UpdatePinMasterStateCode(piecePin, pinMasterStateCode);
                StoredProcedures.UpdatePinMasterPostalCode(piecePin, postalCode);

                if (!isSAPRecordExists)
                {
                    StoredProcedures.DeleteRecordFromSAPPinInfo(piecePin);
                }

                if (IsStartStopShiftPreStaging)
                {
                    StoredProcedures.StopShift(preShipmentEventStagingTerminal);
                }
            }

            dataSets.Add(StoredProcedures.GetSAPPinInfo(piecePin));
            dataSets.Add(StoredProcedures.GetPinMasterInfo(piecePin));

            ShipmentEventStagingParser.InitializeShipmentEventStagingMessage(_postShipmentEventStagingItem.Message);

            _postShipmentEventStagingItem.Message.PiecePin = _postShipmentEventStagingItem.Message.ShipmentPin = piecePin;
            _postShipmentEventStagingItem.Message.EventDateTimeTZ = DateTime.Now;

            if (!IsGenerateUniquePrePostStagingFWO)
            {
                _postShipmentEventStagingItem.Message.FWODocumentNo = fwoDocumentNumber;
            }

            StoredProcedures.InsertSAPMessageSTG(_postShipmentEventStagingItem);

            if (IsStartStopShiftPostStaging)
            {
                StoredProcedures.StartShift(postShipmentEventStagingTerminal);
            }

            StoredProcedures.LoadSAPPinInfo();

            if (IsStartStopShiftPostStaging)
            {
                StoredProcedures.StopShift(postShipmentEventStagingTerminal);
            }

            dataSets.Add(StoredProcedures.GetSAPPinInfo(piecePin));
            dataSets.Add(StoredProcedures.GetPinMasterInfo(piecePin));

            if (!IsPreserveStagingData)
            {
                StoredProcedures.DeleteRecordFromSAPMessageStg(piecePin);
                StoredProcedures.DeleteRecordFromSAPPinInfo(piecePin);
                StoredProcedures.DeleteRecordFromPinMaster(piecePin);
            }

            ApplyDataSetChanges(dataSets, shipmentId, sbShipmentDescription.ToString());

            return dataSets;
        }

        //private void ApplySAPPinInfoAfterDataGridViewStyles()
        //{
        //    DataTable SAPPinInfoBeforeDataSet = ShipmentResultsForm.dataGridViewSAPPinInfoBefore.DataSource as DataTable;
        //    DataTable SAPPinInfoAfterDataSet = ShipmentResultsForm.dataGridViewSAPPinInfoAfter.DataSource as DataTable;

        //    if (SAPPinInfoBeforeDataSet != null && SAPPinInfoAfterDataSet != null)
        //    {
        //        for (int rowIndex = 0; rowIndex < SAPPinInfoAfterDataSet.Rows.Count; rowIndex++)
        //        {
        //            for (int columnIndex = 0; columnIndex < SAPPinInfoAfterDataSet.Columns.Count; columnIndex++)
        //            {
        //                string SAPPinInfoCellBefore = ShipmentResultsForm.dataGridViewSAPPinInfoBefore.Rows[rowIndex].Cells[columnIndex] != null ? ShipmentResultsForm.dataGridViewSAPPinInfoBefore.Rows[rowIndex].Cells[columnIndex].Value.ToString() : String.Empty;
        //                string SAPPinInfoCellAfter = ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells[columnIndex] != null ? ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells[columnIndex].Value.ToString() : String.Empty;
        //                string piecePinCellBefore = ShipmentResultsForm.dataGridViewSAPPinInfoBefore.Rows[rowIndex].Cells["PiecePin"] != null ? ShipmentResultsForm.dataGridViewSAPPinInfoBefore.Rows[rowIndex].Cells["PiecePin"].Value.ToString() : String.Empty;
        //                string pinMasterIdBefore = ShipmentResultsForm.dataGridViewSAPPinInfoBefore.Rows[rowIndex].Cells["PinMasterId"] != null ? ShipmentResultsForm.dataGridViewSAPPinInfoBefore.Rows[rowIndex].Cells["PinMasterId"].Value.ToString() : String.Empty;
        //                string pinMasterIdAfter = ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells["PinMasterId"] != null ? ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells["PinMasterId"].Value.ToString() : String.Empty;
        //                string messageGuid = ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells["MessageGUID"] != null ? ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells["MessageGUID"].Value.ToString() : String.Empty;

        //                if (!String.IsNullOrWhiteSpace(messageGuid) && String.Equals(piecePinCellBefore, String.Empty))
        //                {
        //                    ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells[columnIndex].Style.BackColor = Color.Orange;
        //                }
        //                else if (!String.IsNullOrWhiteSpace(pinMasterIdBefore) && String.IsNullOrWhiteSpace(pinMasterIdAfter))
        //                {
        //                    ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells[columnIndex].Style.ForeColor = Color.White;
        //                    ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells[columnIndex].Style.BackColor = Color.Red;
        //                }
        //                else if (!String.Equals(SAPPinInfoCellBefore, SAPPinInfoCellAfter, StringComparison.OrdinalIgnoreCase))
        //                {
        //                    ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells[columnIndex].Style.BackColor = Color.Yellow;
        //                }
        //            }
        //        }
        //    }
        //}


        private void ApplySAPPinInfoAfterDataGridViewStyles()
        {
            DataTable SAPPinInfoBeforeDataSet = ShipmentResultsForm.dataGridViewSAPPinInfoBefore.DataSource as DataTable;
            DataTable SAPPinInfoAfterDataSet = ShipmentResultsForm.dataGridViewSAPPinInfoAfter.DataSource as DataTable;

            if (SAPPinInfoBeforeDataSet != null && SAPPinInfoAfterDataSet != null)
            {
                for (int rowIndex = 0; rowIndex < SAPPinInfoAfterDataSet.Rows.Count; rowIndex++)
                {
                    for (int columnIndex = 0; columnIndex < SAPPinInfoAfterDataSet.Columns.Count; columnIndex++)
                    {
                        string SAPPinInfoCellBefore = SAPPinInfoBeforeDataSet.Rows[rowIndex][columnIndex] != null ? SAPPinInfoBeforeDataSet.Rows[rowIndex][columnIndex].ToString() : String.Empty;
                        string SAPPinInfoCellAfter = SAPPinInfoAfterDataSet.Rows[rowIndex][columnIndex] != null ? ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells[columnIndex].Value.ToString() : String.Empty;
                        string piecePinCellBefore = SAPPinInfoBeforeDataSet.Rows[rowIndex]["PiecePin"] != null ? SAPPinInfoBeforeDataSet.Rows[rowIndex]["PiecePin"].ToString() : String.Empty;
                        string pinMasterIdBefore = SAPPinInfoBeforeDataSet.Rows[rowIndex]["PinMasterId"] != null ? SAPPinInfoBeforeDataSet.Rows[rowIndex]["PinMasterId"].ToString() : String.Empty;
                        string pinMasterIdAfter = SAPPinInfoAfterDataSet.Rows[rowIndex]["PinMasterId"] != null ? SAPPinInfoAfterDataSet.Rows[rowIndex]["PinMasterId"].ToString() : String.Empty;
                        string messageGuid = SAPPinInfoAfterDataSet.Rows[rowIndex]["MessageGUID"] != null ? SAPPinInfoAfterDataSet.Rows[rowIndex]["MessageGUID"].ToString() : String.Empty;

                        if (!String.IsNullOrWhiteSpace(messageGuid) && String.Equals(piecePinCellBefore, String.Empty))
                        {
                            ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells[columnIndex].Style.BackColor = Color.Orange;
                        }
                        else if (!String.IsNullOrWhiteSpace(pinMasterIdBefore) && String.IsNullOrWhiteSpace(pinMasterIdAfter))
                        {
                            ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells[columnIndex].Style.ForeColor = Color.White;
                            ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells[columnIndex].Style.BackColor = Color.Red;
                        }
                        else if (!String.Equals(SAPPinInfoCellBefore, SAPPinInfoCellAfter, StringComparison.OrdinalIgnoreCase))
                        {
                            ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Rows[rowIndex].Cells[columnIndex].Style.BackColor = Color.Yellow;
                        }
                    }
                }
            }
        }

        private void ApplyPinMasterAfterDataGridViewStyles()
        {
            DataTable pinMasterBeforeDataSet = ShipmentResultsForm.dataGridViewPinMasterBefore.DataSource as DataTable;
            DataTable pinMasterAfterDataSet = ShipmentResultsForm.dataGridViewPinMasterAfter.DataSource as DataTable;

            if (pinMasterBeforeDataSet != null && pinMasterAfterDataSet != null)
            {
                for (int rowIndex = 0; rowIndex < pinMasterAfterDataSet.Rows.Count; rowIndex++)
                {
                    for (int columnIndex = 0; columnIndex < pinMasterAfterDataSet.Columns.Count; columnIndex++)
                    {
                        string pinMasterCellBefore = pinMasterBeforeDataSet.Rows[rowIndex][columnIndex] != null ? pinMasterBeforeDataSet.Rows[rowIndex][columnIndex].ToString() : String.Empty;
                        string pinMasterCellAfter = pinMasterAfterDataSet.Rows[rowIndex][columnIndex] != null ? pinMasterAfterDataSet.Rows[rowIndex][columnIndex].ToString() : String.Empty;
                        string pinMasterIdBefore = pinMasterBeforeDataSet.Rows[rowIndex]["PinMasterId"] != null ? pinMasterBeforeDataSet.Rows[rowIndex]["PinMasterId"].ToString() : String.Empty;
                        string pinMasterIdAfter = pinMasterAfterDataSet.Rows[rowIndex]["PinMasterId"] != null ? pinMasterAfterDataSet.Rows[rowIndex]["PinMasterId"].ToString() : String.Empty;

                        if (String.Equals(pinMasterIdBefore, String.Empty) && !String.Equals(pinMasterIdAfter, String.Empty))
                        {
                            ShipmentResultsForm.dataGridViewPinMasterAfter.Rows[rowIndex].Cells[columnIndex].Style.BackColor = Color.Orange;
                        }
                        else if (!String.IsNullOrWhiteSpace(pinMasterIdBefore) && String.IsNullOrWhiteSpace(pinMasterIdAfter))
                        {
                            ShipmentResultsForm.dataGridViewPinMasterAfter.Rows[rowIndex].Cells[columnIndex].Style.ForeColor = Color.White;
                            ShipmentResultsForm.dataGridViewPinMasterAfter.Rows[rowIndex].Cells[columnIndex].Style.BackColor = Color.Red;
                        }
                        else if (!String.Equals(pinMasterCellBefore, pinMasterCellAfter, StringComparison.OrdinalIgnoreCase))
                        {
                            ShipmentResultsForm.dataGridViewPinMasterAfter.Rows[rowIndex].Cells[columnIndex].Style.BackColor = Color.Yellow;
                        }
                    }
                }
            }
        }

        private void ApplyDataSetChanges(List<DataSet> dataSets, int shipmentId, string shipmentDescription)
        {
            foreach (DataSet dataSet in dataSets)
            {
                if (dataSet.Tables.Count > 0)
                {
                    dataSet.Tables[0].Columns.Add("ID").SetOrdinal(0);
                    dataSet.Tables[0].Columns.Add("Description").SetOrdinal(1);

                    foreach (DataRow dataRow in dataSet.Tables[0].Rows)
                    {
                        dataRow[0] = shipmentId;
                        dataRow[1] = shipmentDescription;
                    }

                    if (dataSet.Tables[0].Columns.Contains("PinMasterId"))
                    {
                        dataSet.Tables[0].Columns["PinMasterId"].SetOrdinal(2);
                    }
                    if (dataSet.Tables[0].Columns.Contains("PiecePin"))
                    {
                        dataSet.Tables[0].Columns["PiecePin"].SetOrdinal(3);
                    }
                    if (dataSet.Tables[0].Columns.Contains("FWODocumentNo"))
                    {
                        dataSet.Tables[0].Columns["FWODocumentNo"].SetOrdinal(4);
                    }
                    if (dataSet.Tables[0].Columns.Contains("PinMasterStateCode"))
                    {
                        dataSet.Tables[0].Columns["PinMasterStateCode"].SetOrdinal(5);
                    }
                    if (dataSet.Tables[0].Columns.Contains("EventExternalCode"))
                    {
                        dataSet.Tables[0].Columns["EventExternalCode"].SetOrdinal(5);
                    }
                    if (dataSet.Tables[0].Columns.Contains("ReturnType"))
                    {
                        dataSet.Tables[0].Columns["ReturnType"].SetOrdinal(6);
                    }
                    if (dataSet.Tables[0].Columns.Contains("Status"))
                    {
                        dataSet.Tables[0].Columns["Status"].SetOrdinal(7);
                    }
                }
            }
        }

        private void LoadShipment()
        {
            DataSet SAPPinInfoBeforeDataSet = new DataSet(), SAPPinInfoAfterDataSet = new DataSet(), pinMasterBeforeDataSet = new DataSet(), pinMasterAfterDataSet = new DataSet();
            List<DataSet> shipmentDataSets = null;
            int shipmentCount = 0;

            if (this.IsInputValid)
            {
                if (File.ReadAllLines(textBoxPreStagingShipmentSource.Text).Length == File.ReadAllLines(textBoxPostStagingShipmentSource.Text).Length)
                {
                    IsShipmentLoaded = false;
                    List<ShipmentEventStaging> preStagingShipmentEventStagingItems = ShipmentEventStagingParser.Parse(textBoxPreStagingShipmentSource.Text, false);
                    List<ShipmentEventStaging> postStagingShipmentEventStagingItems = ShipmentEventStagingParser.Parse(textBoxPostStagingShipmentSource.Text, false);

                    ProgressDialog progressDialog = new ProgressDialog();

                    if (checkBoxCaptureScreen.Checked)
                    {
                        if (!Directory.Exists(ScreenshotsPath))
                        {
                            Directory.CreateDirectory(ScreenshotsPath);
                        }

                        string fileName = Path.Combine(ScreenshotsPath, String.Format("SSBE_TESTER_{0}_{1}.jpg", DateTime.Now.ToString("MMddyyyyhhmmss"), Guid.NewGuid().ToString("N")));
                        BitmapHelper.CaptureView(Bounds, fileName);
                    }

                    Thread thread = new Thread(() =>
                    {
                        bool isAnyEventExternalCodeProcessed = false;
                        StoredProcedures.LoadSAPPinInfo();  // Process any existing records
                        Thread.Sleep(50);

                        DateTime startDateTime = DateTime.Now;
                        DataSet logsDataSet = null;

                        for (int i = 0; i < postStagingShipmentEventStagingItems.Count; i++)
                        {
                            ShipmentEventStaging preStagingShipmentEventStagingItem = preStagingShipmentEventStagingItems[i];
                            ShipmentEventStaging postStagingShipmentEventStagingItem = postStagingShipmentEventStagingItems[i];

                            string eventExternalCode = postStagingShipmentEventStagingItem.Message.EventExternalCode;

                            if (checkedListBoxEventExternalCode.CheckedItems.Contains(eventExternalCode))
                            {
                                isAnyEventExternalCodeProcessed = true;
                                string pinMasterStateCode = String.Empty;

                                if (checkBoxSAPNoRecordPinMasterNoRecord.Checked)
                                {
                                    shipmentCount++;

                                    shipmentDataSets = LoadShipment(shipmentCount, preStagingShipmentEventStagingItem, postStagingShipmentEventStagingItem, String.Empty, false, false);
                                    SAPPinInfoBeforeDataSet.Merge(shipmentDataSets[0]);
                                    pinMasterBeforeDataSet.Merge(shipmentDataSets[1]);
                                    SAPPinInfoAfterDataSet.Merge(shipmentDataSets[2]);
                                    pinMasterAfterDataSet.Merge(shipmentDataSets[3]);
                                }

                                foreach (var checkedListItem in checkedListBoxSAPNoRecordPinMasterState.CheckedItems)
                                {
                                    shipmentCount++;

                                    pinMasterStateCode = checkedListItem.ToString();
                                    shipmentDataSets = LoadShipment(shipmentCount, preStagingShipmentEventStagingItem, postStagingShipmentEventStagingItem, pinMasterStateCode, false, true);

                                    if (shipmentDataSets != null && shipmentDataSets.Count == 4)
                                    {
                                        SAPPinInfoBeforeDataSet.Merge(shipmentDataSets[0]);
                                        pinMasterBeforeDataSet.Merge(shipmentDataSets[1]);
                                        SAPPinInfoAfterDataSet.Merge(shipmentDataSets[2]);
                                        pinMasterAfterDataSet.Merge(shipmentDataSets[3]);
                                    }
                                }

                                if (checkBoxSAPRecordExistsPinMasterNoRecord.Checked)
                                {
                                    shipmentCount++;

                                    shipmentDataSets = LoadShipment(shipmentCount, preStagingShipmentEventStagingItem, postStagingShipmentEventStagingItem, String.Empty, true, false);
                                    SAPPinInfoBeforeDataSet.Merge(shipmentDataSets[0]);
                                    pinMasterBeforeDataSet.Merge(shipmentDataSets[1]);
                                    SAPPinInfoAfterDataSet.Merge(shipmentDataSets[2]);
                                    pinMasterAfterDataSet.Merge(shipmentDataSets[3]);
                                }

                                foreach (var checkedListItem in checkedListBoxSAPRecordExistsPinMasterState.CheckedItems)
                                {
                                    shipmentCount++;

                                    pinMasterStateCode = checkedListItem.ToString();
                                    shipmentDataSets = LoadShipment(shipmentCount, preStagingShipmentEventStagingItem, postStagingShipmentEventStagingItem, pinMasterStateCode, true, true);

                                    if (shipmentDataSets != null && shipmentDataSets.Count == 4)
                                    {
                                        SAPPinInfoBeforeDataSet.Merge(shipmentDataSets[0]);
                                        pinMasterBeforeDataSet.Merge(shipmentDataSets[1]);
                                        SAPPinInfoAfterDataSet.Merge(shipmentDataSets[2]);
                                        pinMasterAfterDataSet.Merge(shipmentDataSets[3]);
                                    }
                                }
                            }
                        }

                        if (isAnyEventExternalCodeProcessed)
                        {
                            logsDataSet = StoredProcedures.GetSAPPinInfoLogs(startDateTime);

                            if (!IsPreserveStagingData)
                            {
                                StoredProcedures.DeleteSAPPinInfoLogs(startDateTime);
                            }
                        }

                        this.Invoke((MethodInvoker)delegate
                        {
                            IsShipmentLoaded = true;
                            progressDialog.Hide();

                            if (!isAnyEventExternalCodeProcessed)
                            {
                                MessageBox.Show("Your Post-Staging Shipment Source File did not contain any of the selected event external code(s).", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            }
                            else
                            {
                                try
                                {
                                    IsSAPInfoAfterBound = IsSAPInfoBeforeBound = false;
                                    IsPinMasterAfterBound = IsPinMasterBeforeBound = false;

                                    ShipmentResultsForm = new ShipmentResultsForm();
                                    SAPPinInfoBeforeDataSet.Tables[0].TableName = "SAPPinInfoBefore";
                                    SAPPinInfoAfterDataSet.Tables[0].TableName = "SAPPinInfoAfter";
                                    pinMasterBeforeDataSet.Tables[0].TableName = "PinMasterBefore";
                                    pinMasterAfterDataSet.Tables[0].TableName = "PinMasterAfter";
                                    ShipmentResultsForm.dataGridViewSAPPinInfoBefore.AutoGenerateColumns = ShipmentResultsForm.dataGridViewSAPPinInfoAfter.AutoGenerateColumns =
                                        ShipmentResultsForm.dataGridViewPinMasterBefore.AutoGenerateColumns = ShipmentResultsForm.dataGridViewPinMasterAfter.AutoGenerateColumns = true;
                                    ShipmentResultsForm.dataGridViewSAPPinInfoBefore.DataSource = SAPPinInfoBeforeDataSet.Tables[0];
                                    ShipmentResultsForm.dataGridViewSAPPinInfoAfter.DataSource = SAPPinInfoAfterDataSet.Tables[0];
                                    ShipmentResultsForm.dataGridViewPinMasterBefore.DataSource = pinMasterBeforeDataSet.Tables[0];
                                    ShipmentResultsForm.dataGridViewPinMasterAfter.DataSource = pinMasterAfterDataSet.Tables[0];
                                    ShipmentResultsForm.dataGridViewSAPPinInfoBefore.DataBindingComplete += dataGridViewSAPPinInfoBefore_DataBindingComplete;
                                    ShipmentResultsForm.dataGridViewPinMasterBefore.DataBindingComplete += dataGridViewPinMasterBefore_DataBindingComplete;
                                    ShipmentResultsForm.dataGridViewSAPPinInfoAfter.DataBindingComplete += dataGridViewSAPPinInfoAfter_DataBindingComplete;
                                    ShipmentResultsForm.dataGridViewPinMasterAfter.DataBindingComplete += dataGridViewPinMasterAfter_DataBindingComplete;
                                    ShipmentResultsForm.dataGridViewSAPPinInfoAfter.Sorted += dataGridViewSAPPinInfoAfter_Sorted;
                                    ShipmentResultsForm.dataGridViewPinMasterAfter.Sorted += dataGridViewPinMasterAfter_Sorted;

                                    if (logsDataSet != null && logsDataSet.Tables.Count > 0)
                                    {
                                        logsDataSet.Tables[0].TableName = "supLogMainAfter";
                                        ShipmentResultsForm.dataGridViewLogs.DataSource = logsDataSet.Tables[0];
                                        DataGridViewHelper.StretchLastColumn(ShipmentResultsForm.dataGridViewLogs);
                                    }

                                    ShipmentResultsForm.ShowDialog();
                                }
                                catch (Exception e)
                                {
                                    ShowErrorMessageBox(e.Message);
                                }
                            }
                        });

                    });

                    thread.Start();

                    if (!IsShipmentLoaded)
                    {
                        progressDialog.ShowDialog();
                    }
                }
                else
                {
                    MessageBox.Show("The number of entries in the Pre-Staging and Post-Staging Shipment Source Files must match.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }
        #endregion
    }
}
