﻿using Purolator.SSBE.TesterUI.Helpers;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Purolator.SSBE.TesterUI
{
    public partial class ShipmentResultsForm : Form
    {
        #region Private Variables
        private Dictionary<string, string> ColumnFormatMapping = new Dictionary<string, string>() 
        { 
            { "FWODocumentNo", "0" }, 
            { "SourceID", "0" },
            { "ShipmentDate", "m/d/yyyy H:mm:ss" },
            { "CreateDateTime", "m/d/yyyy H:mm:ss" },
            { "LastUpdateDateTime", "m/d/yyyy H:mm:ss" },
            { "ExpectedDeliveryDate", "m/d/yyyy H:mm:ss" }
        };
        #endregion
        #region Public Constructors
        public ShipmentResultsForm()
        {
            InitializeComponent();
        }
        #endregion

        #region Event Handlers
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void buttonExportResults_Click(object sender, EventArgs e)
        {
            ShowSaveAsDialog();
        }
        #endregion

        #region Private Methods
        private void ShowSaveAsDialog()
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Filter = "Excel Workbook (*.xlsx)|*.xlsx";
            saveFileDialog.Title = "Save As";
            saveFileDialog.ShowDialog();

            if (!String.IsNullOrWhiteSpace(saveFileDialog.FileName))
            {
                DataGridViewHelper.ExportToExcelFile(saveFileDialog.FileName, ColumnFormatMapping, dataGridViewSAPPinInfoBefore, dataGridViewSAPPinInfoAfter, dataGridViewPinMasterBefore, dataGridViewPinMasterAfter, dataGridViewLogs);
                MessageBox.Show("Your results were successfully exported to: " + saveFileDialog.FileName, "Excel Export", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        #endregion
    }
}
