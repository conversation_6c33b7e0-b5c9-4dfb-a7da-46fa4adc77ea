﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Net;
using System.Runtime.Serialization;
using System.Data.SQLite;

namespace DB3FileConversion
{
    class FileConversion
    {
        static void Main(string[] args)
        {
            if (args.Length < 3)
            {
                Console.WriteLine("Usage: FileConversion <fileType> <input_file> <output_file>");
                Console.WriteLine("Where: <fileType>");
                Console.WriteLine("   1 = Pin file");
                Console.WriteLine("   2 = Preprint");
                Console.WriteLine("   3 = Route Plan  <route_file> <filter_file> <crossdock_file> <alternate_municipalities_file> <terminal_boundaries> <output_file>");
                Console.WriteLine("   4 = Parking Plan");
                Console.WriteLine("   5 = HPU Location Master");
                Console.WriteLine("   6 = HPU Location to PC");
            }
            else
            {
                switch (args[0])
                {

                    case "1":
                        createPINDB(args[1], args[2]);
                        break;

                    case "2":
                        createPrePrintPINDB(args[1], args[2]);
                        break;

                    case "3":
                        createRoutePlanDB(args[1], args[2], args[3], args[4], args[5], args[6]);
                        break;

                    case "4":
                        createRouteMasterDB(args[1], args[2]);
                        break;
                    
                    case "5":
                        createHPULocationMaster(args[1], args[2]);
                        break;

                    case "6":
                        createHPULocationtoPC(args[1], args[2]);
                        break;
                }
            }
        }

        /// <summary>
        /// Method to create PIN DB file
        /// </summary>
        static void createPINDB(string inputPath, string outputPath)
        {
            System.Data.SQLite.SQLiteConnection.CreateFile(outputPath);        // Create the file which will be hosting the database
            string datasource = "data source=";
            datasource = datasource + outputPath;

            using (System.Data.SQLite.SQLiteConnection connection = new System.Data.SQLite.SQLiteConnection(datasource, true))
            {
                using (System.Data.SQLite.SQLiteCommand command = new System.Data.SQLite.SQLiteCommand(connection))
                {
                    try
                    {
                        connection.Open();        // Open the connection to the database
                        string TableQuery = @"CREATE TABLE PIN (TerminalID VARCHAR(6), PIN VARCHAR COLLATE NOCASE PRIMARY KEY, PrimarySort VARCHAR COLLATE NOCASE, SideofBelt VARCHAR (1) COLLATE NOCASE, RouteNumber VARCHAR COLLATE NOCASE, ShelfNumber VARCHAR COLLATE NOCASE, TruckShelfOverride VARCHAR COLLATE NOCASE, DeliverySequenceID VARCHAR COLLATE NOCASE);";
                        command.CommandText = TableQuery;     // Set CommandText to  query that will create the table
                        command.ExecuteNonQuery(); // Execute the query

                        TableQuery = @"CREATE INDEX IF NOT EXISTS PIN_Index on  PIN (PIN)";
                        command.CommandText = TableQuery;     // Set CommandText to  query that will create the index
                        command.ExecuteNonQuery();

                        command.CommandText = "INSERT INTO PIN Values (@TerminalID ,@PIN,@PrimarySort,@SideofBelt,@RouteNumber,@ShelfNumber,@TruckShelfOverride,@DeliverySequenceID)";
                        string[] lines = System.IO.File.ReadAllLines(inputPath, Encoding.Default);

                        SQLiteTransaction m_trans = connection.BeginTransaction();
                        // Add entry into table
                        foreach (string line in lines)
                        {

                            string[] RoutingInfo = line.Split('|');

                            command.Parameters.AddWithValue("@TerminalID", RoutingInfo[0]);
                            command.Parameters.AddWithValue("@PIN", RoutingInfo[1]);
                            command.Parameters.AddWithValue("@PrimarySort", RoutingInfo[2]);
                            command.Parameters.AddWithValue("@SideofBelt", RoutingInfo[3]);
                            command.Parameters.AddWithValue("@RouteNumber", RoutingInfo[4]);
                            command.Parameters.AddWithValue("@ShelfNumber", RoutingInfo[5]);
                            command.Parameters.AddWithValue("@TruckShelfOverride", RoutingInfo[6]);
                            command.Parameters.AddWithValue("@DeliverySequenceID", RoutingInfo[7]);
                            command.ExecuteNonQuery();
                        }
                        m_trans.Commit();
                        connection.Close();
                    }
                    finally
                    {

                    }
                }
            }
        }

        /// <summary>
        ///  Method to create Pre Print PIN DB file
        /// </summary>
        static void createPrePrintPINDB(string inputPath, string outputPath)
        {
            System.Data.SQLite.SQLiteConnection.CreateFile(outputPath);      // Create the file which will be hosting the database
            string datasource = "data source=";
            datasource = datasource + outputPath;

            using (System.Data.SQLite.SQLiteConnection connection = new System.Data.SQLite.SQLiteConnection(datasource, true))
            {
                using (System.Data.SQLite.SQLiteCommand command = new System.Data.SQLite.SQLiteCommand(connection))
                {
                    try
                    {
                        connection.Open();       // Open the connection to the database
                        string TableQuery = @"CREATE TABLE PrePrintPIN (TerminalID VARCHAR(6), PrePrintID VARCHAR COLLATE NOCASE, FromPIN BIGINT, ToPIN BIGINT, PrimarySort VARCHAR COLLATE NOCASE, SideofBelt VARCHAR (1) COLLATE NOCASE, RouteNumber VARCHAR COLLATE NOCASE, ShelfNumber VARCHAR COLLATE NOCASE, PackageLocation VARCHAR COLLATE NOCASE, TruckShelfOverride VARCHAR COLLATE NOCASE, DeliverySequenceID VARCHAR COLLATE NOCASE, PRIMARY KEY (FromPIN, ToPIN));";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the table
                        command.ExecuteNonQuery(); // Execute the query

                        TableQuery = @"CREATE INDEX IF NOT EXISTS PrePrintPIN_Index on  PrePrintPIN (FromPIN DESC, ToPIN DESC)";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the index
                        command.ExecuteNonQuery();

                        command.CommandText = "INSERT INTO PrePrintPIN Values ( @TerminalID,@PrePrintID ,@FromPIN,@ToPIN,@PrimarySort,@SideofBelt,@RouteNumber,@ShelfNumber,@PackageLocation,@TruckShelfOverride,@DeliverySequenceID)";
                        string[] lines = System.IO.File.ReadAllLines(inputPath, Encoding.Default);
                        SQLiteTransaction m_trans = connection.BeginTransaction();

                        // Add entry into table
                        foreach (string line in lines)
                        {
                            string[] RoutingInfo = line.Split('|');

                            command.Parameters.AddWithValue("@TerminalID", RoutingInfo[0]);
                            command.Parameters.AddWithValue("@PrePrintID", RoutingInfo[1]);
                            command.Parameters.AddWithValue("@FromPIN", RoutingInfo[2]);
                            command.Parameters.AddWithValue("@ToPIN", RoutingInfo[3]);
                            command.Parameters.AddWithValue("@PrimarySort", RoutingInfo[4]);
                            command.Parameters.AddWithValue("@SideofBelt", RoutingInfo[5]);
                            command.Parameters.AddWithValue("@RouteNumber", RoutingInfo[6]);
                            command.Parameters.AddWithValue("@ShelfNumber", RoutingInfo[7]);
                            command.Parameters.AddWithValue("@PackageLocation", RoutingInfo[8]);
                            command.Parameters.AddWithValue("@TruckShelfOverride", RoutingInfo[9]);
                            command.Parameters.AddWithValue("@DeliverySequenceID", RoutingInfo[10]);
                            command.ExecuteNonQuery();
                        }
                        m_trans.Commit();
                        connection.Close();
                    }
                    finally
                    {

                    }
                }
            }
        }

        /// <summary>
        /// Method to create Pre Route Plan DB file
        /// </summary>
        static void createRoutePlanDB(string inputPath, string filterPath, string crossDockPath, string altMunicipalities, string terminalBoundaries, string outputPath)
        {
            System.Data.SQLite.SQLiteConnection.CreateFile(outputPath);       // Create the file which will be hosting the database
            string datasource = "data source=";
            datasource = datasource + outputPath;
            using (System.Data.SQLite.SQLiteConnection connection = new System.Data.SQLite.SQLiteConnection(datasource, true))
            {
                using (System.Data.SQLite.SQLiteCommand command = new System.Data.SQLite.SQLiteCommand(connection)
                     , command1 = new System.Data.SQLite.SQLiteCommand(connection))
                {
                    try
                    {
                        connection.Open();        // Open the connection to the database
                        string TableQuery = @"CREATE TABLE RoutePlan (TerminalID VARCHAR(6), RoutePlanID VARCHAR COLLATE NOCASE, RoutePlanVersionID VARCHAR COLLATE NOCASE, AddressRecordType VARCHAR COLLATE NOCASE, PostalCode VARCHAR (6) COLLATE NOCASE, StreetName VARCHAR COLLATE NOCASE, StreetType VARCHAR COLLATE NOCASE, StreetDirection VARCHAR COLLATE NOCASE, FromStreetNumber INTEGER, ToStreetNumber INTEGER, FromStreetNumberSuffix VARCHAR COLLATE NOCASE, ToStreetNumberSuffix VARCHAR COLLATE NOCASE, FromUnitNumber VARCHAR COLLATE NOCASE, ToUnitNumber VARCHAR COLLATE NOCASE, CustomerName VARCHAR COLLATE NOCASE, AddressSequenceCode VARCHAR COLLATE NOCASE, MunicipalityName VARCHAR COLLATE NOCASE, RouteNumber VARCHAR COLLATE NOCASE, ShelfNumber VARCHAR COLLATE NOCASE, TruckShelfOverride VARCHAR COLLATE NOCASE, DeliverySequenceID VARCHAR COLLATE NOCASE, AlternateUnitNumber VARCHAR COLLATE NOCASE, AlternateStreetNumber VARCHAR COLLATE NOCASE, AlternateStreetNumberSuffix VARCHAR COLLATE NOCASE, AlternateStreetName VARCHAR COLLATE NOCASE, AlternateStreetType VARCHAR COLLATE NOCASE, AlternateStreetDirection VARCHAR COLLATE NOCASE, AlternateMunicipalityName VARCHAR COLLATE NOCASE, AlternateProvince VARCHAR COLLATE NOCASE, AlternatePostalCode VARCHAR COLLATE NOCASE, ProvinceCode VARCHAR COLLATE NOCASE, ServicePriority VARCHAR (4) COLLATE NOCASE, HFPULocationID VARCHAR(20) COLLATE NOCASE);";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the table
                        command.ExecuteNonQuery(); // Execute the query  

                        TableQuery = @"CREATE TABLE PostalCodeFilter (Filter VARCHAR (5) COLLATE NOCASE,NextChar VARCHAR (1) COLLATE NOCASE,UNIQUE (Filter,NextChar)ON CONFLICT IGNORE);";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the table
                        command.ExecuteNonQuery(); // Execute the query  
                        

                        TableQuery = @"CREATE INDEX IF NOT EXISTS RoutePlan_Index on  RoutePlan (PostalCode)";
                        command.CommandText = TableQuery;     // Set CommandText to  query that will create the index
                        command.ExecuteNonQuery();


                        TableQuery = @"CREATE TEMP TABLE StreetNamesTMP (CityName VARCHAR COLLATE NOCASE, StreetName VARCHAR COLLATE NOCASE, StreetType VARCHAR COLLATE NOCASE, StreetDirection VARCHAR COLLATE NOCASE, FullStreetName VARCHAR COLLATE NOCASE);";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the table
                        command.ExecuteNonQuery(); // Execute the query

                        command.CommandText = "INSERT INTO RoutePlan Values (@TerminalID,@RoutePlanID,@RoutePlanVersionID,@AddressRecordType,@PostalCode,@StreetName,@StreetType,@StreetDirection,@FromStreetNumber,@ToStreetNumber,@FromStreetNumberSuffix,@ToStreetNumberSuffix,@FromUnitNumber,@ToUnitNumber,@CustomerName,@AddressSequenceCode,@MunicipalityName,@RouteNumber,@ShelfNumber,@TruckShelfOverride,@DeliverySequenceID,@AlternateUnitNumber,@AlternateStreetNumber,@AlternateStreetNumberSuffix,@AlternateStreetName,@AlternateStreetType,@AlternateStreetDirection,@AlternateMunicipalityName,@AlternateProvince,@AlternatePostalCode,@ProvinceCode,@ServicePriority,@HFPULocationID)";
                        command1.CommandText = "INSERT INTO StreetNamesTMP VALUES (@CityName, @StreetName, @StreetType, @StreetDirection, @FullStreetName)";
                        string[] lines = System.IO.File.ReadAllLines(inputPath, Encoding.Default);
                        SQLiteTransaction m_trans = connection.BeginTransaction();
                        // Add entry into table
                        foreach (string line in lines)
                        {
                            string[] RoutePlanInfo = line.Split('|');

                            int i = 0;

                            command.Parameters.AddWithValue("@TerminalID", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@RoutePlanID", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@RoutePlanVersionID", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@AddressRecordType", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@PostalCode", RoutePlanInfo[i++]);
                            string StreetName =  RoutePlanInfo[i++];
                            string StreetType = RoutePlanInfo[i++];
                            string StreetDirection = RoutePlanInfo[i++];
                            command.Parameters.AddWithValue("@StreetName", StreetName);
                            command.Parameters.AddWithValue("@StreetType", StreetType);
                            command.Parameters.AddWithValue("@StreetDirection", StreetDirection);
                            command.Parameters.AddWithValue("@FromStreetNumber", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@ToStreetNumber", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@FromStreetNumberSuffix", ConvertFractionToString(RoutePlanInfo[i++]));
                            command.Parameters.AddWithValue("@ToStreetNumberSuffix", ConvertFractionToString(RoutePlanInfo[i++]));
                            command.Parameters.AddWithValue("@FromUnitNumber", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@ToUnitNumber", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@CustomerName", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@AddressSequenceCode", RoutePlanInfo[i++]);
                            string MunicipalityName = RoutePlanInfo[i++];
                            command.Parameters.AddWithValue("@MunicipalityName", MunicipalityName);
                            command.Parameters.AddWithValue("@RouteNumber", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@ShelfNumber", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@TruckShelfOverride", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@DeliverySequenceID", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@AlternateUnitNumber", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@AlternateStreetNumber", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@AlternateStreetNumberSuffix", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@AlternateStreetName", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@AlternateStreetType", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@AlternateStreetDirection", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@AlternateMunicipalityName", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@AlternateProvince", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@AlternatePostalCode", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@ProvinceCode", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@ServicePriority", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@HFPULocationID", RoutePlanInfo[i++]);
                            
                            command.ExecuteNonQuery();

                            command1.Parameters.AddWithValue("@CityName", MunicipalityName);
                            command1.Parameters.AddWithValue("@StreetName", StreetName);
                            command1.Parameters.AddWithValue("@StreetType", StreetType);
                            command1.Parameters.AddWithValue("@StreetDirection", StreetDirection);
                            command1.Parameters.AddWithValue("@FullStreetName", StreetName + " " + StreetType + " " + StreetDirection);
                            command1.ExecuteNonQuery();
                        }
                       
                        lines = System.IO.File.ReadAllLines(filterPath, Encoding.Default);
                        command.CommandText = "INSERT INTO PostalCodeFilter Values (@Filter,@NextChar)";
                         // Add entry into table
                        foreach (string line in lines)
                        {
                            string[] RoutePlanInfo = line.Split('|');

                            int i = 0;

                            command.Parameters.AddWithValue("@Filter", RoutePlanInfo[i++]);
                            command.Parameters.AddWithValue("@NextChar", RoutePlanInfo[i++]);
                            command.ExecuteNonQuery();
                        }

                        m_trans.Commit();

                        TableQuery = @"CREATE TABLE StreetNames (CityName VARCHAR COLLATE NOCASE, StreetName VARCHAR COLLATE NOCASE, StreetType VARCHAR COLLATE NOCASE, StreetDirection VARCHAR COLLATE NOCASE,  FullStreetName VARCHAR COLLATE NOCASE);";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the table
                        command.ExecuteNonQuery(); // Execute the query

                        TableQuery = @"CREATE INDEX IF NOT EXISTS StreetNames_Index2 on  StreetNames (CityName)";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the index
                        command.ExecuteNonQuery();

                        TableQuery = @"INSERT INTO StreetNames SELECT DISTINCT * FROM StreetNamesTMP";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the index
                        command.ExecuteNonQuery();

                        createCrossDock(crossDockPath, connection);

                        createAlternateMunicipalities(altMunicipalities, connection);

                        createTerminalBoundaries(terminalBoundaries, connection);

                        connection.Close();
                    }
                    finally
                    {

                    }
                }
            }
        }

        /// <summary>
        /// method to create Route Master DB file
        /// </summary>
        static void createRouteMasterDB(string inputPath, string outputPath)
        {
            System.Data.SQLite.SQLiteConnection.CreateFile(outputPath);       // Create the file which will be hosting the database
            string datasource = "data source=";
            datasource = datasource + outputPath;

            using (System.Data.SQLite.SQLiteConnection connection = new System.Data.SQLite.SQLiteConnection(datasource, true))
            {
                using (System.Data.SQLite.SQLiteCommand command = new System.Data.SQLite.SQLiteCommand(connection))
                {
                    try
                    {
                        connection.Open();        // Open the connection to the database
                        string TableQuery = @"CREATE TABLE RouteMaster (TerminalID VARCHAR(6), ParkingPlanID VARCHAR COLLATE NOCASE, RouteNumber VARCHAR COLLATE NOCASE, PrimarySort VARCHAR COLLATE NOCASE, SideofBelt VARCHAR (1) COLLATE NOCASE);";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the table
                        command.ExecuteNonQuery(); // Execute the query

                        TableQuery = @"CREATE INDEX IF NOT EXISTS RouteMaster_Index on  RouteMaster (RouteNumber)";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the index
                        command.ExecuteNonQuery();

                        command.CommandText = "INSERT INTO RouteMaster Values ( @TerminalID ,@ParkingPlanID,@RouteNumber,@PrimarySort,@SideofBelt)";
                        string[] lines = System.IO.File.ReadAllLines(inputPath, Encoding.Default);
                        SQLiteTransaction m_trans = connection.BeginTransaction();

                        // Add entry into table
                        foreach (string line in lines)
                        {

                            string[] RouteMasterInfo = line.Split('|');

                            command.Parameters.AddWithValue("@TerminalID", RouteMasterInfo[0]);
                            command.Parameters.AddWithValue("@ParkingPlanID", RouteMasterInfo[1]);
                            command.Parameters.AddWithValue("@RouteNumber", RouteMasterInfo[2]);
                            command.Parameters.AddWithValue("@PrimarySort", RouteMasterInfo[3]);
                            command.Parameters.AddWithValue("@SideofBelt", RouteMasterInfo[4]);
                            command.ExecuteNonQuery();
                        }
                        m_trans.Commit();
                        connection.Close();
                    }
                    finally
                    {

                    }
                }
            }
        }


        static void createCrossDock(string inputPath, System.Data.SQLite.SQLiteConnection connection)
        {
            using (System.Data.SQLite.SQLiteCommand command = new System.Data.SQLite.SQLiteCommand(connection))
            {                                
                string TableQuery = @"CREATE TABLE CrossDock (PC VARCHAR(6) COLLATE NOCASE PRIMARY KEY, Unicode VARCHAR(3) COLLATE NOCASE);";
                command.CommandText = TableQuery;     // Set CommandText to the query that will create the table
                command.ExecuteNonQuery(); // Execute the query

                TableQuery = @"CREATE INDEX IF NOT EXISTS CrossDock_Index on  CrossDock (PC)";
                command.CommandText = TableQuery;     // Set CommandText to the query that will create the index
                command.ExecuteNonQuery();

                command.CommandText = "INSERT INTO CrossDock Values ( @PC , @Unicode)";

                string[] lines = System.IO.File.ReadAllLines(inputPath, Encoding.Default);
                SQLiteTransaction m_trans = connection.BeginTransaction();

                // Add entry into table
                foreach (string line in lines)
                {

                    string[] locationInfo = line.Split('|');

                    int i = 0;

                    command.Parameters.AddWithValue("@PC", locationInfo[i++]);
                    command.Parameters.AddWithValue("@Unicode", locationInfo[i++]);                                   

                    command.ExecuteNonQuery();
                }
                m_trans.Commit();                                              
            }
        }


        static void createAlternateMunicipalities(string inputPath, System.Data.SQLite.SQLiteConnection connection)
        {
            using (System.Data.SQLite.SQLiteCommand command = new System.Data.SQLite.SQLiteCommand(connection))
            {                                
                string TableQuery = @"CREATE TABLE AlternateMunicipalities (Province VARCHAR(2) COLLATE NOCASE, CityName VARCHAR(60) COLLATE NOCASE, AlternateCityName VARCHAR(60) COLLATE NOCASE);";
                command.CommandText = TableQuery;     // Set CommandText to the query that will create the table
                command.ExecuteNonQuery(); // Execute the query

                TableQuery = @"CREATE TABLE AllCities (Name VARCHAR(60) COLLATE NOCASE);";
                command.CommandText = TableQuery;     // Set CommandText to the query that will create the table
                command.ExecuteNonQuery(); // Execute the query

                TableQuery = @"CREATE INDEX IF NOT EXISTS AllCities_Index on  AllCities (Name)";
                command.CommandText = TableQuery;     // Set CommandText to the query that will create the index
                command.ExecuteNonQuery();

                command.CommandText = "INSERT INTO AlternateMunicipalities Values (@Province, @AlternateCityName , @CityName)";

                string[] lines = System.IO.File.ReadAllLines(inputPath, Encoding.Default);
                SQLiteTransaction m_trans = connection.BeginTransaction();

                // Add entry into table
                foreach (string line in lines)
                {

                    string[] locationInfo = line.Split('|');

                    int i = 0;

                    command.Parameters.AddWithValue("@Province", locationInfo[i++]);
                    command.Parameters.AddWithValue("@CityName", locationInfo[i++]);  
                    command.Parameters.AddWithValue("@AlternateCityName", locationInfo[i++]);                                   

                    command.ExecuteNonQuery();
                }

                command.CommandText = "INSERT INTO AllCities SELECT DISTINCT CityName FROM AlternateMunicipalities UNION SELECT DISTINCT AlternateCityName FROM AlternateMunicipalities";
                command.ExecuteNonQuery();

                m_trans.Commit();
            }            
        }

        
        static void createTerminalBoundaries(string inputPath, System.Data.SQLite.SQLiteConnection connection)
        {
            using (System.Data.SQLite.SQLiteCommand command = new System.Data.SQLite.SQLiteCommand(connection))
            {
                string TableQuery = @"CREATE TABLE TerminalBoundaries (PC VARCHAR(6) COLLATE NOCASE PRIMARY KEY);";
                command.CommandText = TableQuery;     // Set CommandText to the query that will create the table
                command.ExecuteNonQuery(); // Execute the query

                TableQuery = @"CREATE INDEX IF NOT EXISTS TerminalBoundaries_Index on  TerminalBoundaries (PC)";
                command.CommandText = TableQuery;     // Set CommandText to the query that will create the index
                command.ExecuteNonQuery();

                command.CommandText = "INSERT INTO TerminalBoundaries Values (@PC)";

                string[] lines = System.IO.File.ReadAllLines(inputPath, Encoding.Default);
                SQLiteTransaction m_trans = connection.BeginTransaction();

                // Add entry into table
                foreach (string line in lines)
                {
                    string[] locationInfo = line.Split('|');
                    
                    command.Parameters.AddWithValue("@PC", locationInfo[1]);                                                                     

                    command.ExecuteNonQuery();
                }
                m_trans.Commit();                                              
            }
        }
        

        static void createHPULocationMaster(string inputPath, string outputPath)
        {
            System.Data.SQLite.SQLiteConnection.CreateFile(outputPath);       // Create the file which will be hosting the database
            string datasource = "data source=";
            datasource = datasource + outputPath;

            using (System.Data.SQLite.SQLiteConnection connection = new System.Data.SQLite.SQLiteConnection(datasource, true))
            {
                using (System.Data.SQLite.SQLiteCommand command = new System.Data.SQLite.SQLiteCommand(connection))
                {
                    try
                    {
                        connection.Open();        // Open the connection to the database
                        string TableQuery = @"CREATE TABLE HFPULocationMaster (HFPULocationID VARCHAR(20) COLLATE NOCASE PRIMARY KEY, LocationName VARCHAR COLLATE NOCASE, UnitNumber VARCHAR COLLATE NOCASE, StreetNumber VARCHAR COLLATE NOCASE, StreetNumberSuffix VARCHAR COLLATE NOCASE, StreetName VARCHAR COLLATE NOCASE, StreetType VARCHAR COLLATE NOCASE, StreetDirection VARCHAR COLLATE NOCASE, MunicipalityName VARCHAR COLLATE NOCASE, ProvinceCode VARCHAR COLLATE NOCASE, PostalCode VARCHAR COLLATE NOCASE, TerminalID VARCHAR(6));";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the table
                        command.ExecuteNonQuery(); // Execute the query

                        TableQuery = @"CREATE INDEX IF NOT EXISTS HFPULocationMaster_Index on  HFPULocationMaster (HFPULocationID)";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the index
                        command.ExecuteNonQuery();


                        command.CommandText = "INSERT INTO HFPULocationMaster Values ( @HFPULocationID ,@LocationName,@UnitNumber,@StreetNumber,@StreetNumberSuffix,@StreetName,@StreetType,@StreetDirection,@MunicipalityName,@ProvinceCode,@PostalCode,@TerminalID)";

                        string[] lines = System.IO.File.ReadAllLines(inputPath, Encoding.Default);
                        SQLiteTransaction m_trans = connection.BeginTransaction();

                        // Add entry into table
                        foreach (string line in lines)
                        {

                            string[] locationInfo = line.Split('|');

                            int i = 0;

                            command.Parameters.AddWithValue("@HFPULocationID", locationInfo[i++]);
                            command.Parameters.AddWithValue("@LocationName", locationInfo[i++]);
                            command.Parameters.AddWithValue("@UnitNumber", locationInfo[i++]);
                            command.Parameters.AddWithValue("@StreetNumber", locationInfo[i++]);
                            command.Parameters.AddWithValue("@StreetNumberSuffix", locationInfo[i++]);
                            command.Parameters.AddWithValue("@StreetName", locationInfo[i++]);
                            command.Parameters.AddWithValue("@StreetType", locationInfo[i++]);
                            command.Parameters.AddWithValue("@StreetDirection", locationInfo[i++]);
                            command.Parameters.AddWithValue("@MunicipalityName", locationInfo[i++]);
                            command.Parameters.AddWithValue("@ProvinceCode", locationInfo[i++]);
                            command.Parameters.AddWithValue("@PostalCode", locationInfo[i++]);
                            command.Parameters.AddWithValue("@TerminalID", locationInfo[i++]);

                            command.ExecuteNonQuery();
                        }
                        m_trans.Commit();
                        connection.Close();
                    }
                    finally
                    {

                    }
                }
            }
        }

        static void createHPULocationtoPC(string inputPath, string outputPath)
        {
            System.Data.SQLite.SQLiteConnection.CreateFile(outputPath);       // Create the file which will be hosting the database
            string datasource = "data source=";
            datasource = datasource + outputPath;

            using (System.Data.SQLite.SQLiteConnection connection = new System.Data.SQLite.SQLiteConnection(datasource, true))
            {
                using (System.Data.SQLite.SQLiteCommand command = new System.Data.SQLite.SQLiteCommand(connection))
                {
                    try
                    {
                        connection.Open();        // Open the connection to the database
                        string TableQuery = @"CREATE TABLE HFPULocationToPC (HFPULocationID VARCHAR(20) COLLATE NOCASE, PostalCode VARCHAR COLLATE NOCASE, TerminalID VARCHAR(6));";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the table
                        command.ExecuteNonQuery(); // Execute the query

                        TableQuery = @"CREATE INDEX IF NOT EXISTS HFPULocationToPC_Index on  HFPULocationToPC (PostalCode)";
                        command.CommandText = TableQuery;     // Set CommandText to the query that will create the index
                        command.ExecuteNonQuery();

                        command.CommandText = "INSERT INTO HFPULocationToPC Values ( @HFPULocationID ,@PostalCode,@TerminalID)";

                        string[] lines = System.IO.File.ReadAllLines(inputPath, Encoding.Default);
                        SQLiteTransaction m_trans = connection.BeginTransaction();

                        // Add entry into table
                        foreach (string line in lines)
                        {

                            string[] locationInfo = line.Split('|');

                            int i = 0;

                            command.Parameters.AddWithValue("@HFPULocationID", locationInfo[i++]);
                            command.Parameters.AddWithValue("@PostalCode", locationInfo[i++]);
                            command.Parameters.AddWithValue("@TerminalID", locationInfo[i++]);

                            command.ExecuteNonQuery();
                        }
                        m_trans.Commit();
                        connection.Close();
                    }
                    finally
                    {

                    }
                }
            }
        }

        static string ConvertFractionToString(string fraction)
        {
            switch (fraction)
            {
                case "¼":
                    fraction = "1/4";
                    break;
                case "½":
                    fraction = "1/2";
                    break;
                case "¾":
                    fraction = "3/4";
                    break;
                default:
                    break;

            }
            return fraction;
        }
    }
}
