﻿<?xml version="1.0" encoding="utf-8"?>
<!-- DO NOT EDIT the project element - the ToolsVersion specified here does not prevent the solutions 
     and projects in the SolutionToBuild item group from targeting other versions of the .NET framework. 
     -->
<Project DefaultTargets="DesktopBuild" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">

  <!-- Do not edit this -->
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\TeamBuild\Microsoft.TeamFoundation.Build.targets" />
  <Import Project="$(MSBuildExtensionsPath)\MSBuildCommunityTasks\MSBuild.Community.Tasks.Targets" />
 
  <ProjectExtensions>	

    <!-- Team Foundation Build Version - DO NOT CHANGE -->
    <ProjectFileVersion>4</ProjectFileVersion>

    <!--  DESCRIPTION
     This property is included only for backwards compatibility. The description of a build definition 
     is now stored in the database. For compatibility with V1 clients, keep this property in sync with 
     the value in the database.
    -->
    <Description>Smart Sort App</Description>

    <!--  BUILD MACHINE
     This property is included only for backwards compatibility. The default machine used for a build 
     definition is now stored in the database, as the MachineName property of the definition's 
     DefaultBuildAgent. For compatibility with V1 clients, keep this property in sync with the value 
     in the database.

     <BuildMachine></BuildMachine>
    -->

   </ProjectExtensions>

  <PropertyGroup>	 
	  <!--  TEAM PROJECT
     This property is included only for backwards compatibility. The team project for a build 
     definition is now stored in the database. For compatibility with V1 clients, keep this property in 
     sync with the value in the database.

     <TeamProject></TeamProject>
    -->

<!--  BUILD DIRECTORY
     This property is included only for backwards compatibility. The build directory used for a build 
     definition is now stored in the database, as the BuildDirectory property of the definition's 
     DefaultBuildAgent. For compatibility with V1 clients, keep this property in sync with the value 
     in the database.
     
    <BuildDirectoryPath></BuildDirectoryPath>
    -->

    <!--  DROP LOCATION
     This property is included only for backwards compatibility. The drop location used for a build 
     definition is now stored in the database. For compatibility with V1 clients, keep this property 
     in sync with the value in the database.

     <DropLocation></DropLocation>
    -->

    <!--  TESTING
     Set this flag to enable/disable running tests as a post-compilation build step.
    -->
    <RunTest>false</RunTest>

    <!--  CODE ANALYSIS
     Set this property to enable/disable running code analysis. Valid values for this property are 
     Default, Always and Never.

         Default - Perform code analysis as per the individual project settings
         Always  - Always perform code analysis irrespective of project settings
         Never   - Never perform code analysis irrespective of project settings
     -->
    <RunCodeAnalysis>Never</RunCodeAnalysis>

    <!--  CustomPropertiesForClean
     Custom properties to pass to the MSBuild task while calling the "Clean" target for all solutions.
     The format should be: PropertyName1=value1;PropertyName2=value2;...
     -->
    <CustomPropertiesForClean></CustomPropertiesForClean>

    <!--  CustomPropertiesForBuild
     Custom properties to pass to the MSBuild task while calling the default targets for all solutions.
     The format should be: Property1=value1;Property2=value2;...  To pass custom properties to
     individual solutions, use the Properties metadata item of the SolutionToBuild ItemGroup.
     -->
    <CustomPropertiesForBuild></CustomPropertiesForBuild>

  </PropertyGroup>

	<!-- Parameters -->
	<PropertyGroup>
		<Environment  Condition="'$(Environment)'==''">certification</Environment>
	</PropertyGroup>
	
	
  <ItemGroup>
    <!--  SOLUTIONS
     The paths of the solutions to build. Paths can be server paths or local paths, but server paths
     relative to the location of this file are highly recommended.  To add/delete solutions, edit this 
     ItemGroup. For example, to add a solution MySolution.sln, add the following line:
         
         <SolutionToBuild Include="$(BuildProjectFolderPath)/path/MySolution.sln" />

     To change the order in which the solutions are built, modify the order in which the solutions 
     appear below.
     
     To call a target (or targets) other than the default, add a metadata item named Targets.  To pass 
     custom properties to the solution, add a metadata item named Properties.  For example, to call 
     the targets MyCustomTarget1 and MyCustomTarget2, passing in properties Property1 and Property2, 
     add the following:
         
         <SolutionToBuild Include="$(BuildProjectFolderPath)/path/MySolution.sln">
             <Targets>MyCustomTarget1;MyCustomTarget2</Targets>
             <Properties>Property1=Value1;Property2=Value2</Properties>
         </SolutionToBuild>
    -->
    <SolutionToBuild Include="$(BuildProjectFolderPath)/../../SmartSort/SmartSortApp.sln">
        <Targets></Targets>
        <Properties></Properties>
    </SolutionToBuild>

	  <SolutionToBuild Include="$(BuildProjectFolderPath)/../../SSOWebService/SSOWebService.sln">
		  <Targets></Targets>
		  <Properties></Properties>
	  </SolutionToBuild>


	  
  </ItemGroup>

  <ItemGroup>
    <!--  CONFIGURATIONS
     The list of configurations to build. To add/delete configurations, edit this value. For example, 
     to add a new configuration, add the following lines:
         
         <ConfigurationToBuild Include="Debug|x86">
             <FlavorToBuild>Debug</FlavorToBuild>
             <PlatformToBuild>x86</PlatformToBuild>
         </ConfigurationToBuild>

     The Include attribute value should be unique for each ConfigurationToBuild node.
    -->

    <ConfigurationToBuild Include="Release|Any CPU">
        <FlavorToBuild>Release</FlavorToBuild>
        <PlatformToBuild>Any CPU</PlatformToBuild>
    </ConfigurationToBuild>

  </ItemGroup>

  <ItemGroup>
    <!--  TEST ARGUMENTS
     If the RunTest property is set to true then the following test arguments will be used to run 
     tests. Tests can be run by specifying one or more test lists and/or one or more test containers.
     
     To run tests using test lists, add MetaDataFile items and associated TestLists here.  Paths can 
     be server paths or local paths, but server paths relative to the location of this file are highly 
     recommended:
     
        <MetaDataFile Include="$(BuildProjectFolderPath)/HelloWorld/HelloWorld.vsmdi">
            <TestList>BVT1;BVT2</TestList>
        </MetaDataFile>

     To run tests using test containers, add TestContainer items here:
     
        <TestContainer Include="$(OutDir)\HelloWorldTests.dll" />
        <TestContainer Include="$(SolutionRoot)\TestProject\WebTest1.webtest" />
        <TestContainer Include="$(SolutionRoot)\TestProject\LoadTest1.loadtest" />

     Use %2a instead of * and %3f instead of ? to prevent expansion before test assemblies are built
    -->

  </ItemGroup>

  <PropertyGroup>
    <!-- TEST ARGUMENTS
     If the RunTest property is set to true, then particular tests within a
     metadata file or test container may be specified here.  This is
     equivalent to the /test switch on mstest.exe.

     <TestNames>BVT;HighPriority</TestNames>
    -->

  </PropertyGroup>

  <ItemGroup>
    <!--  ADDITIONAL REFERENCE PATH
     The list of additional reference paths to use while resolving references. For example:
     
         <AdditionalReferencePath Include="C:\MyFolder\" />
         <AdditionalReferencePath Include="C:\MyFolder2\" />
    -->
  </ItemGroup>


	<Target Name="BeforeCompile">
		<ItemGroup>
			<BuildEnvironmentFile Include="$(SolutionRoot)\SmartSort\BUILD_ENV" />
			<AssemblyInfoFiles Include="$(SolutionRoot)\**\AssemblyInfo.cs" />
			<VersionFileHTML   Include="$(SolutionRoot)\SmartSort\WebServices\index.html" />
		</ItemGroup>

		<Attrib Files="@(AssemblyInfoFiles)" Normal="true" />
		<Attrib Files="@(VersionFileHTML)" Normal="true" />
		<Attrib Files="@(BuildEnvironmentFile)" Normal="true" />

		<Message Text="Building changeset $(SourceGetVersion) for $(Environment) environment." />

		<FileUpdate Files="@(BuildEnvironmentFile)"
						   Regex=".+"
						   ignoreCase="true"
						   ReplacementText="$(Environment)" />


		<FileUpdate Files="@(AssemblyInfoFiles)"
						   Regex="AssemblyFileVersion\(\&quot;.+\&quot;\)"
						   ignoreCase="true"
						   ReplacementText="AssemblyFileVersion(&quot;1.0.0.$(SourceGetVersion)&quot;)" />

		<FileUpdate Files="@(VersionFileHTML)"
						   Regex="##Build Version##"
					       ignoreCase="false"
						   ReplacementText="Build:$(BuildNumber) Changeset:$(SourceGetVersion)" />



		<!-- Only for production get the config file -->
		<CallTarget Targets="GetProductionConfig"  Condition=" '$(Environment)'=='production' Or '$(Environment)'=='certification' "/>


	</Target>

	<Target Name="BeforeDropBuild">
		<Exec command="rd  /s /q &quot;$(OutDir)tmp&quot;" IgnoreExitCode="true"></Exec>
		<Exec command="mkdir  &quot;$(OutDir)tmp&quot;" IgnoreExitCode="true"></Exec>
		<Exec command="rd  /s /q &quot;$(OutDir)Deploy&quot;" IgnoreExitCode="true"></Exec>
		<Exec command="mkdir  &quot;$(OutDir)Deploy&quot;" IgnoreExitCode="true"></Exec>
		<Exec command="mkdir  &quot;$(OutDir)Deploy\Services&quot;" IgnoreExitCode="true"></Exec>
		<Exec command="mkdir  &quot;$(OutDir)Deploy\WindowsServices&quot;" IgnoreExitCode="true"></Exec>
		<Exec command="mkdir  &quot;$(OutDir)Deploy\WindowsServices\EMEventSender&quot;" IgnoreExitCode="true"></Exec>
		<Exec command="mkdir  &quot;$(OutDir)Deploy\WindowsServices\AWSValidationService&quot;" IgnoreExitCode="true"></Exec>
    <Exec command="mkdir  &quot;$(OutDir)Deploy\WindowsServices\RoutePrintingService&quot;" IgnoreExitCode="true"></Exec>
    <Exec command="mkdir  &quot;$(OutDir)Deploy\WindowsServices\OFDSender&quot;" IgnoreExitCode="true"></Exec>
    <Exec command="mkdir  &quot;$(OutDir)Deploy\WindowsServices\Amazon&quot;" IgnoreExitCode="true"></Exec>
    <Exec command="mkdir  &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;" IgnoreExitCode="true"></Exec>
    

    <!-- Copy SSO web service -->
		<Exec command="xcopy /e /y &quot;$(OutDir)..\Release\_PublishedWebsites\*.*&quot; &quot;$(OutDir)Deploy\Services\&quot;"></Exec>
		       
    
    <!-- Copy EM windows service -->
		<Exec command="copy /y &quot;$(OutDir)*.dll&quot; &quot;$(OutDir)Deploy\WindowsServices\EMEventSender&quot;"></Exec>
		<Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Services.EventSender.exe&quot; &quot;$(OutDir)Deploy\WindowsServices\EMEventSender&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Services.EventSender.exe.config&quot; &quot;$(OutDir)Deploy\WindowsServices\EMEventSender&quot;"></Exec>


    <!-- Copy OFD windows service -->
    <Exec command="copy /y &quot;$(OutDir)*.dll&quot; &quot;$(OutDir)Deploy\WindowsServices\OFDSender&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Services.OFDSender.exe&quot; &quot;$(OutDir)Deploy\WindowsServices\OFDSender&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Services.OFDSender.exe.config&quot; &quot;$(OutDir)Deploy\WindowsServices\OFDSender&quot;"></Exec>

    <!-- Copy Amazon windows service -->
    <Exec command="copy /y &quot;$(OutDir)*.dll&quot; &quot;$(OutDir)Deploy\WindowsServices\Amazon&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Services.Amazon.exe&quot; &quot;$(OutDir)Deploy\WindowsServices\Amazon&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Services.Amazon.exe.config&quot; &quot;$(OutDir)Deploy\WindowsServices\Amazon&quot;"></Exec>
    
    <!-- Copy AWS windows service -->
		<Exec command="copy /y &quot;$(OutDir)*.dll&quot; &quot;$(OutDir)Deploy\WindowsServices\AWSValidationService&quot;"></Exec>
		<Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Services.AddressValidationService.exe&quot; &quot;$(OutDir)Deploy\WindowsServices\AWSValidationService&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Services.AddressValidationService.exe.config&quot; &quot;$(OutDir)Deploy\WindowsServices\AWSValidationService&quot;"></Exec>

    <!-- Copy Printing windows service -->
    <Exec command="copy /y &quot;$(OutDir)*.dll&quot; &quot;$(OutDir)Deploy\WindowsServices\RoutePrintingService&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Service.RoutePrinter.exe&quot; &quot;$(OutDir)Deploy\WindowsServices\RoutePrintingService&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Service.RoutePrinter.exe.config&quot; &quot;$(OutDir)Deploy\WindowsServices\RoutePrintingService&quot;"></Exec>
       
             
    <!-- Copy AWSMunicipalitiesLoader -->
    <Exec command="copy /y &quot;$(OutDir)AWSMunicipalityLoader.exe&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>    
    <Exec command="copy /y &quot;$(OutDir)Microsoft.Practices.EnterpriseLibrary.Common.dll&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Microsoft.Practices.EnterpriseLibrary.Common.xml&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Microsoft.Practices.EnterpriseLibrary.Data.dll&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Microsoft.Practices.EnterpriseLibrary.Data.xml&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Microsoft.Practices.EnterpriseLibrary.Logging.dll&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Microsoft.Practices.EnterpriseLibrary.Logging.xml&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Business.Common.dll&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Business.Entities.dll&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.ChannelIntegration.Clients.AWS.dll&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Common.dll&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Data.Access.dll&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Serializer.dll&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Service.DataContracts.dll&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader&quot;"></Exec>
    
    

    <!-- Only for production get the config file -->
		<CallTarget Targets="EncryptProductionConfig"  Condition=" '$(Environment)'=='production' Or '$(Environment)'=='certification' "/>

    <!-- Copy main web service -->
    <Exec command="mkdir  &quot;$(OutDir)Deploy\Services\Root&quot;" IgnoreExitCode="true"></Exec>
    <Exec command="mkdir  &quot;$(OutDir)Deploy\Services\Root\bin&quot;" IgnoreExitCode="true"></Exec>
    <Exec command="xcopy /e /y &quot;$(OutDir)_PublishedWebsites\WebServices\index.html&quot; &quot;$(OutDir)Deploy\Services\Root\&quot;"></Exec>


    <!-- Copy CourierManifest web service -->
    <Exec command="mkdir  &quot;$(OutDir)Deploy\Services\CourierManifest&quot;" IgnoreExitCode="true"></Exec>
    <Exec command="mkdir  &quot;$(OutDir)Deploy\Services\CourierManifest\bin&quot;" IgnoreExitCode="true"></Exec>
    <Exec command="xcopy /e /y &quot;$(OutDir)_PublishedWebsites\WebServices\bin&quot; &quot;$(OutDir)Deploy\Services\CourierManifest\bin\&quot;"></Exec>
    <Exec command="xcopy /e /y &quot;$(OutDir)_PublishedWebsites\WebServices\CourierManifest.svc&quot; &quot;$(OutDir)Deploy\Services\CourierManifest\&quot;"></Exec>
    <Exec command="xcopy /e /y &quot;$(OutDir)_PublishedWebsites\WebServices\web.config&quot; &quot;$(OutDir)Deploy\Services\CourierManifest\&quot;"></Exec>

    <!-- Copy RestServices web service -->
    <Exec command="mkdir  &quot;$(OutDir)Deploy\Services\RestServices&quot;" IgnoreExitCode="true"></Exec>
    <Exec command="mkdir  &quot;$(OutDir)Deploy\Services\RestServices\bin&quot;" IgnoreExitCode="true"></Exec>
    <Exec command="xcopy /e /y &quot;$(OutDir)_PublishedWebsites\WebServices\bin&quot; &quot;$(OutDir)Deploy\Services\RestServices\bin\&quot;"></Exec>
    <Exec command="xcopy /e /y &quot;$(OutDir)_PublishedWebsites\WebServices\SmartSortLookupService.svc&quot; &quot;$(OutDir)Deploy\Services\RestServices\&quot;"></Exec>
    <Exec command="xcopy /e /y &quot;$(OutDir)_PublishedWebsites\WebServices\SmartSortScanService.svc&quot; &quot;$(OutDir)Deploy\Services\RestServices\&quot;"></Exec>
    <Exec command="xcopy /e /y &quot;$(OutDir)_PublishedWebsites\WebServices\SmartSortTimeZoneService.svc&quot; &quot;$(OutDir)Deploy\Services\RestServices\&quot;"></Exec>
    <Exec command="xcopy /e /y &quot;$(OutDir)_PublishedWebsites\WebServices\web.config&quot; &quot;$(OutDir)Deploy\Services\RestServices\&quot;"></Exec>

    <!-- Copy ShipmentEvent web service -->
    <Exec command="mkdir  &quot;$(OutDir)Deploy\Services\ShipmentEvent&quot;" IgnoreExitCode="true"></Exec>
    <Exec command="mkdir  &quot;$(OutDir)Deploy\Services\ShipmentEvent\bin&quot;" IgnoreExitCode="true"></Exec>
    <Exec command="xcopy /e /y &quot;$(OutDir)_PublishedWebsites\WebServices\bin&quot; &quot;$(OutDir)Deploy\Services\ShipmentEvent\bin\&quot;"></Exec>
    <Exec command="xcopy /e /y &quot;$(OutDir)_PublishedWebsites\WebServices\ShipmentEvent.svc&quot; &quot;$(OutDir)Deploy\Services\ShipmentEvent\&quot;"></Exec>
    <Exec command="xcopy /e /y &quot;$(OutDir)_PublishedWebsites\WebServices\web.config&quot; &quot;$(OutDir)Deploy\Services\ShipmentEvent\&quot;"></Exec>

    
    <ItemGroup>
			<AppZipFiles Include="$(OutDir)Deploy\**" />
		</ItemGroup>

		<Exec command="del /F /Q &quot;$(OutDir)..\..\*.zip&quot;"></Exec>

		<Zip Files="@(AppZipFiles)"
					 ZipFileName="$(OutDir)..\..\$(TeamProject)-$(Environment)-$(BuildNumber).zip"
					 WorkingDirectory="$(OutDir)Deploy\"
					 ZipLevel="9" />

		<!-- Copy Web server zip file -->
		<Exec command="xcopy /e /y &quot;$(OutDir)..\..\$(TeamProject)-$(Environment)-$(BuildNumber).zip&quot; &quot;$(DropLocation)\$(BuildNumber)\&quot;"></Exec>
		<!-- Copy Web services zip file -->

		<!--
			<Exec command="rmdir /S /Q &quot;$(OutDir)..\..&quot;"></Exec>
		-->


	</Target>

	<!-- Constants -->
	<PropertyGroup>
		<TeamFoundationServerUrl>http://w-02073-c2c:8080</TeamFoundationServerUrl>
		<TMPTFSProject>$/SCM-SMARTSORT-Main</TMPTFSProject>
		<SourceProductionConfig>Main\Source\Purolator.SMARTSORT\Deployment\Configuration</SourceProductionConfig>
		<DestinationProductionConfig>SmartSort\Deployment\Configuration</DestinationProductionConfig>
		<TF>"$(VS100COMNTOOLS)..\IDE\tf.exe"</TF>
		<TMPWorkingFolder>$(BuildDirectoryPath)\..\SCM</TMPWorkingFolder>
		<TMPWorkSpaceName>SCM-SMARTSORTAPP-Main</TMPWorkSpaceName>
	</PropertyGroup>

	<Target Name="GetProductionConfig">
		<RemoveDir Directories="$(TMPWorkingFolder)" />
		<MakeDir Directories="$(TMPWorkingFolder)" />
		<Exec Command="$(TF) workspace /delete $(TMPWorkSpaceName) /server:$(TeamFoundationServerUrl) /noprompt" WorkingDirectory="$(TMPWorkingFolder)" ContinueOnError="true" />
		<Message Text="Temporary build Workspace deleted" />
		<Exec Command="$(TF) workspace /new $(TMPWorkSpaceName) /server:$(TeamFoundationServerUrl) /noprompt" WorkingDirectory="$(TMPWorkingFolder)" ContinueOnError="false" />
		<Message Text="New temporary build Workspace created" />
		<Exec Command="$(TF) workfold /workspace:$(TMPWorkSpaceName) /server:$(TeamFoundationServerUrl) /unmap $/" WorkingDirectory="$(TMPWorkingFolder)" ContinueOnError="false" />
		<Message Text="Default working folder for new temporary build workspace unmapped" />
		<Exec Command="$(TF) workfold /workspace:$(TMPWorkSpaceName) /server:$(TeamFoundationServerUrl) /map $(TMPTFSProject) $(TMPWorkingFolder)" WorkingDirectory="$(TMPWorkingFolder)" ContinueOnError="false" />
		<Message Text="Working directory mapped to new temporary build workspace" />
		<Exec Command="$(TF) get &quot;$(TMPTFSProject)&quot; /recursive /version:T /noprompt" WorkingDirectory="$(TMPWorkingFolder)" ContinueOnError="false" />
		<Exec command="xcopy /e /y &quot;$(TMPWorkingFolder)\$(SourceProductionConfig)\production.py&quot; &quot;$(SolutionRoot)\$(DestinationProductionConfig)&quot;"></Exec>
		<Exec command="xcopy /e /y &quot;$(TMPWorkingFolder)\$(SourceProductionConfig)\certification.py&quot; &quot;$(SolutionRoot)\$(DestinationProductionConfig)&quot;"></Exec>
		<Exec Command="$(TF) workspace /delete $(TMPWorkSpaceName) /server:$(TeamFoundationServerUrl) /noprompt" WorkingDirectory="$(TMPWorkingFolder)" ContinueOnError="true" />
		<Message Text="Temporary build Workspace deleted" />
		<RemoveDir Directories="$(TMPWorkingFolder)" />
	</Target>


	<PropertyGroup>
		<DOTNET_DIR>$(WINDIR)\Microsoft.NET\Framework\v4.0.30319</DOTNET_DIR>
		<ASPNET_REGIIS>"$(DOTNET_DIR)\aspnet_regiis.exe"</ASPNET_REGIIS>
	</PropertyGroup>

	<Target Name="EncryptProductionConfig">
		<!-- Web server-->
		<Exec Command="$(ASPNET_REGIIS) -pef connectionStrings &quot;$(OutDir)_PublishedWebsites\WebServices&quot; -prov AppRSAProtectedConfigurationProvider" />
		<Exec Command="$(ASPNET_REGIIS) -pef appSettings &quot;$(OutDir)_PublishedWebsites\WebServices&quot; -prov AppRSAProtectedConfigurationProvider" />

		<!-- EM Sender -->
		<Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Services.EventSender.exe.config&quot; &quot;$(OutDir)tmp\web.config&quot;"></Exec>		
		<Exec Command="$(ASPNET_REGIIS) -pef appSettings  &quot;$(OutDir)tmp&quot; -prov AppRSAProtectedConfigurationProvider" />				
		<Exec command="copy /y &quot;$(OutDir)tmp\web.config&quot; &quot;$(OutDir)Deploy\WindowsServices\EMEventSender\Purolator.SmartSort.Windows.Services.EventSender.exe.config&quot;"></Exec>

    <!-- OFD Sender -->
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Services.OFDSender.exe.config&quot; &quot;$(OutDir)tmp\web.config&quot;"></Exec>
    <Exec Command="$(ASPNET_REGIIS) -pef appSettings  &quot;$(OutDir)tmp&quot; -prov AppRSAProtectedConfigurationProvider" />
    <Exec command="copy /y &quot;$(OutDir)tmp\web.config&quot; &quot;$(OutDir)Deploy\WindowsServices\OFDSender\Purolator.SmartSort.Windows.Services.OFDSender.exe.config&quot;"></Exec>

    <!-- Amazon Sender -->
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Services.Amazon.exe.config&quot; &quot;$(OutDir)tmp\web.config&quot;"></Exec>    
    <Exec Command="$(ASPNET_REGIIS) -pef appSettings  &quot;$(OutDir)tmp&quot; -prov AppRSAProtectedConfigurationProvider" />    
    <Exec command="copy /y &quot;$(OutDir)tmp\web.config&quot; &quot;$(OutDir)Deploy\WindowsServices\OFDSender\Purolator.SmartSort.Windows.Services.Amazon.exe.config&quot;"></Exec>
    
    <!-- AWS Service -->
		<Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Services.AddressValidationService.exe.config&quot; &quot;$(OutDir)tmp\web.config&quot;"></Exec>		
		<Exec Command="$(ASPNET_REGIIS) -pef connectionStrings  &quot;$(OutDir)tmp&quot; -prov AppRSAProtectedConfigurationProvider" />
		<Exec Command="$(ASPNET_REGIIS) -pef appSettings  &quot;$(OutDir)tmp&quot; -prov AppRSAProtectedConfigurationProvider" />
		<Exec command="copy /y &quot;$(OutDir)tmp\web.config&quot; &quot;$(OutDir)Deploy\WindowsServices\AWSValidationService\Purolator.SmartSort.Windows.Services.AddressValidationService.exe.config&quot;"></Exec>


    <!-- AWSMunicipalitiesLoader -->
    <Exec command="copy /y &quot;$(OutDir)AWSMunicipalityLoader.exe.config&quot; &quot;$(OutDir)tmp\web.config&quot;"></Exec>
    <Exec Command="$(ASPNET_REGIIS) -pef connectionStrings  &quot;$(OutDir)tmp&quot; -prov AppRSAProtectedConfigurationProvider" />
    <Exec Command="$(ASPNET_REGIIS) -pef appSettings  &quot;$(OutDir)tmp&quot; -prov AppRSAProtectedConfigurationProvider" />
    <Exec command="copy /y &quot;$(OutDir)tmp\web.config&quot; &quot;$(OutDir)Deploy\AWSMunicipalitiesLoader\AWSMunicipalityLoader.exe.config&quot;"></Exec>

    


    <!-- Printing Service -->
    <Exec command="copy /y &quot;$(OutDir)Purolator.SmartSort.Windows.Service.RoutePrinter.exe.config&quot; &quot;$(OutDir)tmp\web.config&quot;"></Exec>
    <Exec Command="$(ASPNET_REGIIS) -pef connectionStrings  &quot;$(OutDir)tmp&quot; -prov AppRSAProtectedConfigurationProvider" />
    <Exec Command="$(ASPNET_REGIIS) -pef appSettings  &quot;$(OutDir)tmp&quot; -prov AppRSAProtectedConfigurationProvider" />
    <Exec command="copy /y &quot;$(OutDir)tmp\web.config&quot; &quot;$(OutDir)Deploy\WindowsServices\RoutePrintingService\Purolator.SmartSort.Windows.Service.RoutePrinter.exe.config&quot;"></Exec>
  </Target>
</Project>

