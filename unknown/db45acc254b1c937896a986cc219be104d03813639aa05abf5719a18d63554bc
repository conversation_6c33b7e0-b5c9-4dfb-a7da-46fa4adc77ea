﻿using System;
using System.Net;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Xml.Serialization;
using Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.ChannelIntegration.EnterpriseEvent;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using System.Data.SqlClient;
using System.Data;
using Purolator.SmartSort.Serializer;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Data.Access.RouteManagement;
using Purolator.SmartSort.Windows.Service.RoutePrinter;

using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Tables;
using MigraDoc.DocumentObjectModel.Shapes;
using MigraDoc.Rendering;
using System.Diagnostics;
using PdfSharp.Pdf;


namespace TestLoadDeviation
{
   
    public class Program
    {
        /*

        static int counter = 1000;

        public static List<int> getInts()
        {
            List<int> result = new List<int>();
            if (counter - 100 > 0)
            {
                counter -= 100;
                for (int i = 0; i < 100; i++)
                {
                    result.Add(10000 + new Random().Next(10000));
                }
            }
            else
            {
                for (int i = 0; i < counter; i++)
                {
                    result.Add(new Random().Next(2000));
                }
                counter = 0;
            }
            return result;

        }

        public static void testThreads()
        {

            List<int> items = getInts();
            int round = 0;
            while (items.Count > 0)
            {
                Console.WriteLine(DateTime.Now + " - Start round: " + round + ", items=" + items.Count + ", counter = " + counter);
                var workerBlock = new ActionBlock<int>(
                     millisecondsTimeout => Thread.Sleep(millisecondsTimeout),
                    // Specify a maximum degree of parallelism. 
                    new ExecutionDataflowBlockOptions
                    {
                        MaxDegreeOfParallelism = 10
                    });

                foreach (var item in items)
                {
                    workerBlock.Post(item);
                }

                workerBlock.Complete();
                workerBlock.Completion.Wait();
                Console.WriteLine(DateTime.Now + "After completion round: " + round + ", items=" + items.Count + ", counter = " + counter);
                items = getInts();
                round++;
            }
        }

        */

        public static void Main()
        {

            /*

      RoutePrinterImplementation impl = new RoutePrinterImplementation();
      impl.ResetDatabaseQueue();
            
            
      var records = impl.GetWorkItems(1);

      foreach (var item in records)
      {
          Console.Out.WriteLine("Item=" + item.RequestID + "  routeplan = " + item.RoutePlanVersionID);
          impl.Process(item);

     }
             */

            /*
            SplitPlanPrint p = new SplitPlanPrint();
            SplitPlanPrintItem i = new SplitPlanPrintItem();
            i.Addresses = new List<string> { "test1", "test2" };
            i.Pudro = "1";

            SplitPlanPrintItem j = new SplitPlanPrintItem();
            j.Addresses = new List<string> { "test3", "test4" };
            j.Pudro = "1";
            
            p.Terminal = "511";
            p.VersionNumber = "1234";
            p.Pudros = new List<SplitPlanPrintItem> { i, j};

            XmlSerializer serializer = new XmlSerializer(typeof(SplitPlanPrint));

            serializer.Serialize(Console.Out, p);


 */

            System.Globalization.CultureInfo ci = new System.Globalization.CultureInfo("fr-ca");
            System.Threading.Thread.CurrentThread.CurrentCulture = ci;
            RoutePlanRenderResource.Culture = ci;

            SplitPlanPrintRepository repo = new SplitPlanPrintRepository("SSDB");
            RoutePlanPrintRepository repo1 = new RoutePlanPrintRepository("SSDB");            

            RoutePlanPrint list1 = repo1.GetRoutePlan(4);
            SplitPlanPrint list = repo.GetSplitPlan(4);

            SplitPlanPDFForm form = new SplitPlanPDFForm();
            RoutePlanPDFForm form1 = new RoutePlanPDFForm();

       var document = form.CreateDocument(null, list);
       var document1 = form1.CreateDocument(null, list1);

       PdfDocumentRenderer pdfRenderer = new PdfDocumentRenderer(false, PdfFontEmbedding.Always)
       {
           Document = document
       };

       pdfRenderer.RenderDocument();

       PdfDocumentRenderer pdfRenderer1 = new PdfDocumentRenderer(false, PdfFontEmbedding.Always)
       {
           Document = document1
       };

       pdfRenderer1.RenderDocument();


       string uncpath = "\\\\Inno-5cg5250xlr\\pdf";

       using (UNCAccessWithCredentials unc = new UNCAccessWithCredentials())
       {
           if (unc.NetUseWithCredentials(uncpath, "test", "Inno-5cg5250xlr", "Welcome123"))
           {

               string[] dirs = Directory.GetDirectories(uncpath);
               foreach (string d in dirs)
               {
                   Console.Out.WriteLine(d); ;
               }

               using(var writer = File.Create(uncpath + "\\testout.pdf"))
               {
                   pdfRenderer.Save(writer, true);

               }

               using (var writer = File.Create(uncpath + "\\testout_r.pdf"))
               {
                   pdfRenderer1.Save(writer, true);

               }
                    
           }
           else
           {
               // The connection has failed. Use the LastError to get the system error code
               Console.Out.WriteLine("Failed to connect to " + uncpath +
                               "\r\nLastError = " + unc.LastError.ToString(),
                               "Failed to connect");
           }                
       }


           

       Console.Out.WriteLine("Done");


      


           // testThreads();

            /*
            List<EnterpriseEvent> result = new List<EnterpriseEvent>();

            using (SqlConnection conn = new SqlConnection("Data Source=W-00821-D2C.cpggpc.ca\\SmartSort,14330;Initial Catalog= SSBE;User ID= smartsortapp;Password=***********;Pooling=true;Max Pool Size=100;Min Pool Size=0;Connection Lifetime=60"))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand("dbo.EventGetData", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Add(new SqlParameter("fetchNo", 2));
                    cmd.Parameters.Add(new SqlParameter("server_name", Environment.MachineName));

                    using (var reader = cmd.ExecuteReader())
                    {

                        while (reader.Read())
                        {
                            object dataObj = reader["data"];

                            if (dataObj != null)
                            {
                                EnterpriseEvent ev = new EnterpriseEvent();
                                try
                                {
                                    ev = Serializer.Deserialize<EnterpriseEvent>(dataObj.ToString());
                                    ev.SSBERequestID = int.Parse(reader["SSBERequestID"].ToString());
                                    result.Add(ev);
                                }
                                catch (Exception ex)
                                {

                                    Console.WriteLine("Error loading enterprise event XML:" + ex.Message + ":" + dataObj.ToString());
                                }
                            }
                        }
                    }
                }
            }
            


            
                       string decryptionKey = CreateKey(24);
                       string validationKey = CreateKey(64);

                       Console.WriteLine("<machineKey validationKey=\"{0}\" decryptionKey=\"{1}\" validation=\"SHA1\"/>", validationKey, decryptionKey);
            
          

            Type objType = typeof(Purolator.SmartSort.Service.WCFMessageInspector.WCFClientLoggingBehaviour);
       
            
           
                       EnterpriseEventClient client = new EnterpriseEventClient();

                       EnterpriseEvent ev = new EnterpriseEvent();

                       ev.BarcodeType = "1";
                       ev.EnterpriseEventID = 123;
                       ev.EventDateTimeTZ = DateTime.Now.ToString();
                       ev.TransmissionDate = DateTime.Now.ToString();
                       ev.EventExternalCode = 4010;
                       ev.PiecePin = "DAN123456794";
                       ev.Pin = "PIN";
                       ev.RouteID = "52B51120150710";
                       ev.Route = "52B";
                       ev.SS = "SS";
                       ev.SSID = "SSID";
                       ev.Terminal = "511";
            
            

                      client.SendEvent(ev);

            CallerContext ctx = ContextManager.Instance.Get();
            if (ctx != null)
            {
                Console.WriteLine("Request" + ctx.RequestXML);
            }
           
             */
                       
            /*
            
            
            TestSortEvent.SortEventService client = new TestSortEvent.SortEventService();
            client.Url = "https://nonprod-soa-gw-01.cpggpc.ca/integration1/SortEvent";
            client.Credentials = new NetworkCredential("PURO_SMARTSORT_APP", "PURO_SMARTSORT-INT");
            
            TestSortEvent.EventHeaderType header = new TestSortEvent.EventHeaderType();

            TestSortEvent.EventLocationType location = new TestSortEvent.EventLocationType();

            TestSortEvent.EventConfirmationType confirmation = new TestSortEvent.EventConfirmationType();

            TestSortEvent.EventReferenceType[] references = new TestSortEvent.EventReferenceType[] {};

            TestSortEvent.AdditionalInfoType[] aditional = new TestSortEvent.AdditionalInfoType[] {};
            
            string result = client.SortEvent(header, location, confirmation, references, aditional);
           
            */
             
            /*
            XmlSerializer serializer =  new XmlSerializer(typeof(CourierManifest));
            
            CourierManifest manifest = new CourierManifest();

            List<CourierManifestRow> rows = new List<CourierManifestRow>();

            CourierManifestRow row = new CourierManifestRow();
           
            row.City = "city1";
            row.Stopid = 1;
            row.DeliverySeqId = 0;
          
            row.PostalCode = "L3G2R4";
            
            row.StreetName = "street1";
            row.StreetNumber = "123";
            CourierManifestPackage pkg = new CourierManifestPackage();
            pkg.AlternateAddressFlag = "0";
            pkg.ChainOfSignature = "";
            pkg.CustomerName = "Cust1";
            pkg.DangerousGoods = "";
            pkg.HoldForPickup = "";
            pkg.PackageLocation = "a";
            pkg.PackageType = "M";
            pkg.PiecePin = "1234";
            pkg.ServiceTime = "12:30";
            pkg.ShelfNumber = "1";
            pkg.UnitNumber = "1A";


            
            List<CourierManifestPackage> pkgs = new List<CourierManifestPackage>();
            pkg.AlternateAddressFlag = "0";
            pkg.ChainOfSignature = "";
            pkg.CustomerName = "Cust2";
            pkg.DangerousGoods = "";
            pkg.HoldForPickup = "";
            pkg.PackageLocation = "a";
            pkg.PackageType = "M";
            pkg.PiecePin = "1235";
            pkg.ServiceTime = "12:30";
            pkg.ShelfNumber = "1";
            pkg.UnitNumber = "1A";
            pkgs.Add(pkg);
            row.Packages = pkgs;
            rows.Add(row);


            row = new CourierManifestRow();

            row.Stopid = 2;
            pkgs = new List<CourierManifestPackage>();
            pkgs.Add(pkg);
            row.Packages = pkgs;
            rows.Add(row);

            manifest.ManifestRows = rows;

            serializer.Serialize(Console.Out, manifest);
*/                       
        }

        static String CreateKey(int numBytes)
        {
            RNGCryptoServiceProvider rng = new RNGCryptoServiceProvider();
            byte[] buff = new byte[numBytes];

            rng.GetBytes(buff);
            return BytesToHexString(buff);
        }

        static String BytesToHexString(byte[] bytes)
        {
            StringBuilder hexString = new StringBuilder(64);

            for (int counter = 0; counter < bytes.Length; counter++)
            {
                hexString.Append(String.Format("{0:X2}", bytes[counter]));
            }
            return hexString.ToString();
        }
    }
}
