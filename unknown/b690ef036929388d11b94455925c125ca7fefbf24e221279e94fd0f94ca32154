﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.34209
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.34209.
// 
#pragma warning disable 1591

namespace TestLoadDeviation.TestSortEvent {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.0.30319.34209")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="SortEventSOAP1.2", Namespace="http://www.purolator.com/ws/tracking/sortevent/soap/2015/05")]
    public partial class SortEventService : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback SortEventOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public SortEventService() {
            this.SoapVersion = System.Web.Services.Protocols.SoapProtocolVersion.Soap12;
            this.Url = global::TestLoadDeviation.Properties.Settings.Default.TestLoadDeviation_TestSortEvent_SortEventService;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event SortEventCompletedEventHandler SortEventCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("SortEvent", RequestElementName="SortEventRequest", RequestNamespace="http://www.purolator.com/ws/tracking/sortevent/2015/05", ResponseNamespace="http://www.purolator.com/ws/tracking/sortevent/2015/05", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        [return: System.Xml.Serialization.XmlElementAttribute("Success", Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string SortEvent([System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)] EventHeaderType SortEventHeader, [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)] EventLocationType EventLocation, [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)] EventConfirmationType EventConfirmation, [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)] [System.Xml.Serialization.XmlArrayItemAttribute("EventReference", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)] EventReferenceType[] EventReferences, [System.Xml.Serialization.XmlArrayAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)] [System.Xml.Serialization.XmlArrayItemAttribute("AdditionalInfo", Form=System.Xml.Schema.XmlSchemaForm.Unqualified, IsNullable=false)] AdditionalInfoType[] AdditionalEventInfos) {
            object[] results = this.Invoke("SortEvent", new object[] {
                        SortEventHeader,
                        EventLocation,
                        EventConfirmation,
                        EventReferences,
                        AdditionalEventInfos});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SortEventAsync(EventHeaderType SortEventHeader, EventLocationType EventLocation, EventConfirmationType EventConfirmation, EventReferenceType[] EventReferences, AdditionalInfoType[] AdditionalEventInfos) {
            this.SortEventAsync(SortEventHeader, EventLocation, EventConfirmation, EventReferences, AdditionalEventInfos, null);
        }
        
        /// <remarks/>
        public void SortEventAsync(EventHeaderType SortEventHeader, EventLocationType EventLocation, EventConfirmationType EventConfirmation, EventReferenceType[] EventReferences, AdditionalInfoType[] AdditionalEventInfos, object userState) {
            if ((this.SortEventOperationCompleted == null)) {
                this.SortEventOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSortEventOperationCompleted);
            }
            this.InvokeAsync("SortEvent", new object[] {
                        SortEventHeader,
                        EventLocation,
                        EventConfirmation,
                        EventReferences,
                        AdditionalEventInfos}, this.SortEventOperationCompleted, userState);
        }
        
        private void OnSortEventOperationCompleted(object arg) {
            if ((this.SortEventCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SortEventCompleted(this, new SortEventCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.purolator.com/ws/tracking/eventcommon/2015/05")]
    public partial class EventHeaderType {
        
        private int eventCodeField;
        
        private string trackingIdCodeField;
        
        private string trackingIdField;
        
        private string sourceSystemIdField;
        
        private string subProcessIdField;
        
        private string subSubProcessIdField;
        
        private System.DateTime eventTimestampField;
        
        private System.DateTime transmissionTimestampField;
        
        private bool transmissionTimestampFieldSpecified;
        
        private AddressValidationStatusType addressValidationStatusField;
        
        private bool addressValidationStatusFieldSpecified;
        
        private string reasonForCaseField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public int EventCode {
            get {
                return this.eventCodeField;
            }
            set {
                this.eventCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string TrackingIdCode {
            get {
                return this.trackingIdCodeField;
            }
            set {
                this.trackingIdCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string TrackingId {
            get {
                return this.trackingIdField;
            }
            set {
                this.trackingIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string SourceSystemId {
            get {
                return this.sourceSystemIdField;
            }
            set {
                this.sourceSystemIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string SubProcessId {
            get {
                return this.subProcessIdField;
            }
            set {
                this.subProcessIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string SubSubProcessId {
            get {
                return this.subSubProcessIdField;
            }
            set {
                this.subSubProcessIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public System.DateTime EventTimestamp {
            get {
                return this.eventTimestampField;
            }
            set {
                this.eventTimestampField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public System.DateTime TransmissionTimestamp {
            get {
                return this.transmissionTimestampField;
            }
            set {
                this.transmissionTimestampField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TransmissionTimestampSpecified {
            get {
                return this.transmissionTimestampFieldSpecified;
            }
            set {
                this.transmissionTimestampFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public AddressValidationStatusType AddressValidationStatus {
            get {
                return this.addressValidationStatusField;
            }
            set {
                this.addressValidationStatusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool AddressValidationStatusSpecified {
            get {
                return this.addressValidationStatusFieldSpecified;
            }
            set {
                this.addressValidationStatusFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string ReasonForCase {
            get {
                return this.reasonForCaseField;
            }
            set {
                this.reasonForCaseField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.purolator.com/ws/tracking/eventcommon/2015/05")]
    public enum AddressValidationStatusType {
        
        /// <remarks/>
        OK,
        
        /// <remarks/>
        NotFound,
        
        /// <remarks/>
        ValidButNotUnique,
        
        /// <remarks/>
        OkButDifferentAddrId,
        
        /// <remarks/>
        RangeFoundButNoPocs,
        
        /// <remarks/>
        OkButPostalCodeChanged,
        
        /// <remarks/>
        Unparsable,
        
        /// <remarks/>
        OkButSuiteNotfound,
        
        /// <remarks/>
        OkButFuzzyStreetUsed,
        
        /// <remarks/>
        OkButFuzzyMunicipalityUsed,
        
        /// <remarks/>
        OkButStreetNotFound,
        
        /// <remarks/>
        StreetNotFound,
        
        /// <remarks/>
        MunicipalityNotFound,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.purolator.com/ws/tracking/eventcommon/2015/05")]
    public partial class AdditionalInfoType {
        
        private string additionalInfoType1Field;
        
        private string additionalInfoValueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AdditionalInfoType", Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string AdditionalInfoType1 {
            get {
                return this.additionalInfoType1Field;
            }
            set {
                this.additionalInfoType1Field = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string AdditionalInfoValue {
            get {
                return this.additionalInfoValueField;
            }
            set {
                this.additionalInfoValueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.purolator.com/ws/tracking/eventcommon/2015/05")]
    public partial class EventReferenceType {
        
        private string referenceIdTypeField;
        
        private string referenceIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string ReferenceIdType {
            get {
                return this.referenceIdTypeField;
            }
            set {
                this.referenceIdTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string ReferenceId {
            get {
                return this.referenceIdField;
            }
            set {
                this.referenceIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.purolator.com/ws/tracking/eventcommon/2015/05")]
    public partial class EventConfirmationType {
        
        private ConfirmationTypeType confirmationTypeField;
        
        private string confirmationIdField;
        
        private string confirmationSubIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public ConfirmationTypeType ConfirmationType {
            get {
                return this.confirmationTypeField;
            }
            set {
                this.confirmationTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string ConfirmationId {
            get {
                return this.confirmationIdField;
            }
            set {
                this.confirmationIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string ConfirmationSubId {
            get {
                return this.confirmationSubIdField;
            }
            set {
                this.confirmationSubIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.purolator.com/ws/tracking/eventcommon/2015/05")]
    public enum ConfirmationTypeType {
        
        /// <remarks/>
        ROUTE_CHART,
        
        /// <remarks/>
        LOAD_CHART,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.purolator.com/ws/tracking/eventcommon/2015/05")]
    public partial class EventLocationType {
        
        private LocationTypeType locationTypeField;
        
        private string locationIdField;
        
        private string locationSubIdField;
        
        private DeviceModeType deviceModeField;
        
        private bool deviceModeFieldSpecified;
        
        private string sourceUserIdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public LocationTypeType LocationType {
            get {
                return this.locationTypeField;
            }
            set {
                this.locationTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string LocationId {
            get {
                return this.locationIdField;
            }
            set {
                this.locationIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string LocationSubId {
            get {
                return this.locationSubIdField;
            }
            set {
                this.locationSubIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public DeviceModeType DeviceMode {
            get {
                return this.deviceModeField;
            }
            set {
                this.deviceModeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DeviceModeSpecified {
            get {
                return this.deviceModeFieldSpecified;
            }
            set {
                this.deviceModeFieldSpecified = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string SourceUserId {
            get {
                return this.sourceUserIdField;
            }
            set {
                this.sourceUserIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.purolator.com/ws/tracking/eventcommon/2015/05")]
    public enum LocationTypeType {
        
        /// <remarks/>
        TERMINAL,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.purolator.com/ws/tracking/eventcommon/2015/05")]
    public enum DeviceModeType {
        
        /// <remarks/>
        ScanAndSlap,
        
        /// <remarks/>
        Remediate,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.0.30319.34209")]
    public delegate void SortEventCompletedEventHandler(object sender, SortEventCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.0.30319.34209")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SortEventCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SortEventCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591