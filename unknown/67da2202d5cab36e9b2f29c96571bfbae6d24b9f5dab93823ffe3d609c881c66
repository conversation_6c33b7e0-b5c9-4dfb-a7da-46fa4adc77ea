﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Xml.Serialization;
using System.ComponentModel.DataAnnotations;

namespace Purolator.SmartSort.Service.DataContracts
{
    [DataContract(Name = "PinEvent", Namespace = ServiceNamespace.DataType, IsReference = false)]
    public class PinEvent
    {        
        [DataMember(Name = "Pin", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]
        //[StringLength(15, MinimumLength = 1)]
        public string Pin { get; set; }
     
        [DataMember(Name = "Route", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 1)]
        //[StringLength(4, MinimumLength = 1)]
        public string Route { get; set; }

        [DataMember(Name = "EventTimestamp", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]
        //[StringLength(25, MinimumLength = 1)]
        public string EventTimestamp { get; set; }
    }
}
