﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Windows.Services.Amazon
{
    class AmazonSendException : Exception
    {
        public int HttpStatus { get; set; }
        public string Request { get; set; }        
        public string Response { get; set; }

        public AmazonSendException(string msg, Exception reason) : base(msg, reason)
        {            
        }
    }
}
