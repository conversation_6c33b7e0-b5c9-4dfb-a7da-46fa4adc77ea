﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{361C1D76-1D64-4486-AE6B-A1976AD0FA34}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SmartSortWebServices</RootNamespace>
    <AssemblyName>SmartSortWebServices</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <WcfConfigValidationEnabled>True</WcfConfigValidationEnabled>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
    <Use64BitIISExpress />
    <UseGlobalApplicationHostFile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>..\SmartSortRuleSet.ruleset</CodeAnalysisRuleSet>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Common, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.Common.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Data, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\EnterpriseLibrary.Data.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Data.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.Logging.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Logging.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Validation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.Validation.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Validation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="index.html" />
    <Content Include="SmartSortTimeZoneService.svc" />
    <Content Include="empty.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="SmartSortLookupService.svc" />
    <Content Include="CourierManifest.svc" />
    <Content Include="ShipmentEvent.svc" />
    <Content Include="SmartSortScanService.svc" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Purolator.SmartSort.Service.Contracts\Purolator.SmartSort.Service.Contracts.csproj">
      <Project>{1ac49b5c-7f91-4e55-9701-68ab8ad35170}</Project>
      <Name>Purolator.SmartSort.Service.Contracts</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Service.Implementation.CourierManifest\Purolator.SmartSort.Service.Implementation.CourierManifest.csproj">
      <Project>{759632ce-7537-4246-b891-158142e736b1}</Project>
      <Name>Purolator.SmartSort.Service.Implementation.CourierManifest</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Service.Implementation.ShipmentEvent\Purolator.SmartSort.Service.Implementation.ShipmentEvent.csproj">
      <Project>{cd36f7de-a423-4a5e-bd45-113d91b28911}</Project>
      <Name>Purolator.SmartSort.Service.Implementation.ShipmentEvent</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Service.Implementation.SmartSortLookup\Purolator.SmartSort.Service.Implementation.SmartSortLookup.csproj">
      <Project>{c9334151-3fa6-4a10-b92f-51ac1157d512}</Project>
      <Name>Purolator.SmartSort.Service.Implementation.SmartSortLookup</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Service.Implementation.SmartSortScan\Purolator.SmartSort.Service.Implementation.SmartSortScan.csproj">
      <Project>{bb815650-be30-45d3-8ee8-61f94996d60d}</Project>
      <Name>Purolator.SmartSort.Service.Implementation.SmartSortScan</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Service.Implementation.SmartSortTimeZoneService\Purolator.SmartSort.Service.Implementation.SmartSortTimeZone.csproj">
      <Project>{bd9d3564-9f51-4cc2-a55e-99b056f24c5d}</Project>
      <Name>Purolator.SmartSort.Service.Implementation.SmartSortTimeZone</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Service.WCFMessageInspector\Purolator.SmartSort.Service.WCFMessageInspector.csproj">
      <Project>{d603ba92-87b3-485f-bb3c-35c9aa994a50}</Project>
      <Name>Purolator.SmartSort.Service.WCFMessageInspector</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Service.WSDLInterceptor\Purolator.SmartSort.Service.WSDLInterceptor.csproj">
      <Project>{72450b38-a6b8-41f9-ba92-fafb870cf713}</Project>
      <Name>Purolator.SmartSort.Service.WSDLInterceptor</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="web.config">
      <SubType>Designer</SubType>
    </Content>
    <None Include="web.Debug.config" />
    <None Include="web.Release.config" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup>
    <RunPostBuildEvent>Always</RunPostBuildEvent>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>12041</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:43233/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
    <MonoDevelop>
      <Properties>
        <XspParameters Port="8080" Address="127.0.0.1" SslMode="None" SslProtocol="Default" KeyType="None" CertFile="" KeyFile="" PasswordOptions="None" Password="" Verbose="True" />
      </Properties>
    </MonoDevelop>
  </ProjectExtensions>
  <PropertyGroup>
    <PreBuildEvent>cd "$(ProjectDir)..\Deployment" &amp;&amp; attrib -R ..\WebServices\web.config  &amp;&amp; postbuild.exe -config "..\WebServices\web.config" -template "web.config" &amp;&amp; postbuild.exe -config "..\WebServices\bin\development.ps1" -template "deploy.ps1" -env "development" &amp;&amp; postbuild.exe -config "..\WebServices\bin\integration.ps1" -template "deploy.ps1" -env "integration" &amp;&amp; postbuild.exe -config "..\WebServices\bin\staging.ps1" -template "deploy.ps1" -env "staging" &amp;&amp; postbuild.exe -config "..\WebServices\bin\production.ps1" -template "deploy.ps1" -env "production"</PreBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>