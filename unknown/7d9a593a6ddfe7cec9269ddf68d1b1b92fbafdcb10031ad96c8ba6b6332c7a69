﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.ServiceModel;
using System.ServiceModel.Activation;

using Purolator.SmartSort.Business.Common.Exception;
using Purolator.SmartSort.Service.MessageContracts;
using System.ServiceModel.Web;
using System.Xml.Linq;
using Purolator.SmartSort.Service.DataContracts;

using SmartSortError = Purolator.SmartSort.Business.Common.Exception.SmartSortError;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data.Sql;
using System.Configuration;
using System.Data;
using Purolator.SmartSort.Data.Access;

using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Resources;
using Purolator.SmartSort.Common;
using System.Threading.Tasks.Dataflow;

namespace Purolator.SmartSort.Service.Implementation.SmartSortScan
{

    // NOTE: You can use the "Rename" command on the "Refactor" menu to change the class name "SmartSortScanService" in code, svc and config file together.
    // NOTE: In order to launch WCF Test Client for testing this service, please select SmartSortScanService.svc or SmartSortScanService.svc.cs at the Solution Explorer and start debugging.
    [AspNetCompatibilityRequirements(RequirementsMode = AspNetCompatibilityRequirementsMode.Allowed)]
    public class SmartSortScanService : ISmartSortScanService
    {
        string[] scanItems;
        
        int deviceIdIndex, userLogginIdIndex, terminalIdIndexIndex, recordIndex;
        String deviceId, userLogginId, terminalId;

        bool insertedRecord = false;

        
        delegate void SendMethod(Entities.SmartSortScan item, ref bool insertedRecord, ref SmartSortScanResponse response);

        public SmartSortError SmartSortScanError(String scansReceived)
        {
            SmartSortError smartError = new SmartSortError("Error Recevied");
            return smartError;

        }

        //public static void Process(string connectionInfo, Entities.SmartSortScan item)
        public static void Process(Entities.SmartSortScan item, ref bool insertedRecord, ref SmartSortScanResponse response)
        {
            Logger.Debug("Processing smart sort scan item " + item.SSLog.PiecePin, LogCategories.SCAN_SERVICE);

            try
            {


                SmartSortScanRepository sssRepo = new SmartSortScanRepository(Constants.DB_CONNECTION_NAME);
                sssRepo.InsertSmartSortScan(item);


                if (item.SSLog.Status == true)
                {
                    insertedRecord = true;
                }
                else
                {
                    Contracts.Common.SmartSortResponseError smartSortResponseError = new Contracts.Common.SmartSortResponseError();                    
                    smartSortResponseError.ErrorMessage = ErrorResources.E2001 + ". "+ item.SSLog.ErrorMessage;
                    smartSortResponseError.ErrorCode = "2001";
                    smartSortResponseError.Pin = item.SSLog.PiecePin;
                    response.Errors.Add(smartSortResponseError);
                }
            }
            catch (Exception ex)
            {
                Contracts.Common.SmartSortResponseError smartSortResponseError = new Contracts.Common.SmartSortResponseError();
                smartSortResponseError.ErrorMessage = ErrorResources.E2003 + ". " + ex.Message;
                smartSortResponseError.ErrorCode = "2003";
                smartSortResponseError.Pin = item.SSLog.PiecePin;
                response.Errors.Add(smartSortResponseError);

                Logger.Error("Error procesing smart sort scan log", LogCategories.SCAN_SERVICE, ex);
            }
        }

        private void DoWork(string[] scans, SmartSortScanResponse response)
        {
            try
            {


                SendMethod workerMethod = SmartSortScanService.Process;

                int concurrentWorkers;
                if(int.TryParse(System.Configuration.ConfigurationManager.AppSettings["SmartSortScanLog_ConcurrentWorkers"], out concurrentWorkers))
                {

                }
                else
                {
                    concurrentWorkers = 10;
                }
                var workerBlock = new ActionBlock<Entities.SmartSortScan>(
                   actionEvent => workerMethod(actionEvent,ref insertedRecord, ref response),
                    //actionEvent => workerMethod(Constants.DB_CONNECTION_NAME, actionEvent),
                    // Specify a maximum degree of parallelism. 
                   new ExecutionDataflowBlockOptions
                   {                       
                       MaxDegreeOfParallelism = concurrentWorkers
                        
                   });




                foreach (string scan in scans)
                {

                    //First Record is the header
                    scanItems = scan.Split('|');

                    //DEVICE_ID|USERLOGINID|TERMINALID^
                    //RoutePlanVersionID|ParkingPlanMasterVersionID|PIN|PrePrintID|PrimarySort|BeltSide|Route|SSStatusReason|Shelf|DeliverySequenceID|
                    //TruckShelfOverride|SmartSortMode|ScanDateTime|PostalCode|ProvinceCode|MunicipalityName|
                    //StreetNumber|StreetNumberSuffix|StreetName|StreetType|StreetDirection|UnitNumber|CustomerName|
                    //PrintDateTime|DeliveryTime|ShipmentType|DeliveryType|DiversionCode|HandlingClassType|PackageType|
                    //BarcodeType|ResolvedBy|AlternateAddressFlag^

                    int fieldCount = 33;

                    //Test for Header Record
                    if (recordIndex == 0)
                    {
                        deviceId = getScanItem(deviceIdIndex);
                        userLogginId = getScanItem(userLogginIdIndex);
                        terminalId = getScanItem(terminalIdIndexIndex);
                    }
                    else
                    {
                        if (scanItems.Length != fieldCount)
                        {                            
                            Contracts.Common.SmartSortResponseError smartSortResponseError = new Contracts.Common.SmartSortResponseError();
                            //smartSortResponseError.ErrorMessage = "Items in scan list do not match correct number of elements: " + fieldCount;
                            smartSortResponseError.ErrorMessage = String.Format(ErrorResources.E2002, fieldCount);
                            smartSortResponseError.ErrorCode = "2002";
                            smartSortResponseError.Pin = String.Empty;
                            response.Errors.Add(smartSortResponseError);

                        }
                        else
                        {


                            Entities.SmartSortScan ssScan = new Entities.SmartSortScan();
                            ssScan = Translators.TranslateSmartSortScan.Translate(deviceId, userLogginId, terminalId, scan);

                            //Threading
                            workerBlock.Post(ssScan);

                        }
                    }

                    recordIndex++;
                }

                workerBlock.Complete();
                workerBlock.Completion.Wait();
            }
            catch (Exception ex)
            {
                Logger.Error("Error in Smart Sort Log worker", LogCategories.SCAN_SERVICE, ex);

                Contracts.Common.SmartSortResponseError smartSortResponseError = new Contracts.Common.SmartSortResponseError();
                smartSortResponseError.ErrorMessage = ErrorResources.E2004;
                smartSortResponseError.ErrorCode = "2004";
                response.Errors.Add(smartSortResponseError);
            }
        }

        private static bool ISRUNNING = true;
        
            // TODO Test code
            [WebInvoke(Method = "POST",
           RequestFormat = WebMessageFormat.Json,
           ResponseFormat = WebMessageFormat.Json,
           BodyStyle = WebMessageBodyStyle.Bare,
           UriTemplate = "flip")]
            public void flip(bool isRunning)
            {
                ISRUNNING = isRunning;
            }

         [WebInvoke(Method = "GET",
           RequestFormat = WebMessageFormat.Json,
           ResponseFormat = WebMessageFormat.Json,
           BodyStyle = WebMessageBodyStyle.Bare,
           UriTemplate = "isserviceup")]
        public bool IsServiceUp()
        {
            try
            {
                /*
                CourierManifestRepository repo = new CourierManifestRepository(Constants.DB_CONNECTION_NAME);
                return repo.isServiceUp();
                 */
                //TODO temp code, revert it
                return ISRUNNING;
            }
            catch (Exception ex)
            {
                Logger.Error("Error isServiceUp:", LogCategories.SCAN_SERVICE, ex);
            }
            return false;
        }


       [WebInvoke(Method = "POST",
           RequestFormat = WebMessageFormat.Json,
           ResponseFormat = WebMessageFormat.Json,
           BodyStyle = WebMessageBodyStyle.Bare,
           UriTemplate = "smartsortscan")]
        public SmartSortScanResponse SmartSortScan(String scansReceived)
        {
            
            deviceId = String.Empty;
            userLogginId = String.Empty;
            terminalId = String.Empty;
            
            SmartSortScanResponse response = new SmartSortScanResponse();
            try
            {
               

                response.Errors = new List<Contracts.Common.SmartSortResponseError>();
                List<SmartSortError> errorList = new List<SmartSortError>();

                //Error first then prove otherwise
                response.StatusCode = "Error"; //StatusCode.Error;
                
                string[] scans = scansReceived.Split('^');
                string returnString = string.Empty;

                setIndexes();

                DoWork(scans, response);


                if (response.Errors.Count > 0)
                {

                    if (insertedRecord)
                    {
                        //Partial Error                        
                        response.StatusCode = StatusCode.Partial.ToString();
                    }
                    else
                    {
                        //No records were added Full Error
                        response.StatusCode = StatusCode.Error.ToString();
                    }
                }
                else
                {
                    response.StatusCode = StatusCode.Success.ToString();

                }
               
            }
        
            catch(Exception ex)
            {                
                Logger.Error("Error loading scan logs for terminal:" + terminalId + " device:" + deviceId, LogCategories.SCAN_SERVICE, ex);
                Contracts.Common.SmartSortResponseError smartSortResponseError = new Contracts.Common.SmartSortResponseError();              
                smartSortResponseError.ErrorMessage = ErrorResources.E2001 + " Message: " + ex.Message;
                smartSortResponseError.ErrorCode = "2001";
                smartSortResponseError.Pin = String.Empty;
                response.Errors.Add(smartSortResponseError);
            }
            finally {}
            
            return response;
        
        }


        String getScanItem(int Index)
        {
            if (!String.IsNullOrEmpty(scanItems[Index]))
            {
                return Uri.UnescapeDataString(scanItems[Index].ToString());
            }
            else
            {
                return null;
            }
        }

        private void setIndexes()
        {
            recordIndex = 0;

            deviceIdIndex = 0;
            userLogginIdIndex = 1;
            terminalIdIndexIndex = 2;

        }
    }
}
