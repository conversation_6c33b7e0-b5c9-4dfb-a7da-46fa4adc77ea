﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Configuration;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Common;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Serializer;
using Purolator.SmartSort.ChannelIntegration.TrackingHelper;

namespace Purolator.SmartSort.Windows.Services.OFDSender
{
    class OFDSenderImplementation
    {
        public static void ResetDatabaseQueue(string connectionInfo)
        {            
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionInfo))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("dbo.OFDResetData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("server_name", Environment.MachineName));

                        int result = cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error resetting OFD queue", LogCategories.ENTERPRISE_EVENT, ex);                            
            }           
        }
        

        public static List<Entities.OFDEvent> GetWorkItems(string connectionInfo, int fetchAmount)
        {
            List<Entities.OFDEvent> result = new List<Entities.OFDEvent>();
            
            using (SqlConnection conn = new SqlConnection(connectionInfo))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand("dbo.OFDGetData", conn))
                {    
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Add(new SqlParameter("fetchNo", fetchAmount));
                    cmd.Parameters.Add(new SqlParameter("server_name", Environment.MachineName));

                    using (var reader = cmd.ExecuteReader())
                    {

                        while (reader.Read())
                        {
                            object dataObj = reader["data"];
                            // idd TMP object dataObj = "<OFDEvent><OFDQueueID>2</OFDQueueID><PiecePin>1234567890</PiecePin><Terminal>123</Terminal><Route>456</Route><EventDateTime>2018-01-24T14:12:30.950Z</EventDateTime></OFDEvent>";

                            if (dataObj != null)
                            {
                                Entities.OFDEvent ev = new Entities.OFDEvent();
                                try
                                {
                                    ev = Serializer.Serializer.Deserialize<Entities.OFDEvent>(dataObj.ToString());
                                    result.Add(ev);
                                }
                                catch (Exception ex)
                                {
                                    Logger.Error("Error loading OFD event XML:" + dataObj.ToString(), LogCategories.ENTERPRISE_EVENT, ex);
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }

        private static void UpdateEvent(string connectionInfo, int queueId, char status, string errorCode)
        {
            try
            {                
                using (SqlConnection conn = new SqlConnection(connectionInfo))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("dbo.OFDUpdateItem", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("OFDQueueID", queueId));
                        cmd.Parameters.Add(new SqlParameter("Status", status));
                        cmd.Parameters.Add(new SqlParameter("Error_Reason_Cd", errorCode));

                        int result = cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error updating enterprise event " + queueId, LogCategories.ENTERPRISE_EVENT, ex);
            }
        }


        public static void Process(string connectionInfo, Entities.OFDEvent item)
        {
            bool success = false;
            string errorMessage = string.Empty;
            
            try
            {
                Logger.Debug("Processing OFD event " + item.OFDQueueID, LogCategories.ENTERPRISE_EVENT);

                var client = new TrackingHelperClient();
                success = client.SendOFDEvent(item, out errorMessage);
            }
            catch (Exception ex)
            {
                CallerContext ctx = ContextManager.Instance.Get();
                if (ctx != null)
                {
                    Console.WriteLine("Request" + ctx.RequestXML);
                }

                Logger.Error("Error sending OFD event" + item.OFDQueueID + " Request=" + ctx.RequestXML, LogCategories.ENTERPRISE_EVENT, ex);
                errorMessage = ex.Message + ": " + ex.StackTrace;
            }
            finally
            {                
                try
                {
                    char status =  success  ? 'S' : 'E';
                    Logger.Debug("Finished sending OFD event" + item.OFDQueueID + " status=" + status, LogCategories.ENTERPRISE_EVENT);
                    if (errorMessage.Length > 255)
                    {
                        errorMessage = errorMessage.Substring(0, 254);
                    }
                    UpdateEvent(connectionInfo, item.OFDQueueID, status, errorMessage);
                }
                catch (Exception ex)
                {
                    Logger.Error("Error updating error status on preprint record:" + item.OFDQueueID, LogCategories.ENTERPRISE_EVENT, ex);
                }             
            }

        }
    }
}
