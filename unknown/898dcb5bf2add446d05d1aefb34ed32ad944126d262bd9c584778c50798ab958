﻿using Purolator.SmartSort.Service.Common;
using Purolator.SmartSort.Service.DataContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;
using System.Xml.Serialization;
using System.ComponentModel.DataAnnotations;

namespace Purolator.SmartSort.Service.MessageContracts
{
    [DataContract(Name = "CourierManifestRequest", Namespace = ServiceNamespace.DataType, IsReference = false)]  
    public class CourierManifestRequest : Request
    {               
        [DataMember(Name = "DeviceID", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]
        //[StringLength(64, MinimumLength = 1)]
        public string DeviceID  { get; set; }
                
        [DataMember(Name = "TerminalID", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 1)]
        //[StringLength(3, MinimumLength = 1)]
        public string TerminalID  { get; set; }
                
        [DataMember(Name = "Route", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]
        //[StringLength(4, MinimumLength = 1)]
        public string Route  { get; set; }
        
        [DataMember(Name = "Date", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 3)]
        //[StringLength(10, MinimumLength = 1)]
        public string Date { get; set; }   
    }
}
