﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Runtime.Serialization;
using System.Text;
using Purolator.SmartSort.Service.Contracts.Common;
using Purolator.SmartSort.Service.DataContracts;
using Purolator.SmartSort.Service.Common;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Service.MessageContracts
{
    [DataContract(Name = "CloseTruckResponse", Namespace = ServiceNamespace.DataType, IsReference = false)] 
    public class CloseTruckResponse
    {        
        //[XmlElement, MessageBodyMember(Name = "IsSuccess", Order = ServiceDataMember.MemberStartNumber + 0)]
        [DataMember(Name = "IsSuccess", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]  
        public bool IsSuccess { get; set; }
        
        //[XmlElement, MessageBodyMember(Name = "ErrorCode", Order = ServiceDataMember.MemberStartNumber + 1)]
        [DataMember(Name = "ErrorCode", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 1)]  
        public string ErrorCode { get; set; }
        
        //[XmlElement, MessageBodyMember(Name = "ErrorMessage", Order = ServiceDataMember.MemberStartNumber + 2)]
        [DataMember(Name = "ErrorMessage", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]  
        public string ErrorMessage  { get; set; }        
    }
}
