﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Common;
using Purolator.SmartSort.Serializer;
using Purolator.SmartSort.Data.Access;
using Purolator.SmartSort.ChannelIntegration.Clients.AWS;
using Purolator.SmartSort.Service.Translators;
using System.Transactions;
using Newtonsoft.Json;
using Amazon;
using Amazon.SQS;
using Amazon.SQS.Model;


namespace Purolator.SmartSort.Windows.Services.AddressValidationService
{
    class AddressValidationServiceImplementation
    {
        public static void ResetDatabaseQueue(string connectionInfo)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionInfo))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("dbo.ResetPreprintDataForAWS", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("@ServerName", Environment.MachineName));

                        int result = cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error resetting preprint queue", LogCategories.ADDRESS_VALIDATION, ex);
            }
        }


        public static List<Entities.PrePrintDetails> GetWorkItems(int fetchAmount)
        {
            List<Entities.PrePrintDetails> result = new List<Entities.PrePrintDetails>();

            try
            {
                AddressValidationRepository repo = new AddressValidationRepository(Constants.DB_CONNECTION_NAME);
                result = repo.GetPrePrintDataForAWS(Environment.MachineName, fetchAmount);
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading preprint records", LogCategories.ADDRESS_VALIDATION, ex);
            }

            return result;
        }

        public static void Process(Entities.PrePrintDetails item)
        {
            bool success = false;
            try
            {
                Logger.Debug("Processing preprint record:" + item.PrePrintStgId, LogCategories.ADDRESS_VALIDATION);

                if (!item.AVSFinished)
                {

                    Entities.Address address = TranslatePreprint.Translate(item);
                    AWS client = new AWS();
                    Purolator.SmartSort.Business.Common.BOResult<ValidateAddressDetailsResult> awsResult = client.ValidateAddress(address);

                    if (awsResult.IsSuccess)
                    {
                        ValidateAddressDetailsResult validatedResult = awsResult.ReturnObject;

                        string statusCode = "Unparsable";
                        if (validatedResult.IsParseable)
                        {
                            if (validatedResult.IsValid)
                            {
                                statusCode = "Valid";
                            }
                            else
                            {
                                statusCode = "Invalid";
                            }
                        }

                        TransactionOptions txnOptions = new TransactionOptions
                        {
                            IsolationLevel = System.Transactions.IsolationLevel.ReadCommitted
                        };

                        using (TransactionScope scope = new TransactionScope(TransactionScopeOption.Required, txnOptions))
                        {
                            //Insert Record
                            PrePrintMasterRepository repository = new PrePrintMasterRepository(Constants.DB_CONNECTION_NAME);
                            repository.Add(TranslatePreprint.Translate(item, validatedResult.MatchingSuggestedAddress, statusCode));
                            Logger.Debug("Insert preprintMaster for preprint record:" + item.PrePrintStgId + " AWS status=" + statusCode, LogCategories.ADDRESS_VALIDATION);

                            //Set Status Completed
                            UpdatePreprintStatus(item.PrePrintStgId, Entities.PrePrintDetailsStatusEnum.Complete);
                            scope.Complete();
                        }
                    }
                }

                success = true;
            }
            catch (Exception ex)
            {
                Logger.Error("Error processing preprint record:" + item.PrePrintStgId, LogCategories.ADDRESS_VALIDATION, ex);
            }
            finally
            {
                if (!success)
                {
                    try
                    {
                        UpdatePreprintStatus(item.PrePrintStgId, Entities.PrePrintDetailsStatusEnum.Error);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error("Error updating error status on preprint record:" + item.PrePrintStgId, LogCategories.ADDRESS_VALIDATION, ex);
                    }
                }
            }

            // send to infohub
            try
            {
                InfohubPreprintRepository infohubRepo = new InfohubPreprintRepository(Constants.DB_CONNECTION_NAME);
                Entities.PrePrintDetails reloaded = infohubRepo.GetPrePrint(item.PrePrintStgId);
                if (reloaded != null)
                {
                    string json = JsonConvert.SerializeObject(reloaded);
                    Console.WriteLine(json);

                    IAmazonSQS sqs = new AmazonSQSClient(AddressValidationSettings.Default.AWSKey, AddressValidationSettings.Default.AWSPwd, RegionEndpoint.USEast1);

                    SendMessageRequest sendMessageRequest = new SendMessageRequest();
                    sendMessageRequest.QueueUrl = AddressValidationSettings.Default.SQSUrl;
                    sendMessageRequest.MessageBody = json;
                    sqs.SendMessage(sendMessageRequest);
                }
                else
                {
                    Logger.Error("Get preprint item for infohub returned null for id=" + item.PrePrintStgId, LogCategories.ADDRESS_VALIDATION);
                }

                // update status when finished only if already AVS finished before.
                if(item.AVSFinished)
                {
                    UpdatePreprintStatus(item.PrePrintStgId, Entities.PrePrintDetailsStatusEnum.Complete);
                }

            }
            catch (Exception ex)
            {
                Logger.Error("Error sending to Infohub preprint record:" + item.PrePrintStgId, LogCategories.ADDRESS_VALIDATION, ex);
                UpdatePreprintStatus(item.PrePrintStgId, Entities.PrePrintDetailsStatusEnum.Error);                
            }
        }


        private static void UpdatePreprintStatus(int recordId, Entities.PrePrintDetailsStatusEnum status)
        {
            UpdatePrePrintDataForAWSRepository repo = new UpdatePrePrintDataForAWSRepository(Constants.DB_CONNECTION_NAME);
            Entities.PrePrintDetailsUpdate info = new Entities.PrePrintDetailsUpdate
            {
                PrePrintStgId = recordId,
                Status = status
            };

            Logger.Error("Updating status " + status + " for preprint record:" + recordId, LogCategories.ADDRESS_VALIDATION);
            repo.Save(info);            
        }
    }
}
