<?xml version="1.0" encoding="utf-8"?>
<xsd:schema xmlns:pns="http://www.purolator.com/ws/tracking/sortevent/2015/05" xmlns:cns="http://www.purolator.com/ws/tracking/eventcommon/2015/05" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:tns="http://www.purolator.com/ws/tracking/sortevent/soap/2015/05" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" targetNamespace="http://www.purolator.com/ws/tracking/sortevent/2015/05" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:import schemaLocation="SortEvent.xsd2.xsd" namespace="http://www.purolator.com/ws/tracking/eventcommon/2015/05" />
  <xsd:element name="SortEventRequest">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="1" maxOccurs="1" name="SortEventHeader" type="cns:EventHeaderType">
          <xsd:annotation>
            <xsd:documentation>
									Event's identification information
								</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element minOccurs="1" maxOccurs="1" name="EventLocation" type="cns:EventLocationType">
          <xsd:annotation>
            <xsd:documentation>
									Information about the location where the event took place
								</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element minOccurs="0" maxOccurs="1" name="EventConfirmation" type="cns:EventConfirmationType">
          <xsd:annotation>
            <xsd:documentation>
									Event message confirmation status
								</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element minOccurs="0" maxOccurs="1" name="EventReferences" type="cns:EventReferencesType">
          <xsd:annotation>
            <xsd:documentation>
									External reference identifiers associated to this event.  For example an additional tracking id 
								</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
        <xsd:element minOccurs="0" maxOccurs="1" name="AdditionalEventInfos" type="cns:AdditionalEventInfosType">
          <xsd:annotation>
            <xsd:documentation>
									Additional info about the event. For example some description about the route or segment detail
								</xsd:documentation>
          </xsd:annotation>
        </xsd:element>
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="SortEventResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element name="Success" type="xsd:string" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
</xsd:schema>