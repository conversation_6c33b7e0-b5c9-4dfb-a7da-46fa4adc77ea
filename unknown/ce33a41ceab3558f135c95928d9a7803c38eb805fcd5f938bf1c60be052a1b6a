﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Business.Common.Exception;
using Purolator.SmartSort.Service.Contracts.Common;
using Purolator.SmartSort.Service.Common;
using Purolator.SmartSort.Service.DataContracts;
using System.Xml.Serialization;
using System.Runtime.Serialization;

namespace Purolator.SmartSort.Service.MessageContracts
{
    /// <summary>
    /// Represents the base class of all the response. 
    /// The class has a flag indicates if the call succeeds 
    /// and a list of all the errors.
    /// </summary>
    [DataContract]
    public class DeviceResponseBase
    {

        /// <summary>
        /// Initializes a new instance of ResponseResultBase. 
        /// </summary>
        public DeviceResponseBase()
        {           
        }
        
       //[XmlElement, MessageBodyMember(Name = "StatusCode", Order = ServiceDataMember.MemberStartNumber + 0)]
       [DataMember(Name = "StatusCode", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]
       public StatusCode StatusCode  { get; set; }       
       
       //[XmlElement, MessageBodyMember(Name = "PinErrors", Order = ServiceDataMember.MemberStartNumber + 1)]
       [DataMember(Name = "PinErrors", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 1)]
       public List<PinResponseError> Errors  { get; set; }
        
       //[XmlElement, MessageBodyMember(Name = "ErrorCode", Order = ServiceDataMember.MemberStartNumber + 2)]
       [DataMember(Name = "ErrorCode", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]
       public string ErrorCode  { get; set; }
       
       //[XmlElement, MessageBodyMember(Name = "ErrorMessage", Order = ServiceDataMember.MemberStartNumber + 3)]
       [DataMember(Name = "ErrorMessage", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 3)]
       public string ErrorMessage  { get; set; }


       public void InferStatusCode()
       {
           if (string.IsNullOrEmpty(ErrorCode))
           {
               if (Errors == null || Errors.Count == 0)
               {
                   StatusCode = StatusCode.Success;
               }
               else
               {
                   StatusCode = StatusCode.Partial;
               }
           }
           else
           {
               StatusCode = StatusCode.Error;
           }
       }
                
        
        public void AddPinError(PinResponseError error)
        {
            if (Errors == null) Errors = new List<PinResponseError>();
            Errors.Add(error);
        }

        private string GetLanguage()
        {
            if (ContextManager.Instance.Get() == null)
                return Constants.LANGUAGE_ENGLISH;

            if (ContextManager.Instance.Get().Language == Constants.LANGUAGE_FRENCH)
                return Constants.LANGUAGE_FRENCH;
            else
                return Constants.LANGUAGE_ENGLISH;
        }
    }
}
