﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceProcess;
using System.Text;
//
using Purolator.SmartSort.Business.Entities;
using Newtonsoft.Json;

namespace Purolator.SmartSort.Windows.Services.AddressValidationService
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        static void Main()
        {

            
             ServiceBase[] ServicesToRun;
             ServicesToRun = new ServiceBase[] 
             { 
                 new AddressValidationService() 
             };
             ServiceBase.Run(ServicesToRun);            

            
           //string ConnectionInfo = "Data Source= W-00821-D2C.cpggpc.ca\\SmartSort,14330;Initial Catalog= SSBE;User ID= smartsortapp;Password=***********;Pooling=true;Max Pool Size=100;Min Pool Size=0;Connection Lifetime=60";
           ////AddressValidationServiceImplementation.ResetDatabaseQueue(ConnectionInfo);


           //var items = AddressValidationServiceImplementation.GetWorkItems(10);
           //foreach (var item in items)
           //{
           //    AddressValidationServiceImplementation.Process(item);
           //}
           

            //PrePrintDetails p = new PrePrintDetails();
            //p.PrePrintStgId = 1;
            //p.Province = "ON";
            //string json = JsonConvert.SerializeObject(p);
            //Console.WriteLine(json);
            
        }
    }
}
