﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Purolator.SmartSort.Service.Contracts;
using Purolator.SmartSort.Service.MessageContracts;
using System.Data.SqlClient;
using System.Configuration;
using System.ServiceModel;
using System.ServiceModel.Web;
using System.ServiceModel.Activation;
//using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Service.Common;
using Entities = Purolator.SmartSort.Business.Entities;
using Translators = Purolator.SmartSort.Service.Translators;
using Purolator.SmartSort.Data.Access;
using DevTrends.WCFDataAnnotations;
using Purolator.SmartSort.Common;




namespace Purolator.SmartSort.Service.Implementation.ShipmentEvent
{
    [ServiceBehavior(Name = "ShipmentEventService", Namespace = ServiceNamespace.Service,
     InstanceContextMode = InstanceContextMode.PerSession, ConcurrencyMode = ConcurrencyMode.Single,
     AddressFilterMode = AddressFilterMode.Any)]
    [ValidateDataAnnotationsBehavior]
    public class ShipmentEventService : IShipmentEventService
    {             
        public ShipmentEventResponse LoadShipmentEvent(ShipmentEventRequest shipmentEventRequest)
        {
            ShipmentEventResponse response = new ShipmentEventResponse();
            var startTime = DateTime.Now;
            try
            {
                Entities.ShipmentEventStaging shipInfo = Translators.TranslateShipmentEvent.Translate(shipmentEventRequest);
                ShipmentEventRepository shipEvRepo = new ShipmentEventRepository(Constants.DB_CONNECTION_NAME);
                shipEvRepo.InsertShipmentEvent(shipInfo);
                response.isSuccess = true;
            }
            catch(Exception ex)
            {
                if (ex.Message.StartsWith("Violation of PRIMARY KEY"))
                {
                    WebOperationContext ctx = WebOperationContext.Current;
                    ctx.OutgoingResponse.StatusCode = System.Net.HttpStatusCode.BadRequest;
                }

                string pin = string.Empty;
                string guid = string.Empty;
                if (shipmentEventRequest != null && shipmentEventRequest.TrackingInfo != null)
                {
                    pin = shipmentEventRequest.TrackingInfo.PiecePIN;
                    guid = shipmentEventRequest.TrackingInfo.MessageGUID;
                }
                    Logger.Error("ShipmentEventService error for pin=" + pin + " msdguid=" + guid, LogCategories.SHIPMENT_INCOMING, ex);
                throw;
            }
            finally 
            {
                var endTime = DateTime.Now;
                TimeSpan span = endTime.Subtract(startTime);
                if (span.TotalMilliseconds > 10000)
                {
                    Logger.Warn("ShipmentEventService took: " + span.TotalMilliseconds / 1000 + " seconds", LogCategories.SHIPMENT_INCOMING);
                }
            }
            
            return response;

        }

    }
}

