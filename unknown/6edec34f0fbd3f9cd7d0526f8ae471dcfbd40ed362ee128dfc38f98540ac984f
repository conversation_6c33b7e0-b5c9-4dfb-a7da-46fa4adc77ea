﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="specFlow" type="TechTalk.SpecFlow.Configuration.ConfigurationSectionHandler, TechTalk.SpecFlow" />
    <section name="loggingConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.LoggingSettings, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="true" />
    <section name="dataConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings, Microsoft.Practices.EnterpriseLibrary.Data" />
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="TestLoadDeviation.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
	<loggingConfiguration name="" tracingEnabled="true" defaultCategory="General">
		<listeners>
			<add name="SSDefaultEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSDefault" formatter="EventLogTextFormatter" log="SSDefault" machineName="." traceOutputOptions="None" filter="Warning" />
			<add name="SSWebPortalEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSWebPortal" formatter="EventLogTextFormatter" log="SSWebPortal" filter="Warning" />
			<add name="SSIncomingWSEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSIncomingWS" formatter="EventLogTextFormatter" log="SSIncomingWS" filter="Warning" />
			<add name="SSEventServiceEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSEventService" formatter="EventLogTextFormatter" log="SSEventService" filter="Warning" />
			<add name="SSAVServiceEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSAVService" formatter="EventLogTextFormatter" log="SSAVService" filter="Error" />
			<add name="SSScanSyncWSEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSScanSyncWS" formatter="EventLogTextFormatter" log="SSScanSyncWS" filter="Warning" />
			<add name="CourierManifestEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSCourierManifestW" formatter="EventLogTextFormatter" log="SSCourierManifestWS" filter="Warning" />
			<add name="DefaultTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\defaultTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="WebPortalTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\webportalTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="ShipmentIncomingTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\shipmentIncomingTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="CourierManifestTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\courierManifestTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="ScanLogSyncTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\scanLogSyncTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="EnterpriseEventServiceTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\enterpriseEventTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="AddressValidationServiceTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\addressValidationTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollSizeKB="10240" maxArchivedFiles="20" />
		</listeners>
		<formatters>
			<add type="Microsoft.Practices.EnterpriseLibrary.Logging.Formatters.TextFormatter, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" template="Timestamp: {timestamp}{newline}&#xA;Message: {message}{newline}&#xA;Category: {category}{newline}&#xA;Priority: {priority}{newline}&#xA;EventId: {eventid}{newline}&#xA;Severity: {severity}{newline}&#xA;Title:{title}{newline}&#xA;Machine: {localMachine}{newline}&#xA;App Domain: {localAppDomain}{newline}&#xA;ProcessId: {localProcessId}{newline}&#xA;Process Name: {localProcessName}{newline}&#xA;Thread Name: {threadName}{newline}&#xA;Win32 ThreadId:{win32ThreadId}{newline}&#xA;Extended Properties: {dictionary({key} - {value}{newline})}" name="EventLogTextFormatter" />
			<add type="Microsoft.Practices.EnterpriseLibrary.Logging.Formatters.TextFormatter, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" template="Timestamp: {timestamp}{newline}&#xA;Message: {message}{newline}&#xA;Category: {category}{newline}&#xA;Priority: {priority}{newline}&#xA;EventId: {eventid}{newline}&#xA;Severity: {severity}{newline}&#xA;Title:{title}{newline}&#xA;Machine: {localMachine}{newline}&#xA;App Domain: {localAppDomain}{newline}&#xA;ProcessId: {localProcessId}{newline}&#xA;Process Name: {localProcessName}{newline}&#xA;Thread Name: {threadName}{newline}&#xA;Win32 ThreadId:{win32ThreadId}{newline}&#xA;Extended Properties: {dictionary({key} - {value}{newline})}" name="DebugFormatter" />
		</formatters>
		<categorySources>
			<add switchValue="All" name="General">
				<listeners>
					<add name="SSDefaultEventLog" />
					<add name="DefaultTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="WebPortal">
				<listeners>
					<add name="SSWebPortalEventLog" />
					<add name="WebPortalTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="ShipmentIncoming">
				<listeners>
					<add name="SSIncomingWSEventLog" />
					<add name="ShipmentIncomingTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="CourierManifest">
				<listeners>
					<add name="CourierManifestEventLog" />
					<add name="CourierManifestTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="ScanLogSync">
				<listeners>
					<add name="SSScanSyncWSEventLog" />
					<add name="ScanLogSyncTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="EnterpriseEvent">
				<listeners>
					<add name="SSEventServiceEventLog" />
					<add name="EnterpriseEventServiceTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="AddressValidation">
				<listeners>
					<add name="SSAVServiceEventLog" />
					<add name="AddressValidationServiceTrace" />
				</listeners>
			</add>
		</categorySources>
		<specialSources>
			<allEvents switchValue="All" name="All Events" />
			<notProcessed switchValue="All" name="Unprocessed Category" />
			<errors switchValue="All" name="Logging Errors &amp; Warnings">
				<listeners>
					<add name="SSDefaultEventLog" />
				</listeners>
			</errors>
		</specialSources>
	</loggingConfiguration>


	<system.diagnostics>
		<sources>
			<source name="System.ServiceModel" switchValue="Information, ActivityTracing" propagateActivity="true">
				<listeners>
					<add name="xml" />
				</listeners>
			</source>
			<source name="System.ServiceModel.MessageLogging">
				<listeners>
					<add name="xml" />
				</listeners>
			</source>
			<source name="myUserTraceSource" switchValue="Information, ActivityTracing">
				<listeners>
					<add name="xml" />
				</listeners>
			</source>
		</sources>
		<sharedListeners>
			<add name="xml" type="System.Diagnostics.XmlWriterTraceListener" initializeData="C:\AppLogs\SmartSort\Traces.svclog" />
		</sharedListeners>
	</system.diagnostics>
	
	<system.serviceModel>		
			<diagnostics wmiProviderEnabled="true">
				<messageLogging logEntireMessage="true" logMalformedMessages="true" logMessagesAtServiceLevel="true" logMessagesAtTransportLevel="true" maxMessagesToLog="3000" />
			</diagnostics>
		<client>
			<endpoint address="https://nonprod-soa-gw-01.cpggpc.ca/staging2/SortEvent" behaviorConfiguration="clientInspectorsAdded" binding="wsHttpBinding" bindingConfiguration="DataPowerBinding" contract="SortEventProxy.SortEventPortType" name="DP_SortEvent" />
			<endpoint address="https://nonprod-soa-gw-01.cpggpc.ca/staging2/UpdateAddressEvent" behaviorConfiguration="clientInspectorsAdded" binding="wsHttpBinding" bindingConfiguration="DataPowerBinding" contract="AddressEventProxy.UpdateAddressEventPortType" name="DP_AddressEvent" />
		</client>
    <behaviors>
      <endpointBehaviors>
        <behavior name="clientInspectorsAdded">
          <clientInterceptors />
        </behavior>
      </endpointBehaviors>
    </behaviors>
    <extensions>
      <behaviorExtensions>
        <add name="clientInterceptors" type="Purolator.SmartSort.Service.WCFMessageInspector.WCFClientLoggingExtensionElement, Purolator.SmartSort.Service.WCFMessageInspector, Version=*******, Culture=neutral, PublicKeyToken=null" />
      </behaviorExtensions>
    </extensions>
		<bindings>
			<wsHttpBinding>
				<binding name="DataPowerBinding" bypassProxyOnLocal="false" transactionFlow="false" openTimeout="00:00:30" closeTimeout="00:00:30" sendTimeout="00:00:30" receiveTimeout="00:00:30" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxReceivedMessageSize="65536" messageEncoding="Text" textEncoding="utf-8" useDefaultWebProxy="true" allowCookies="false">
					<reliableSession ordered="true" inactivityTimeout="00:10:00" enabled="false" />
					<security mode="TransportWithMessageCredential">
						<transport clientCredentialType="None" proxyCredentialType="None" realm="" />
						<message clientCredentialType="UserName" negotiateServiceCredential="false" algorithmSuite="Default" establishSecurityContext="false" />
					</security>
				</binding>
			</wsHttpBinding>

		</bindings>
	</system.serviceModel>
	<!--
	<system.net>
		<defaultProxy enabled = "true" useDefaultCredentials = "true">
			<proxy autoDetect="false" bypassonlocal="false" proxyaddress="http://127.0.0.1:9999" usesystemdefault="false" />
		</defaultProxy>
	</system.net>
-->
	<specFlow>
    <!-- For additional details on SpecFlow configuration options see http://go.specflow.org/doc-config -->
  <!-- For additional details on SpecFlow configuration options see http://go.specflow.org/doc-config --><!-- For additional details on SpecFlow configuration options see http://go.specflow.org/doc-config --><unitTestProvider name="NUnit" /><!-- For additional details on SpecFlow configuration options see http://go.specflow.org/doc-config --></specFlow>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" /></startup>
  
  
  <appSettings>
    <add key="FetchAmount" value="2" />
    <add key="ProcessPeriodInSeconds" value="1300" />
    <add key="UNCSavePath" value="\\Inno-5cg5250xlr\pdf" />
    <add key="UNCUser" value="dan.hristodorescu" />
    <add key="UNCDomain" value="innovapost" />
    <add key="UNCPassword" value="Turajicats51$" />

    <add key="DataPower_UserName" value="PURO_SMARTSORT_APP" />
    <add key="DataPower_Password" value="PURO_SMARTSORT-QA" />
  </appSettings>

  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.EnterpriseLibrary.Common" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <applicationSettings>
    <TestLoadDeviation.Properties.Settings>
      <setting name="TestLoadDeviation_TestSortEvent_SortEventService" serializeAs="String">
        <value>https://localhost:9090/SortEvent</value>
      </setting>
    </TestLoadDeviation.Properties.Settings>
  </applicationSettings>

  <dataConfiguration defaultDatabase="SSDB" />
  <connectionStrings>
    <add name="SSDB" connectionString="Data Source= CTSSDTAV301\CTSSDTAV301,1433;Initial Catalog= SSBE;User ID= smartsortapp;Password=!Spring2015;Pooling=true;Max Pool Size=100;Min Pool Size=0;Connection Lifetime=60" providerName="System.Data.SqlClient" />
  </connectionStrings>
  
</configuration>
