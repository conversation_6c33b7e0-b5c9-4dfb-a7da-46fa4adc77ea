﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Threading.Tasks;
//
using Entities = Purolator.SmartSort.Business.Entities;
using System.Configuration;


namespace Purolator.SmartSort.Windows.Services.Amazon
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        static void Main()
        {
           
            ServiceBase[] ServicesToRun;
            ServicesToRun = new ServiceBase[] 
            { 
                new AmazonSender() 
            };
            ServiceBase.Run(ServicesToRun);
            /*

           AppSettingsReader settingsReader = new AppSettingsReader();
           string connectionInfo = (string)settingsReader.GetValue(AmazonSender.DB_CONNECTION_PROPERTY, typeof(String));

           List<Entities.AmazonShipment> shipments = AmazonSenderImplementation.GetWorkItems(connectionInfo, 1);
           AmazonSender.APIKey = (string)settingsReader.GetValue(AmazonSender.APIKEY_PROPERTY, typeof(String));
           AmazonSenderImplementation.Process(connectionInfo, shipments[0]);
            */
        }
    }
}
