﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="Purolator.SmartSort.Windows.Services.Amazon" GeneratedClassName="AmazonSenderSettings">
  <Profiles />
  <Settings>
    <Setting Name="FetchAmount" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">100</Value>
    </Setting>
    <Setting Name="ProcessPeriodInSeconds" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">30</Value>
    </Setting>
    <Setting Name="ConcurrentWorkers" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">2</Value>
    </Setting>
    <Setting Name="URL" Type="System.String" Scope="Application">
      <Value Profile="(Default)">https://qa.shiptrackapi.com</Value>
    </Setting>
  </Settings>
</SettingsFile>