﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Runtime.Serialization;
using System.Text;
using Purolator.SmartSort.Service.Contracts.Common;
using Purolator.SmartSort.Service.DataContracts;
using Purolator.SmartSort.Service.Common;
using System.Xml.Serialization;


namespace Purolator.SmartSort.Service.MessageContracts
{
    [DataContract(Name = "CourierManifestResponse", Namespace = ServiceNamespace.DataType, IsReference = false)]  
    public class CourierManifestResponse
    {        
      // [XmlElement, MessageBodyMember(Name = "IsSuccess", Order = ServiceDataMember.MemberStartNumber + 0)]
       [DataMember(Name = "IsSuccess", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]
       public bool IsSuccess  { get; set; }
        
       //[XmlElement, MessageBodyMember(Name = "ErrorCode", Order = ServiceDataMember.MemberStartNumber + 1)]
       [DataMember(Name = "ErrorCode", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]
       public string ErrorCode  { get; set; }
       
       //[XmlElement, MessageBodyMember(Name = "ErrorMessage", Order = ServiceDataMember.MemberStartNumber + 2)]
       [DataMember(Name = "ErrorMessage", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 2)]
       public string ErrorMessage  { get; set; }
       
       //[XmlElement, MessageBodyMember(Name = "TerminalID", Order = ServiceDataMember.MemberStartNumber + 3)]
       [DataMember(Name = "TerminalID", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 3)]
       public string TerminalID  { get; set; }

       //[XmlElement, MessageBodyMember(Name = "Route", Order = ServiceDataMember.MemberStartNumber + 4)]
       [DataMember(Name = "Route", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 4)]
       public string Route  { get; set; }
                
       //[XmlElement, MessageBodyMember(Name = "VehicleStops", Order = ServiceDataMember.MemberStartNumber + 5)]
       [DataMember(Name = "VehicleStops", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 5)]
       public List<VehicleStop> VehicleStops  { get; set; }
    }
}
