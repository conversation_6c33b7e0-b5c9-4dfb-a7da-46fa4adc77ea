﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Service.MessageContracts;
using Purolator.SmartSort.Service.DataContracts;
using System.Numerics;

namespace Purolator.SmartSort.Service.Translators
{
    public class TranslateSmartSortLookupAddressValidation
    {

        public static Entities.SmartSortLookupAddressValidation Translate(MessageContracts.SmartSortGetRouteShelfRequest from, Purolator.SmartSort.ChannelIntegration.Clients.AWS.ValidateAddressDetailsResult avd)
        //public static Entities.SmartSortLookupAddressValidation Translate(MessageContracts.SmartSortGetRouteShelfRequest from, Entities.SmartSortLookupAddressValidation sslAV)
        {
        
            Entities.SmartSortLookupAddressValidation to = new Entities.SmartSortLookupAddressValidation();
            to.TerminalID = from.TerminalID.ToString();
            to.PIN = from.PIN;
            to.DeviceID = from.DeviceID;
            to.CustomerName = from.CustomerName;
            to.UnitSuiteNumber = from.UnitNumber;
            to.StreetNumber = from.StreetNumber;

            to.AddressLine1 = from.AddressLine1;
            to.AddressLine2 = from.AddressLine2;
            to.City = from.MunicipalityName;
            to.Province = from.ProvinceCode;
            to.PostalCode = from.PostalCode;
            to.PremiumService =  "" + (int)from.PremiumService;
            to.ShipmentType = from.ShipmentType;
            //to.DeliveryType = from.DeliveryType;
            try
            {
                //to.DeliveryType = (DeliveryType)int.Parse(from.DeliveryType);
                to.DeliveryType = from.DeliveryType;
            }
            catch (Exception e)
            {
               // throw new Exception("Delivery Type is not valid");

               // p.PackageType = PackageType.Unclassified;
                to.DeliveryType = DeliveryType.Regular;
                //to.ErrorMessage = "Delivery Type is not valid";
            }

            to.DiversionCode = from.DiversionCode;
            to.HandlingClassType = from.HandlingClassType;
            
            to.PurolatorAVStatus = avd.AWSStatusCode.ToString();

            if (avd.MatchingSuggestedAddress.AddressType != ChannelIntegration.Clients.AWS.SuggestedAddress.AddressTypes.Street
                && avd.MatchingSuggestedAddress.AddressType != ChannelIntegration.Clients.AWS.SuggestedAddress.AddressTypes.Unknown)
            {
                to.PurolatorAVInfo = "SRR"; //Non Civic Street
            }
            to.CorrectCity = avd.MatchingSuggestedAddress.City;
            to.CorrectPostalCode = avd.MatchingSuggestedAddress.PostalCode;
            to.CorrectStreetDir = avd.MatchingSuggestedAddress.StreetDirection;
            to.CorrectStreetNum = avd.MatchingSuggestedAddress.StreetNumber;
            to.CorrectStreetType = avd.MatchingSuggestedAddress.StreetType;
            to.CorrectStreetName = avd.MatchingSuggestedAddress.StreetName;
            to.CorrectUnit = avd.MatchingSuggestedAddress.Suite;
            to.Province = avd.MatchingSuggestedAddress.ProvinceCode; // fix incident 2822972, missing province code in pinmaster 
            to.CorrectProvince = avd.MatchingSuggestedAddress.ProvinceCode;
            /*to.CorrectCity = sslAV.CorrectCity;
            to.CorrectPostalCode = sslAV.CorrectProvince;
            to.CorrectStreetDir = sslAV.CorrectStreetName;
            to.CorrectStreetNum = sslAV.CorrectStreetNumSuffix;
            to.CorrectStreetType = sslAV.CorrectUnit;*/
            

           
            return to;
        }
    }
}
