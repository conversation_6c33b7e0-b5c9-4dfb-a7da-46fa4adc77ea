﻿using System;
using System.Collections.Generic;
using System.ServiceModel;
using System.Runtime.Serialization;
using Purolator.SmartSort.Service.Contracts;
using Purolator.SmartSort.Service.DataContracts;
using Purolator.SmartSort.Service.Common;

namespace Purolator.SmartSort.Service.MessageContracts
{
    //[MessageContract(IsWrapped=false)]
    [DataContract(Name = "ShipmentEventRequest", Namespace = ServiceNamespace.DataType, IsReference = false)]  
    public class ShipmentEventRequest
    {
        //[MessageBodyMember(Name = "Tracking", Order = ServiceDataMember.MemberStartNumber + 0)]
        //[DataMember(IsRequired = true)]
        [DataMember(Name = "Tracking", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]
        public TrackingInfo TrackingInfo  { get; set; }
        
        //[MessageBodyMember(Name = "Product", Order = ServiceDataMember.MemberStartNumber + 1)]
        //[DataMember(IsRequired = true)]
        [DataMember(Name = "Product", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 1)]
        public ProductInfo ProductInfo  { get; set; }

        //[MessageBodyMember(Name = "Services", Order = ServiceDataMember.MemberStartNumber + 2)]
        //[DataMember(IsRequired = true)]
        [DataMember(Name = "Services", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 2)]
        public List<ServiceInfo> ServiceInfo  { get; set; }
        
        //[MessageBodyMember(Name = "LocationInfo", Order = ServiceDataMember.MemberStartNumber + 3)]
        //[DataMember(IsRequired = true)]
        [DataMember(Name = "LocationInfo", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 3)]
        public Locations LocationInfo  { get; set; }
        
        //[MessageBodyMember(Name = "DeclaredReceiverParty", Order = ServiceDataMember.MemberStartNumber + 4)]
        //[DataMember(IsRequired = true)]
        [DataMember(Name = "DeclaredReceiverParty", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 4)]
        public DeclaredReceiverParty DeclaredReceiverParty  { get; set; }

        //[MessageBodyMember(Name = "CurrentReceiverParty", Order = ServiceDataMember.MemberStartNumber + 5)]
        //[DataMember(IsRequired = true)]
        [DataMember(Name = "CurrentReceiverParty", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 5)]
        public CurrentReceiverParty CurrentReceiverPart  { get; set; }        
    }
}
