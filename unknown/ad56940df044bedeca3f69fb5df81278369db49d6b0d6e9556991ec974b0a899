﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.ServiceProcess;
using System.Configuration;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Common;

namespace Purolator.SmartSort.Windows.Services.OFDSender
{

    delegate void SendMethod(string connectionInfo, Entities.OFDEvent item);


    public partial class OFDSender : ServiceBase
    {
        private static Timer timer;

        private static int ProcessPeriodInSeconds = OFDSenderSettings.Default.ProcessPeriodInSeconds;
        private static int ConcurrentWorkers = OFDSenderSettings.Default.ConcurrentWorkers;
        private static int FetchAmount = OFDSenderSettings.Default.FetchAmount;
        private static string ConnectionInfo = string.Empty;

        public static string DB_CONNECTION_PROPERTY = "SSDB" ;

        public OFDSender()
        {
            InitializeComponent();
        }

        protected override void OnStart(string[] args)
        {
            AppSettingsReader settingsReader = new AppSettingsReader();
            ConnectionInfo = (string)settingsReader.GetValue(OFDSender.DB_CONNECTION_PROPERTY, typeof(String));        
            OFDSenderImplementation.ResetDatabaseQueue(ConnectionInfo);
            TimerCallback tmrCallBack = new TimerCallback(Timer_TimerCallback);
            timer = new Timer(tmrCallBack);            
            timer.Change(new TimeSpan(0, 0, 1), new TimeSpan(0, 0, ProcessPeriodInSeconds));
        }

        protected override void OnStop()
        {
        }


        private void Timer_TimerCallback(object state)
        {
            Logger.Debug("Timer wakes up!", LogCategories.ENTERPRISE_EVENT);
            //Manually stop the timer...           
            timer.Change(Timeout.Infinite, Timeout.Infinite);     
            
            // reload the config
            OFDSenderSettings.Default.Reload();
            ProcessPeriodInSeconds = OFDSenderSettings.Default.ProcessPeriodInSeconds;
            ConcurrentWorkers = OFDSenderSettings.Default.ConcurrentWorkers;
            FetchAmount = OFDSenderSettings.Default.FetchAmount;
            AppSettingsReader settingsReader = new AppSettingsReader();
            ConnectionInfo = (string)settingsReader.GetValue(OFDSender.DB_CONNECTION_PROPERTY, typeof(String));             
            
            DoWork();
         
            // set back the timer
            Logger.Debug("Timer goes to sleep again!", LogCategories.ENTERPRISE_EVENT);
            timer.Change(new TimeSpan(0, 0, ProcessPeriodInSeconds), new TimeSpan(0, 0, ProcessPeriodInSeconds));                      
        }
       

        private void DoWork()
        {
            try
            {
                SendMethod workerMethod = OFDSenderImplementation.Process;
                List<Entities.OFDEvent> items = OFDSenderImplementation.GetWorkItems(ConnectionInfo, FetchAmount);
                Logger.Debug("QUEUE got " + items.Count + "records", LogCategories.ENTERPRISE_EVENT);

                while (items.Count > 0)
                {
                    var workerBlock = new ActionBlock<Entities.OFDEvent>(
                        actionEvent => workerMethod(ConnectionInfo, actionEvent),
                        // Specify a maximum degree of parallelism. 
                        new ExecutionDataflowBlockOptions
                        {
                            MaxDegreeOfParallelism = ConcurrentWorkers
                        });

                    foreach (var item in items)
                    {
                        workerBlock.Post(item);
                    }

                    workerBlock.Complete();
                    workerBlock.Completion.Wait();

                    items = OFDSenderImplementation.GetWorkItems(ConnectionInfo, FetchAmount);
                    Logger.Debug("QUEUE got " + items.Count + "records", LogCategories.ENTERPRISE_EVENT);
                }
            }
           catch (Exception ex)
            {
                Logger.Error("Error in Enterprise Event worker", LogCategories.ENTERPRISE_EVENT, ex);            
            }
        }
    }
}
