﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.Serialization;
using Purolator.SmartSort.Service.Common;
using Purolator.SmartSort.Service.DataContracts;

namespace Purolator.SmartSort.Service.MessageContracts
{
    [DataContract(Name = "IsServiceUpResponse", Namespace = ServiceNamespace.DataType, IsReference = false)]  
    public class IsServiceUpResponse
    {
        [DataMember(Name = "IsServiceUp", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]
        public bool IsServiceUp { get; set; }
    }
}
