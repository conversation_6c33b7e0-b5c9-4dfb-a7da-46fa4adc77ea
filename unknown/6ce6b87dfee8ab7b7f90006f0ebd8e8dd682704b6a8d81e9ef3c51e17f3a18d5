﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Entities = Purolator.SmartSort.Business.Entities;


namespace Purolator.SmartSort.Windows.Services.Amazon
{
    class Translator
    {
        public static Shipment Translate(Entities.AmazonShipment from)
        {
            Shipment to = new Shipment();
            
            to.BillToAccountNumber  = "AMAZON";
            to.Pickup_Company  = from.Pickup_Company;
            to.Pickup_Address1  = from.Pickup_Address1;
            to.Pickup_Address2  = from.Pickup_Address2;
            to.Pickup_Address3  = from.Pickup_Address3;
            to.Pickup_City  = from.Pickup_City;
            to.Pickup_ProvState  = from.Pickup_ProvState;
            to.Pickup_PostalZip  = from.Pickup_PostalZip;
            to.Pickup_Country  = from.Pickup_Country;
            to.Delivery_Company  = from.Delivery_Company;
            to.Delivery_Address1  = from.Delivery_Address1;
            to.Delivery_Address2  = from.Delivery_Address2;
            to.Delivery_Address3  = from.Delivery_Address3;
            to.Delivery_City  = from.Delivery_City;
            to.Delivery_ProvState  = from.Delivery_ProvState;
            to.Delivery_PostalZip  = from.Delivery_PostalZip;
            to.Delivery_Country  = from.Delivery_Country;
            to.Delivery_Phone  = from.Delivery_Phone;
            to.Delivery_Email  = from.Delivery_Email;
            to.ShipperReference  = from.ShipperReference;
            to.CarrierReferenceNumber  = from.CarrierReferenceNumber;
            to.Notes  = from.Notes;
            to.ServiceCode  = from.ServiceCode;
            to.RouteCode = from.RouteCode;

            ShipmentItem item = new ShipmentItem();
            item.Barcode = from.Barcode;
            item.Description = from.Description;
            item.Piece = from.Piece;
            item.UnitOfMeasure = from.UnitOfMeasure;
            item.Weight = from.Weight;

            List<ShipmentItem> lItems = new List<ShipmentItem>();
            lItems.Add(item);

            to.Items = lItems.ToArray();

            return to;
        }
    }
}
