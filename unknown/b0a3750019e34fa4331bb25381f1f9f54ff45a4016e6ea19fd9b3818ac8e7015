﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Business.Common.Exception;
using Purolator.SmartSort.Service.Contracts.Common;
using Purolator.SmartSort.Service.Common;
using Purolator.SmartSort.Service.DataContracts;

namespace Purolator.SmartSort.Service.MessageContracts
{
    
    [Serializable]
    public class SmartSortGetRouteShelfResponse
    {
        private string terminalID;
        [MessageBodyMember(Name = "TerminalID", Order = ServiceDataMember.MemberStartNumber + 0)]
        public String TerminalID
        {
            get { return terminalID; }
            set { terminalID = value; }
        }

        private string pin;
        [MessageBodyMember(Name = "PIN", Order = ServiceDataMember.MemberStartNumber + 1)]
        public String PIN
        {
            get { return pin; }
            set { pin = value; }
        }

        private String statusCode;

        /// <summary>
        /// Indicates if the call succeeded. The default value is false.
        /// </summary>
        [MessageBodyMember(Name = "StatusCode", Order = ServiceDataMember.MemberStartNumber + 2)]
        public String StatusCode
        {
            get { return statusCode; }
            set { statusCode = value; }
        }
       


        private List<ResponseError> errorList;
        /// <summary>
        /// Get the ResponseError list.
        /// </summary>
        [MessageBodyMember(Name = "Errors", Order = ServiceDataMember.MemberStartNumber + 3)]
        public List<ResponseError> Errors
        {
            get { return errorList; }
            set { errorList = value; }
        }

        private string primarySort;
        [MessageBodyMember(Name = "PrimarySort", Order = ServiceDataMember.MemberStartNumber + 4)]
        public String PrimarySort
        {
            get { return primarySort; }
            set { primarySort = value; }
        }

        private string conveyerSideIdentifier;
        [MessageBodyMember(Name = "ConveyerSideIdentifier", Order = ServiceDataMember.MemberStartNumber + 5)]
        public String ConveyerSideIdentifier
        {
            get { return conveyerSideIdentifier; }
            set { conveyerSideIdentifier = value; }
        }

        private string routeNumber;
        [MessageBodyMember(Name = "RouteNumber", Order = ServiceDataMember.MemberStartNumber + 6)]
        public String RouteNumber
        {
            get { return routeNumber; }
            set { routeNumber = value; }
        }

        private string shelfNumber;
        [MessageBodyMember(Name = "ShelfNumber", Order = ServiceDataMember.MemberStartNumber + 7)]
        public String ShelfNumber
        {
            get { return shelfNumber; }
            set { shelfNumber = value; }

        }

        private string truckShelfOverride;
        [MessageBodyMember(Name = "TruckShelfOverride", Order = ServiceDataMember.MemberStartNumber + 8)]
        public String TruckShelfOverride
        {
            get { return truckShelfOverride; }
            set { truckShelfOverride = value; }
        }

        private string deliverySequenceID;
        [MessageBodyMember(Name = "DeliverySequenceID", Order = ServiceDataMember.MemberStartNumber + 9)]
        public String DeliverySequenceID
        {
            get { return deliverySequenceID; }
            set { deliverySequenceID = value; }
        }



        /// <summary>
        /// Initializes a new instance of ResponseResultBase. 
        /// </summary>
        public SmartSortGetRouteShelfResponse()
        {
         
        }


        public void AddEventError(ResponseError error)
        {
            errorList.Add(error);
        }

        private string GetLanguage()
        {
            if (ContextManager.Instance.Get() == null)
                return Constants.LANGUAGE_ENGLISH;

            if (ContextManager.Instance.Get().Language == Constants.LANGUAGE_FRENCH)
                return Constants.LANGUAGE_FRENCH;
            else
                return Constants.LANGUAGE_ENGLISH;
        }

    }
}




