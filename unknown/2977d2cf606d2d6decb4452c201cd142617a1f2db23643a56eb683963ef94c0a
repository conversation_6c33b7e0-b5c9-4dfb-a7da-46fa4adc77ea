﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Configuration;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Common;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Serializer;


namespace Purolator.SmartSort.Windows.Services.Amazon
{
    class AmazonSenderImplementation
    {      

        public static void ResetDatabaseQueue(string connectionInfo)
        {            
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionInfo))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("dbo.AmazonResetData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("server_name", Environment.MachineName));

                        int result = cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error resetting Amazon queue", LogCategories.ENTERPRISE_EVENT, ex);                            
            }           
        }


        public static List<Entities.AmazonShipment> GetWorkItems(string connectionInfo, int fetchAmount)
        {
            List<Entities.AmazonShipment> result = new List<Entities.AmazonShipment>();
            
            using (SqlConnection conn = new SqlConnection(connectionInfo))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand("dbo.AmazonGetData", conn))
                {    
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Add(new SqlParameter("fetchNo", fetchAmount));
                    cmd.Parameters.Add(new SqlParameter("server_name", Environment.MachineName));

                    using (var reader = cmd.ExecuteReader())
                    {

                        while (reader.Read())
                        {
                            object dataObj = reader["data"];

                            if (dataObj != null)
                            {
                                Entities.AmazonShipment ev = new Entities.AmazonShipment();
                                try
                                {
                                    ev = Serializer.Serializer.Deserialize<Entities.AmazonShipment>(dataObj.ToString());
                                    ev.FWODocumentNo = reader["FWODocumentNo"].ToString();
                                    result.Add(ev);
                                }
                                catch (Exception ex)
                                {
                                    Logger.Error("Error loading Amazon shipment XML:" + dataObj.ToString(), LogCategories.ENTERPRISE_EVENT, ex);
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }

        private static void UpdateEvent(string connectionInfo, string eventId, char status, string errorCode)
        {
            try
            {                
                using (SqlConnection conn = new SqlConnection(connectionInfo))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("dbo.AmazonUpdateItem", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("FWODocumentNo", eventId));
                        cmd.Parameters.Add(new SqlParameter("Status", status));
                        cmd.Parameters.Add(new SqlParameter("Error_Reason_Cd", errorCode));

                        int result = cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error updating Amazon shipment with FWODocumentNo " + eventId, LogCategories.ENTERPRISE_EVENT, ex);
            }
        }


        public static void Process(string connectionInfo, Entities.AmazonShipment item)
        {
            bool success = false;
            string errorMessage = string.Empty;
            
            try
            {
                Logger.Debug("Processing Amazon shipment " + item.Barcode, LogCategories.ENTERPRISE_EVENT);
                
                Shipment shipment = Translator.Translate(item);

                AmazonClient client = new AmazonClient();
                client.SendShipment(AmazonSender.BaseURL, AmazonSender.APIKey, shipment);

                success = true;
            }
            catch (AmazonSendException ex)
            {
                Logger.Error("Error sending Amazon shipment" + item.Barcode + "HttpStatus=" + ex.HttpStatus + " Request=" + ex.Request + "\nResponse=" + ex.Response, LogCategories.ENTERPRISE_EVENT, ex);
                errorMessage = ex.HttpStatus + ": " + ex.Response + " - " + (ex.InnerException != null ? ex.InnerException.Message : string.Empty);
            }
            finally
            {                
                try
                {
                    char status =  success  ? 'S' : 'E';
                    Logger.Debug("Finished sending Amazon Amazon" + item.Barcode + " status=" + status, LogCategories.ENTERPRISE_EVENT);
                    if (errorMessage.Length > 255)
                    {
                        errorMessage = errorMessage.Substring(0, 254);
                    }
                    UpdateEvent(connectionInfo, item.FWODocumentNo, status, errorMessage);
                }
                catch (Exception ex)
                {
                    Logger.Error("Error updating error status onAmazon shipment:" + item.Barcode, LogCategories.ENTERPRISE_EVENT, ex);
                }             
            }

        }
    }
}
