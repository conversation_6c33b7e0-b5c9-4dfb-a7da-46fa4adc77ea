﻿using Purolator.SmartSort.Service.Common;
using Purolator.SmartSort.Service.DataContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;
using System.Xml.Serialization;
using System.ComponentModel.DataAnnotations;

namespace Purolator.SmartSort.Service.MessageContracts
{
    [DataContract(Name = "UnloadFromTruckRequest", Namespace = ServiceNamespace.DataType, IsReference = false)] 
    public class UnloadFromTruckRequest: Request
    {                
        [DataMember(Name = "DeviceID", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]
        //[StringLength(64, MinimumLength = 1)]
        public string DeviceID  { get; set; }
             
        [DataMember(Name = "TerminalID", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 1)]
        //[StringLength(3, MinimumLength = 1)]
        public string TerminalID  { get; set; }
                     
        [DataMember(Name = "PinEvents", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]
        public List<PinEvent> PinEvents  { get; set; }
    }
}
