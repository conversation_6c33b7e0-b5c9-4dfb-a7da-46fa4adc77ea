﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="Purolator.SmartSort.Windows.Services.AddressValidationService" GeneratedClassName="AddressValidationSettings">
  <Profiles />
  <Settings>
    <Setting Name="ProcessPeriodInSeconds" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">60</Value>
    </Setting>
    <Setting Name="ConcurrentWorkers" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">10</Value>
    </Setting>
    <Setting Name="FetchAmount" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">100</Value>
    </Setting>
    <Setting Name="SQSUrl" Type="System.String" Scope="Application">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="AWSKey" Type="System.String" Scope="Application">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="AWSPwd" Type="System.String" Scope="Application">
      <Value Profile="(Default)" />
    </Setting>
  </Settings>
</SettingsFile>