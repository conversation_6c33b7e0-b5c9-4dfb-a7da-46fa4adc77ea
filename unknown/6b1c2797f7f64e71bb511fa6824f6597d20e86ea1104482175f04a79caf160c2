﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using Newtonsoft.Json;
using Purolator.SmartSort.Common;



namespace Purolator.SmartSort.Windows.Services.Amazon
{
    class AmazonClient
    {

        private const string JSON = "application/json";

        private static readonly HttpClient httpClient = new HttpClient();
       
       

        // not needed, we'll use token
        public async Task<string> GetApiKeyAsync(string baseURL, string username, string password)
        {
            string url = baseURL + "/api/rest/Accounts/getSystemAccessAPIKey?username=" + username + "&password=" + password;
            HttpResponseMessage response = await httpClient.GetAsync(url);
            string content = await response.Content.ReadAsStringAsync();
            return content;
        }



        public void SendShipment(string url, string key, Shipment shipment) 
        {
            string requestStr = string.Empty;
            string responseStr = string.Empty;
            int httpStatus = 0;

            try
            {
                List<Shipment> shipments = new List<Shipment>
                {
                    shipment
                };
                AmazonRequest request = new AmazonRequest
                {
                    apikey = key,
                    shipments = shipments.ToArray()
                };
                requestStr = JsonConvert.SerializeObject(request);
                var content = new StringContent(requestStr, Encoding.UTF8, JSON);

                Logger.Debug("Amazon sending request: " + requestStr, LogCategories.ENTERPRISE_EVENT);

                HttpResponseMessage response = httpClient.PostAsync(url, content).Result;

                responseStr = response.Content.ReadAsStringAsync().Result;
                httpStatus = (int) response.StatusCode;

                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                AmazonSendException toThrow = new AmazonSendException("Error sending Amazon shipment " + shipment.Items[0].Barcode, ex)
                {
                    HttpStatus = httpStatus,
                    Request = requestStr,
                    Response = responseStr
                };
                throw toThrow; 
            }
        }
        
    }
}
