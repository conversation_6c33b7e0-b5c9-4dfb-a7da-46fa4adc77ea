<?xml version="1.0" encoding="utf-8"?>
<xsd:schema xmlns:tns="http://www.purolator.com/ws/tracking/eventcommon/2015/05" targetNamespace="http://www.purolator.com/ws/tracking/eventcommon/2015/05" version="1.0" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:simpleType name="EventCodeType">
    <xsd:restriction base="xsd:int">
      <xsd:pattern value="[0-9]{4}" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TrackingIdCodeType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="20" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TrackingIdType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="15" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="SourceSystemIdType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="20" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="SubProcessIdType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="20" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="SubSubProcessIdType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="20" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AddressValidationStatusType">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="OK" />
      <xsd:enumeration value="NotFound" />
      <xsd:enumeration value="ValidButNotUnique" />
      <xsd:enumeration value="OkButDifferentAddrId" />
      <xsd:enumeration value="RangeFoundButNoPocs" />
      <xsd:enumeration value="OkButPostalCodeChanged" />
      <xsd:enumeration value="Unparsable" />
      <xsd:enumeration value="OkButSuiteNotfound" />
      <xsd:enumeration value="OkButFuzzyStreetUsed" />
      <xsd:enumeration value="OkButFuzzyMunicipalityUsed" />
      <xsd:enumeration value="OkButStreetNotFound" />
      <xsd:enumeration value="StreetNotFound" />
      <xsd:enumeration value="MunicipalityNotFound" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ReasonForCaseType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="20" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="LocationTypeType">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="TERMINAL" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="LocationIdType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="20" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="LocationSubIdType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="20" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DeviceModeType">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="ScanAndSlap" />
      <xsd:enumeration value="Remediate" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="SourceUserIdType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="17" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="LDUType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="3" />
      <xsd:maxLength value="3" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="FSAType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="3" />
      <xsd:maxLength value="3" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CompanyOrPersonNameType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="40" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AttentionToNameType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="40" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DepartmentNameType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="40" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AddressLineType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="40" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="FloorNbrType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="10" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="SuiteType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="10" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="StreetNbrType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="10" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="StreetSuffixType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="1" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="StreetNameType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="30" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="StreetDirectionType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="2" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="StreetTypeCodeType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="6" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="PostCodeType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="10" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="MunicipalityType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="30" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TelephoneNbrType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="12" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="TelephoneExtType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="5" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="FaxNbrType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="12" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="CountryCodeType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="2" />
      <xsd:maxLength value="2" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="RegionCodeType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="2" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ConfirmationTypeType">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="ROUTE_CHART" />
      <xsd:enumeration value="LOAD_CHART" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ConfirmationIdType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="50" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ConfirmationSubIdType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="50" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ReferenceIdTypeType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="20" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ReferenceIdType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="50" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AdditionalInfoTypeType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="32" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="AdditionalInfoValueType">
    <xsd:restriction base="xsd:string">
      <xsd:minLength value="1" />
      <xsd:maxLength value="200" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="DateTimeWIthTimezoneType">
    <xsd:restriction base="xsd:dateTime">
      <xsd:pattern value="\d{4}-\d\d-\d\dT\d\d:\d\d:(\d\d|\d\d.\d+)(Z|(\+|-)\d\d:\d\d)" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="EventConfirmationType">
    <xsd:annotation>
      <xsd:documentation>
				Event message confirmation status
			</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element minOccurs="1" maxOccurs="1" name="ConfirmationType" type="tns:ConfirmationTypeType">
        <xsd:annotation>
          <xsd:documentation>
						Object type used to confirm event
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="ConfirmationId" type="tns:ConfirmationIdType">
        <xsd:annotation>
          <xsd:documentation>
						Identifier for confirmation object type. Ex: for
						Smart Sort can version of load chart
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="ConfirmationSubId" type="tns:ConfirmationSubIdType">
        <xsd:annotation>
          <xsd:documentation>
						Most specific identifier of Confirmation.
						Possible values sent From Smart Sort can be
						Route Chart Plan Number
						Load Chart Plan Number
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="EventReferenceType">
    <xsd:annotation>
      <xsd:documentation>
				Further reference information about the event
			</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element minOccurs="1" maxOccurs="1" name="ReferenceIdType" type="tns:ReferenceIdTypeType">
        <xsd:annotation>
          <xsd:documentation>
						Tracking ID code, EX: ROUTE_ID
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="1" maxOccurs="1" name="ReferenceId" type="tns:ReferenceIdType">
        <xsd:annotation>
          <xsd:documentation>
						Tracking ID
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="EventHeaderType">
    <xsd:annotation>
      <xsd:documentation>
				Event's identification information
			</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element minOccurs="1" maxOccurs="1" name="EventCode" type="tns:EventCodeType">
        <xsd:annotation>
          <xsd:documentation>Unique Code to identify the specific event: Ex:
						4025: Remediation Required
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="1" maxOccurs="1" name="TrackingIdCode" type="tns:TrackingIdCodeType">
        <xsd:annotation>
          <xsd:documentation>
						The type of tracking id such as PIN
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="1" maxOccurs="1" name="TrackingId" type="tns:TrackingIdType">
        <xsd:annotation>
          <xsd:documentation>
						The unique tracking identifier
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="SourceSystemId" type="tns:SourceSystemIdType">
        <xsd:annotation>
          <xsd:documentation>
						Identifies source system that generated the
						event. SS for SmartSort
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="SubProcessId" type="tns:SubProcessIdType">
        <xsd:annotation>
          <xsd:documentation>
						Identifies the sub source system process that
						generated the event
						For example:
						SSID - scans received from Smart
						Sort
						SFID - events received from Sales Force
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="SubSubProcessId" type="tns:SubSubProcessIdType">
        <xsd:annotation>
          <xsd:documentation>
						3rd level identifier to specify the component of
						the source system generating the event.
						For example:
						Smart Sort
						Sales Force
						AutoTriage
						PDT
						TriagedQueue
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="1" maxOccurs="1" name="EventTimestamp" type="tns:DateTimeWIthTimezoneType">
        <xsd:annotation>
          <xsd:documentation>
						Local Timestamp of when event occurred
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="TransmissionTimestamp" type="tns:DateTimeWIthTimezoneType">
        <xsd:annotation>
          <xsd:documentation>
						Local Timestamp of when this event was
						transmitted
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="AddressValidationStatus" type="tns:AddressValidationStatusType">
        <xsd:annotation>
          <xsd:documentation>
						Result of address validation. Ex:
						StreetNotFound, unparseable, etc
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="ReasonForCase" type="tns:ReasonForCaseType">
        <xsd:annotation>
          <xsd:documentation>
						Reason why that case was created
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="EventLocationType">
    <xsd:annotation>
      <xsd:documentation>
				Information about the location where the event
				took place
			</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element minOccurs="1" maxOccurs="1" name="LocationType" type="tns:LocationTypeType">
        <xsd:annotation>
          <xsd:documentation>
						The type of location tied to LocationId. Ex.Terminal
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="LocationId" type="tns:LocationIdType">
        <xsd:annotation>
          <xsd:documentation>
						Unique identifier for a location.Ex. Terminal number
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="LocationSubId" type="tns:LocationSubIdType">
        <xsd:annotation>
          <xsd:documentation>
						Unique Sub Identifier. Ex. Device number
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="DeviceMode" type="tns:DeviceModeType">
        <xsd:annotation>
          <xsd:documentation>
						This is the mode of the device being used when
						event occurred
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="SourceUserId" type="tns:SourceUserIdType">
        <xsd:annotation>
          <xsd:documentation>
						The userID of the person who created the event
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="EventAddressType">
    <xsd:annotation>
      <xsd:documentation>
				Information about the shipment address
			</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="LDU" type="tns:LDUType">
        <xsd:annotation>
          <xsd:documentation>
						1st 3 characters of postal code
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="FSA" type="tns:FSAType">
        <xsd:annotation>
          <xsd:documentation>
						Last 3 characters of postal code
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="CompanyOrPersonName" type="tns:CompanyOrPersonNameType">
        <xsd:annotation>
          <xsd:documentation>
						Company or Person Name from Label
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="AttentionToName" type="tns:AttentionToNameType">
        <xsd:annotation>
          <xsd:documentation>
						Attention To from Label
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="DepartmentName" type="tns:DepartmentNameType">
        <xsd:annotation>
          <xsd:documentation>
						Department from Label
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="AddressLine1" type="tns:AddressLineType">
        <xsd:annotation>
          <xsd:documentation>
						Validated Address Line 1
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="AddressLine2" type="tns:AddressLineType">
        <xsd:annotation>
          <xsd:documentation>
						Validated Address Line 2
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="FloorNbr" type="tns:FloorNbrType">
        <xsd:annotation>
          <xsd:documentation>
						Floor Number
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="Suite" type="tns:SuiteType">
        <xsd:annotation>
          <xsd:documentation>
						Suite Number
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="StreetNbr" type="tns:StreetNbrType">
        <xsd:annotation>
          <xsd:documentation>
						Street Number
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="StreetSuffix" type="tns:StreetSuffixType">
        <xsd:annotation>
          <xsd:documentation>
						Street Suffix
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="StreetName" type="tns:StreetNameType">
        <xsd:annotation>
          <xsd:documentation>
						Street Name
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="StreetDirection" type="tns:StreetDirectionType">
        <xsd:annotation>
          <xsd:documentation>
						Street Direction
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="StreetTypeCode" type="tns:StreetTypeCodeType">
        <xsd:annotation>
          <xsd:documentation>
						Street Type
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="PostCode" type="tns:PostCodeType">
        <xsd:annotation>
          <xsd:documentation>
						Postal code
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="Municipality" type="tns:MunicipalityType">
        <xsd:annotation>
          <xsd:documentation>
						Town or city
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="TelephoneNbr" type="tns:TelephoneNbrType">
        <xsd:annotation>
          <xsd:documentation>
						Telephone number
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="TelephoneExt" type="tns:TelephoneExtType">
        <xsd:annotation>
          <xsd:documentation>
						Telephone Ext
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="FaxNbr" type="tns:FaxNbrType">
        <xsd:annotation>
          <xsd:documentation>
						Fax Number
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="CountryCode" type="tns:CountryCodeType">
        <xsd:annotation>
          <xsd:documentation>
						ISO 2 digit country code
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" maxOccurs="1" name="RegionCode" type="tns:RegionCodeType">
        <xsd:annotation>
          <xsd:documentation>
						Regional Code ( Province Code in Canada)
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AdditionalInfoType">
    <xsd:annotation>
      <xsd:documentation>
				Additional info about the event
			</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element minOccurs="1" maxOccurs="1" name="AdditionalInfoType" type="tns:AdditionalInfoTypeType">
        <xsd:annotation>
          <xsd:documentation>
						The type of informational data being passed.
						Example of value Z_ROUTE - route Number, Z_SEGMENT - Defines
						location, Z_SEQUENCE - Specific spot in segment
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="1" maxOccurs="1" name="AdditionalInfoValue" type="tns:AdditionalInfoValueType">
        <xsd:annotation>
          <xsd:documentation>
						Values related to the AdditionalInfoType
						For
						example for Z_ROUTE the route number will be entered
					</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="EventReferencesType">
    <xsd:sequence>
      <xsd:element minOccurs="1" maxOccurs="10" name="EventReference" type="tns:EventReferenceType" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AdditionalEventInfosType">
    <xsd:sequence>
      <xsd:element minOccurs="1" maxOccurs="10" name="AdditionalInfo" type="tns:AdditionalInfoType" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="EventResponseType">
    <xsd:sequence>
      <xsd:element name="Success" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
</xsd:schema>