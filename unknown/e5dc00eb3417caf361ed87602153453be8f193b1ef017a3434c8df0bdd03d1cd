﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Purolator.SmartSort.Windows.Services.EventSender {
    
    
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "15.9.0.0")]
    internal sealed partial class EventSenderSettings : global::System.Configuration.ApplicationSettingsBase {
        
        private static EventSenderSettings defaultInstance = ((EventSenderSettings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new EventSenderSettings())));
        
        public static EventSenderSettings Default {
            get {
                return defaultInstance;
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("100")]
        public int FetchAmount {
            get {
                return ((int)(this["FetchAmount"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("30")]
        public int ProcessPeriodInSeconds {
            get {
                return ((int)(this["ProcessPeriodInSeconds"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("2")]
        public int ConcurrentWorkers {
            get {
                return ((int)(this["ConcurrentWorkers"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("arn:aws:sns:us-east-1:853215655166:infohub-events-dev-ScanEventsIntake")]
        public string EVENTS_SNS_TOPIC {
            get {
                return ((string)(this["EVENTS_SNS_TOPIC"]));
            }
        }
    }
}
