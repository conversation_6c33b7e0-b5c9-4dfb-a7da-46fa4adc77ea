<?xml version="1.0"?>
<doc>
    <assembly>
        <name>AWSSDK.Core</name>
    </assembly>
    <members>
        <member name="T:Amazon.AWSConfigs">
            <summary>
            Configuration options that apply to the entire SDK.
            
            These settings can be configured through app.config or web.config.
            Below is a full sample configuration that illustrates all the possible options.
            <code>
            &lt;configSections&gt;
              &lt;section name="aws" type="Amazon.AWSSection, AWSSDK"/&gt;
            &lt;/configSections&gt;
            &lt;aws region="us-west-2"&gt;
              &lt;logging logTo="Log4Net, SystemDiagnostics" logResponses="Always" logMetrics="true" /&gt;
              &lt;s3 useSignatureVersion4="true" /&gt;
              &lt;proxy host="localhost" port="8888" username="1" password="1" /&gt;
              
              &lt;dynamoDB&gt;
                &lt;dynamoDBContext tableNamePrefix="Prod-"&gt;
            
                  &lt;tableAliases&gt;
                    &lt;alias fromTable="FakeTable" toTable="People" /&gt;
                    &lt;alias fromTable="Persons" toTable="People" /&gt;
                  &lt;/tableAliases&gt;
            
                  &lt;mappings&gt;
                    &lt;map type="Sample.Tests.Author, SampleDLL" targetTable="People" /&gt;
                    &lt;map type="Sample.Tests.Editor, SampleDLL" targetTable="People"&gt;
                      &lt;property name="FullName" attribute="Name" /&gt;
                      &lt;property name="EmployeeId" attribute="Id" /&gt;
                      &lt;property name="ComplexData" converter="Sample.Tests.ComplexDataConverter, SampleDLL" /&gt;
                      &lt;property name="Version" version="true" /&gt;
                      &lt;property name="Password" ignore="true" /&gt;
                    &lt;/map&gt;
                  &lt;/mappings&gt;
            
                &lt;/dynamoDBContext&gt;
              &lt;/dynamoDB&gt;
            &lt;/aws&gt;
            </code>
            </summary>
            <summary>
            Configuration options that apply to the entire SDK.
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.ManualClockCorrection">
            <summary>
            Manual offset to apply to client clock.  This is a global setting that overrides 
            ClockOffset value calculated for all service endpoints.
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.CorrectForClockSkew">
            <summary>
            Determines if the SDK should correct for client clock skew
            by determining the correct server time and reissuing the
            request with the correct time.
            Default value of this field is True.
            <seealso cref="P:Amazon.AWSConfigs.ClockOffset"/> will be updated with the calculated
            offset even if this field is set to false, though requests
            will not be corrected or retried.
            Ignored if <seealso cref="P:Amazon.AWSConfigs.ManualClockCorrection"/> is set.
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.ClockOffset">
            <summary>
            The calculated clock skew correction, if there is one.
            This field will be set if a service call resulted in an exception
            and the SDK has determined that there is a difference between local
            and server times.
            
            If <seealso cref="P:Amazon.AWSConfigs.CorrectForClockSkew"/> is set to true, this
            value will be set to the correction, but it will not be used by the
            SDK and clock skew errors will not be retried.
            </summary>
        </member>
        <member name="F:Amazon.AWSConfigs.AWSRegionKey">
            <summary>
            Key for the AWSRegion property.
            <seealso cref="P:Amazon.AWSConfigs.AWSRegion"/>
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.AWSRegion">
            <summary>
            Configures the default AWS region for clients which have not explicitly specified a region.
            Changes to this setting will only take effect for newly constructed instances of AWS clients.
            
            This setting can be configured through the App.config. For example:
            <code>
            &lt;configSections&gt;
              &lt;section name="aws" type="Amazon.AWSSection, AWSSDK"/&gt;
            &lt;/configSections&gt;
            &lt;aws region="us-west-2" /&gt;
            </code>
            </summary>
        </member>
        <member name="F:Amazon.AWSConfigs.AWSProfileNameKey">
            <summary>
            Key for the AWSProfileName property.
            <seealso cref="P:Amazon.AWSConfigs.AWSProfileName"/>
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.AWSProfileName">
            <summary>
            Profile name for stored AWS credentials that will be used to make service calls.
            Changes to this setting will only take effect in newly-constructed clients.
            <para>
            To reference the account from an application's App.config or Web.config use the AWSProfileName setting.
            <code>
            &lt;?xml version="1.0" encoding="utf-8" ?&gt;
            &lt;configuration&gt;
                &lt;appSettings&gt;
                    &lt;add key="AWSProfileName" value="development"/&gt;
                &lt;/appSettings&gt;
            &lt;/configuration&gt;
            </code>
            </para>
            </summary>
        </member>
        <member name="F:Amazon.AWSConfigs.AWSProfilesLocationKey">
            <summary>
            Key for the AWSProfilesLocation property.
            <seealso cref="P:Amazon.AWSConfigs.LogMetrics"/>
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.AWSProfilesLocation">
            <summary>
            Location of the credentials file shared with other AWS SDKs.
            By default, the credentials file is stored in the .aws directory in the current user's home directory.
            
            Changes to this setting will only take effect in newly-constructed clients.
            <para>
            To reference the profile from an application's App.config or Web.config use the AWSProfileName setting.
            <code>
            &lt;?xml version="1.0" encoding="utf-8" ?&gt;
            &lt;configuration&gt;
                &lt;appSettings&gt;
                    &lt;add key="AWSProfilesLocation" value="c:\config"/&gt;
                &lt;/appSettings&gt;
            &lt;/configuration&gt;
            </code>
            </para>
            </summary>
        </member>
        <member name="F:Amazon.AWSConfigs.LoggingKey">
            <summary>
            Key for the Logging property.
            <seealso cref="P:Amazon.AWSConfigs.Logging"/>
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.Logging">
            <summary>
            Configures how the SDK should log events, if at all.
            Changes to this setting will only take effect in newly-constructed clients.
            
            The setting can be configured through App.config, for example:
            <code>
            &lt;appSettings&gt;
              &lt;add key="AWSLogging" value="log4net"/&gt;
            &lt;/appSettings&gt;
            </code>
            </summary>
        </member>
        <member name="F:Amazon.AWSConfigs.ResponseLoggingKey">
            <summary>
            Key for the ResponseLogging property.
            
            <seealso cref="P:Amazon.AWSConfigs.ResponseLogging"/>
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.ResponseLogging">
            <summary>
            Configures when the SDK should log service responses.
            Changes to this setting will take effect immediately.
            
            The setting can be configured through App.config, for example:
            <code>
            &lt;appSettings&gt;
              &lt;add key="AWSResponseLogging" value="OnError"/&gt;
            &lt;/appSettings&gt;
            </code>
            </summary>
        </member>
        <member name="F:Amazon.AWSConfigs.LogMetricsKey">
            <summary>
            Key for the LogMetrics property.
            <seealso cref="P:Amazon.AWSConfigs.LogMetrics"/>
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.LogMetrics">
            <summary>
            Configures if the SDK should log performance metrics.
            This setting configures the default LogMetrics property for all clients/configs.
            Changes to this setting will only take effect in newly-constructed clients.
            
            The setting can be configured through App.config, for example:
            <code>
            &lt;appSettings&gt;
              &lt;add key="AWSLogMetrics" value="true"/&gt;
            &lt;/appSettings&gt;
            </code>
            </summary>
        </member>
        <member name="F:Amazon.AWSConfigs.EndpointDefinitionKey">
            <summary>
            Key for the EndpointDefinition property.
            <seealso cref="P:Amazon.AWSConfigs.EndpointDefinition"/>
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.EndpointDefinition">
            <summary>
            Configures if the SDK should use a custom configuration file that defines the regions and endpoints.
            <code>
            &lt;configSections&gt;
              &lt;section name="aws" type="Amazon.AWSSection, AWSSDK"/&gt;
            &lt;/configSections&gt;
            &lt;aws endpointDefinition="c:\config\endpoints.json" /&gt;
            </code>
            </summary>
        </member>
        <member name="F:Amazon.AWSConfigs.UseSdkCacheKey">
            <summary>
            Key for the UseSdkCache property.
            <seealso cref="P:Amazon.AWSConfigs.UseSdkCache"/>
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.UseSdkCache">
            <summary>
            Configures if the SDK Cache should be used, the default value is true.
            <code>
            &lt;configSections&gt;
              &lt;section name="aws" type="Amazon.AWSSection, AWSSDK"/&gt;
            &lt;/configSections&gt;
            &lt;aws useSdkCache="true" /&gt;
            </code>
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.LoggingConfig">
            <summary>
            Configuration for the Logging section of AWS configuration.
            Changes to some settings may not take effect until a new client is constructed.
            
            Example section:
            <code>
            &lt;configSections&gt;
              &lt;section name="aws" type="Amazon.AWSSection, AWSSDK"/&gt;
            &lt;/configSections&gt;
            &lt;aws&gt;
              &lt;logging logTo="Log4Net, SystemDiagnostics" logResponses="Always" logMetrics="true" /&gt;
            &lt;/aws&gt;
            </code>
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.ProxyConfig">
            <summary>
            Configuration for the Proxy section of AWS configuration.
            Changes to some settings may not take effect until a new client is constructed.
            
            Example section:
            <code>
            &lt;configSections&gt;
              &lt;section name="aws" type="Amazon.AWSSection, AWSSDK"/&gt;
            &lt;/configSections&gt;
            &lt;aws&gt;
              &lt;proxy host="localhost" port="8888" username="1" password="1" bypassList="addressexpr1;addressexpr2;..." bypassOnLocal="true" /&gt;
            &lt;/aws&gt;
            </code>
            </summary>
        </member>
        <member name="P:Amazon.AWSConfigs.RegionEndpoint">
            <summary>
            Configuration for the region endpoint section of AWS configuration.
            Changes may not take effect until a new client is constructed.
            
            Example section:
            <code>
            &lt;configSections&gt;
              &lt;section name="aws" type="Amazon.AWSSection, AWSSDK"/&gt;
            &lt;/configSections&gt;
            &lt;aws region="us-west-2" /&gt;
            </code>
            </summary>
        </member>
        <member name="F:Amazon.AWSConfigs.propertyChangedLock">
            <summary>
            Lock for SomeEvent delegate access.
            </summary>
        </member>
        <member name="M:Amazon.AWSConfigs.GetUtcNow">
            <summary>
            This method should never be called directly.
            Call AWSSDKUtils.CorrectedUtcNow instead.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.LoggingOptions">
            <summary>
            Logging options.
            Can be combined to enable multiple loggers.
            </summary>
        </member>
        <member name="F:Amazon.LoggingOptions.None">
            <summary>
            No logging
            </summary>
        </member>
        <member name="F:Amazon.LoggingOptions.Log4Net">
            <summary>
            Log using log4net
            </summary>
        </member>
        <member name="F:Amazon.LoggingOptions.SystemDiagnostics">
            <summary>
            Log using System.Diagnostics
            </summary>
        </member>
        <member name="F:Amazon.LoggingOptions.Console">
            <summary>
            Log to the console
            </summary>
        </member>
        <member name="F:Amazon.LoggingOptions.File">
            <summary>
            Log to a file
            </summary>
        </member>
        <member name="T:Amazon.ResponseLoggingOption">
            <summary>
            Response logging option.
            </summary>
        </member>
        <member name="F:Amazon.ResponseLoggingOption.Never">
            <summary>
            Never log service response
            </summary>
        </member>
        <member name="F:Amazon.ResponseLoggingOption.OnError">
            <summary>
            Only log service response when there's an error
            </summary>
        </member>
        <member name="F:Amazon.ResponseLoggingOption.Always">
            <summary>
            Always log service response
            </summary>
        </member>
        <member name="T:Amazon.LogMetricsFormatOption">
            <summary>
            Format for metrics data in the logs
            </summary>
        </member>
        <member name="F:Amazon.LogMetricsFormatOption.Standard">
            <summary>
            Emit metrics in human-readable format
            </summary>
        </member>
        <member name="F:Amazon.LogMetricsFormatOption.JSON">
            <summary>
            Emit metrics as JSON data
            </summary>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifier">
            <summary>
            An access control policy action identifies a specific action in a service
            that can be performed on a resource. For example, sending a message to a
            queue.
            <para>
            ActionIdentifiers allow you to limit what your access control policy statement affects.
            For example, you could create a policy statement that enables a certain group
            of users to send messages to your queue, but not allow them to perform any
            other actions on your queue.
            </para>
            <para>
            The action is B in the statement
            "A has permission to do B to C where D applies."
            </para>
            <para>Free form access control policy actions may include a wildcard (*) to match
            multiple actions.
            </para>
            <para>
            Constants for known actions can be found in the Amazon.Auth.AccessControlPolicy.ActionIdentifiers namespace.
            </para>
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ActionIdentifier.#ctor(System.String)">
            <summary>
            Constructs an Actionidentifer with the given action name.
            </summary>
            <param name="actionName">The name of the action</param>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.ActionIdentifier.ActionName">
            <summary>
            Gets and sets the name of this action. For example, 'sqs:SendMessage' is the
            name corresponding to the SQS action that enables users to send a message
            to an SQS queue.
            </summary>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.Condition">
            <summary>
            AWS access control policy conditions are contained in <see cref="T:Amazon.Auth.AccessControlPolicy.Statement"/>
            objects, and affect when a statement is applied. For example, a statement
            that allows access to an Amazon SQS queue could use a condition to only apply
            the effect of that statement for requests that are made before a certain
            date, or that originate from a range of IP addresses.
            <para>
            Multiple conditions can be included in a single statement, and all conditions
            must evaluate to true in order for the statement to take effect.
            </para>
            <para>
            The set of conditions is D in the statement
            "A has permission to do B to C where D applies."
            </para>
            <para>
            A condition is composed of three parts:
            <list type="definition">
                <item>
                    <term>Condition Key</term>
                    <description>The condition key declares which value of a
            request to pull in and compare against when a policy is evaluated by AWS. For
            example, using <see cref="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.SOURCE_IP_CONDITION_KEY"/> will cause
            AWS to pull in the current request's source IP as the first value to compare
            against every time your policy is evaluated.
                    </description>
                </item>
                <item>
                    <term>Comparison Type</term>
                    <description>This is a static value used as the second value
            in the comparison when your policy is evaluated. Depending on the comparison
            type, this value can optionally use wildcards. See the documentation for
            individual comparison types for more information.
                    </description>
                </item>
                <item>
                    <term>Comparison Value</term>
                    <description>This is a static value used as the second value
            in the comparison when your policy is evaluated. Depending on the comparison
            type, this value can optionally use wildcards. See the documentation for
            individual comparison types for more information.
                    </description>
                </item>
            </list>
            </para>
            </summary>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Condition.Type">
            <summary>
            Gets the type of this condition.
            </summary>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Condition.ConditionKey">
            <summary>
            Gets and Sets the name of the condition key involved in this condition.
            Condition keys are predefined values supported by AWS that provide input
            to a condition's evaluation, such as the current time, or the IP address
            of the incoming request.
            <para>
            Your policy is evaluated for each incoming request, and condition keys
            specify what information to pull out of those incoming requests and plug
            into the conditions in your policy.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Condition.Values">
            <summary>
            Gets and Sets the values specified for this access control policy condition.
            For example, in a condition that compares the incoming IP address of a
            request to a specified range of IP addresses, the range of IP addresses
            is the single value in the condition.
            <para>
            Most conditions accept only one value, but multiple values are possible.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ConditionFactory">
            <summary>
            A factory for creating conditions to be used in the policy.
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.CURRENT_TIME_CONDITION_KEY">
            <summary>
            Condition key for the current time.
            <para>
            This condition key should only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.DateComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.SECURE_TRANSPORT_CONDITION_KEY">
            <summary>
            Condition key for whether or not an incoming request is using a secure
            transport to make the request (i.e. HTTPS instead of HTTP).
            <para>
            This condition key should only be used with the boolean overload of NewCondition.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.SOURCE_IP_CONDITION_KEY">
            <summary>
            Condition key for the source IP from which a request originates.
            <para>
            This condition key should only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.IpAddressComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.USER_AGENT_CONDITION_KEY">
            <summary>
            Condition key for the user agent included in a request.
            <para>
            This condition key should only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType"/>
            enum.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.EPOCH_TIME_CONDITION_KEY">
            <summary>
            Condition key for the current time, in epoch seconds.
            <para>
            This condition key should only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.NumericComparisonType"/> enum.
            objects.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.REFERRER_CONDITION_KEY">
            <summary>
            Condition key for the referrer specified by a request.
            <para>
            This condition key should only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType"/>
            objects.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.SOURCE_ARN_CONDITION_KEY">
            <summary>
            Condition key for the Amazon Resource Name (ARN) of the source specified
            in a request. The source ARN indicates which resource is affecting the
            resource listed in your policy. For example, an SNS topic is the source
            ARN when publishing messages from the topic to an SQS queue.
            <para>
            This condition key should only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.ArnComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.ArnComparisonType">
            <summary>
            Enumeration of the supported ways an ARN comparison can be evaluated.
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.ArnComparisonType.ArnEquals">
            <summary>Exact matching</summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.ArnComparisonType.ArnLike">
            <summary>
            Loose case-insensitive matching of the ARN. Each of the six
            colon-delimited components of the ARN is checked separately and each
            can include a multi-character match wildcard (*) or a
            single-character match wildcard (?).
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.ArnComparisonType.ArnNotEquals">
            <summary>Negated form of ArnEquals</summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.ArnComparisonType.ArnNotLike">
            <summary>Negated form of ArnLike</summary>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.DateComparisonType">
            <summary>
            Enumeration of the supported ways a date comparison can be evaluated.
            </summary>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.IpAddressComparisonType">
            <summary>
            Enumeration of the supported ways an IP address comparison can be evaluated.
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.IpAddressComparisonType.IpAddress">
            <summary>
            Matches an IP address against a CIDR IP range, evaluating to true if
            the IP address being tested is in the condition's specified CIDR IP
            range.
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.IpAddressComparisonType.NotIpAddress">
            <summary>
            Negated form of IpAddress
            </summary>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.NumericComparisonType">
            <summary>
            Enumeration of the supported ways a numeric comparison can be evaluated
            </summary>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType">
            <summary>
            Enumeration of the supported ways a string comparison can be evaluated.
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType.StringEquals">
            <summary>
            Case-sensitive exact string matching
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType.StringEqualsIgnoreCase">
            <summary>
            Case-insensitive string matching
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType.StringLike">
            <summary>
            Loose case-insensitive matching. The values can include a
            multi-character match wildcard (*) or a single-character match
            wildcard (?) anywhere in the string.
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType.StringNotEquals">
            <summary>
            Negated form of StringEquals.
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType.StringNotEqualsIgnoreCase">
            <summary>
            Negated form of StringEqualsIgnorecase.
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType.StringNotLike">
            <summary>
            Negated form of StringLike.
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewCondition(Amazon.Auth.AccessControlPolicy.ConditionFactory.ArnComparisonType,System.String,System.String)">
            <summary>
            Constructs a new access control policy condition that compares ARNs (Amazon Resource Names).
            </summary>
            <param name="key"> The access policy condition key specifying where to get the first ARN for the comparison</param>
            <param name="type">The type of comparison to perform.</param>
            <param name="value">The second ARN to compare against. When using ArnLike or ArnNotLike this may contain the
                multi-character wildcard (*) or the single-character wildcard</param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewCondition(System.String,System.Boolean)">
            <summary>
            Constructs a new access policy condition that performs a boolean
            comparison.
            </summary>
            <param name="key">The access policy condition key specifying where to get the
                       first boolean value for the comparison (ex: aws:SecureTransport).</param>
            <param name="value">The boolean to compare against.</param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewCondition(Amazon.Auth.AccessControlPolicy.ConditionFactory.DateComparisonType,System.DateTime)">
            <summary>
            This method is deprecated. Invoking this method results in non-UTC DateTimes
            not being marshalled correctly. Use NewConditionUtc instead.
            Constructs a new access policy condition that compares the current time
            (on the AWS servers) to the specified date.
            </summary>
            <param name="type">The type of comparison to perform. For example,
                       DateComparisonType.DateLessThan will cause this policy
                       condition to evaluate to true if the current date is less than
                       the date specified in the second argument.</param>
            <param name="date">The date to compare against.</param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewConditionUtc(Amazon.Auth.AccessControlPolicy.ConditionFactory.DateComparisonType,System.DateTime)">
            <summary>
            Constructs a new access policy condition that compares the current time
            (on the AWS servers) to the specified date.
            </summary>
            <param name="type">The type of comparison to perform. For example,
                       DateComparisonType.DateLessThan will cause this policy
                       condition to evaluate to true if the current date is less than
                       the date specified in the second argument.</param>
            <param name="date">The date to compare against.</param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewIpAddressCondition(System.String)">
            <summary>
            Constructs a new access policy condition that compares the source IP
            address of the incoming request to an AWS service against the specified
            CIDR range. The condition evaluates to true (meaning the policy statement
            containing it will be applied) if the incoming source IP address is
            within that range.
            <para>
            To achieve the opposite effect (i.e. cause the condition to evaluate to
            true when the incoming source IP is <b>not</b> in the specified CIDR
            range) use the alternate constructor form and specify
            IpAddressComparisonType.NotIpAddress.
            </para>
            </summary>
            <param name="ipAddressRange">The CIDR IP range involved in the policy condition.</param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewCondition(Amazon.Auth.AccessControlPolicy.ConditionFactory.IpAddressComparisonType,System.String)">
            <summary>
            Constructs a new access policy condition that compares the source IP
            address of the incoming request to an AWS service against the specified
            CIDR range. When the condition evaluates to true (i.e. when the incoming
            source IP address is within the CIDR range or not) depends on the
            specified IpAddressComparisonType.
            </summary>
            <param name="type">The type of comparison to to perform.</param>
            <param name="ipAddressRange">The CIDR IP range involved in the policy condition.</param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewCondition(Amazon.Auth.AccessControlPolicy.ConditionFactory.NumericComparisonType,System.String,System.String)">
            <summary>
            Constructs a new access policy condition that compares two numbers.
            </summary>
            <param name="type">The type of comparison to perform.</param>
            <param name="key">The access policy condition key specifying where to get the
                      first number for the comparison.</param>
            <param name="value">The second number to compare against.</param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewCondition(Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType,System.String,System.String)">
            <summary>
            Constructs a new access control policy condition that compares two
            strings.
            </summary>
            <param name="type">The type of comparison to perform</param>
            <param name="key">The access policy condition key specifying where to get the
                       first string for the comparison (ex: aws:UserAgent). 
            </param>
            <param name="value">The second string to compare against. When using
                       StringComparisonType.StringLike or
                       StringComparisonType.StringNotLike this may contain
                       the multi-character wildcard (*) or the single-character
                       wildcard (?).
            </param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewSourceArnCondition(System.String)">
             <summary>
             Constructs a new access policy condition that compares the Amazon
             Resource Name (ARN) of the source of an AWS resource that is modifying
             another AWS resource with the specified pattern.
             <para>
             For example, the source ARN could be an Amazon SNS topic ARN that is
             sending messages to an Amazon SQS queue. In that case, the SNS topic ARN
             would be compared the ARN pattern specified here.
             </para>
             <para>
             The endpoint pattern may optionally contain the multi-character wildcard
            * (*) or the single-character wildcard (?). Each of the six colon-delimited
             components of the ARN is checked separately and each can include a
             wildcard.
             </para>
             <code>
             Policy policy = new Policy("MyQueuePolicy");
             policy.WithStatements(new Statement(Statement.StatementEffect.Allow)
                 .WithPrincipals(new Principal("*")).WithActionIdentifiers(SQSActionIdentifiers.SendMessage)
                 .WithResources(new Resource(myQueueArn))
                 .WithConditions(ConditionFactory.NewSourceArnCondition(myTopicArn)));
             </code>
             </summary>
             <param name="arnPattern">The ARN pattern against which the source ARN will be compared.
                 Each of the six colon-delimited components of the ARN is
                 checked separately and each can include a wildcard.</param>
             <returns>A new access control policy condition that compares the ARN of
                    the source specified in an incoming request with the ARN pattern
                    specified here.</returns>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewSecureTransportCondition">
            <summary>
            Constructs a new access control policy condition that tests if the
            incoming request was sent over a secure transport (HTTPS).
            </summary>
            <returns>A new access control policy condition that tests if the incoming
                            request was sent over a secure transport (HTTPS).</returns>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.S3_CANNED_ACL_CONDITION_KEY">
            <summary>
            Condition key for the canned ACL specified by a request.
            <para>
            This condition key may only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.S3_LOCATION_CONSTRAINT_CONDITION_KEY">
            <summary>
            Condition key for the location constraint specified by a request.
            <para>
            This condition key may only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.S3_PREFIX_CONDITION_KEY">
            <summary>
            Condition key for the prefix specified by a request.
            <para>
            This condition key may only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.S3_DELIMITER_CONDITION_KEY">
            <summary>
            Condition key for the delimiter specified by a request.
            <para>
            This condition key may only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.S3_MAX_KEYS_CONDITION_KEY">
            <summary>
            Condition key for the max keys specified by a request.
            <para>
            This condition key may only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.S3_COPY_SOURCE_CONDITION_KEY">
            <summary>
            Condition key for the source object specified by a request to copy an
            object.
            <para>
            This condition key may only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.S3_METADATA_DIRECTIVE_CONDITION_KEY">
            <summary>
            Condition key for the metadata directive specified by a request to copy
            an object.
            <para>
            This condition key may only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.S3_VERSION_ID_CONDITION_KEY">
            <summary>
            Condition key for the version ID of an object version specified by a
            request.
            <para>
            This condition key may only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewCannedACLCondition(System.String)">
            <summary>
            Constructs a new access policy condition that compares an Amazon S3
            canned ACL with the canned ACL specified by an incoming request.
            <para>
            You can use this condition to ensure that any objects uploaded to an
            Amazon S3 bucket have a specific canned ACL set.
            </para>
            </summary>
            <param name="cannedAcl">The Amazon S3 canned ACL to compare against.</param>
            <returns>A new access control policy condition that compares the Amazon S3
                    canned ACL specified in incoming requests against the value
                    specified.</returns>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.SNS_ENDPOINT_CONDITION_KEY">
            <summary>
            Condition key for The URL, e-mail address, or ARN from a Subscribe
            request or a previously confirmed subscription. Use with string
            conditions to restrict access to specific endpoints (e.g.,
            *@mycompany.com).
            <para>
            This condition key may only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.ConditionFactory.SNS_PROTOCOL_CONDITION_KEY">
            <summary>
            Condition key for the protocol value from a Subscribe request or a
            previously confirmed subscription. Use with string conditions to restrict
            publication to specific delivery protocols (e.g., HTTPS).
            <para>
            This condition key may only be used with <see cref="T:Amazon.Auth.AccessControlPolicy.ConditionFactory.StringComparisonType"/> enum.
            </para>
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewEndpointCondition(System.String)">
            <summary>
            Constructs a new access policy condition that compares the requested
            endpoint used to subscribe to an Amazon SNS topic with the specified
            endpoint pattern. The endpoint pattern may optionally contain the
            multi-character wildcard (*) or the single-character wildcard (?).
            <para>
            For example, this condition can restrict subscriptions to a topic to
            email addresses in a certain domain ("*@my-company.com").
            </para>
            <code>
            Policy policy = new Policy("MyTopicPolicy");
            policy.WithStatements(new Statement(Statement.StatementEffect.Allow)
                   .WithPrincipals(new Principal("*")).WithActionIdentifiers(SNSActionIdentifiers.Subscribe)
                   .WithResources(new Resource(myTopicArn))
                   .WithConditions(ConditionFactory.NewEndpointCondition("*@my-company.com")));
            </code>
            </summary>
            <param name="endpointPattern">The endpoint pattern against which to compare the requested
                       endpoint for an Amazon SNS topic subscription.</param>
            <returns>A new access control policy condition that compares the endpoint
                    used in a request to subscribe to an Amazon SNS topic with the
                    endpoint pattern specified.</returns>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ConditionFactory.NewProtocolCondition(System.String)">
            <summary>
            Constructs a new AWS access control policy condition that allows an
            access control statement to restrict subscriptions to an Amazon SNS topic
            based on the protocol being used for the subscription. For example, this
            condition can restrict subscriptions to a topic to endpoints using HTTPS
            to ensure that messages are securely delivered.
            </summary>
            <param name="protocol">The protocol against which to compare the requested protocol
                       for an Amazon SNS topic subscription.</param>
            <returns>A new access control policy condition that compares the
                    notification protocol requested in a request to subscribe to an
                    Amazon SNS topic with the protocol value specified.</returns>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.Policy">
            <summary>
            An AWS access control policy is a object that acts as a container for one or
            more statements, which specify fine grained rules for allowing or denying
            various types of actions from being performed on your AWS resources.
            <para>
            By default, all requests to use your resource coming from anyone but you are
            denied. Access control polices can override that by allowing different types
            of access to your resources, or by explicitly denying different types of
            access.
            </para>
            <para>
            Each statement in an AWS access control policy takes the form:
            "A has permission to do B to C where D applies".
            <list type="definition">
                <item>
                    <term>A is the prinicpal</term>
                    <description>The AWS account that is making a request to
                        access or modify one of your AWS resources.
                    </description>
                </item>
                <item>
                    <term>B is the action</term>
                    <description>the way in which your AWS resource is being accessed or modified, such
                        as sending a message to an Amazon SQS queue, or storing an object in an Amazon S3 bucket.
                    </description>
                </item>
                <item>
                    <term>C is the resource</term>
                    <description>your AWS entity that the principal wants to access, such
                        as an Amazon SQS queue, or an object stored in Amazon S3.
                    </description>
                </item>
                <item>
                    <term>D is the set of conditions</term>
                    <description>optional constraints that specify when to allow or deny
                        access for the principal to access your resource.  Many expressive conditions are available,
                        some specific to each service.  For example you can use date conditions to allow access to
                        your resources only after or before a specific time.
                    </description>
                </item>
            </list>
            </para>
            <para>
            Note that an AWS access control policy should not be confused with the
            similarly named "POST form policy" concept used in Amazon S3.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.Policy.DEFAULT_POLICY_VERSION">
            <summary>
            The default policy version
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Policy.#ctor">
            <summary>
            Constructs an empty AWS access control policy ready to be populated with
            statements.
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Policy.#ctor(System.String)">
            <summary>
            Constructs a new AWS access control policy with the specified policy ID.
            The policy ID is a user specified string that serves to help developers
            keep track of multiple polices. Policy IDs are often used as a human
            readable name for a policy.
            </summary>
            <param name="id">The policy ID for the new policy object. Policy IDs serve to
                       help developers keep track of multiple policies, and are often
                       used to give the policy a meaningful, human readable name.</param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Policy.#ctor(System.String,System.Collections.Generic.IList{Amazon.Auth.AccessControlPolicy.Statement})">
            <summary>
            Constructs a new AWS access control policy with the specified policy ID
            and collection of statements. The policy ID is a user specified string
            that serves to help developers keep track of multiple polices. Policy IDs
            are often used as a human readable name for a policy.
            </summary>
            <param name="id">The policy ID for the new policy object. Policy IDs serve to
                       help developers keep track of multiple policies, and are often
                       used to give the policy a meaningful, human readable name.</param>
            <param name="statements">The statements to include in the new policy.</param>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Policy.Id">
            <summary>
            Gets and Sets the policy ID for this policy. Policy IDs serve to help
            developers keep track of multiple policies, and are often used as human
            readable name for a policy.
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Policy.WithId(System.String)">
            <summary>
            Sets the policy ID for this policy and returns the updated policy so that
            multiple calls can be chained together.
            <para>
            Policy IDs serve to help developers keep track of multiple policies, and
            are often used as human readable name for a policy.
            </para>
            </summary>
            <paraparam name="id">The polich ID for this policy</paraparam>
            <returns>this instance</returns>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Policy.Version">
            <summary>
            Gets and sets the version of this AWS policy.
            </summary>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Policy.Statements">
            <summary>
            Gets and Sets the collection of statements contained by this policy. Individual
            statements in a policy are what specify the rules that enable or disable
            access to your AWS resources.
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Policy.CheckIfStatementExists(Amazon.Auth.AccessControlPolicy.Statement)">
            <summary>
            Checks to see if the permissions set in the statement are already set by another
            statement in the policy.
            </summary>
            <param name="statement">The statement to verify</param>
            <returns>True if the statement's permissions are already allowed by the statement</returns>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Policy.WithStatements(Amazon.Auth.AccessControlPolicy.Statement[])">
            <summary>
            Sets the collection of statements contained by this policy and returns
            this policy object so that additional method calls can be chained
            together.
            <para>
            Individual statements in a policy are what specify the rules that enable
            or disable access to your AWS resources.
            </para>
            </summary>
            <param name="statements">The collection of statements included in this policy.</param>
            <returns>this instance</returns>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Policy.ToJson">
            <summary>
            Returns a JSON string representation of this AWS access control policy,
            suitable to be sent to an AWS service as part of a request to set an
            access control policy.
            </summary>
            <returns>A JSON string representation of this AWS access control policy.</returns>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Policy.ToJson(System.Boolean)">
            <summary>
            Returns a JSON string representation of this AWS access control policy,
            suitable to be sent to an AWS service as part of a request to set an
            access control policy.
            </summary>
            <param name="prettyPrint">Toggle pretty print for the generated JSON document</param>
            <returns>A JSON string representation of this AWS access control policy.</returns>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Policy.FromJson(System.String)">
            <summary>
            Parses a JSON document of a policy and creates a Policy object.
            </summary>
            <param name="json">JSON document of a policy.</param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.Principal">
            <summary>
            A principal is an AWS account which is being allowed or denied access to a
            resource through an access control policy. The principal is a property of the
            Statement object, not directly the <see cref="T:Amazon.Auth.AccessControlPolicy.Policy"/> object.
            <para>
            The principal is A in the statement
            "A has permission to do B to C where D applies."
            </para>
            <para>
            In an access control policy statement, you can set the principal to all
            authenticated AWS users through the <see cref="F:Amazon.Auth.AccessControlPolicy.Principal.AllUsers"/> member. This
            is useful when you don't want to restrict access based on the identity of the
            requester, but instead on other identifying characteristics such as the
            requester's IP address.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.Principal.AllUsers">
            <summary>
            Principal instance that includes all authenticated AWS users.
            <para>
            This is useful when you don't want to restrict access based on the
            identity of the requester, but instead on other identifying
            characteristics such as the requester's IP address.
            </para>
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.Principal.Anonymous">
            <summary>
            The anonymous Principal.
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.Principal.AWS_PROVIDER">
            <summary>
            The default Principal provider for AWS accounts.
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.Principal.CANONICAL_USER_PROVIDER">
            <summary>
            Principal provider for Canonical User IDs.
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.Principal.FEDERATED_PROVIDER">
            <summary>
            Principal provider for federated users (using a SAML identity provider)
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.Principal.SERVICE_PROVIDER">
            <summary>
            Principal provider for assume role policies that will be assumed by an AWS service
            (e.g. "ec2.amazonaws.com").
            </summary>
        </member>
        <member name="F:Amazon.Auth.AccessControlPolicy.Principal.ANONYMOUS_PROVIDER">
            <summary>
            Dummy principal provider for anonynous.
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Principal.#ctor(System.String)">
            <summary>
            Constructs a new principal with the specified AWS account ID.
            </summary>
            <param name="accountId">An AWS account ID.</param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Principal.#ctor(System.String,System.String)">
            <summary>
            Constructs a new principal with the specified provider and id
            </summary>
            <param name="provider">The provider of the principal</param>
            <param name="id">The unique ID of the Principal within the provider</param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Principal.#ctor(System.String,System.String,System.Boolean)">
            <summary>
            Constructs a new principal with the specified provider and id 
            and optionally strips hyphens from the id
            </summary>
            <param name="provider">The provider of the principal</param>
            <param name="id">The unique ID of the Principal within the provider</param>
            <param name="stripHyphen">Strip hyphen</param>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Principal.Provider">
            <summary>
            Gets and sets the provider for this principal, which indicates in what group of
            users this principal resides.
            </summary>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Principal.Id">
            <summary>
            Gets the unique ID for this principal.
            </summary>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.Resource">
            <summary>
            Represents a resource involved in an AWS access control policy statement.
            Resources are the service specific AWS entities owned by your account. Amazon
            SQS queues, Amazon S3 buckets and objects, and Amazon SNS topics are all
            examples of AWS resources.
            <para>
            The standard way of specifying an AWS resource is with an Amazon Resource
            Name (ARN).
            </para>
            <para>
            The resource is C in the statement
            "A has permission to do B to C where D applies."
            </para>
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Resource.#ctor(System.String)">
            <summary>
            Constructs a new AWS access control policy resource. Resources are
            typically specified as Amazon Resource Names (ARNs).
            <para>
            You specify the resource using the following Amazon Resource Name (ARN)
            format: arn:aws:&lt;vendor>:&lt;region>:&lt;namespace>:&lt;relative-id>
            <list type="bullet">
                <item>
                    <description>>vendor identifies the AWS product (e.g., sns)</description>
                </item>
                <item>
                    <description>region is the AWS Region the resource resides in (e.g., us-east-1), if any</description>
                </item>
                <item>
                    <description>namespace is the AWS account ID with no hyphens (e.g., ************)</description>
                </item>
                <item>
                    <description>relative-id is the service specific portion that identifies the specific resource</description>
                </item>
            </list>
            </para>
            <para>
            For example, an Amazon SQS queue might be addressed with the following
            ARN: arn:aws:sqs:us-east-1:************:MyQueue
            </para>
            <para>
            Some resources may not use every field in an ARN. For example, resources
            in Amazon S3 are global, so they omit the region field:
            arn:aws:s3:::bucket/*
            </para>
            </summary>
            <param name="resource">The Amazon Resource Name (ARN) uniquely identifying the desired AWS resource.</param>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Resource.Id">
            <summary>
            Gets the resource ID, typically an Amazon Resource Name (ARN),
            identifying this resource.
            </summary>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ResourceFactory">
            <summary>
            A factory for creating resources to be used in the policy.
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ResourceFactory.NewS3BucketResource(System.String)">
            <summary>
            Constructs a new bucket resource that represents the the specified bucket
            but <b>not any of the contained objects</b>.
            </summary>
            <param name="bucketName">The name of the bucket represented by this AWS access control
                       policy resource.</param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ResourceFactory.NewS3ObjectResource(System.String,System.String)">
            <summary>
            Constructs a new object resource that represents the specified objects.
            The keyPattern argument may contain the '*' wildcard to match multiple
            objects. For example, an object resource created for bucket 'mybucket'
            and key pattern 'foo*' will match any object stored in 'mybucket' with a
            key that starts with 'foo'.
            </summary>
            <param name="bucketName">The name of the bucket containing the object or objects
                       represented by this resource.</param>
            <param name="keyPattern">The key or key pattern, which can optionally contain the '*'
                       wildcard to include multiple objects in the resource.</param>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.ResourceFactory.NewSQSQueueResource(System.String,System.String)">
            <summary>
            Constructs a new SQS queue resource for an access control policy. A
            policy statement using this resource will allow or deny actions on the
            specified queue.
            </summary>
            <param name="accountId">The AWS account ID of the queue owner.</param>
            <param name="queueName">The name of the Amazon SQS queue.</param>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.Statement">
            <summary>
            A statement is the formal description of a single permission, and is always
            contained within a policy object.
            <para>
            A statement describes a rule for allowing or denying access to a specific AWS
            resource based on how the resource is being accessed, and who is attempting
            to access the resource. Statements can also optionally contain a list of
            conditions that specify when a statement is to be honored.
            </para>
            <para>
            For example, consider a statement that:
            <list type="definition">
                <item>
                    <term>A is the prinicpal</term>
                    <description>The AWS account that is making a request to
                        access or modify one of your AWS resources.
                    </description>
                </item>
                <item>
                    <term>B is the action</term>
                    <description>the way in which your AWS resource is being accessed or modified, such
                        as sending a message to an Amazon SQS queue, or storing an object in an Amazon S3 bucket.
                    </description>
                </item>
                <item>
                    <term>C is the resource</term>
                    <description>your AWS entity that the principal wants to access, such
                        as an Amazon SQS queue, or an object stored in Amazon S3.
                    </description>
                </item>
                <item>
                    <term>D is the set of conditions</term>
                    <description>optional constraints that specify when to allow or deny
                        access for the principal to access your resource.  Many expressive conditions are available,
                        some specific to each service.  For example you can use date conditions to allow access to
                        your resources only after or before a specific time.
                    </description>
                </item>
            </list>
            </para>
            <para>
            There are many resources and conditions available for use in statements, and
            you can combine them to form fine grained custom access control polices.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.Statement.StatementEffect">
            <summary>
            The effect is the result that you want a policy statement to return at
            evaluation time. A policy statement can either allow access or explicitly
            deny access.
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Statement.#ctor(Amazon.Auth.AccessControlPolicy.Statement.StatementEffect)">
            <summary>
            Constructs a new access control policy statement with the specified
            effect.
            <para>
            Before a statement is valid and can be sent to AWS, callers must set the
            principals, resources, and actions (as well as any optional conditions)
            involved in the statement.
            </para>
            </summary>
            <param name="effect">The effect this statement has (allowing access or denying
                       access) when all conditions, resources, principals, and
                       actions are matched.</param>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Statement.Id">
            <summary>
            Gets and Sets the ID for this statement. Statement IDs serve to help keep track
            of multiple statements, and are often used to give the statement a
            meaningful, human readable name.
            <para>
            Developers should be careful to not use the same statement ID for
            multiple statements in the same policy. Reusing the same statement ID in
            different policies is not a problem.
            </para>
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Statement.WithId(System.String)">
            <summary>
            Sets the ID for this statement and returns the updated statement so
            multiple calls can be chained together.
            <para>
            Statement IDs serve to help keep track of multiple statements, and are
            often used to give the statement a meaningful, human readable name.
            </para>
            <para>
            Developers should be careful to not use the same statement ID for
            multiple statements in the same policy. Reusing the same statement ID in
            different policies is not a problem.
            </para>
            </summary>
            <param name="id">The new statement ID for this statement.</param>
            <returns>this instance</returns>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Statement.Effect">
            <summary>
            Gets and Sets the result effect of this policy statement when it is evaluated.
            A policy statement can either allow access or explicitly
            </summary>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Statement.Actions">
            <summary>
            Gets and Sets the list of actions to which this policy statement applies.
            Actions limit a policy statement to specific service operations that are
            being allowed or denied by the policy statement. For example, you might
            want to allow any AWS user to post messages to your SQS queue using the
            SendMessage action, but you don't want to allow those users other actions
            such as ReceiveMessage or DeleteQueue.
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Statement.WithActionIdentifiers(Amazon.Auth.AccessControlPolicy.ActionIdentifier[])">
            <summary>
            Sets the list of actions to which this policy statement applies and
            returns this updated Statement object so that additional method calls can
            be chained together.
            <para>
            Actions limit a policy statement to specific service operations that are
            being allowed or denied by the policy statement. For example, you might
            want to allow any AWS user to post messages to your SQS queue using the
            SendMessage action, but you don't want to allow those users other actions
            such as ReceiveMessage or DeleteQueue.
            </para>
            </summary>
            <param name="actions">The list of actions to which this statement applies.</param>
            <returns>this instance</returns>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Statement.Resources">
            <summary>
            Gets and Sets the resources associated with this policy statement. Resources
            are what a policy statement is allowing or denying access to, such as an
            Amazon SQS queue or an Amazon SNS topic.
            <para>
            Note that some services allow only one resource to be specified per
            policy statement.
            </para>
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Statement.WithResources(Amazon.Auth.AccessControlPolicy.Resource[])">
            <summary>
            Sets the resources associated with this policy statement and returns this
            updated Statement object so that additional method calls can be chained
            together.
            <para>
            Resources are what a policy statement is allowing or denying access to,
            such as an Amazon SQS queue or an Amazon SNS topic.
            </para>
            <para>
            Note that some services allow only one resource to be specified per
            policy statement.
            </para>
            </summary>
            <param name="resources">The resources associated with this policy statement.</param>
            <returns>this instance</returns>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Statement.Conditions">
            <summary>
            Gets and Sets the conditions associated with this policy statement. Conditions
            allow policy statements to be conditionally evaluated based on the many
            available condition types.
            <para>
            For example, a statement that allows access to an Amazon SQS queue could
            use a condition to only apply the effect of that statement for requests
            that are made before a certain date, or that originate from a range of IP
            addresses.
            </para>
            <para>
            When multiple conditions are included in a single statement, all
            conditions must evaluate to true in order for the statement to take
            effect.
            </para>
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Statement.WithConditions(Amazon.Auth.AccessControlPolicy.Condition[])">
            <summary>
            Sets the conditions associated with this policy statement, and returns
            this updated Statement object so that additional method calls can be
            chained together.
            <para>
            Conditions allow policy statements to be conditionally evaluated based on
            the many available condition types.
            </para>
            <para>
            For example, a statement that allows access to an Amazon SQS queue could
            use a condition to only apply the effect of that statement for requests
            that are made before a certain date, or that originate from a range of IP
            addresses.
            </para>
            <para>
            Multiple conditions can be included in a single statement, and all
            conditions must evaluate to true in order for the statement to take
            effect.
            </para>
            </summary>
            <param name="conditions">The conditions associated with this policy statement.</param>
            <returns>this instance</returns>
        </member>
        <member name="P:Amazon.Auth.AccessControlPolicy.Statement.Principals">
            <summary>
            Gets and Sets the principals associated with this policy statement, indicating
            which AWS accounts are affected by this policy statement.
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Statement.WithPrincipals(Amazon.Auth.AccessControlPolicy.Principal[])">
            <summary>
            Sets the principals associated with this policy statement, and returns
            this updated Statement object. Principals control which AWS accounts are
            affected by this policy statement.
            <para>
            If you don't want to restrict your policy to specific users, you can use
            <see cref="F:Amazon.Auth.AccessControlPolicy.Principal.AllUsers"/> to apply the policy to any user trying to
            access your resource.
            </para>
            </summary>
            <param name="principals">The list of principals associated with this policy statement.</param>
            <returns>this instance</returns>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.AppStreamActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon AppStream.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.AutoScalingActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Auto Scaling.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.BillingActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS Billing.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.CloudFormationActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS CloudFormation.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.CloudFrontActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon CloudFront.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.CloudSearchActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon CloudSearch.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.CloudTrailActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS CloudTrail.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.CloudWatchActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon CloudWatch.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.CloudWatchLogsActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon CloudWatch Logs.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.CognitoIdentityActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon Cognito Identity.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.CognitoSyncActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon Cognito Sync.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.DirectConnectActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS Direct Connect.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.DynamoDBActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon DynamoDB.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.EC2ActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon EC2.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.ElastiCacheActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS ElastiCache.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.ElasticBeanstalkActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS Elastic Beanstalk.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.ElasticLoadBalancingActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Elastic Load Balancing.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.ElasticMapReduceActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon Elastic MapReduce.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.ElasticTranscoderActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon Elastic Transcoder.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.GlacierActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon Glacier.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.IdentityandAccessManagementActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS Identity and Access Management.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.ImportExportActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS Import Export.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.KinesisActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon Kinesis.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.MarketplaceActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS Marketplace.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.MarketplaceManagementPortalActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS Marketplace Management Portal.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.MobileAnalyticsActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon Mobile Analytics.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.OpsWorksActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS OpsWorks.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.RDSActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon RDS.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.RedshiftActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon Redshift.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.Route53ActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon Route 53.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.S3ActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon S3.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.SecurityTokenServiceActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS Security Token Service.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.SESActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon SES.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.SimpleDBActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon SimpleDB.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.SimpleWorkflowServiceActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon Simple Workflow Service.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.SNSActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon SNS.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.SQSActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon SQS.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.StorageGatewayActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon Storage Gateway.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.WhispersyncActionIdentifiers">
            <summary>
            The available AWS access control policy actions for AWS Whispersync.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.ActionIdentifiers.ZocaloActionIdentifiers">
            <summary>
            The available AWS access control policy actions for Amazon Zocalo.
            </summary>
            <see cref="P:Amazon.Auth.AccessControlPolicy.Statement.Actions"/>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.Internal.JsonPolicyReader">
            <summary>
            Deserializes a JSON string into a AWS policy object.
            </summary>
        </member>
        <member name="T:Amazon.Auth.AccessControlPolicy.Internal.JsonPolicyWriter">
            <summary>
            Serializes an AWS policy object to a JSON string, suitable for sending to an
            AWS service.
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Internal.JsonPolicyWriter.WritePolicyToString(System.Boolean,Amazon.Auth.AccessControlPolicy.Policy)">
             Converts the specified AWS policy object to a JSON string, suitable for
             passing to an AWS service.
            
             @param policy
                        The AWS policy object to convert to a JSON string.
            
             @return The JSON string representation of the specified policy object.
            
             @throws IllegalArgumentException
                         If the specified policy is null or invalid and cannot be
                         serialized to a JSON string.
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Internal.JsonPolicyWriter.writePrincipals(Amazon.Auth.AccessControlPolicy.Statement,ThirdParty.Json.LitJson.JsonWriter)">
            <summary>
            Uses the specified generator to write the JSON data for the principals in
            the specified policy statement.
            </summary>
        </member>
        <member name="M:Amazon.Auth.AccessControlPolicy.Internal.JsonPolicyWriter.sortConditionsByTypeAndKey(System.Collections.Generic.IList{Amazon.Auth.AccessControlPolicy.Condition})">
            <summary>
            This sorts the conditions by condition type and key with the list of values for that combination.
            </summary>
            <param name="conditions">The list of conditions to be sorted.</param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.AmazonClientException">
            <summary>
            Exception thrown by the SDK for errors that occur within the SDK.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.AmazonDateTimeUnmarshallingException">
            <summary>
            This exception is thrown when there is a parse error on the response back from AWS.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AmazonDateTimeUnmarshallingException.InvalidDateTimeToken">
            <summary>
            The string value which could not be converted into a valid DateTime instance.
            </summary>
        </member>
        <member name="E:Amazon.Runtime.AmazonServiceClient.BeforeMarshallingEvent">
            <summary>
            Occurs before a request is marshalled.
            </summary>
        </member>
        <member name="E:Amazon.Runtime.AmazonServiceClient.BeforeRequestEvent">
            <summary>
            Occurs before a request is issued against the service.
            </summary>
        </member>
        <member name="E:Amazon.Runtime.AmazonServiceClient.AfterResponseEvent">
            <summary>
            Occurs after a response is received from the service.
            </summary>
        </member>
        <member name="E:Amazon.Runtime.AmazonServiceClient.ExceptionEvent">
            <summary>
            Occurs after an exception is encountered.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.AmazonServiceClient.DontUnescapePathDotsAndSlashes(System.Uri)">
            <summary>
            Patches the in-flight uri to stop it unescaping the path etc (what Uri did before
            Microsoft deprecated the constructor flag). This is particularly important for
            Amazon S3 customers who want to use backslash (\) in their key names.
            </summary>
            <remarks>
            Different behavior in the various runtimes has been observed and in addition some 
            'documented' ways of doing this between 2.x and 4.x runtimes has also been observed 
            to not be reliable.
            
            This patch effectively emulates what adding a schemesettings element to the 
            app.config file with value 'name="http" genericUriParserOptions="DontUnescapePathDotsAndSlashes"'
            does. As we're a dll, that avenue is not open to us.
            </remarks>
            <param name="uri"></param>
        </member>
        <member name="M:Amazon.Runtime.AmazonServiceClient.CloneConfig``1">
            <summary>
            Used to create a copy of the config for a different service than the current instance.
            </summary>
            <typeparam name="C">Target service ClientConfig</typeparam>
            <returns>The new ClientConfig for the desired service</returns>
        </member>
        <member name="T:Amazon.Runtime.AmazonServiceException">
            <summary>
            A base exception for some Amazon Web Services.
            <para>
            Most exceptions thrown to client code will be service-specific exceptions, though some services
            may throw this exception if there is a problem which is caught in the core client code.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AmazonServiceException.ErrorType">
            <summary>
            Whether the error was attributable to <c>Sender</c> or <c>Reciever</c>.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AmazonServiceException.ErrorCode">
            <summary>
            The error code returned by the service
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AmazonServiceException.RequestId">
            <summary>
            The id of the request which generated the exception.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AmazonServiceException.StatusCode">
            <summary>
            The HTTP status code from the service response
            </summary>
        </member>
        <member name="T:Amazon.Runtime.AmazonUnmarshallingException">
            <summary>
            This exception is thrown when there is a parse error on the response back from AWS.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AmazonUnmarshallingException.LastKnownLocation">
            <summary>
            Last known location in the response that was parsed, if available.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AmazonUnmarshallingException.ResponseBody">
            <summary>
            The entire response body that caused this exception, if available.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.AmazonWebServiceRequest">
            <summary>
            Base class for request used by some of the services.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AmazonWebServiceRequest.Amazon#Runtime#Internal#IAmazonWebServiceRequest#UseSigV4">
            <summary>
            This flag specifies if SigV4 will be used for the current request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AmazonWebServiceRequest.Expect100Continue">
            <summary>
            Gets or Sets a value indicating if "Expect: 100-continue" HTTP header will be 
            sent by the client for this request. The default value is false.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.AmazonWebServiceRequest.CreateSigner">
            <summary>
            Gets the signer to use for this request.
            A null return value indicates to use the configured
            signer for the service that this request is part of.
            </summary>
            <returns>A signer for this request, or null.</returns>
        </member>
        <member name="T:Amazon.Runtime.AmazonWebServiceResponse">
            <summary>
            Abstract class for Response objects, contains only metadata, 
            and no result information.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AmazonWebServiceResponse.ResponseMetadata">
            <summary>
            Contains additional information about the request, such as the 
            Request Id.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AmazonWebServiceResponse.ContentLength">
            <summary>
            Returns the content length of the HTTP response.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AmazonWebServiceResponse.HttpStatusCode">
            <summary>
            Returns the status code of the HTTP response.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.AWSRegion">
            <summary>
            Base class for determining region based on inspection.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.AWSRegion.SetRegionFromName(System.String)">
            <summary>
            Sets the Region property by looking up the corresponding RegionEndpoint
            from the supplied region system name (us-east-1, us-west-2 etc).
            </summary>
            <param name="regionSystemName">The system name of the region.</param>
        </member>
        <member name="T:Amazon.Runtime.AppConfigAWSRegion">
            <summary>
            Determines region based on application configuration settings. If the configuration does not contain
            the region setting key an InvalidOperationException is thrown.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.AppConfigAWSRegion.#ctor">
            <summary>
            Attempts to construct an instance of ApplicationConfigAWSRegion. If no region is found in the
            application configuration file then an InvalidOperationException is thrown.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.FallbackRegionFactory">
            <summary>
            Probing mechanism to determine region from various sources.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.CapacityManager">
            <summary>
            This class is responsible for keeping track of Retry capacity across different ServiceURLs.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.CapacityManager.TryAcquireCapacity(Amazon.Runtime.Internal.RetryCapacity)">
            <summary>
            This method acquires a said retry capacity if the container has the capacity.
            </summary>
            <param name="retryCapacity">Contains the RetryCapacity object for the said ServiceURL.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.CapacityManager.TryReleaseCapacity(System.Boolean,Amazon.Runtime.Internal.RetryCapacity)">
            <summary>
            This method calls a method to release capacity back 
            based on whether it was a successful response or a successful retry response. This is invoked by a retry request response.
            </summary>
            <param name="isRetryRequest">if this request is a retry, use a different capacity cost</param>
            <param name="retryCapacity">Contains the RetryCapacity object for the said ServiceURL.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.CapacityManager.GetRetryCapacity(System.String)">
            <summary>
            Ths method fetches the RetryCapacity for the given ServiceURL from CapacityManager.CapacityContainer
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.CapacityManager.ReleaseCapacity(System.Int32,Amazon.Runtime.Internal.RetryCapacity)">
            <summary>
            This method releases capacity back. This is invoked by the TryReleaseCapacity method.
            </summary>
            <param name="retryCapacity">Contains the RetryCapacity object for the said ServiceURL.</param>
            <param name="capacity">The capacity that needs to be released based on whether it was a successful response or a successful retry response.</param>
        </member>
        <member name="T:Amazon.Runtime.Internal.RetryCapacity">
            <summary>
            This class is the RetryCapacity class for a given ServiceURL.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.ClientContext">
            <summary>
            This class composes Client Context header for Amazon Web Service client.
            It contains information like app title, version code, version name, client id, OS platform etc.
            </summary>
            <summary>
            This class composes Client Context header for Amazon Web Service client.
            It contains information like app title, version code, version name, client id, OS platform etc.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.ClientContext.AddCustomAttributes(System.String,System.String)">
            <summary>
            Adds the custom attributes to the Client Context.
            </summary>
            <param name="key">Key.</param>
            <param name="value">Value.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.ClientContext.ToJsonString">
            <summary>
            Gets a Json Representation of the Client Context.
            </summary>
            <returns>Json Representation of Client Context</returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.ParameterCollection">
            <summary>
            Collection of parameters that an SDK client will send to a service.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.ParameterCollection.#ctor">
            <summary>
            Constructs empty ParameterCollection.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.ParameterCollection.Add(System.String,System.String)">
            <summary>
            Adds a parameter with a string value.
            </summary>
            <param name="key"></param>
            <param name="value"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.ParameterCollection.Add(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Adds a parameter with a list-of-strings value.
            </summary>
            <param name="key"></param>
            <param name="values"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.ParameterCollection.GetSortedParametersList">
            <summary>
            Converts the current parameters into a list of key-value pairs.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.AutoConstructedList`1">
            <summary>
            Collection used to indicate if the property was initialized was created by the SDK.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:Amazon.Runtime.Internal.AutoConstructedDictionary`2">
            <summary>
            Collection used to indicate if the property was initialized was created by the SDK.
            </summary>
            <typeparam name="K"></typeparam>
            <typeparam name="V"></typeparam>
        </member>
        <member name="T:Amazon.Runtime.Internal.DefaultRequest">
            <summary>
            Default implementation of the IRequest interface.
            <para>
            This class is only intended for internal use inside the AWS client libraries.
            Callers shouldn't ever interact directly with objects of this class.
            </para>
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRequest.#ctor(Amazon.Runtime.AmazonWebServiceRequest,System.String)">
            <summary>
            Constructs a new DefaultRequest with the specified service name and the
            original, user facing request object.
            </summary>
            <param name="request">The orignal request that is being wrapped</param>
            <param name="serviceName">The service name</param>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.RequestName">
            <summary>
            The name of the request
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.HttpMethod">
            <summary>
            Gets and sets the type of http request to make, whether it should be POST,GET or DELETE
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.UseQueryString">
            <summary>
            Gets and sets a flag that indicates whether the request is sent as a query string instead of the request body.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.OriginalRequest">
            <summary>
            Returns the original, user facing request object which this internal
            request object is representing.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.Headers">
            <summary>
            Returns a dictionary of the headers included in this request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.Parameters">
            <summary>
            Returns a dictionary of the parameters included in this request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.ParameterCollection">
            <summary>
            Collection of parameters included in this request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.SubResources">
            <summary>
            Returns the subresources that should be appended to the resource path.
            This is used primarily for Amazon S3, where object keys can contain '?'
            characters, making string-splitting of a resource path potentially 
            hazardous.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRequest.AddSubResource(System.String)">
            <summary>
            Adds a new null entry to the SubResources collection for the request
            </summary>
            <param name="subResource">The name of the subresource</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRequest.AddSubResource(System.String,System.String)">
            <summary>
            Adds a new entry to the SubResources collection for the request
            </summary>
            <param name="subResource">The name of the subresource</param>
            <param name="value">Value of the entry</param>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.Endpoint">
            <summary>
            Gets and Sets the endpoint for this request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.ResourcePath">
            <summary>
            Gets and Sets the resource path added on to the endpoint.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.Content">
            <summary>
            Gets and Sets the content for this request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.SetContentFromParameters">
            <summary>
            Flag that signals that Content was and should be set
            from the Parameters collection.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.ContentStream">
            <summary>
            Gets and sets the content stream.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.OriginalStreamPosition">
            <summary>
            Gets and sets the original stream position.
            If ContentStream is null or does not support seek, this propery
            should be equal to -1.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRequest.ComputeContentStreamHash">
            <summary>
            Computes the SHA 256 hash of the content stream. If the stream is not
            seekable, it searches the parent stream hierarchy to find a seekable
            stream prior to computation. Once computed, the hash is cached for future
            use. If a suitable stream cannot be found to use, null is returned.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.ServiceName">
            <summary>
            The name of the service to which this request is being sent.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.AlternateEndpoint">
            <summary>
            Alternate endpoint to use for this request, if any.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.HostPrefix">
            <summary>
            Host prefix value to prepend to the endpoint for this request, if any.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.Suppress404Exceptions">
            <summary>
            Gets and sets the Suppress404Exceptions property. If true then 404s return back from AWS will not cause an exception and 
            an empty response object will be returned.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.AWS4SignerResult">
            <summary>
            If using AWS4 signing protocol, contains the resultant parts of the
            signature that we may need to make use of if we elect to do a chunked
            encoding upload.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.UseChunkEncoding">
            <summary>
            Determine whether to use a chunked encoding upload for the request
            (applies to Amazon S3 PutObject and UploadPart requests only). 
            </summary>
            <returns></returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.CanonicalResourcePrefix">
            <summary>
            Used for Amazon S3 requests where the bucket name is removed from
            the marshalled resource path into the host header. To comply with
            AWS2 signature calculation, we need to recover the bucket name
            and include it in the resource canonicalization, which we do using
            this field.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.UseSigV4">
            <summary>
            This flag specifies if SigV4 is required for the current request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.AuthenticationRegion">
            <summary>
            The authentication region to use for the request.
            Set from Config.AuthenticationRegion.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRequest.DeterminedSigningRegion">
            <summary>
            The region in which the service request was signed.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRequest.IsRequestStreamRewindable">
            <summary>
            Checks if the request stream can be rewinded.
            </summary>
            <returns>Returns true if the request stream can be rewinded ,
            else false.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRequest.MayContainRequestBody">
            <summary>
            Returns true if the request can contain a request body, else false.
            </summary>
            <returns>Returns true if the currect request can contain a request body, else false.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRequest.HasRequestBody">
            <summary>
            Returns true if the request has a body, else false.
            </summary>
            <returns>Returns true if the request has a body, else false.</returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.DiscoveryEndpointBase">
            <summary>
            Class containing the data for one endpoint returned from a endpoint discovery request
            <para>
            This class is only intended for internal use inside the AWS client libraries.
            Callers shouldn't ever interact directly with objects of this class.
            </para>
            </summary>    
        </member>
        <member name="M:Amazon.Runtime.Internal.DiscoveryEndpointBase.#ctor(System.String,System.Int64)">
            <summary>
            Constructs a new DiscoveryEndpoint
            </summary>
            <param name="address">The address of the endpoint</param>        
            <param name="cachePeriodInMinutes">The cache period for the endpoint in minutes</param>
        </member>
        <member name="P:Amazon.Runtime.Internal.DiscoveryEndpointBase.Address">
            <summary>
            The address of the endpoint.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DiscoveryEndpointBase.CachePeriodInMinutes">
            <summary>
            The cache period for the endpoint in minutes
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.DiscoveryEndpointBase.HasExpired">
            <summary>
            Calculates if this endpoint has expired
            </summary>
            <returns>A boolean value indicating if the cache period has expired</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.DiscoveryEndpointBase.ExtendExpiration(System.Int64)">
            <summary>
            Extends the endpoint expiration by the specified number of minutes from now.
            </summary>        
        </member>
        <member name="T:Amazon.Runtime.Internal.DiscoveryEndpoint">
            <summary>
            Class containing the data for one endpoint returned from a endpoint discovery request
            <para>
            This class is only intended for internal use inside the AWS client libraries.
            Callers shouldn't ever interact directly with objects of this class.
            </para>
            </summary>    
        </member>
        <member name="T:Amazon.Runtime.Internal.EndpointDiscoveryDataBase">
            <summary>
            Class containing the data to use with endpoint discovery
            <para>
            This class is only intended for internal use inside the AWS client libraries.
            Callers shouldn't ever interact directly with objects of this class.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.EndpointDiscoveryDataBase.Required">
            <summary>
            Gets/sets and flag indicating if endpoint discovery is required for the request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.EndpointDiscoveryDataBase.Identifiers">
            <summary>
            Sorted dictionary of the identifiers that must be sent with the endpoint discovery request. These
            identifiers are used to construct the cache key for the cache that stores discovered endpoints. A
            sorted dictionary is used instead of a dictionary to ensure the cache key comes out in the same order
            each time it is constructed.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.EndpointDiscoveryData">
            <summary>
            Represents the data to be used with endpoint discovery operations
            <para>
            This class is only intended for internal use inside the AWS client libraries.
            Callers shouldn't ever interact directly with objects of this class.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.EndpointDiscoveryResolverBase">
            <summary>
            Class used to resolve endpoints using Endpoint Discovery.
            <para>
            This class is only intended for internal use inside the AWS client libraries.
            Callers shouldn't ever interact directly with objects of this class.
            </para>
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.EndpointDiscoveryResolverBase.ResolveEndpoints(Amazon.Runtime.Internal.EndpointOperationContextBase,System.Func{System.Collections.Generic.IList{Amazon.Runtime.Internal.DiscoveryEndpointBase}})">
            <summary>
            Method that performs endpoint discovery for the current operation
            </summary>
            <param name="context">Context information used in calculations for endpoint discovery</param>
            <param name="InvokeEndpointOperation">The operation to fetch endpoints from the server</param>
            <returns></returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.EndpointDiscoveryResolverBase.CacheCount">
            <summary>
            Gets the number of cache keys in the cache
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.EndpointDiscoveryResolver">
            <summary>
            Class used to resolve endpoints using Endpoint Discovery.
            <para>
            This class is only intended for internal use inside the AWS client libraries.
            Callers shouldn't ever interact directly with objects of this class.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.EndpointOperationContextBase">
            <summary>
            Class containing context information to use with endpoint discovery
            <para>
            This class is only intended for internal use inside the AWS client libraries.
            Callers shouldn't ever interact directly with objects of this class.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.EndpointOperationContextBase.CustomerCredentials">
            <summary>
            Gets the customer credential information.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.EndpointOperationContextBase.OperationName">
            <summary>
            Gets the operation name.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.EndpointOperationContextBase.EndpointDiscoveryData">
            <summary>
            Gets the current marshalled endpoint discovery data.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.EndpointOperationContextBase.EvictCacheKey">
            <summary>
            Gets the flag indicating if the specified key should be evicted from the cache.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.EndpointOperationContextBase.EvictUri">
            <summary>
            Gets the Uri that should be evicted if EvictCacheKey is set to true.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.EndpointOperationContext">
            <summary>
            Class containing context information to use with endpoint discovery
            <para>
            This class is only intended for internal use inside the AWS client libraries.
            Callers shouldn't ever interact directly with objects of this class.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.ErrorResponse.Type">
            <summary>
            Error type, one of Sender, Receiver, Unknown
            Only applies to XML-based services.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.ErrorResponse.Code">
            <summary>
            Name of the exception class to return
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.ErrorResponse.Message">
            <summary>
            Error message
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.ErrorResponse.RequestId">
            <summary>
            RequestId of the error.
            Only applies to XML-based services.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.InvokeOptionsBase">
            <summary>
            Class containing the members used to invoke service calls
            <para>
            This class is only intended for internal use inside the AWS client libraries.
            Callers shouldn't ever interact directly with objects of this class.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.InvokeOptions">
            <summary>
            Class containing the members used to invoke service calls
            <para>
            This class is only intended for internal use inside the AWS client libraries.
            Callers shouldn't ever interact directly with objects of this class.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.IRequest">
            <summary>
            Represents a request being sent to an Amazon Web Service, including the
            parameters being sent as part of the request, the endpoint to which the
            request should be sent, etc.
            <para>
            This class is only intended for internal use inside the AWS client libraries.
            Callers shouldn't ever interact directly with objects of this class.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.RequestName">
            <summary>
            The name of the request
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.Headers">
            <summary>
            Returns a dictionary of the headers included in this request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.UseQueryString">
            <summary>
            Gets and sets a flag that indicates whether the request is sent as a query string instead of the request body.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.Parameters">
            <summary>
            Returns a dictionary of the parameters included in this request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.ParameterCollection">
            <summary>
            Collection of parameters included in this request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.SubResources">
            <summary>
            Returns the subresources that should be appended to the resource path.
            This is used primarily for Amazon S3, where object keys can contain '?'
            characters, making string-splitting of a resource path potentially 
            hazardous.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.IRequest.AddSubResource(System.String)">
            <summary>
            Adds a new null entry to the SubResources collection for the request
            </summary>
            <param name="subResource">The name of the subresource</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.IRequest.AddSubResource(System.String,System.String)">
            <summary>
            Adds a new entry to the SubResources collection for the request
            </summary>
            <param name="subResource">The name of the subresource</param>
            <param name="value">Value of the entry</param>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.HttpMethod">
            <summary>
            Gets and sets the type of http request to make, whether it should be POST,GET or DELETE
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.Endpoint">
            <summary>
            Gets and Sets the endpoint for this request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.ResourcePath">
            <summary>
            Gets and Sets the resource path added on to the endpoint.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.Content">
            <summary>
            Gets and Sets the content for this request.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.IRequest.GetHeaderValue(System.String)">
            <summary>
            Gets the header value from the request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.SetContentFromParameters">
            <summary>
            Flag that signals that Content was and should be set
            from the Parameters collection.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.ContentStream">
            <summary>
            Gets and sets the content stream.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.OriginalStreamPosition">
            <summary>
            Gets and sets the original stream position.
            If ContentStream is null or does not support seek, this propery
            should be equal to -1.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.IRequest.ComputeContentStreamHash">
            <summary>
            Computes the SHA 256 hash of the content stream. If the stream is not
            seekable, it searches the parent stream hierarchy to find a seekable
            stream prior to computation. Once computed, the hash is cached for future
            use.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.ServiceName">
            <summary>
            The name of the service to which this request is being sent.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.OriginalRequest">
            <summary>
            Returns the original, user facing request object which this internal
            request object is representing.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.AlternateEndpoint">
            <summary>
            Alternate endpoint to use for this request, if any.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.HostPrefix">
            <summary>
            Host prefix value to prepend to the endpoint for this request, if any. 
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.Suppress404Exceptions">
            <summary>
            Gets and sets the Suppress404Exceptions property. If true then 404s return back from AWS will not cause an exception and 
            an empty response object will be returned.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.AWS4SignerResult">
            <summary>
            If using AWS4 signing protocol, contains the resultant parts of the
            signature that we may need to make use of if we elect to do a chunked
            encoding upload.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.UseChunkEncoding">
            <summary>
            Determine whether to use a chunked encoding upload for the request
            (applies to Amazon S3 PutObject and UploadPart requests only). 
            </summary>
            <returns></returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.CanonicalResourcePrefix">
            <summary>
            Used for Amazon S3 requests where the bucket name is removed from
            the marshalled resource path into the host header. To comply with
            AWS2 signature calculation, we need to recover the bucket name
            and include it in the resource canonicalization, which we do using
            this field.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.UseSigV4">
            <summary>
            This flag specifies if SigV4 is required for the current request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.AuthenticationRegion">
            <summary>
            The authentication region to use for the request.
            Set from Config.AuthenticationRegion.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRequest.DeterminedSigningRegion">
            <summary>
            The region in which the service request was signed.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.IRequest.IsRequestStreamRewindable">
            <summary>
            Checks if the request stream can be rewinded.
            </summary>
            <returns>Returns true if the request stream can be rewinded ,
            else false.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.IRequest.MayContainRequestBody">
            <summary>
            Returns true if the request can contain a request body, else false.
            </summary>
            <returns>Returns true if the currect request can contain a request body, else false.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.IRequest.HasRequestBody">
            <summary>
            Returns true if the request has a body, else false.
            </summary>
            <returns>Returns true if the request has a body, else false.</returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.IServiceMetadata.ServiceId">
            <summary>
            Gets the value of the Service Id.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IServiceMetadata.OperationNameMapping">
            <summary>
            Gets the dictionary that gives mapping of renamed operations
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.ParametersDictionaryFacade">
            <summary>
            IDictionary{String, String} access to ParameterCollection.
            TODO: remove this class in version 3.4 of the SDK.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.ParametersDictionaryFacade.#ctor(Amazon.Runtime.Internal.ParameterCollection)">
            <summary>
            Constructs ParametersDictionaryFacade for a ParameterCollection
            </summary>
            <param name="collection"></param>
        </member>
        <member name="T:Amazon.Runtime.Internal.RuntimePipelineCustomizerRegistry">
            <summary>
            A registry of object that will manipulate the runtime pipeline used by service clients.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipelineCustomizerRegistry.Register(Amazon.Runtime.Internal.IRuntimePipelineCustomizer)">
            <summary>
            Registers a customizer that will be applied for all service clients created. Each customizer has a unique name associated with it. If a customizer is registered more 
            than once with the same unique name then the calls after the first will be ignored.
            </summary>
            <param name="customizer"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipelineCustomizerRegistry.Deregister(Amazon.Runtime.Internal.IRuntimePipelineCustomizer)">
            <summary>
            Deregistered the runtime pipeline customizer
            </summary>
            <param name="customizer"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipelineCustomizerRegistry.Deregister(System.String)">
            <summary>
            Deregistered the runtime pipeline customizer
            </summary>
            <param name="uniqueName"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipelineCustomizerRegistry.ApplyCustomizations(System.Type,Amazon.Runtime.Internal.RuntimePipeline)">
            <summary>
            Applies all of the registered customizers on the runtime pipeline
            </summary>
            <param name="pipeline">The service clients runtime pipeline.</param>
            <param name="type">Type object for the service client being created</param>
        </member>
        <member name="T:Amazon.Runtime.Internal.IRuntimePipelineCustomizer">
            <summary>
            Interface for objects that will customize the runtime pipleine for newly created service clients.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.IRuntimePipelineCustomizer.UniqueName">
            <summary>
            The unique name for the customizer that identifies the customizer in the registry. The name is also used to identify the customizer on the SDK logs.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.IRuntimePipelineCustomizer.Customize(System.Type,Amazon.Runtime.Internal.RuntimePipeline)">
            <summary>
            Called on service clients as they are being constructed to customize their runtime pipeline.
            </summary>
            <param name="pipeline"></param>
            <param name="type">Type object for the service client being created</param>
        </member>
        <member name="T:Amazon.Runtime.Internal.ServiceMetadata">
            <summary>
            Default ServiceMetadata implementation.
            This implementation will be used if the service doesn't have a
            IServiceMetadata implementation.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.ServiceMetadata.ServiceId">
            <summary>
            Gets the value of the Service Id.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.ServiceMetadata.OperationNameMapping">
            <summary>
            Gets the dictionary that gives mapping of renamed operations
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AbstractAWSSigner.ComputeHash(System.String,System.String,Amazon.Runtime.SigningAlgorithm)">
            <summary>
            Computes RFC 2104-compliant HMAC signature.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AbstractAWSSigner.ComputeHash(System.Byte[],System.String,Amazon.Runtime.SigningAlgorithm)">
            <summary>
            Computes RFC 2104-compliant HMAC signature.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AbstractAWSSigner.UseV4Signing(System.Boolean,Amazon.Runtime.Internal.IRequest,Amazon.Runtime.IClientConfig)">
            <summary>
            Inspects the supplied evidence to return the signer appropriate for the operation
            </summary>
            <param name="useSigV4Setting">Global setting for the service</param>
            <param name="request">The request.</param>
            <param name="config">Configuration for the client</param>
            <returns>True if signature v4 request signing should be used</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS3Signer.Sign(Amazon.Runtime.Internal.IRequest,Amazon.Runtime.IClientConfig,Amazon.Runtime.Internal.Util.RequestMetrics,System.String,System.String)">
            <summary>
            Signs the specified request with the AWS3 signing protocol by using the
            AWS account credentials given in the method parameters.
            </summary>
            <param name="awsAccessKeyId">The AWS public key</param>
            <param name="awsSecretAccessKey">The AWS secret key used to sign the request in clear text</param>
            <param name="metrics">Request metrics</param>
            <param name="clientConfig">The configuration that specifies which hashing algorithm to use</param>
            <param name="request">The request to have the signature compute for</param>
            <exception cref="T:Amazon.Runtime.SignatureException">If any problems are encountered while signing the request</exception>
        </member>
        <member name="T:Amazon.Runtime.Internal.Auth.AWS4Signer">
            <summary>
            AWS4 protocol signer for service calls that transmit authorization in the header field "Authorization".
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.Sign(Amazon.Runtime.Internal.IRequest,Amazon.Runtime.IClientConfig,Amazon.Runtime.Internal.Util.RequestMetrics,System.String,System.String)">
            <summary>
            Calculates and signs the specified request using the AWS4 signing protocol by using the
            AWS account credentials given in the method parameters. The resulting signature is added
            to the request headers as 'Authorization'. Parameters supplied in the request, either in
            the resource path as a query string or in the Parameters collection must not have been
            uri encoded. If they have, use the SignRequest method to obtain a signature.
            </summary>
            <param name="request">
            The request to compute the signature for. Additional headers mandated by the AWS4 protocol 
            ('host' and 'x-amz-date') will be added to the request before signing.
            </param>
            <param name="clientConfig">
            Client configuration data encompassing the service call (notably authentication
            region, endpoint and service name).
            </param>
            <param name="metrics">
            Metrics for the request
            </param>
            <param name="awsAccessKeyId">
            The AWS public key for the account making the service call.
            </param>
            <param name="awsSecretAccessKey">
            The AWS secret key for the account making the call, in clear text.
            </param>
            <exception cref="T:Amazon.Runtime.SignatureException">
            If any problems are encountered while signing the request.
            </exception>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.SignRequest(Amazon.Runtime.Internal.IRequest,Amazon.Runtime.IClientConfig,Amazon.Runtime.Internal.Util.RequestMetrics,System.String,System.String)">
            <summary>
            Calculates and signs the specified request using the AWS4 signing protocol by using the
            AWS account credentials given in the method parameters.
            </summary>
            <param name="request">
            The request to compute the signature for. Additional headers mandated by the AWS4 protocol 
            ('host' and 'x-amz-date') will be added to the request before signing.
            </param>
            <param name="clientConfig">
            Client configuration data encompassing the service call (notably authentication
            region, endpoint and service name).
            </param>
            <param name="metrics">
            Metrics for the request.
            </param>
            <param name="awsAccessKeyId">
            The AWS public key for the account making the service call.
            </param>
            <param name="awsSecretAccessKey">
            The AWS secret key for the account making the call, in clear text.
            </param>
            <exception cref="T:Amazon.Runtime.SignatureException">
            If any problems are encountered while signing the request.
            </exception>
            <remarks>
            Parameters passed as part of the resource path should be uri-encoded prior to
            entry to the signer. Parameters passed in the request.Parameters collection should
            be not be encoded; encoding will be done for these parameters as part of the 
            construction of the canonical request.
            </remarks>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.InitializeHeaders(System.Collections.Generic.IDictionary{System.String,System.String},System.Uri)">
            <summary>
            Sets the AWS4 mandated 'host' and 'x-amz-date' headers, returning the date/time that will
            be used throughout the signing process in various elements and formats.
            </summary>
            <param name="headers">The current set of headers</param>
            <param name="requestEndpoint"></param>
            <returns>Date and time used for x-amz-date, in UTC</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.InitializeHeaders(System.Collections.Generic.IDictionary{System.String,System.String},System.Uri,System.DateTime)">
            <summary>
            Sets the AWS4 mandated 'host' and 'x-amz-date' headers, accepting and returning the date/time that will
            be used throughout the signing process in various elements and formats.
            </summary>
            <param name="headers">The current set of headers</param>
            <param name="requestEndpoint"></param>
            <param name="requestDateTime"></param>
            <returns>Date and time used for x-amz-date, in UTC</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.ComputeSignature(Amazon.Runtime.ImmutableCredentials,System.String,System.DateTime,System.String,System.String,System.String)">
            <summary>
            Computes and returns an AWS4 signature for the specified canonicalized request
            </summary>
            <param name="credentials"></param>
            <param name="region"></param>
            <param name="signedAt"></param>
            <param name="service"></param>
            <param name="signedHeaders"></param>
            <param name="canonicalRequest"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.ComputeSignature(System.String,System.String,System.String,System.DateTime,System.String,System.String,System.String)">
            <summary>
            Computes and returns an AWS4 signature for the specified canonicalized request
            </summary>
            <param name="awsAccessKey"></param>
            <param name="awsSecretAccessKey"></param>
            <param name="region"></param>
            <param name="signedAt"></param>
            <param name="service"></param>
            <param name="signedHeaders"></param>
            <param name="canonicalRequest"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.ComputeSignature(System.String,System.String,System.String,System.DateTime,System.String,System.String,System.String,Amazon.Runtime.Internal.Util.RequestMetrics)">
            <summary>
            Computes and returns an AWS4 signature for the specified canonicalized request
            </summary>
            <param name="awsAccessKey"></param>
            <param name="awsSecretAccessKey"></param>
            <param name="region"></param>
            <param name="signedAt"></param>
            <param name="service"></param>
            <param name="signedHeaders"></param>
            <param name="canonicalRequest"></param>
            <param name="metrics"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.FormatDateTime(System.DateTime,System.String)">
            <summary>
            Formats the supplied date and time for use in AWS4 signing, where various formats are used.
            </summary>
            <param name="dt"></param>
            <param name="formatString">The required format</param>
            <returns>The UTC date/time in the requested format</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.ComposeSigningKey(System.String,System.String,System.String,System.String)">
            <summary>
            Compute and return the multi-stage signing key for the request.
            </summary>
            <param name="awsSecretAccessKey">The clear-text AWS secret key, if not held in secureKey</param>
            <param name="region">The region in which the service request will be processed</param>
            <param name="date">Date of the request, in yyyyMMdd format</param>
            <param name="service">The name of the service being called by the request</param>
            <returns>Computed signing key</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.SetRequestBodyHash(Amazon.Runtime.Internal.IRequest)">
            <summary>
            If the caller has already set the x-amz-content-sha256 header with a pre-computed
            content hash, or it is present as ContentStreamHash on the request instance, return
            the value to be used in request canonicalization. 
            If not set as a header or in the request, attempt to compute a hash based on
            inspection of the style of the request content.
            </summary>
            <param name="request"></param>
            <returns>
            The computed hash, whether already set in headers or computed here. Null
            if we were not able to compute a hash.
            </returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.SetRequestBodyHash(Amazon.Runtime.Internal.IRequest,System.Boolean)">
            <summary>
            If signPayload is false set the x-amz-content-sha256 header to
            the UNSIGNED-PAYLOAD magic string and return it.
            Otherwise, if the caller has already set the x-amz-content-sha256 header with a pre-computed
            content hash, or it is present as ContentStreamHash on the request instance, return
            the value to be used in request canonicalization.
            If not set as a header or in the request, attempt to compute a hash based on
            inspection of the style of the request content.
            </summary>
            <param name="request"></param>
            <param name="signPayload"></param>
            <returns>
            The computed hash, whether already set in headers or computed here. Null
            if we were not able to compute a hash.
            </returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.SignBlob(System.Byte[],System.String)">
            <summary>
            Returns the HMAC256 for an arbitrary blob using the specified key
            </summary>
            <param name="key"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.SignBlob(System.Byte[],System.Byte[])">
            <summary>
            Returns the HMAC256 for an arbitrary blob using the specified key
            </summary>
            <param name="key"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.ComputeKeyedHash(Amazon.Runtime.SigningAlgorithm,System.Byte[],System.String)">
            <summary>
            Compute and return the hash of a data blob using the specified key
            </summary>
            <param name="algorithm">Algorithm to use for hashing</param>
            <param name="key">Hash key</param>
            <param name="data">Data blob</param>
            <returns>Hash of the data</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.ComputeKeyedHash(Amazon.Runtime.SigningAlgorithm,System.Byte[],System.Byte[])">
            <summary>
            Compute and return the hash of a data blob using the specified key
            </summary>
            <param name="algorithm">Algorithm to use for hashing</param>
            <param name="key">Hash key</param>
            <param name="data">Data blob</param>
            <returns>Hash of the data</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.ComputeHash(System.String)">
            <summary>
            Computes the non-keyed hash of the supplied data
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.ComputeHash(System.Byte[])">
            <summary>
            Computes the non-keyed hash of the supplied data
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.CanonicalizeRequest(System.Uri,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.String,System.String)">
            <summary>
            Computes and returns the canonical request
            </summary>
            <param name="endpoint">The endpoint URL</param>
            <param name="resourcePath">the path of the resource being operated on</param>
            <param name="httpMethod">The http method used for the request</param>
            <param name="sortedHeaders">The full request headers, sorted into canonical order</param>
            <param name="canonicalQueryString">The query parameters for the request</param>
            <param name="precomputedBodyHash">
            The hash of the binary request body if present. If not supplied, the routine
            will look for the hash as a header on the request.
            </param>
            <returns>Canonicalised request as a string</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.SortAndPruneHeaders(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Reorders the headers for the request for canonicalization.
            </summary>
            <param name="requestHeaders">The set of proposed headers for the request</param>
            <returns>List of headers that must be included in the signature</returns>
            <remarks>For AWS4 signing, all headers are considered viable for inclusion</remarks>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.CanonicalizeHeaders(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Computes the canonical headers with values for the request. Only headers included in the signature
            are included in the canonicalization process.
            </summary>
            <param name="sortedHeaders">All request headers, sorted into canonical order</param>
            <returns>Canonicalized string of headers, with the header names in lower case.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.CanonicalizeHeaderNames(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Returns the set of headers included in the signature as a flattened, ;-delimited string
            </summary>
            <param name="sortedHeaders">The headers included in the signature</param>
            <returns>Formatted string of header names</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.GetParametersToCanonicalize(Amazon.Runtime.Internal.IRequest)">
            <summary>
            Collects the subresource and query string parameters into one collection
            ready for canonicalization
            </summary>
            <param name="request">The in-flight request being signed</param>
            <returns>The fused set of parameters</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.CanonicalizeQueryParameters(System.String,System.Boolean)">
            <summary>
            Computes and returns the canonicalized query string, if query parameters have been supplied.
            Parameters with no value will be canonicalized as 'param='. The expectation is that parameters
            have not already been url encoded prior to canonicalization.
            </summary>
            <param name="queryString">The set of parameters being passed on the uri</param>
            <param name="uriEncodeParameters">
            Parameters must be uri encoded into the canonical request and by default the signer expects
            that the supplied collection contains non-encoded data. Set this to false if the encoding was
            done prior to signer entry.
            </param>
            <returns>The uri encoded query string parameters in canonical ordering</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.CanonicalizeQueryParameters(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}},System.Boolean)">
            <summary>
            Computes and returns the canonicalized query string, if query parameters have been supplied.
            Parameters with no value will be canonicalized as 'param='. The expectation is that parameters
            have not already been url encoded prior to canonicalization.
            </summary>
            <param name="parameters">The set of parameters to be encoded in the query string</param>
            <param name="uriEncodeParameters">
            Parameters must be uri encoded into the canonical request and by default the signer expects
            that the supplied collection contains non-encoded data. Set this to false if the encoding was
            done prior to signer entry.
            </param>
            <returns>The uri encoded query string parameters in canonical ordering</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4Signer.GetRequestPayloadBytes(Amazon.Runtime.Internal.IRequest)">
            <summary>
            Returns the request parameters in the form of a query string.
            </summary>
            <param name="request">The request instance</param>
            <returns>Request parameters in query string format</returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Auth.AWS4PreSignedUrlSigner">
            <summary>
            AWS4 protocol signer for Amazon S3 presigned urls.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4PreSignedUrlSigner.Sign(Amazon.Runtime.Internal.IRequest,Amazon.Runtime.IClientConfig,Amazon.Runtime.Internal.Util.RequestMetrics,System.String,System.String)">
            <summary>
            Calculates and signs the specified request using the AWS4 signing protocol by using the
            AWS account credentials given in the method parameters. The resulting signature is added
            to the request headers as 'Authorization'.
            </summary>
            <param name="request">
            The request to compute the signature for. Additional headers mandated by the AWS4 protocol 
            ('host' and 'x-amz-date') will be added to the request before signing.
            </param>
            <param name="clientConfig">
            Adding supporting data for the service call required by the signer (notably authentication
            region, endpoint and service name).
            </param>
            <param name="metrics">
            Metrics for the request
            </param>
            <param name="awsAccessKeyId">
            The AWS public key for the account making the service call.
            </param>
            <param name="awsSecretAccessKey">
            The AWS secret key for the account making the call, in clear text
            </param>
            <exception cref="T:Amazon.Runtime.SignatureException">
            If any problems are encountered while signing the request.
            </exception>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4PreSignedUrlSigner.SignRequest(Amazon.Runtime.Internal.IRequest,Amazon.Runtime.IClientConfig,Amazon.Runtime.Internal.Util.RequestMetrics,System.String,System.String)">
            <summary>
            Calculates the AWS4 signature for a presigned url.
            </summary>
            <param name="request">
            The request to compute the signature for. Additional headers mandated by the AWS4 protocol 
            ('host' and 'x-amz-date') will be added to the request before signing. If the Expires parameter
            is present, it is renamed to 'X-Amz-Expires' before signing.
            </param>
            <param name="clientConfig">
            Adding supporting data for the service call required by the signer (notably authentication
            region, endpoint and service name).
            </param>
            <param name="metrics">
            Metrics for the request
            </param>
            <param name="awsAccessKeyId">
            The AWS public key for the account making the service call.
            </param>
            <param name="awsSecretAccessKey">
            The AWS secret key for the account making the call, in clear text
            </param>
            <exception cref="T:Amazon.Runtime.SignatureException">
            If any problems are encountered while signing the request.
            </exception>
            <remarks>
            Parameters passed as part of the resource path should be uri-encoded prior to
            entry to the signer. Parameters passed in the request.Parameters collection should
            be not be encoded; encoding will be done for these parameters as part of the 
            construction of the canonical request.
            </remarks>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4PreSignedUrlSigner.SignRequest(Amazon.Runtime.Internal.IRequest,Amazon.Runtime.IClientConfig,Amazon.Runtime.Internal.Util.RequestMetrics,System.String,System.String,System.String,System.String)">
             <summary>
             Calculates the AWS4 signature for a presigned url.
             </summary>
             <param name="request">
             The request to compute the signature for. Additional headers mandated by the AWS4 protocol 
             ('host' and 'x-amz-date') will be added to the request before signing. If the Expires parameter
             is present, it is renamed to 'X-Amz-Expires' before signing.
             </param>
             <param name="clientConfig">
             Adding supporting data for the service call required by the signer (notably authentication
             region, endpoint and service name).
             </param>
             <param name="metrics">
             Metrics for the request
             </param>
             <param name="awsAccessKeyId">
             The AWS public key for the account making the service call.
             </param>
             <param name="awsSecretAccessKey">
             The AWS secret key for the account making the call, in clear text
             </param>
             <param name="service">
             The service to sign for
             </param>
             <param name="overrideSigningRegion">
             The region to sign to, if null then the region the client is configured for will be used.
             </param>
             <exception cref="T:Amazon.Runtime.SignatureException">
             If any problems are encountered while signing the request.
             </exception>
             <remarks>
             Parameters passed as part of the resource path should be uri-encoded prior to
             entry to the signer. Parameters passed in the request.Parameters collection should
             be not be encoded; encoding will be done for these parameters as part of the 
             construction of the canonical request.
            
             The X-Amz-Content-SHA256 is cleared out of the request.
             If the request is for S3 then the UNSIGNED_PAYLOAD value is used to generate the canonical request.
             If the request isn't for S3 then the empty body SHA is used to generate the canonical request.
             </remarks>
        </member>
        <member name="T:Amazon.Runtime.Internal.Auth.AWS4SigningResult">
            <summary>
            Encapsulates the various fields and eventual signing value that makes up 
            an AWS4 signature. This can be used to retrieve the required authorization string
            or authorization query parameters for the final request as well as hold ongoing
            signature computations for subsequent calls related to the initial signing.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.AWS4SigningResult.#ctor(System.String,System.DateTime,System.String,System.String,System.Byte[],System.Byte[])">
            <summary>
            Constructs a new signing result instance for a computed signature
            </summary>
            <param name="awsAccessKeyId">The access key that was included in the signature</param>
            <param name="signedAt">Date/time (UTC) that the signature was computed</param>
            <param name="signedHeaders">The collection of headers names that were included in the signature</param>
            <param name="scope">Formatted 'scope' value for signing (YYYYMMDD/region/service/aws4_request)</param>
            <param name="signingKey">Returns the key that was used to compute the signature</param>
            <param name="signature">Computed signature</param>
        </member>
        <member name="P:Amazon.Runtime.Internal.Auth.AWS4SigningResult.AccessKeyId">
            <summary>
            The access key that was used in signature computation.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Auth.AWS4SigningResult.ISO8601DateTime">
            <summary>
            ISO8601 formatted date/time that the signature was computed
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Auth.AWS4SigningResult.ISO8601Date">
            <summary>
            ISO8601 formatted date that the signature was computed
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Auth.AWS4SigningResult.SignedHeaders">
            <summary>
            The ;-delimited collection of header names that were included in the signature computation
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Auth.AWS4SigningResult.Scope">
            <summary>
            Formatted 'scope' value for signing (YYYYMMDD/region/service/aws4_request)
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Auth.AWS4SigningResult.SigningKey">
            <summary>
            Returns a copy of the key that was used to compute the signature
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Auth.AWS4SigningResult.Signature">
            <summary>
            Returns the hex string representing the signature
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Auth.AWS4SigningResult.SignatureBytes">
            <summary>
            Returns a copy of the byte array containing the signature
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Auth.AWS4SigningResult.ForAuthorizationHeader">
            <summary>
            Returns the signature in a form usable as an 'Authorization' header value.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Auth.AWS4SigningResult.ForQueryParameters">
            <summary>
            Returns the signature in a form usable as a set of query string parameters.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Auth.NullSigner">
            <summary>
            Null Signer which does a no-op.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.QueryStringSigner.Sign(Amazon.Runtime.Internal.IRequest,Amazon.Runtime.IClientConfig,Amazon.Runtime.Internal.Util.RequestMetrics,System.String,System.String)">
            <summary>
            Signs the specified request with the AWS2 signing protocol by using the
            AWS account credentials given in the method parameters.
            </summary>
            <param name="awsAccessKeyId">The AWS public key</param>
            <param name="awsSecretAccessKey">The AWS secret key used to sign the request in clear text</param>
            <param name="metrics">Request metrics</param>
            <param name="clientConfig">The configuration that specifies which hashing algorithm to use</param>
            <param name="request">The request to have the signature compute for</param>
            <exception cref="T:Amazon.Runtime.SignatureException">If any problems are encountered while signing the request</exception>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.S3Signer.#ctor">
            <summary>
            S3 signer constructor
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Auth.S3Signer.#ctor(System.Boolean,Amazon.Runtime.Internal.Auth.S3Signer.RegionDetectionUpdater)">
            <summary>
            S3 signer constructor
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Auth.SignatureException">
            <summary>
            This exception is thrown if there are problems signing the request.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.ErrorResponseUnmarshaller">
            <summary>
               Response Unmarshaller for all Errors
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.ErrorResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Build an ErrorResponse from XML 
            </summary>
            <param name="context">The XML parsing context. 
            Usually an <c>Amazon.Runtime.Internal.UnmarshallerContext</c>.</param>
            <returns>An <c>ErrorResponse</c> object.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.ErrorResponseUnmarshaller.GetInstance">
            <summary>
            Return an instance of and ErrorResponseUnmarshaller.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.IResponseUnmarshaller`2">
            <summary>
            Interface for unmarshallers which unmarshall service responses.
            The Unmarshallers are stateless, and only encode the rules for what data 
            in the XML stream goes into what members of an object. 
            </summary>
            <typeparam name="T">The type of object the unmarshaller returns</typeparam>
            <typeparam name="R">The type of the XML unmashaller context, which contains the
            state of parsing the XML stream. Uaually an instance of 
            <c>Amazon.Runtime.Internal.Transform.UnmarshallerContext</c>.</typeparam>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.IResponseUnmarshaller`2.UnmarshallException(`1,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Extracts an exeption with data from an ErrorResponse.
            </summary>
            <param name="input">The XML parsing context.</param>
            <param name="innerException">An inner exception to be included with the returned exception</param>
            <param name="statusCode">The HttpStatusCode from the ErrorResponse</param>
            <returns>Either an exception based on the ErrorCode from the ErrorResponse, or the 
            general service exception for the service in question.</returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.IUnmarshaller`2">
            <summary>
            Interface for unmarshallers which unmarshall objects from response data.
            The Unmarshallers are stateless, and only encode the rules for what data 
            in the XML stream goes into what members of an object. 
            </summary>
            <typeparam name="T">The type of object the unmarshaller returns</typeparam>
            <typeparam name="R">The type of the XML unmashaller context, which contains the
            state during parsing of the XML stream. Usually an instance of 
            <c>Amazon.Runtime.Internal.Transform.UnmarshallerContext</c>.</typeparam>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.IUnmarshaller`2.Unmarshall(`1)">
            <summary>
            Given the current position in the XML stream, extract a T.
            </summary>
            <param name="input">The XML parsing context</param>
            <returns>An object of type T populated with data from the XML stream.</returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.JsonErrorResponseUnmarshaller">
            <summary>
               Response Unmarshaller for all Errors
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.JsonErrorResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext)">
            <summary>
            Build an ErrorResponse from json 
            </summary>
            <param name="context">The json parsing context. 
            Usually an <c>Amazon.Runtime.Internal.JsonUnmarshallerContext</c>.</param>
            <returns>An <c>ErrorResponse</c> object.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.JsonErrorResponseUnmarshaller.GetInstance">
            <summary>
            Return an instance of JsonErrorResponseUnmarshaller.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext">
            <summary>
            Wraps a json string for unmarshalling.
            
            Each <c>Read()</c> operation gets the next token.
            <c>TestExpression()</c> is used to match the current key-chain
            to an xpath expression. The general pattern looks like this:
            <code>
            JsonUnmarshallerContext context = new JsonUnmarshallerContext(jsonString);
            while (context.Read())
            {
                if (context.IsKey)
                {
                    if (context.TestExpresion("path/to/element"))
                    {
                        myObject.stringMember = stringUnmarshaller.GetInstance().Unmarshall(context);
                        continue;
                    }
                }
            }
            </code>
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.#ctor(System.IO.Stream,System.Boolean,Amazon.Runtime.Internal.Transform.IWebResponseData)">
            <summary>
            Wrap the jsonstring for unmarshalling.
            </summary>
            <param name="responseStream">Stream that contains the JSON for unmarshalling</param>
            <param name="maintainResponseBody"> If set to true, maintains a copy of the complete response body as the stream is being read.</param>
            <param name="responseData">Response data coming back from the request</param>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.IsStartOfDocument">
            <summary>
            Are we at the start of the json document.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.IsEndElement">
            <summary>
            Is the current token the end of an object
            </summary>    
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.IsStartElement">
            <summary>
            Is the current token the start of an object
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.CurrentDepth">
            <summary>
                Returns the element depth of the parser's current position in the json
                document being parsed.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.CurrentPath">
            <summary>
            The current Json path that is being unmarshalled.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.Read">
            <summary>
                Reads to the next token in the json document, and updates the context
                accordingly.
            </summary>
            <returns>
                True if a token was read, false if there are no more tokens to read.
            </returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.Peek(ThirdParty.Json.LitJson.JsonToken)">
            <summary>
            Peeks at the next token. This peek implementation
            reads the next token and makes the subsequent Read() return the same data.
            If Peek is called successively, it will return the same data.
            Only the first one calls Read(), subsequent calls 
            will return the same data until a Read() call is made.
            </summary>
            <param name="token">Token to peek.</param>
            <returns>Returns true if the peeked token matches given token.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.ReadText">
            <summary>
                Returns the text contents of the current token being parsed.
            </summary>
            <returns>
                The text contents of the current token being parsed.
            </returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.CurrentTokenType">
            <summary>
            The type of the current token
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.Stream">
            <summary>
            Get the base stream of the jsonStream.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.Peek">
            <summary>
            Peeks at the next (non-whitespace) character in the jsonStream.
            </summary>
            <returns>The next (non-whitespace) character in the jsonStream, or -1 if at the end.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext.StreamPeek">
            <summary>
            Peeks at the next character in the stream.
            If the data isn't buffered into the StreamReader (Peek() returns -1),
            we flush the buffered data and try one more time.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.ResponseUnmarshaller">
            <summary>
            Abstract class for unmarshalling service responses.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.XmlResponseUnmarshaller">
            <summary>
            Class for unmarshalling XML service responses.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.EC2ResponseUnmarshaller">
            <summary>
            Class for unmarshalling EC2 service responses.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.JsonResponseUnmarshaller">
            <summary>
            Class for unmarshalling JSON service responses.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.IntUnmarshaller">
            <summary>
            Unmarshaller for int fields
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.NullableIntUnmarshaller">
            <summary>
            Unmarshaller for nullable int fields. Implemented only for JSON context
            to handle cases where value can be null e.g. {'Priority': null}.
            This unmarshaller is not implemented for XML context, as XML responses
            will null elements (xsi:nil='true') will be skipped by the XML parser.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.LongUnmarshaller">
            <summary>
            Unmarshaller for long fields
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.FloatUnmarshaller">
            <summary>
            Unmarshaller for float fields
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.DoubleUnmarshaller">
            <summary>
            Unmarshaller for double fields
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.DecimalUnmarshaller">
            <summary>
            Unmarshaller for decimal fields
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.BoolUnmarshaller">
            <summary>
            Unmarshaller for bool fields
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.StringUnmarshaller">
            <summary>
            Unmarshaller for string fields
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.ByteUnmarshaller">
            <summary>
            Unmarshaller for byte fields
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.DateTimeUnmarshaller">
            <summary>
            Unmarshaller for DateTime fields
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.DateTimeUnmarshaller.UnmarshallInternal(System.String,System.Boolean)">
            <summary>
             Unmarshalls given string as a DateTime. Handles cases where we want to unmarshall 
             as just a DateTime or a nullable Datetime.
            </summary>
            <param name="text">Value to be parsed</param>
            <param name="treatAsNullable">If true, the method will return null if text is null. 
            If false, the method will return default(DateTime), if text is null.</param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.NullableDateTimeUnmarshaller">
            <summary>
            Unmarshaller for nullable DateTime fields. Implemented only for JSON context
            to handle cases where value can be null e.g. {'Priority': null}.
            This unmarshaller is not implemented for XML context, as XML responses
            will null elements (xsi:nil='true') will be skipped by the XML parser.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.MemoryStreamUnmarshaller">
            <summary>
            Unmarshaller for MemoryStream fields
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.ResponseMetadataUnmarshaller">
            <summary>
            Unmarshaller for ResponseMetadata
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.UnmarshallerContext">
            <summary>
            Base class for the UnmarshallerContext objects that are used
            to unmarshall a web-service response.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.UnmarshallerContext.TestExpression(System.String)">
            <summary>
                Tests the specified expression against the current position in the XML
                document </summary>
            <param name="expression">
                The pseudo-XPath expression to test.</param>
            <returns>
                True if the expression matches the current position in the document, 
                false otherwise.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.UnmarshallerContext.TestExpression(System.String,System.Int32)">
            <summary>
                Tests the specified expression against the current position in the XML
                document being parsed, and restricts the expression to matching at the
                specified stack depth. </summary>
            <param name="expression">
                The pseudo-XPath expression to test.</param>
            <param name="startingStackDepth">
                The depth in the stack representing where the expression must
                start matching in order for this method to return true. </param>
            <returns>
                True if the specified expression matches the current position in
                the XML document, starting from the specified depth. </returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.UnmarshallerContext.ReadAtDepth(System.Int32)">
            <summary>
            Reads the next token at depth greater than or equal to target depth.
            </summary>
            <param name="targetDepth">Tokens are read at depth greater than or equal to target depth.</param>
            <returns>True if a token was read and current depth is greater than or equal to target depth.</returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.UnmarshallerContext.CurrentPath">
            <summary>
            The current path that is being unmarshalled.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.UnmarshallerContext.CurrentDepth">
            <summary>
            Returns the element depth of the parser's current position in the
            document being parsed.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.UnmarshallerContext.Read">
            <summary>
            Reads to the next node in the document, and updates the context accordingly.
            </summary>
            <returns>
            True if a node was read, false if there are no more elements to read.
            </returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.UnmarshallerContext.ReadText">
            <summary>
                Returns the text contents of the current element being parsed.
            </summary>
            <returns>
                The text contents of the current element being parsed.
            </returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.UnmarshallerContext.IsStartElement">
            <summary>
            True if <c>NodeType</c> is <c>Element</c>.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.UnmarshallerContext.IsEndElement">
            <summary>
            True if <c>NodeType</c> is <c>EndElement</c>.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.UnmarshallerContext.IsStartOfDocument">
            <summary>
            True if the context is at the start of the document.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.UnmarshallerContext.Dispose(System.Boolean)">
            <summary>
            Implements the Dispose pattern
            </summary>
            <param name="disposing">Whether this object is being disposed via a call to Dispose
            or garbage collected.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.UnmarshallerContext.Dispose">
            <summary>
            Disposes of all managed and unmanaged resources.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext">
            <summary>
            Wrap an <c>XmltextReader</c> for simulating an event stream.
            
            Each <c>Read()</c> operation goes either to the next element or next attribute within
            the current element. <c>TestExpression()</c> is used to match the current event
            to an xpath expression. The general pattern looks like this:
            <code>
            UnmarshallerContext context = new UnmarshallerContext(...);
            while (context.Read())
            {
                if (context.TestExpresion("path/to/element"))
                {
                    myObject.stringMember = stringUnmarshaller.GetInstance().Unmarshall(context);
                    continue;
                }
                if (context.TestExpression("path/to/@attribute"))
                    myObject.MyComplexTypeMember = MyComplexTypeUnmarshaller.GetInstance().Unmarshall(context);
            }
            </code>
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext.#ctor(System.IO.Stream,System.Boolean,Amazon.Runtime.Internal.Transform.IWebResponseData)">
            <summary>
            Wrap an XmlTextReader with state for event-based parsing of an XML stream.
            </summary>
            <param name="responseStream"><c>Stream</c> with the XML from a service response.</param>
            <param name="maintainResponseBody"> If set to true, maintains a copy of the complete response body as the stream is being read.</param>
            <param name="responseData">Response data coming back from the request</param>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext.CurrentPath">
            <summary>
            The current XML path that is being unmarshalled.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext.CurrentDepth">
            <summary>
                Returns the element depth of the parser's current position in the XML
                document being parsed.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext.Read">
            <summary>
            Reads to the next node in the XML document, and updates the context accordingly.
            </summary>
            <returns>
            True if a node was read, false if there are no more elements to read./
            </returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext.ReadText">
            <summary>
                Returns the text contents of the current element being parsed.
            </summary>
            <returns>
                The text contents of the current element being parsed.
            </returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext.IsStartElement">
            <summary>
            True if <c>NodeType</c> is <c>Element</c>.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext.IsEndElement">
            <summary>
            True if <c>NodeType</c> is <c>EndElement</c>.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext.IsStartOfDocument">
            <summary>
            True if the context is at the start of the document.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext.IsAttribute">
            <summary>
            True if <c>NodeType</c> is <c>Attribute</c>.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.EC2UnmarshallerContext.#ctor(System.IO.Stream,System.Boolean,Amazon.Runtime.Internal.Transform.IWebResponseData)">
            <summary>
            Wrap an XmlTextReader with state for event-based parsing of an XML stream.
            </summary>
            <param name="responseStream"><c>Stream</c> with the XML from a service response.</param>
            <param name="maintainResponseBody"> If set to true, maintains a copy of the complete response body as the stream is being read.</param>
            <param name="responseData">Response data coming back from the request</param>
        </member>
        <member name="P:Amazon.Runtime.Internal.Transform.EC2UnmarshallerContext.RequestId">
            <summary>
            RequestId value, if found in response
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Transform.EC2UnmarshallerContext.Read">
            <summary>
            Reads to the next node in the XML document, and updates the context accordingly.
            If node is RequestId, reads the contents and stores in RequestId property.
            </summary>
            <returns>
            True if a node was read, false if there are no more elements to read./
            </returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.CachingWrapperStream">
            <summary>
            A stream which caches the contents of the underlying stream as it reads it.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.CachingWrapperStream.AllReadBytes">
            <summary>
            All the bytes read by the stream.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.CachingWrapperStream.#ctor(System.IO.Stream,System.Int32)">
            <summary>
            Initializes the CachingWrapperStream with a base stream.
            </summary>
            <param name="baseStream">The stream to be wrapped.</param>
            <param name="cacheLimit">Maximum number of bytes to be cached.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.CachingWrapperStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a sequence of bytes from the current stream and advances the position
            within the stream by the number of bytes read.
            </summary>
            <param name="buffer">
            An array of bytes. When this method returns, the buffer contains the specified
            byte array with the values between offset and (offset + count - 1) replaced
            by the bytes read from the current source.
            </param>
            <param name="offset">
            The zero-based byte offset in buffer at which to begin storing the data read
            from the current stream.
            </param>
            <param name="count">
            The maximum number of bytes to be read from the current stream.
            </param>
            <returns>
            The total number of bytes read into the buffer. This can be less than the
            number of bytes requested if that many bytes are not currently available,
            or zero (0) if the end of the stream has been reached.
            </returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.CachingWrapperStream.CanSeek">
            <summary>
            Gets a value indicating whether the current stream supports seeking.
            CachingWrapperStream does not support seeking, this will always be false.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.CachingWrapperStream.Position">
            <summary>
            Gets or sets the position within the current stream.
            CachingWrapperStream does not support seeking, attempting to set Position
            will throw NotSupportedException.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.CachingWrapperStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the current stream.
            CachingWrapperStream does not support seeking, attempting to call Seek
            will throw NotSupportedException.
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">
            A value of type System.IO.SeekOrigin indicating the reference point used
            to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.ChunkedUploadWrapperStream">
            <summary>
            Stream wrapper that double-buffers from a wrapped stream and
            returns the buffered content as a series of signed 'chunks'
            for the AWS4 ('Signature V4') protocol.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.ChunkedUploadWrapperStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads some or all of the processed chunk to the consumer, constructing
            and streaming a new chunk if more input data is available.
            </summary>
            <param name="buffer"></param>
            <param name="offset"></param>
            <param name="count"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.ChunkedUploadWrapperStream.HeaderSigningResult">
            <summary>
            Results of the header-signing portion of the request
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.ChunkedUploadWrapperStream.PreviousChunkSignature">
            <summary>
            Computed signature of the chunk prior to the one in-flight, in
            hex
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.ChunkedUploadWrapperStream.ConstructOutputBufferChunk(System.Int32)">
            <summary>
            Computes the derived signature for a chunk of data of given length in the input buffer, 
            placing a formatted chunk with headers, signature and data into the output buffer
            ready for streaming back to the consumer.
            </summary>
            <param name="dataLen"></param>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.ChunkedUploadWrapperStream.Length">
            <summary>
            Length override to return the true length of the payload plus the metainfo
            supplied with each chunk
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.ChunkedUploadWrapperStream.ComputeChunkedContentLength(System.Int64)">
            <summary>
            Computes the total size of the data payload, including the chunk metadata.
            Called externally so as to be able to set the correct Content-Length header
            value.
            </summary>
            <param name="originalLength"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.ChunkedUploadWrapperStream.CalculateChunkHeaderLength(System.Int64)">
            <summary>
            Computes the size of the header data for each chunk.
            </summary>
            <param name="chunkDataSize"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.ChunkedUploadWrapperStream.FillInputBuffer">
            <summary>
            Attempt to read sufficient data for a whole chunk from the wrapped stream,
            returning the number of bytes successfully read to be processed into a chunk
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.AlwaysSendList`1">
            <summary>
            A list object that will always be sent to AWS services,
            even if it is empty.
            The AWS .NET SDK does not send empty collections to services, unless
            the collection is of this type.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.AlwaysSendDictionary`2">
            <summary>
            A dictionary object that will always be sent to AWS services,
            even if it is empty.
            The AWS .NET SDK does not send empty collections to services, unless
            the collection is of this type.
            </summary>
            <typeparam name="TKey"></typeparam>
            <typeparam name="TValue"></typeparam>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.BackgroundDispatcher`1">
            <summary>
            Class to perform actions on a background thread.
            Uses a single background thread and performs actions
            on it in the order the data was sent through the instance.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.BackgroundDispatcher`1.Dispose(System.Boolean)">
            <summary>
            Implements the Dispose pattern
            </summary>
            <param name="disposing">Whether this object is being disposed via a call to Dispose
            or garbage collected.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.BackgroundDispatcher`1.Dispose">
            <summary>
            Disposes of all managed and unmanaged resources.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.BackgroundInvoker">
            <summary>
            Class to invoke actions on a background thread.
            Uses a single background thread and invokes actions
            on it in the order they were invoked through the instance.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.EndianConversionUtility">
            <summary>
            PCL doesn't have IPAddress. These methods are copied from the Microsoft source code.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.Extensions.HasRequestData(Amazon.Runtime.Internal.IRequest)">
            <summary>
            Returns true if the Content is set or there are
            query parameters.
            </summary>
            <param name="request">This request</param>
            <returns>True if data is present; false otherwise.</returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.GuidUtils">
            <summary>
            Utilities for dealing with Guids
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.Hashing.Hash(System.Object[])">
            <summary>
            Hashes a set of objects.
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.Hashing.CombineHashes(System.Int32[])">
            <summary>
            Combines a set of hashses.
            </summary>
            <param name="hashes"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.Hashing.CombineHashesInternal(System.Int32,System.Int32)">
            <summary>
            Combines two hashes.
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.HashingWrapper.Dispose">
            <summary>
            Disposes of all managed and unmanaged resources.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.HashingWrapper.Dispose(System.Boolean)">
            <summary>
            Implements the Dispose pattern
            </summary>
            <param name="disposing">Whether this object is being disposed via a call to Dispose
            or garbage collected.</param>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.HashStream">
            <summary>
            A wrapper stream that calculates a hash of the base stream as it
            is being read.
            The calculated hash is only available after the stream is closed or
            CalculateHash is called. After calling CalculateHash, any further reads
            on the streams will not change the CalculatedHash.
            If an ExpectedHash is specified and is not equal to the calculated hash,
            Close or CalculateHash methods will throw an AmazonClientException.
            If CalculatedHash is calculated for only the portion of the stream that
            is read.
            </summary>
            <exception cref="T:Amazon.Runtime.AmazonClientException">
            Exception thrown during Close() or CalculateHash(), if ExpectedHash is set and
            is different from CalculateHash that the stream calculates, provided that
            CalculatedHash is not a zero-length byte array.
            </exception>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.HashStream.Algorithm">
            <summary>
            Algorithm to use to calculate hash.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.HashStream.FinishedHashing">
            <summary>
            True if hashing is finished and no more hashing should be done;
            otherwise false.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.HashStream.CurrentPosition">
            <summary>
            Current position in the stream.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.HashStream.CalculatedHash">
            <summary>
            Calculated hash for the stream.
            This value is set only after the stream is closed.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.HashStream.ExpectedHash">
            <summary>
            Expected hash value. Compared against CalculatedHash upon Close().
            If the hashes are different, an AmazonClientException is thrown.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.HashStream.ExpectedLength">
            <summary>
            Expected length of stream.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.HashStream.#ctor(System.IO.Stream,System.Byte[],System.Int64)">
            <summary>
            Initializes an HashStream with a hash algorithm and a base stream.
            </summary>
            <param name="baseStream">Stream to calculate hash for.</param>
            <param name="expectedHash">
            Expected hash. Will be compared against calculated hash on stream close.
            Pass in null to disable check.
            </param>
            <param name="expectedLength">
            Expected length of the stream. If the reading stops before reaching this
            position, CalculatedHash will be set to empty array.
            </param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.HashStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a sequence of bytes from the current stream and advances the position
            within the stream by the number of bytes read.
            </summary>
            <param name="buffer">
            An array of bytes. When this method returns, the buffer contains the specified
            byte array with the values between offset and (offset + count - 1) replaced
            by the bytes read from the current source.
            </param>
            <param name="offset">
            The zero-based byte offset in buffer at which to begin storing the data read
            from the current stream.
            </param>
            <param name="count">
            The maximum number of bytes to be read from the current stream.
            </param>
            <returns>
            The total number of bytes read into the buffer. This can be less than the
            number of bytes requested if that many bytes are not currently available,
            or zero (0) if the end of the stream has been reached.
            </returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.HashStream.CanSeek">
            <summary>
            Gets a value indicating whether the current stream supports seeking.
            HashStream does not support seeking, this will always be false.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.HashStream.Position">
            <summary>
            Gets or sets the position within the current stream.
            HashStream does not support seeking, attempting to set Position
            will throw NotSupportedException.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.HashStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the current stream.
            HashStream does not support seeking, attempting to call Seek
            will throw NotSupportedException.
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">
            A value of type System.IO.SeekOrigin indicating the reference point used
            to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.HashStream.Length">
            <summary>
            Gets the overridden length used to construct the HashStream
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.HashStream.CalculateHash">
            <summary>
            Calculates the hash for the stream so far and disables any further
            hashing.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.HashStream.Reset">
            <summary>
            Resets the hash stream to starting state.
            Use this if the underlying stream has been modified and needs
            to be rehashed without reconstructing the hierarchy.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.HashStream.ValidateBaseStream">
            <summary>
            Validates the underlying stream.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.HashStream.CompareHashes(System.Byte[],System.Byte[])">
            <summary>
            Compares two hashes (arrays of bytes).
            </summary>
            <param name="expected">Expected hash.</param>
            <param name="actual">Actual hash.</param>
            <returns>
            True if the hashes are identical; otherwise false.
            </returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.HashStream`1">
            <summary>
            A wrapper stream that calculates a hash of the base stream as it
            is being read or written.
            The calculated hash is only available after the stream is closed or
            CalculateHash is called. After calling CalculateHash, any further reads
            on the streams will not change the CalculatedHash.
            If an ExpectedHash is specified and is not equal to the calculated hash,
            Close or CalculateHash methods will throw an AmazonClientException.
            If base stream's position is not 0 or HashOnReads is true and the entire stream is
            not read, the CalculatedHash will be set to an empty byte array and
            comparison to ExpectedHash will not be made.
            </summary>
            <exception cref="T:Amazon.Runtime.AmazonClientException">
            Exception thrown during Close() or CalculateHash(), if ExpectedHash is set and
            is different from CalculateHash that the stream calculates, provided that
            CalculatedHash is not a zero-length byte array.
            </exception>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.HashStream`1.#ctor(System.IO.Stream,System.Byte[],System.Int64)">
            <summary>
            Initializes an HashStream with a hash algorithm and a base stream.
            </summary>
            <param name="baseStream">Stream to calculate hash for.</param>
            <param name="expectedHash">
            Expected hash. Will be compared against calculated hash on stream close.
            Pass in null to disable check.
            </param>
            <param name="expectedLength">
            Expected length of the stream. If the reading stops before reaching this
            position, CalculatedHash will be set to empty array.
            </param>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.MD5Stream">
            <summary>
            A wrapper stream that calculates an MD5 hash of the base stream as it
            is being read or written.
            The calculated hash is only available after the stream is closed or
            CalculateHash is called. After calling CalculateHash, any further reads
            on the streams will not change the CalculatedHash.
            If an ExpectedHash is specified and is not equal to the calculated hash,
            Close or CalculateHash methods will throw an AmazonClientException.
            If base stream's position is not 0 or HashOnReads is true and the entire stream is
            not read, the CalculatedHash will be set to an empty byte array and
            comparison to ExpectedHash will not be made.
            </summary>
            <exception cref="T:Amazon.Runtime.AmazonClientException">
            Exception thrown during Close() or CalculateHash(), if ExpectedHash is set and
            is different from CalculateHash that the stream calculates, provided that
            CalculatedHash is not a zero-length byte array.
            </exception>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.MD5Stream.#ctor(System.IO.Stream,System.Byte[],System.Int64)">
            <summary>
            Initializes an MD5Stream with a base stream.
            </summary>
            <param name="baseStream">Stream to calculate hash for.</param>
            <param name="expectedHash">
            Expected hash. Will be compared against calculated hash on stream close.
            Pass in null to disable check.
            </param>
            <param name="expectedLength">
            Expected length of the stream. If the reading stops before reaching this
            position, CalculatedHash will be set to empty array.
            </param>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.HostPrefixUtils">
            <summary>
            Utilities for validating label values for host prefix templates
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.HostPrefixUtils.IsValidLabelValue(System.String)">
            <summary>
            Validates a label value
            </summary>        
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalConsoleLogger.Error(System.Exception,System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net Error method.
            </summary>
            <param name="exception"></param>
            <param name="messageFormat"></param>
            <param name="args"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalConsoleLogger.Debug(System.Exception,System.String,System.Object[])">
            <summary>
            Write debug message to Console
            </summary>
            <param name="exception"></param>
            <param name="messageFormat"></param>
            <param name="args"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalConsoleLogger.DebugFormat(System.String,System.Object[])">
            <summary>
            Write debug message to Console
            </summary>
            <param name="message"></param>
            <param name="arguments"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalConsoleLogger.InfoFormat(System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net InfoFormat method.
            </summary>
            <param name="message"></param>
            <param name="arguments"></param>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.Logger">
            <summary>
            This is a dynamic wrapper around log4net so we can avoid log4net being required
            to be distributed with the SDK.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.InternalLogger">
            <summary>
            Abstract logger class, base for any custom/specific loggers.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalLogger.Flush">
            <summary>
            Flushes the logger contents.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.InternalLogger.IsErrorEnabled">
            <summary>
            Simple wrapper around the log4net IsErrorEnabled property.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.InternalLogger.IsDebugEnabled">
            <summary>
            Simple wrapper around the log4net IsDebugEnabled property.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.InternalLogger.IsInfoEnabled">
            <summary>
            Simple wrapper around the log4net IsInfoEnabled property.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalLogger.Error(System.Exception,System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net Error method.
            </summary>
            <param name="exception"></param>
            <param name="messageFormat"></param>
            <param name="args"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalLogger.Debug(System.Exception,System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net Debug method.
            </summary>
            <param name="exception"></param>
            <param name="messageFormat"></param>
            <param name="args"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalLogger.DebugFormat(System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net DebugFormat method.
            </summary>
            <param name="message"></param>
            <param name="arguments"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalLogger.InfoFormat(System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net InfoFormat method.
            </summary>
            <param name="message"></param>
            <param name="arguments"></param>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.InternalLog4netLogger">
            <summary>
            Logger wrapper for reflected log4net logging methods.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalLog4netLogger.loadStatics">
            <summary>
            This should be a one time call to use reflection to find all the types and methods
            needed for the logging API.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.InternalLog4netLogger.IsErrorEnabled">
            <summary>
            Simple wrapper around the log4net IsErrorEnabled property.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalLog4netLogger.Error(System.Exception,System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net Error method.
            </summary>
            <param name="exception"></param>
            <param name="messageFormat"></param>
            <param name="args"></param>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.InternalLog4netLogger.IsDebugEnabled">
            <summary>
            Simple wrapper around the log4net IsDebugEnabled property.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalLog4netLogger.Debug(System.Exception,System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net Debug method.
            </summary>
            <param name="exception"></param>
            <param name="messageFormat"></param>
            <param name="args"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalLog4netLogger.DebugFormat(System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net DebugFormat method.
            </summary>
            <param name="message"></param>
            <param name="arguments"></param>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.InternalLog4netLogger.IsInfoEnabled">
            <summary>
            Simple wrapper around the log4net IsInfoEnabled property.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalLog4netLogger.InfoFormat(System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net InfoFormat method.
            </summary>
            <param name="message"></param>
            <param name="arguments"></param>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.LogMessage">
            <summary>
            A single logged message
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.LogMessage.Args">
            <summary>
            Log message arguments.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.LogMessage.Provider">
            <summary>
            Culture-specific formatting provider.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.LogMessage.Format">
            <summary>
            Log message format.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.LruCache`2">
             <summary>
             a size-limited cache of key value pairs
            
             Once the maximum size has been reached, the least recently
             used pairs will be evicted to make room for any new items.
             </summary>
             <typeparam name="TKey"></typeparam>
             <typeparam name="TValue"></typeparam>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.LruCache`2.MaxEntries">
            <summary>
            the maximum number of entries this LruCache will hold
            before items begin getting evicted
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.LruCache`2.Count">
            <summary>
            the number of items in the cache
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.LruCache`2.#ctor(System.Int32)">
            <summary>
            Construct a new LruCache.
            </summary>
            <param name="maxEntries">maximum number of entries before items are evicted</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.LruCache`2.AddOrUpdate(`0,`1)">
             <summary>
             Add the key/value pair to the cache, or update
             the value if the key already exists.
            
             If the cache is full, evicts the least recently used item.
             </summary>
             <param name="key"></param>
             <param name="value"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.LruCache`2.Evict(`0)">
            <summary>
            Evicts a specific key out of the cache if it exists
            </summary>
            <param name="key">the key to evict from the cache</param>        
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.LruCache`2.TryGetValue(`0,`1@)">
            <summary>
            Try to get the value associated with the key.
            </summary>
            <param name="key">the key to look up</param>
            <param name="value">the value, if the key exists</param>
            <returns>true if there is a value associated with the key, or false if no value is associated with the key</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.LruCache`2.Clear">
            <summary>
            Clear the LruCache of all entries.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.LruList`2">
            <summary>
            Helper class to support LruCache.
            Does not implement the error checking and synchronization that
            would be necessary for it to stand alone.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.LruListItem`2">
            <summary>
            Item to be stored in the LruList
            linked list structure.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.RequestMetrics.Properties">
            <summary>
            Collection of properties being tracked
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.RequestMetrics.Timings">
            <summary>
            Timings for metrics being tracked
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.RequestMetrics.Counters">
            <summary>
            Counters being tracked
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.RequestMetrics.IsEnabled">
            <summary>
            Whether metrics are enabled for the request
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.RequestMetrics.#ctor">
            <summary>
            Constructs an empty, disabled metrics object
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.RequestMetrics.StartEvent(Amazon.Runtime.Metric)">
            <summary>
            Starts timing an event. Logs an exception if an event
            of the same type was started but not stopped.
            </summary>
            <param name="metric"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.RequestMetrics.StopEvent(Amazon.Runtime.Metric)">
            <summary>
            Stops timing an event. Logs an exception if the event wasn't started.
            </summary>
            <param name="metric"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.RequestMetrics.AddProperty(Amazon.Runtime.Metric,System.Object)">
            <summary>
            Adds a property for a metric. If there are multiple, the
            object is added as a new item in a list.
            </summary>
            <param name="metric"></param>
            <param name="property"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.RequestMetrics.SetCounter(Amazon.Runtime.Metric,System.Int64)">
            <summary>
            Sets a counter for a specific metric.
            </summary>
            <param name="metric"></param>
            <param name="value"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.RequestMetrics.IncrementCounter(Amazon.Runtime.Metric)">
            <summary>
            Increments a specific metric counter.
            If counter doesn't exist yet, it is set to 1.
            </summary>
            <param name="metric"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.RequestMetrics.GetErrors">
            <summary>
            Returns errors associated with the metric, including
            if there are still any timing events in-flight.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.RequestMetrics.ToString">
            <summary>
            Returns a string representation of the current metrics.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.RequestMetrics.ToJSON">
            <summary>
            Return a JSON represenation of the current metrics
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.Timing">
            <summary>
            Timing information for a metric
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.Timing.#ctor">
            <summary>
            Empty, stopped timing object
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.Timing.#ctor(System.Int64)">
            <summary>
            Timing object in a started state
            </summary>
            <param name="currentTime"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.Timing.Stop(System.Int64)">
            <summary>
            Stops timing
            </summary>
            <param name="currentTime"></param>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.Timing.IsFinished">
            <summary>
            Whether the timing has been stopped
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.Timing.ElapsedTicks">
            <summary>
            Elapsed ticks from start to stop.
            If timing hasn't been stopped yet, returns 0.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.Timing.ElapsedTime">
            <summary>
            Elapsed time from start to stop.
            If timing hasn't been stopped yet, returns TimeSpan.Zero
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.TimingEvent">
            <summary>
            Timing event, stops timing of a metric when disposed
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.TimingEvent.Dispose(System.Boolean)">
            <summary>
            Implements the Dispose pattern
            </summary>
            <param name="disposing">Whether this object is being disposed via a call to Dispose
            or garbage collected.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.TimingEvent.Dispose">
            <summary>
            Disposes of all managed and unmanaged resources.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.TimingEvent.Finalize">
            <summary>
            The destructor for the client class.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.NonDisposingWrapperStream">
            <summary>
            A wrapper stream which supresses disposal of the underlying stream.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.NonDisposingWrapperStream.#ctor(System.IO.Stream)">
            <summary>
            Constructor for NonDisposingWrapperStream.
            </summary>
            <param name="baseStream">The base stream to wrap.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.NonDisposingWrapperStream.Dispose(System.Boolean)">
            <summary>
            The Dispose implementation for this wrapper stream
            does not close the underlying stream.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.PartialWrapperStream">
            <summary>
            This class is used to wrap a stream for a particular segment of a stream.  It 
            makes that segment look like you are reading from beginning to end of the stream.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.ReadOnlyWrapperStream">
            <summary>
            Wrapper stream that only supports reading
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.PartialReadOnlyWrapperStream">
            <summary>
            Partial wrapper stream that only supports reading
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.S3Uri">
            <summary>
            Uri wrapper that can parse out information (bucket, key, region, style) from an
            S3 URI.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.S3Uri.IsPathStyle">
            <summary>
            True if the URI contains the bucket in the path, false if it contains the bucket in the authority.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.S3Uri.Bucket">
            <summary>
            The bucket name parsed from the URI (or null if no bucket specified).
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.S3Uri.Key">
            <summary>
            The key parsed from the URI (or null if no key specified).
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.S3Uri.Region">
            <summary>
            The region parsed from the URI (or null if no region specified).
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.S3Uri.#ctor(System.String)">
            <summary>
            Constructs a parser for the S3 URI specified as a string.
            </summary>
            <param name="uri">The S3 URI to be parsed.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.S3Uri.#ctor(System.Uri)">
            <summary>
            Constructs a parser for the S3 URI specified as a Uri instance.
            </summary>
            <param name="uri">The S3 URI to be parsed.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.S3Uri.Decode(System.String)">
            <summary>
            Percent-decodes the given string, with a fast path for strings that are not
            percent-encoded.
            </summary>
            <param name="s">The string to decode</param>
            <returns>The decoded string</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.S3Uri.Decode(System.String,System.Int32)">
            <summary>
            Percent-decodes the given string.
            </summary>
            <param name="s">The string to decode</param>
            <param name="firstPercent">The index of the first '%' in the string</param>
            <returns>The decoded string</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.S3Uri.AppendDecoded(System.Text.StringBuilder,System.String,System.Int32)">
            <summary>
            Decodes the percent-encoded character at the given index in the string
            and appends the decoded value to the string under construction.
            </summary>
            <param name="builder">
            The string under construction to which the decoded character will be 
            appended.
            </param>
            <param name="s">The string being decoded.</param>
            <param name="index">The index of the '%' character in the string.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.S3Uri.FromHex(System.Char)">
            <summary>
            Converts a hex character (0-9A-Fa-f) into its corresponding quad value.
            </summary>
            <param name="c">The hex character</param>
            <returns>The quad value</returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.ICache">
            <summary>
            Interface for a non-generic cache.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.ICache.Clear">
            <summary>
            Clears the entire cache.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.ICache.MaximumItemLifespan">
            <summary>
            Maximum time to keep an item around after its last use.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.ICache.CacheClearPeriod">
            <summary>
            How often should the cache be cleared of old items.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.ICache.ItemCount">
            <summary>
            The number of items in the cache.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.ICache`2">
            <summary>
            Interface for a generic cache.
            </summary>
            <typeparam name="TKey"></typeparam>
            <typeparam name="TValue"></typeparam>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.ICache`2.GetValue(`0,System.Func{`0,`1})">
            <summary>
            Retrieves a value out of the cache or from the source.
            </summary>
            <param name="key"></param>
            <param name="creator"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.ICache`2.GetValue(`0,System.Func{`0,`1},System.Boolean@)">
            <summary>
            Retrieves a value out of the cache or from the source.
            If the item was in the cache, isStaleItem is set to true;
            otherwise, if the item comes from the source, isStaleItem is false.
            </summary>
            <param name="key"></param>
            <param name="creator"></param>
            <param name="isStaleItem"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.ICache`2.Clear(`0)">
            <summary>
            Clears a specific value from the cache if it's there.
            </summary>
            <param name="key"></param>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.ICache`2.Keys">
            <summary>
            Returns the keys for all items in the cache.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.ICache`2.UseCache``1(`0,System.Func{``0},System.Action,System.Predicate{System.Exception})">
            <summary>
            Executes specified operation, catches exception, clears the cache for
            the given key, retries the operation.
            </summary>
            <typeparam name="TOut"></typeparam>
            <param name="key"></param>
            <param name="operation"></param>
            <param name="onError"></param>
            <param name="shouldRetryForException"></param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.SdkCache">
            <summary>
            SDK-wide cache.
            Provides access to caches specific to a particular set of credentials
            and target region.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.SdkCache.Clear">
            <summary>
            Clear all caches
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.SdkCache.Clear(System.Object)">
            <summary>
            Clear all caches of a particular type
            </summary>
            <param name="cacheType"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.SdkCache.GetCache``2(System.Object,System.Object,System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Retrieve a cache of a specific type for a client object.
            The client object can be null in cases where a cache does
            not correspond to a specific AWS account or target region.
            </summary>
            <typeparam name="TKey"></typeparam>
            <typeparam name="TValue"></typeparam>
            <param name="client"></param>
            <param name="cacheIdentifier"></param>
            <param name="keyComparer"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.SdkCache.GetCache``2(Amazon.Runtime.AmazonServiceClient,System.Object,System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Retrieve a cache of a specific type for a client object.
            The client object can be null in cases where a cache does
            not correspond to a specific AWS account or target region.
            </summary>
            <typeparam name="TKey"></typeparam>
            <typeparam name="TValue"></typeparam>
            <param name="client"></param>
            <param name="cacheIdentifier"></param>
            <param name="keyComparer"></param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.StringUtils">
            <summary>
            Utilities for converting objects to strings. Used by the marshaller classes.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.StringUtils.FromDateTimeToISO8601(System.DateTime)">
            <summary>
            Converts a DateTime to ISO8601 formatted string.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.StringUtils.FromDateTimeToRFC822(System.DateTime)">
            <summary>
            Converts a DateTime to RFC822 formatted string.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.StringUtils.FromDateTimeToUnixTimestamp(System.DateTime)">
            <summary>
            Converts a DateTime to Unix epoch time formatted string.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.WrapperStream">
            <summary>
            A wrapper stream.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.WrapperStream.BaseStream">
            <summary>
            Base stream.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WrapperStream.#ctor(System.IO.Stream)">
            <summary>
            Initializes WrapperStream with a base stream.
            </summary>
            <param name="baseStream"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WrapperStream.GetNonWrapperBaseStream">
            <summary>
            Returns the first base non-WrapperStream.
            </summary>
            <returns>First base stream that is non-WrapperStream.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WrapperStream.GetSeekableBaseStream">
            <summary>
            Returns the first base non-WrapperStream.
            </summary>
            <returns>First base stream that is non-WrapperStream.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WrapperStream.GetNonWrapperBaseStream(System.IO.Stream)">
            <summary>
            Returns the first base non-WrapperStream.
            </summary>
            <param name="stream">Potential WrapperStream</param>
            <returns>Base non-WrapperStream.</returns>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.WrapperStream.CanRead">
            <summary>
            Gets a value indicating whether the current stream supports reading.
            True if the stream supports reading; otherwise, false.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.WrapperStream.CanSeek">
            <summary>
            Gets a value indicating whether the current stream supports seeking.
            True if the stream supports seeking; otherwise, false.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.WrapperStream.CanWrite">
            <summary>
            Gets a value indicating whether the current stream supports writing.
            True if the stream supports writing; otherwise, false.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WrapperStream.Dispose(System.Boolean)">
            <summary>
            Closes the current stream and releases any resources (such as sockets and
            file handles) associated with the current stream.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.WrapperStream.Length">
            <summary>
            Gets the length in bytes of the stream.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.WrapperStream.Position">
            <summary>
            Gets or sets the position within the current stream.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.WrapperStream.ReadTimeout">
            <summary>
            Gets or sets a value, in miliseconds, that determines how long the stream
            will attempt to read before timing out.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.WrapperStream.WriteTimeout">
            <summary>
            Gets or sets a value, in miliseconds, that determines how long the stream
            will attempt to write before timing out.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WrapperStream.Flush">
            <summary>
            Clears all buffers for this stream and causes any buffered data to be written
            to the underlying device.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WrapperStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a sequence of bytes from the current stream and advances the position
            within the stream by the number of bytes read.
            </summary>
            <param name="buffer">
            An array of bytes. When this method returns, the buffer contains the specified
            byte array with the values between offset and (offset + count - 1) replaced
            by the bytes read from the current source.
            </param>
            <param name="offset">
            The zero-based byte offset in buffer at which to begin storing the data read
            from the current stream.
            </param>
            <param name="count">
            The maximum number of bytes to be read from the current stream.
            </param>
            <returns>
            The total number of bytes read into the buffer. This can be less than the
            number of bytes requested if that many bytes are not currently available,
            or zero (0) if the end of the stream has been reached.
            </returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WrapperStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the current stream.
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">
            A value of type System.IO.SeekOrigin indicating the reference point used
            to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WrapperStream.SetLength(System.Int64)">
            <summary>
            Sets the length of the current stream.
            </summary>
            <param name="value">The desired length of the current stream in bytes.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WrapperStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a sequence of bytes to the current stream and advances the current
            position within this stream by the number of bytes written.
            </summary>
            <param name="buffer">
            An array of bytes. This method copies count bytes from buffer to the current stream.
            </param>
            <param name="offset">
            The zero-based byte offset in buffer at which to begin copying bytes to the
            current stream.
            </param>
            <param name="count">The number of bytes to be written to the current stream.</param>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.AsyncHelpers">
            <summary>
            This is a utility class to be used in last resort for code paths that are synchronous but need to call an asynchronous method.
            This should never be used for methods that are called at high volume as this utility class has a performance cost. For example this 
            class was added for the refreshing credentials like Cognito which would need to use this about once an hour.
            
            This code is taken from: http://stackoverflow.com/questions/5095183/how-would-i-run-an-async-taskt-method-synchronously
            which is licensed under Creative Commons Attribution-ShareAlike 3.0 (http://creativecommons.org/licenses/by-sa/3.0/)
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.AsyncHelpers.RunSync(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Execute's an async Task&lt;T&gt; which has a void return value synchronously
            </summary>
            <param name="task">Task&lt;T&gt; method to execute</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.AsyncHelpers.RunSync``1(System.Func{System.Threading.Tasks.Task{``0}})">
            <summary>
            Execute's an async Task&lt;T&gt; method which has a T return type synchronously
            </summary>
            <typeparam name="T">Return Type</typeparam>
            <param name="task">Task&lt;T&gt; method to execute</param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.WebProxy">
            <summary>
            Custom WebProxy implementation that creates a webproxy based on 
            user inputs(Host name and port number)
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WebProxy.#ctor(System.Uri)">
            <summary>
            Set the ProxyUri
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WebProxy.#ctor(System.String,System.Int32)">
            <summary>
            Create a Uri based on the user inputs(Host name and port number)
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.WebProxy.ProxyUri">
            <summary>
            Proxy Uri property
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.WebProxy.Credentials">
            <summary>
            Proxy Credentials property
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WebProxy.GetProxy(System.Uri)">
            <summary>
            Getter to fetch the set proxy
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.WebProxy.IsBypassed(System.Uri)">
            <summary>
            Method to determine if the proxy should be bypassed when accessing an internet source
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.InternalFileLogger.IsErrorEnabled">
            <summary>
            Error is always enabled
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.InternalFileLogger.IsDebugEnabled">
            <summary>
            Debug logging is always enabled
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.InternalFileLogger.IsInfoEnabled">
            <summary>
            Info logging is always enabled
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalFileLogger.Error(System.Exception,System.String,System.Object[])">
            <summary>
            Prints the Log With Error Level
            </summary>
            <param name="exception"></param>
            <param name="messageFormat"></param>
            <param name="args"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalFileLogger.Debug(System.Exception,System.String,System.Object[])">
            <summary>
            Prints the Log with Debug Level
            </summary>
            <param name="exception"></param>
            <param name="messageFormat"></param>
            <param name="args"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalFileLogger.DebugFormat(System.String,System.Object[])">
            <summary>
            Prints the Log with Debug Level
            </summary>
            <param name="message"></param>
            <param name="arguments"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.InternalFileLogger.InfoFormat(System.String,System.Object[])">
            <summary>
            Prints the Log With Info Level
            </summary>
            <param name="message"></param>
            <param name="arguments"></param>
        </member>
        <member name="T:Amazon.Runtime.Internal.Util.MobileLogger">
            <summary>
            Logger wrapper for Console.WriteLine()
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.MobileLogger.IsErrorEnabled">
            <summary>
            Simple wrapper around the log4net IsErrorEnabled property.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.MobileLogger.Error(System.Exception,System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net Error method.
            </summary>
            <param name="exception"></param>
            <param name="messageFormat"></param>
            <param name="args"></param>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.MobileLogger.IsDebugEnabled">
            <summary>
            Simple wrapper around the log4net IsDebugEnabled property.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.MobileLogger.Debug(System.Exception,System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net Debug method.
            </summary>
            <param name="exception"></param>
            <param name="messageFormat"></param>
            <param name="args"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.MobileLogger.DebugFormat(System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net DebugFormat method.
            </summary>
            <param name="message"></param>
            <param name="arguments"></param>
        </member>
        <member name="P:Amazon.Runtime.Internal.Util.MobileLogger.IsInfoEnabled">
            <summary>
            Simple wrapper around the log4net IsInfoEnabled property.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Util.MobileLogger.InfoFormat(System.String,System.Object[])">
            <summary>
            Simple wrapper around the log4net InfoFormat method.
            </summary>
            <param name="message"></param>
            <param name="arguments"></param>
        </member>
        <member name="T:Amazon.Runtime.Internal.AsyncRunner">
            <summary>
            This class is no longer used anymore and should be avoided as it is just meant
            as a last resort when blending async and sync operations. The code is still
            here to support versions before ******* of AWSSDK.DynamoDBv2.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.RequestContext.IsLastExceptionRetryable">
            <summary>
            Property to denote that the last exception returned by an AWS Service
            was retryable or not.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.PipelineHandler">
            <summary>
            An abstract pipeline handler that has implements IPipelineHandler,
            and has the default implmentation. This is the base class for most of
            the handler implementations.
            </summary>    
        </member>
        <member name="P:Amazon.Runtime.Internal.PipelineHandler.Logger">
            <summary>
            The logger used to log messages.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.PipelineHandler.InnerHandler">
            <summary>
            The inner handler which is called after the current 
            handler completes it's processing.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.PipelineHandler.OuterHandler">
            <summary>
            The outer handler which encapsulates the current handler.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.PipelineHandler.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Contains the processing logic for a synchronous request invocation.
            This method calls InnerHandler.InvokeSync to continue processing of the
            request by the pipeline.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.PipelineHandler.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Contains the processing logic for an asynchronous request invocation.
            This method calls InnerHandler.InvokeSync to continue processing of the
            request by the pipeline.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.PipelineHandler.LogMetrics(Amazon.Runtime.IExecutionContext)">
            <summary>
            Logs the metrics for the current execution context.
            </summary>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
        </member>
        <member name="T:Amazon.Runtime.Internal.RuntimePipeline">
            <summary>
            A runtime pipeline contains a collection of handlers which represent
            different stages of request and response processing.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.RuntimePipeline.Handler">
            <summary>
            The top-most handler in the pipeline.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.#ctor(Amazon.Runtime.IPipelineHandler)">
            <summary>
            Constructor for RuntimePipeline.
            </summary>
            <param name="handler">The handler with which the pipeline is initalized.</param>        
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.#ctor(System.Collections.Generic.IList{Amazon.Runtime.IPipelineHandler})">
            <summary>
            Constructor for RuntimePipeline.
            </summary>        
            <param name="handlers">List of handlers with which the pipeline is initalized.</param>                
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.#ctor(System.Collections.Generic.IList{Amazon.Runtime.IPipelineHandler},Amazon.Runtime.Internal.Util.ILogger)">
            <summary>
            Constructor for RuntimePipeline.
            </summary>        
            <param name="handlers">List of handlers with which the pipeline is initalized.</param>        
            <param name="logger">The logger used to log messages.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.#ctor(Amazon.Runtime.IPipelineHandler,Amazon.Runtime.Internal.Util.ILogger)">
            <summary>
            Constructor for RuntimePipeline.
            </summary>
            <param name="handler">The handler with which the pipeline is initalized.</param>
            <param name="logger">The logger used to log messages.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Invokes the pipeline synchronously.
            </summary>
            <param name="executionContext">Request context</param>
            <returns>Response context</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Invokes the pipeline asynchronously.
            </summary>
            <param name="executionContext">Request context</param>
            <returns>Response context</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.AddHandler(Amazon.Runtime.IPipelineHandler)">
            <summary>
            Adds a new handler to the top of the pipeline.
            </summary>
            <param name="handler">The handler to be added to the pipeline.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.AddHandlerAfter``1(Amazon.Runtime.IPipelineHandler)">
            <summary>
            Adds a handler after the first instance of handler of type T.        
            </summary>
            <typeparam name="T">Type of the handler after which the given handler instance is added.</typeparam>
            <param name="handler">The handler to be added to the pipeline.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.AddHandlerBefore``1(Amazon.Runtime.IPipelineHandler)">
            <summary>
            Adds a handler before the first instance of handler of type T.
            </summary>
            <typeparam name="T">Type of the handler before which the given handler instance is added.</typeparam>
            <param name="handler">The handler to be added to the pipeline.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.RemoveHandler``1">
            <summary>
            Removes the first occurance of a handler of type T.
            </summary>
            <typeparam name="T">Type of the handler which will be removed.</typeparam>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.ReplaceHandler``1(Amazon.Runtime.IPipelineHandler)">
            <summary>
            Replaces the first occurance of a handler of type T with the given handler.
            </summary>
            <typeparam name="T">Type of the handler which will be replaced.</typeparam>
            <param name="handler">The handler to be added to the pipeline.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.InsertHandler(Amazon.Runtime.IPipelineHandler,Amazon.Runtime.IPipelineHandler)">
            <summary>
            Inserts the given handler after current handler in the pipeline.
            </summary>
            <param name="handler">Handler to be inserted in the pipeline.</param>
            <param name="current">Handler after which the given handler is inserted.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.GetInnermostHandler(Amazon.Runtime.IPipelineHandler)">
            <summary>
            Gets the innermost handler by traversing the inner handler till 
            it reaches the last one.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.RuntimePipeline.Handlers">
            <summary>
            Retrieves a list of handlers, in the order of their execution.
            </summary>
            <returns>Handlers in the current pipeline.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.RuntimePipeline.EnumerateHandlers">
            <summary>
            Retrieves current handlers, in the order of their execution.
            </summary>
            <returns>Handlers in the current pipeline.</returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.ErrorHandler">
            <summary>
            This handler processes exceptions thrown from the HTTP handler and
            unmarshalls error responses.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.Internal.ErrorHandler._exceptionHandlers">
            <summary>
            Default set of exception handlers.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.ErrorHandler.ExceptionHandlers">
            <summary>
            Default set of exception handlers.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.ErrorHandler.#ctor(Amazon.Runtime.Internal.Util.ILogger)">
            <summary>
            Constructor for ErrorHandler.
            </summary>
            <param name="logger">an ILogger instance.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.ErrorHandler.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Handles and processes any exception thrown from underlying handlers.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.ErrorHandler.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Handles and processes any exception thrown from underlying handlers.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.ErrorHandler.DisposeReponse(Amazon.Runtime.IResponseContext)">
            <summary>
            Disposes the response body.
            </summary>
            <param name="responseContext">The response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.ErrorHandler.ProcessException(Amazon.Runtime.IExecutionContext,System.Exception)">
            <summary>
            Processes an exception by invoking a matching exception handler
            for the given exception.
            </summary>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <param name="exception">The exception to be processed.</param>
            <returns>
            This method returns a boolean value which indicates if the original exception
            should be rethrown.
            This method can also throw a new exception that may be thrown by exception
            processing by a matching exception handler.
            </returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.ExceptionHandler`1">
            <summary>
            The abstract base class for exception handlers.
            </summary>
            <typeparam name="T">The exception type.</typeparam>
        </member>
        <member name="T:Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler">
            <summary>
            The exception handler for HttpErrorResponseException exception.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.#ctor(Amazon.Runtime.Internal.Util.ILogger)">
            <summary>
            The constructor for HttpErrorResponseExceptionHandler.
            </summary>
            <param name="logger">in instance of ILogger.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleException(Amazon.Runtime.IExecutionContext,Amazon.Runtime.Internal.HttpErrorResponseException)">
            <summary>
            Handles an exception for the given execution context.
            </summary>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <param name="exception">The exception to handle.</param>
            <returns>
            Returns a boolean value which indicates if the original exception
            should be rethrown.
            This method can also throw a new exception to replace the original exception.
            </returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.HttpErrorResponseExceptionHandler.HandleSuppressed404(Amazon.Runtime.IExecutionContext,Amazon.Runtime.Internal.Transform.IWebResponseData)">
            <summary>
            Checks if a HTTP 404 status code is returned which needs to be suppressed and 
            processes it.
            If a suppressed 404 is present, it unmarshalls the response and returns true to 
            indicate that a suppressed 404 was processed, else returns false.
            </summary>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <param name="httpErrorResponse"></param>
            <returns>
            If a suppressed 404 is present, returns true, else returns false.
            </returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.CallbackHandler">
            <summary>
            A pipeline handler which provides hooks to run
            external code in the pre-invoke and post-invoke phases.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.CallbackHandler.OnPreInvoke">
            <summary>
            Action to execute on the pre invoke phase.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.CallbackHandler.OnPostInvoke">
            <summary>
            Action to execute on the post invoke phase.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.CallbackHandler.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Calls the PreInvoke and PostInvoke methods before and after calling the next handler 
            in the pipeline.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.CallbackHandler.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Calls the PreInvoke and PostInvoke methods before and after calling the next handler 
            in the pipeline.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.CallbackHandler.PreInvoke(Amazon.Runtime.IExecutionContext)">
            <summary>
            Executes the OnPreInvoke action as part of pre-invoke.
            </summary>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.CallbackHandler.PostInvoke(Amazon.Runtime.IExecutionContext)">
            <summary>
            Executes the OnPreInvoke action as part of post-invoke.
            </summary>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
        </member>
        <member name="T:Amazon.Runtime.Internal.CredentialsRetriever">
            <summary>
            This handler retrieved the AWS credentials to be used for the current call.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.CredentialsRetriever.#ctor(Amazon.Runtime.AWSCredentials)">
            <summary>
            The constructor for CredentialsRetriever.
            </summary>
            <param name="credentials">An AWSCredentials instance.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.CredentialsRetriever.PreInvoke(Amazon.Runtime.IExecutionContext)">
            <summary>
            Retrieves the credentials to be used for the current call before 
            invoking the next handler.
            </summary>
            <param name="executionContext"></param>
        </member>
        <member name="M:Amazon.Runtime.Internal.CredentialsRetriever.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Calls pre invoke logic before calling the next handler 
            in the pipeline.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.CredentialsRetriever.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Calls pre invoke logic before calling the next handler 
            in the pipeline.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.EndpointDiscoveryHandler">
            <summary>
            This handler resolves the endpoint to be used for the current request.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Calls pre invoke logic before calling the next handler 
            in the pipeline.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.EndpointDiscoveryHandler.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Calls pre invoke logic before calling the next handler 
            in the pipeline.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.EndpointDiscoveryHandler.PreInvoke(Amazon.Runtime.IExecutionContext)">
            <summary>
            Resolves the endpoint to be used for the current request
            before invoking the next handler.
            </summary>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>                
        </member>
        <member name="T:Amazon.Runtime.Internal.EndpointResolver">
            <summary>
            This handler resolves the endpoint to be used for the current request.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.EndpointResolver.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Calls pre invoke logic before calling the next handler 
            in the pipeline.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.EndpointResolver.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Calls pre invoke logic before calling the next handler 
            in the pipeline.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.EndpointResolver.PreInvoke(Amazon.Runtime.IExecutionContext)">
            <summary>
            Resolves the endpoint to be used for the current request
            before invoking the next handler.
            </summary>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.EndpointResolver.DetermineEndpoint(Amazon.Runtime.IRequestContext)">
            <summary>
            Determines the endpoint for the request.
            </summary>
            <param name="requestContext">The request context.</param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.ErrorCallbackHandler">
            <summary>
            This handler provides an OnError action that can be used as hook for
            external code to handle exceptions in the runtime pipeline.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.ErrorCallbackHandler.OnError">
            <summary>
            Action to execute if an exception occurs during the 
            execution of any underlying handlers.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.ErrorCallbackHandler.HandleException(Amazon.Runtime.IExecutionContext,System.Exception)">
            <summary>
            Executes the OnError action if an exception occurs during the 
            execution of any underlying handlers.
            </summary>
            <param name="executionContext"></param>
            <param name="exception"></param>
        </member>
        <member name="T:Amazon.Runtime.Internal.Marshaller">
            <summary>
            This handler marshalls the request before calling invoking the next handler.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Marshaller.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Calls pre invoke logic before calling the next handler 
            in the pipeline.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Marshaller.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Calls pre invoke logic before calling the next handler 
            in the pipeline.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Marshaller.PreInvoke(Amazon.Runtime.IExecutionContext)">
            <summary>
            Marshalls the request before calling invoking the next handler.
            </summary>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
        </member>
        <member name="T:Amazon.Runtime.Internal.MetricsHandler">
            <summary>
            This handler manages the metrics used to time the complete call and
            logs the final metrics.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.MetricsHandler.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Captures the overall execution time and logs final metrics.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.MetricsHandler.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Captures the overall execution time and logs final metrics.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">
            The execution context, it contains the request and response context.
            </param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.RedirectHandler">
            <summary>
            This handler processes HTTP redirects and reissues the call to the
            redirected location.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.RedirectHandler.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Processes HTTP redirects and reissues the call to the
            redirected location.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RedirectHandler.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Processes HTTP redirects and reissues the call to the
            redirected location.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.RedirectHandler.HandleRedirect(Amazon.Runtime.IExecutionContext)">
            <summary>
            Checks if an HTTP 307 (temporary redirect) has occured and changes the 
            request endpoint to the redirected location.
            </summary>
            <param name="executionContext">
            The execution context, it contains the request and response context.
            </param>
            <returns>
            A boolean value that indicates if a redirect has occured.
            </returns>
        </member>
        <member name="T:Amazon.Runtime.Internal.Signer">
            <summary>
            This handler signs the request.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Signer.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Calls pre invoke logic before calling the next handler 
            in the pipeline.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Signer.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Calls pre invoke logic before calling the next handler 
            in the pipeline.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Signer.PreInvoke(Amazon.Runtime.IExecutionContext)">
            <summary>
            Signs the request before invoking the next handler.
            </summary>
            <param name="executionContext">
            The execution context, it contains the request and response context.
            </param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Signer.ShouldSign(Amazon.Runtime.IRequestContext)">
            <summary>
            Determines if the request should be signed.
            </summary>
            <param name="requestContext">The request context.</param>
            <returns>A boolean value that indicated if the request should be signed.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Signer.SignRequest(Amazon.Runtime.IRequestContext)">
            <summary>
            Signs the request.
            </summary>
            <param name="requestContext">The request context.</param>
        </member>
        <member name="T:Amazon.Runtime.Internal.Unmarshaller">
            <summary>
            This handler unmarshalls the HTTP response.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.Unmarshaller.#ctor(System.Boolean)">
            <summary>
            The constructor for Unmarshaller.
            </summary>
            <param name="supportsResponseLogging">
            Boolean value which indicated if the unmarshaller 
            handler supports response logging.
            </param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Unmarshaller.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Unmarshalls the response returned by the HttpHandler.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Unmarshaller.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Unmarshalls the response returned by the HttpHandler.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.Unmarshaller.Unmarshall(Amazon.Runtime.IExecutionContext)">
            <summary>
            Unmarshalls the HTTP response.
            </summary>
            <param name="executionContext">
            The execution context, it contains the request and response context.
            </param>
        </member>
        <member name="M:Amazon.Runtime.Internal.Unmarshaller.UnmarshallAsync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Unmarshalls the HTTP response.
            </summary>
            <param name="executionContext">
            The execution context, it contains the request and response context.
            </param>
        </member>
        <member name="T:Amazon.Runtime.Internal.HttpHandler`1">
            <summary>
            The HTTP handler contains common logic for issuing an HTTP request that is 
            independent of the underlying HTTP infrastructure.
            </summary>
            <typeparam name="TRequestContent"></typeparam>
        </member>
        <member name="P:Amazon.Runtime.Internal.HttpHandler`1.CallbackSender">
            <summary>
            The sender parameter used in any events raised by this handler.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.HttpHandler`1.#ctor(Amazon.Runtime.IHttpRequestFactory{`0},System.Object)">
            <summary>
            The constructor for HttpHandler.
            </summary>
            <param name="requestFactory">The request factory used to create HTTP Requests.</param>
            <param name="callbackSender">The sender parameter used in any events raised by this handler.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.HttpHandler`1.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Issues an HTTP request for the current request context.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.HttpHandler`1.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Issues an HTTP request for the current request context.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.HttpHandler`1.WriteContentToRequestBody(`0,Amazon.Runtime.IHttpRequest{`0},Amazon.Runtime.IRequestContext)">
            <summary>
            Determines the content for request body and uses the HTTP request
            to write the content to the HTTP request body.
            </summary>
            <param name="requestContent">Content to be written.</param>
            <param name="httpRequest">The HTTP request.</param>
            <param name="requestContext">The request context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.HttpHandler`1.CreateWebRequest(Amazon.Runtime.IRequestContext)">
            <summary>
            Creates the HttpWebRequest and configures the end point, content, user agent and proxy settings.
            </summary>
            <param name="requestContext">The async request.</param>
            <returns>The web request that actually makes the call.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.HttpHandler`1.Dispose">
            <summary>
            Disposes the HTTP handler.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.Internal.DefaultRetryPolicy">
            <summary>
            The default implementation of the retry policy.
            </summary>
            <summary>
            The default implementation of the retry policy.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRetryPolicy.MaxBackoffInMilliseconds">
            <summary>
            The maximum value of exponential backoff in milliseconds, which will be used to wait
            before retrying a request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRetryPolicy.HttpStatusCodesToRetryOn">
            <summary>
            List of HTTP Status codes codes which are returned as part of the error response.
            These status codes will be retried.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRetryPolicy.ErrorCodesToRetryOn">
            <summary>
            List of AWS specific error codes which are returned as part of the error response.
            These error codes will be retried.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.DefaultRetryPolicy.WebExceptionStatusesToRetryOn">
            <summary>
            List of WebExceptionStatus for a WebException which will be retried.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.#ctor(System.Int32)">
            <summary>
            Constructor for DefaultRetryPolicy.
            </summary>
            <param name="maxRetries">The maximum number of retries before throwing
            back a exception. This does not count the initial request.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.#ctor(Amazon.Runtime.IClientConfig)">
            <summary>
            Constructor for DefaultRetryPolicy.
            </summary>
            <param name="config">The Client config object. This is used to 
            retrieve the maximum number of retries  before throwing
            back a exception(This does not count the initial request) and
            the service URL for the request.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.CanRetry(Amazon.Runtime.IExecutionContext)">
            <summary>
            Returns true if the request is in a state where it can be retried, else false.
            </summary>
            <param name="executionContext">Request context containing the state of the request.</param>
            <returns>Returns true if the request is in a state where it can be retried, else false.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.RetryForException(Amazon.Runtime.IExecutionContext,System.Exception)">
            <summary>
            Return true if the request should be retried.
            </summary>
            <param name="executionContext">Request context containing the state of the request.</param>
            <param name="exception">The exception thrown by the previous request.</param>
            <returns>Return true if the request should be retried.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.OnRetry(Amazon.Runtime.IExecutionContext)">
            <summary>
            Virtual method that gets called when a retry request is initiated. If retry throttling is
            enabled, the value returned is true if the required capacity is retured, false otherwise. 
            If retry throttling is disabled, true is returned.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.OnRetry(Amazon.Runtime.IExecutionContext,System.Boolean)">
            <summary>
            Virtual method that gets called when a retry request is initiated. If retry throttling is
            enabled, the value returned is true if the required capacity is retured, false otherwise. 
            If retry throttling is disabled, true is returned.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
            <param name="bypassAcquireCapacity">true to bypass any attempt to acquire capacity on a retry</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.NotifySuccess(Amazon.Runtime.IExecutionContext)">
            <summary>
            Virtual method that gets called on a success Response. If its a retry success response, the entire 
            retry acquired capacity is released(default is 5). If its just a success response a lesser value capacity 
            is released(default is 1).
            </summary>
            <param name="executionContext">Request context containing the state of the request.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.RetryForExceptionSync(System.Exception)">
            <summary>
            Perform the processor-bound portion of the RetryForException logic.
            This is shared by the sync, async, and APM versions of the RetryForException method.
            </summary>
            <param name="exception">The exception thrown by the previous request.</param>
            <returns>Return true if the request should be retried.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.RetryForExceptionSync(System.Exception,Amazon.Runtime.IExecutionContext)">
            <summary>
            Perform the processor-bound portion of the RetryForException logic.
            This is shared by the sync, async, and APM versions of the RetryForException method.
            </summary>
            <param name="exception">The exception thrown by the previous request.</param>
            <param name="executionContext">Request context containing the state of the request.</param>
            <returns>Return true if the request should be retried.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.RetryLimitReached(Amazon.Runtime.IExecutionContext)">
            <summary>
            Checks if the retry limit is reached.
            </summary>
            <param name="executionContext">Request context containing the state of the request.</param>
            <returns>Return false if the request can be retried, based on number of retries.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.WaitBeforeRetry(Amazon.Runtime.IExecutionContext)">
            <summary>
            Waits before retrying a request. The default policy implements a exponential backoff.
            </summary>
            <param name="executionContext">Request context containing the state of the request.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.RetryForExceptionAsync(Amazon.Runtime.IExecutionContext,System.Exception)">
            <summary>
            Return true if the request should be retried.
            </summary>
            <param name="executionContext">Request context containing the state of the request.</param>
            <param name="exception">The exception thrown by the previous request.</param>
            <returns>Return true if the request should be retried.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.DefaultRetryPolicy.WaitBeforeRetryAsync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Waits before retrying a request.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="T:Amazon.Runtime.Internal.RetryHandler">
            <summary>
            The retry handler has the generic logic for retrying requests.
            It uses a retry policy which specifies when 
            a retry should be performed.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.RetryHandler.Logger">
            <summary>
            The logger used to log messages.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.Internal.RetryHandler.RetryPolicy">
            <summary>
            The retry policy which specifies when 
            a retry should be performed.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.Internal.RetryHandler.#ctor(Amazon.Runtime.RetryPolicy)">
            <summary>
            Constructor which takes in a retry policy.
            </summary>
            <param name="retryPolicy">Retry Policy</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RetryHandler.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Invokes the inner handler and performs a retry, if required as per the
            retry policy.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.Internal.RetryHandler.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Invokes the inner handler and performs a retry, if required as per the
            retry policy.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="M:Amazon.Runtime.Internal.RetryHandler.PrepareForRetry(Amazon.Runtime.IRequestContext)">
            <summary>
            Prepares the request for retry.
            </summary>
            <param name="requestContext">Request context containing the state of the request.</param>
        </member>
        <member name="T:Amazon.Runtime.ClientConfig">
            <summary>
            This class is the base class of all the configurations settings to connect
            to a service.
            </summary>
            <summary>
            This class is the base class of all the configurations settings to connect
            to a service.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.ServiceVersion">
            <summary>
            Gets Service Version
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.SignatureMethod">
            <summary>
            Gets and sets of the signatureMethod property.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.SignatureVersion">
             <summary>
             Gets and sets of the SignatureVersion property.
            
             Note: This property exists for backward compatibility but is no longer
             used by any service other than S3.
             </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.UserAgent">
            <summary>
            Gets and sets of the UserAgent property.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.RegionEndpoint">
            <summary>
            Gets and sets the RegionEndpoint property.  The region constant that 
            determines the endpoint to use.
            
            Setting this property to null will force the SDK to recalculate the
            RegionEndpoint value based on App/WebConfig, environment variables,
            profile, etc.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.RegionEndpointServiceName">
            <summary>
            The constant used to lookup in the region hash the endpoint.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.ServiceURL">
            <summary>
            Gets and sets of the ServiceURL property.
            This is an optional property; change it
            only if you want to try a different service
            endpoint.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.UseHttp">
            <summary>
            Gets and sets the UseHttp.
            If this property is set to true, the client attempts
            to use HTTP protocol, if the target endpoint supports it.
            By default, this property is set to false.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.ClientConfig.DetermineServiceURL">
            <summary>
            Given this client configuration, return a string form ofthe service endpoint url.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.AuthenticationRegion">
            <summary>
            Gets and sets the AuthenticationRegion property.
            Used in AWS4 request signing, this is an optional property; 
            change it only if the region cannot be determined from the 
            service endpoint.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.AuthenticationServiceName">
            <summary>
            Gets and sets the AuthenticationServiceName property.
            Used in AWS4 request signing, this is the short-form
            name of the service being called.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.MaxErrorRetry">
            <summary>
            Gets and sets of the MaxErrorRetry property.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.LogResponse">
            <summary>
            Gets and sets the LogResponse property.
            If this property is set to true, the service response is logged.
            The size of response being logged is controlled by the AWSConfigs.LoggingConfig.LogResponsesSizeLimit property.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.ReadEntireResponse">
            <summary>
            Gets and sets the ReadEntireResponse property.
            NOTE: This property does not effect response processing and is deprecated.
            To enable response logging, the ClientConfig.LogResponse and AWSConfigs.LoggingConfig
            properties can be used.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.BufferSize">
            <summary>
            Gets and Sets the BufferSize property.
            The BufferSize controls the buffer used to read in from input streams and write 
            out to the request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.ProgressUpdateInterval">
            <summary>
            <para>
            Gets or sets the interval at which progress update events are raised
            for upload operations. By default, the progress update events are 
            raised at every 100KB of data transferred. 
            </para>
            <para>
            If the value of this property is set less than ClientConfig.BufferSize, 
            progress updates events will be raised at the interval specified by ClientConfig.BufferSize.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.ResignRetries">
            <summary>
            Flag on whether to resign requests on retry or not.
            For Amazon S3 and Amazon Glacier this value will always be set to true.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.AllowAutoRedirect">
            <summary>
            This flag controls if .NET HTTP infrastructure should follow redirection
             responses (e.g. HTTP 307 - temporary redirect).
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.LogMetrics">
            <summary>
            Flag on whether to log metrics for service calls.
            
            This can be set in the application's configs, as below:
            <code>
            &lt;?xml version="1.0" encoding="utf-8" ?&gt;
            &lt;configuration&gt;
                &lt;appSettings&gt;
                    &lt;add key="AWSLogMetrics" value"true"/&gt;
                &lt;/appSettings&gt;
            &lt;/configuration&gt;
            </code>
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.DisableLogging">
            <summary>
            Gets and sets the DisableLogging. If true logging for this client will be disabled.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.ProxyCredentials">
            <summary>
            Credentials to use with a proxy.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.Timeout">
            <remarks>
            <para>
            If the value is set, the value is assigned to the Timeout property of the HttpWebRequest/HttpClient object used
            to send requests.
            </para>
            <para>
            Please specify a timeout value only if the operation will not complete within the default intervals
            specified for an HttpWebRequest/HttpClient.
            </para>
            </remarks>
            <exception cref="T:System.ArgumentNullException">The timeout specified is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The timeout specified is less than or equal to zero and is not Infinite.</exception>
            <seealso cref="P:System.Net.HttpWebRequest.Timeout"/>
            <seealso cref="P:System.Net.Http.HttpClient.Timeout"/>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.UseDualstackEndpoint">
            <summary>
            Configures the endpoint calculation for a service to go to a dual stack (ipv6 enabled) endpoint
            for the configured region.
            </summary>
            <remarks>
            Note: AWS services are enabling dualstack endpoints over time. It is your responsibility to check 
            that the service actually supports a dualstack endpoint in the configured region before enabling 
            this option for a service.
            </remarks>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.ThrottleRetries">
            <summary>
            Enable or disable the Retry Throttling feature by setting the ThrottleRetries flag to True/False resepctively.
            Retry Throttling is a feature that intelligently throttles retry attempts when a large precentage of requests 
            are failing and retries are unsuccessful as well. In such situations the allotted retry capacity for the service URL
            will be drained until requests start to succeed again. Once the requisite capacity is available, retries would 
            be permitted again. When retries are throttled, the service enters a fail-fast behaviour as the traditional retry attempt
            for the request would be circumvented. Hence, errors will resurface quickly. This will result in a greated number of exceptions
            but prevents requests being tied up in unsuccessful retry attempts.
            Note: Retry Throttling is enabled by default. Set the ThrottleRetries flag to false to switch off this feature.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.ClientConfig.SetUseNagleIfAvailable(System.Boolean)">
            <summary>
            Enable or disable the Nagle algorithm on the underlying http
            client.
            
            This method is not intended to be called by consumers of the AWS SDK for .NET
            </summary>
            <param name="useNagle"></param>
        </member>
        <member name="M:Amazon.Runtime.ClientConfig.Validate">
            <summary>
            Performs validation on this config object.
            Throws exception if any of the required values are missing/invalid.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.CorrectedUtcNow">
            <summary>
            Returns the current UTC now after clock correction for this endpoint.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.ClockOffset">
            <summary>
            The calculated clock skew correction for a specific endpoint, if there is one.
            This field will be set if a service call resulted in an exception
            and the SDK has determined that there is a difference between local
            and server times.
            
            If <seealso cref="!:CorrectForClockSkew"/> is set to true, this
            value will still be set to the correction, but it will not be used by the
            SDK and clock skew errors will not be retried.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.DisableHostPrefixInjection">
            <summary>
            Gets and sets the DisableHostPrefixInjection flag. If true, host prefix injection will be disabled for this client, the default value of this flag is false. 
            Host prefix injection prefixes the service endpoint with request members from APIs which use this feature. 
            Example: for a hostPrefix of "foo-name." and a endpoint of "service.region.amazonaws.com" the default behavior is to
            prefix the endpoint with the hostPrefix resulting in a final endpoint of "foo-name.service.region.amazonaws.com". Setting 
            DisableHostPrefixInjection to true will disable hostPrefix injection resulting in a final endpoint of
            "service.region.amazonaws.com" regardless of the value of hostPrefix. E.g. You may want to disable host prefix injection for testing against a local mock endpoint.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.EndpointDiscoveryEnabled">
            <summary>
            Returns the flag indicating if endpoint discovery should be enabled or disabled for operations that are not required to use endpoint discovery.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.EndpointDiscoveryCacheLimit">
            <summary>
            Returns the maximum number of discovered endpoints that can be stored within the cache for the client. The default limit is 1000 cache entries.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.ClientConfig.ValidateTimeout(System.Nullable{System.TimeSpan})">
            <summary>
            Throw an exception if the boxed TimeSpan parameter doesn't have a value or is out of range.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.ClientConfig.GetTimeoutValue(System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan})">
            <summary>
            Returns the request timeout value if its value is set, 
            else returns client timeout value.
            </summary>        
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.CacheHttpClient">
            <summary>
            <para>
            This is a switch used for performance testing and is not intended for production applications 
            to change. This switch may be removed in a future version of the SDK as the .NET Core platform matures.
            </para>
            <para>
            If true, the HttpClient is cached and reused for every request made by the service client 
            and shared with other service clients.
            </para>
            <para>
            For the .NET Core platform this is default to true because the HttpClient manages the connection
            pool.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.HttpClientCacheSize">
            <summary>
            If CacheHttpClient is set to true then HttpClientCacheSize controls the number of HttpClients cached.
            <para>
            On Windows the default value is 1 since the underlying native implementation does not have throttling constraints
            like the non Windows Curl based implementation. For non Windows based platforms the default is the value return from 
            System.Environment.ProcessorCount.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.ReadWriteTimeout">
            <summary>
            Overrides the default read-write timeout value.
            On Unity platform, this value is not used as Unity HTTP client does not support timeouts, so 
            </summary>
            <remarks>
            <para>
            If the value is set, the value is assigned to the ReadWriteTimeout property of the HttpWebRequest object used
            to send requests.
            </para>
            <exception cref="T:System.ArgumentNullException">The timeout specified is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">The timeout specified is less than or equal to zero and is not Infinite.</exception>
            </remarks>
            <seealso cref="P:System.Net.HttpWebRequest.ReadWriteTimeout"/>
        </member>
        <member name="M:Amazon.Runtime.ClientConfig.GetWebProxy">
            <summary>
            Returns a WebProxy instance configured to match the proxy settings
            in the client configuration.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.ClientConfig.SetWebProxy(System.Net.IWebProxy)">
            <summary>
            Unpacks the host, port and any credentials info into the instance's
            proxy-related fields.
            Unlike the SetWebProxy implementation on .NET 3.5/4.5,the Host and the Port are not reconstructed from the 
            input proxyuri
            </summary>
            <param name="proxy">The proxy details</param>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.ProxyHost">
            <summary>
            Gets and sets of the ProxyHost property.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ClientConfig.ProxyPort">
            <summary>
            Gets and sets of the ProxyPort property.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:Amazon.Runtime.ClientConfig.HttpClientFactory" -->
        <member name="M:Amazon.Runtime.ClientConfig.CacheHttpClients(Amazon.Runtime.IClientConfig)">
            <summary>
            Returns true if the clients should be cached by HttpRequestMessageFactory, false otherwise.
            </summary>
            <param name="clientConfig"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.ClientConfig.DisposeHttpClients(Amazon.Runtime.IClientConfig)">
            <summary>
            Returns true if the SDK should dispose HttpClients after one use, false otherwise.
            </summary>
            <param name="clientConfig"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.ClientConfig.CreateConfigUniqueString(Amazon.Runtime.IClientConfig)">
            <summary>
             Create a unique string used for caching the HttpClient based on the settings that are used from the ClientConfig that are set on the HttpClient.
            </summary>
            <param name="clientConfig"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.ClientConfig.UseGlobalHttpClientCache(Amazon.Runtime.IClientConfig)">
             <summary>
             Determines if HttpClients created with the given IClientConfig should be cached at the SDK
             client level, or cached globally.
            
             If there is no HttpClientFactory assigned and proxy or proxy credentials are set
             this returns false because those properties can't be serialized as part of the key in the global http client cache.
             </summary>
        </member>
        <member name="T:Amazon.Runtime.ConstantClass">
            <summary>
            Base class for constant class that holds the value that will be sent to AWS for the static constants.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ConstantClass.Value">
            <summary>
            Gets the value that needs to be used when send the value to AWS
            </summary>
        </member>
        <member name="M:Amazon.Runtime.ConstantClass.Intern">
            <summary>
            Attempt to find correct-cased constant value using whatever cased value the user
            has provided. This is primarily useful for mapping any-cased values from a CLI
            tool to the specific casing required by the service, avoiding the need for the
            user to (a) remember the specific case and (b) actually type it correctly.
            </summary>
            <returns>The properly cased service constant matching the value</returns>
        </member>
        <member name="T:Amazon.Runtime.CorrectClockSkew">
            <summary>
            Utility class that stores clock skew information.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.CorrectClockSkew.GlobalClockCorrection">
            <summary>
            GlobalClockCorrection should be only set by AWSConfigs.ManualClockCorrection property
            and is only available to maintain backward compatibilty.  This should override any
            endpoint specific clockskew correction.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.CorrectClockSkew.GetClockCorrectionForEndpoint(System.String)">
            <summary>
            Return clock skew correction value for an endpoint if there is one.
            
            AWSConfigs.ManualClockCorrection overrides globally any calculated service endpoint specific
            clock correction value.
            </summary>
            <param name="endpoint">Endpoint should in a form such as "https://s3"</param>
            <returns>Clock correction value for an endpoint in TimeSpan.  TimeSpan.Zero if no such clock correction is set.</returns>
        </member>
        <member name="M:Amazon.Runtime.CorrectClockSkew.GetCorrectedUtcNowForEndpoint(System.String)">
            <summary>
            Get clock skew corrected UTC now value.  If ManualClockCorrection is set, 
            use ManualClockCorrection instead of endpoint specific clock correction value.
            </summary>
            <param name="endpoint"></param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.FallbackEndpointDiscoveryEnabledFactory">
            <summary>
            Probing mechanism to determine the endpoint discovery enabled value from various sources.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.SigningAlgorithm">
            <summary>
            The valid hashing algorithm supported by the sdk for request signing.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.ErrorType">
            <summary>
            Which end of a request was responsible for a service error response.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.ErrorType.Sender">
            <summary>
            The sender was responsible for the error, i.e. the client
            request failed validation or was improperly formatted.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.ErrorType.Receiver">
            <summary>
            The error occured within the service.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.ErrorType.Unknown">
            <summary>
            An unrecognized error type was returned.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.IAmazonService">
            <summary>
            All Amazon service interfaces like IAmazonS3 extend from this interface. This allows all the 
            Amazon service interfaces be identified by this base interface and helps with generic constraints.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IAmazonService.Config">
            <summary>
            A readonly view of the configuration for the service client.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.IClientConfig">
            <summary>
            This interface is the read only access to the ClientConfig object used when setting up service clients. Once service clients
            are initiated the config object should not be changed to avoid issues with using a service client in a multi threaded environment.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.RegionEndpoint">
            <summary>
            Gets the RegionEndpoint property. The region constant to use that 
            determines the endpoint to use.  If this is not set
            then the client will fallback to the value of ServiceURL.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.RegionEndpointServiceName">
            <summary>
            The constant used to lookup in the region hash the endpoint.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.ServiceURL">
            <summary>
            Gets and sets of the ServiceURL property.
            This is an optional property if you need to set a specific service URL 
            instead setting the region with the RegionEndpoint property.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.UseHttp">
            <summary>
            Gets the UseHttp property.
            If this property is set to true, the client attempts
            to use HTTP protocol.
            By default, this property is set to false.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.ServiceVersion">
            <summary>
            Gets Service Version
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.SignatureMethod">
            <summary>
            Gets the signatureMethod property.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.SignatureVersion">
             <summary>
             Gets the SignatureVersion property.
            
             Note: This property exists for backward compatibility but is no longer
             used by any service other than S3.
             </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.AuthenticationRegion">
            <summary>
            Gets the AuthenticationRegion property.
            Used in AWS4 request signing, this is an optional property; 
            change it only if the region cannot be determined from the 
            service endpoint.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.AuthenticationServiceName">
            <summary>
            Gets the AuthenticationServiceName property.
            Used in AWS4 request signing, this is the short-form
            name of the service being called.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.UserAgent">
            <summary>
            Gets the UserAgent property.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.DisableLogging">
            <summary>
            Gets the DisableLogging. If true logging for this client will be disabled.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.LogMetrics">
            <summary>
            Flag on whether to log metrics for service calls.
            
            This can be set in the application's configs, as below:
            <code>
            &lt;?xml version="1.0" encoding="utf-8" ?&gt;
            &lt;configuration&gt;
                &lt;appSettings&gt;
                    &lt;add key="AWSLogMetrics" value"true"/&gt;
                &lt;/appSettings&gt;
            &lt;/configuration&gt;
            </code>
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.LogResponse">
            <summary>
            Gets the LogResponse property.
            If this property is set to true, the service response
            is read in its entirety and logged.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.ReadEntireResponse">
            <summary>
            Gets the ReadEntireResponse.
            If this property is set to true, the service response
            is read in its entirety before being processed.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.AllowAutoRedirect">
            <summary>
            This flag controls if .NET HTTP infrastructure should follow redirection
             responses (e.g. HTTP 307 - temporary redirect).
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.BufferSize">
            <summary>
            Gets the BufferSize property.
            The BufferSize controls the buffer used to read in from input streams and write 
            out to the request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.MaxErrorRetry">
            <summary>
            Gets the MaxErrorRetry property.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.ProgressUpdateInterval">
            <summary>
            Gets the interval at which progress update events are raised
            for upload operations. By default, the progress update events are 
            raised at every 100KB of data transferred. 
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.ResignRetries">
            <summary>
            Flag on whether to resign requests on retry or not.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.ProxyCredentials">
            <summary>
            Credentials to use with a proxy.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.Timeout">
            <summary>
            Gets the default request timeout value.
            </summary>
            <remarks>
            <para>
            If the value is set, the value is assigned to the Timeout property of the HTTPWebRequest/HttpClient object used
            to send requests.
            </para>
            <para>
            Please specify a timeout value only if the operation will not complete within the default intervals
            specified for an HttpWebRequest/HttpClient.
            </para>
            </remarks>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.UseDualstackEndpoint">
            <summary>
            Configures the endpoint calculation for a service to go to a dual stack (ipv6 enabled) endpoint
            for the configured region.
            </summary>
            <remarks>
            Note: AWS services are enabling dualstack endpoints over time. It is your responsibility to check 
            that the service actually supports a dualstack endpoint in the configured region before enabling 
            this option for a service.
            </remarks>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.ThrottleRetries">
            <summary>
            Configures a flag enabling to either opt in or opt out of the retry throttling service.
            Note: set value to true to enable retry throttling feature. The Default value for this flag is false.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.IClientConfig.DetermineServiceURL">
            <summary>
            Using either the RegionEndpoint or the ServiceURL determine what the URL to the service is.
            </summary>
            <returns>The URL to the service.</returns>
        </member>
        <member name="M:Amazon.Runtime.IClientConfig.Validate">
            <summary>
            Performs validation on this config object.
            Throws exception if any of the required values are missing/invalid.
            </summary>
            <exception cref="T:Amazon.Runtime.AmazonClientException">The timeout specified is null.</exception>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.CorrectedUtcNow">
            <summary>
            Returns the clock skew adjusted utc now.  This value is affected by AWSConfigs.ManualClockCorrection
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.ClockOffset">
            <summary>
            Returns the calculated clock skew value for this config's service endpoint. If AWSConfigs.CorrectForClockSkew is false,
            this value won't be used to construct service requests.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.DisableHostPrefixInjection">
            <summary>
            Gets the DisableHostPrefixInjection flag. If true, host prefix injection will be disabled for this client, the default value of this flag is false. 
            Host prefix injection prefixes the service endpoint with request members from APIs which use this feature. 
            Example: for a hostPrefix of "foo-name." and a endpoint of "service.region.amazonaws.com" the default behavior is to
            prefix the endpoint with the hostPrefix resulting in a final endpoint of "foo-name.service.region.amazonaws.com". Setting 
            DisableHostPrefixInjection to true will disable hostPrefix injection resulting in a final endpoint of
            "service.region.amazonaws.com" regardless of the value of hostPrefix. E.g. You may want to disable host prefix injection for testing against a local mock endpoint.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.EndpointDiscoveryEnabled">
            <summary>
            Returns the flag indicating if endpoint discovery should be enabled or disabled for operations that are not required to use endpoint discovery.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.EndpointDiscoveryCacheLimit">
            <summary>
            Returns the maximum number of discovered endpoints that can be stored within the cache for the client. The default limit is 1000 cache entries.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.CacheHttpClient">
            <summary>
            <para>
            This is a switch used for performance testing and is not intended for production applications 
            to change. This switch may be removed in a future version of the SDK as the .NET Core platform matures.
            </para>
            <para>
            If true, the HttpClient is cached and reused for every request made by the service client 
            and shared with other service clients.
            </para>
            <para>
            For the .NET Core platform this is default to true because the HttpClient manages the connection
            pool.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.HttpClientCacheSize">
            <summary>
            If CacheHttpClient is set to true then HttpClientCacheSize controls the number of HttpClients cached.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.ProxyHost">
            <summary>
            Gets the ProxyHost property.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IClientConfig.ProxyPort">
            <summary>
            Gets the ProxyPort property.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.IClientConfig.GetWebProxy">
            <summary>
            Returns a WebProxy instance configured to match the proxy settings
            in the client configuration.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "P:Amazon.Runtime.IClientConfig.HttpClientFactory" -->
        <member name="T:Amazon.Runtime.ILogMessage">
            <summary>
            Interface for a single logged message.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ILogMessage.Format">
            <summary>
            Log message format.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ILogMessage.Args">
            <summary>
            Log message arguments.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ILogMessage.Provider">
            <summary>
            Culture-specific formatting provider.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.ParameterValue">
            <summary>
            Abstract parameter value.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.StringParameterValue">
            <summary>
            String parameter value.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.StringParameterValue.Value">
            <summary>
            String value of the parameter.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.StringParameterValue.#ctor(System.String)">
            <summary>
            Constructs ParameterValue for a single string.
            </summary>
            <param name="value"></param>
        </member>
        <member name="T:Amazon.Runtime.StringListParameterValue">
            <summary>
            String list parameter value.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.StringListParameterValue.Value">
            <summary>
            List of strings value of the parameter.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.StringListParameterValue.#ctor(System.Collections.Generic.List{System.String})">
            <summary>
            Constructs ParameterValue for a list of strings.
            </summary>
            <param name="values"></param>
        </member>
        <member name="T:Amazon.Runtime.IRequestMetrics">
            <summary>
            Metrics collected by the SDK on a per-request basis. 
            </summary>
            <remarks>
            Each request made to an AWS service by the SDK can have metrics
            collected and logged. This interface represents the collected 
            metrics for a request. The metrics include properties (i.e. request id 
            and other metadata), timings for each stage of the request, and counters.
            </remarks>
        </member>
        <member name="P:Amazon.Runtime.IRequestMetrics.Properties">
            <summary>
            Collection of properties being tracked
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IRequestMetrics.Timings">
            <summary>
            Timings for metrics being tracked
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IRequestMetrics.Counters">
            <summary>
            Counters being tracked
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IRequestMetrics.IsEnabled">
            <summary>
            Whether metrics are enabled for the request
            </summary>
        </member>
        <member name="M:Amazon.Runtime.IRequestMetrics.ToJSON">
            <summary>
            JSON representation of the current metrics
            </summary>
            <returns>JSON string</returns>
        </member>
        <member name="T:Amazon.Runtime.IMetricsTiming">
            <summary>
            Represents how long a phase of an SDK request took.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IMetricsTiming.IsFinished">
            <summary>
            Whether the timing has been stopped
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IMetricsTiming.ElapsedTicks">
            <summary>
            Elapsed ticks from start to stop.
            If timing hasn't been stopped yet, returns 0.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IMetricsTiming.ElapsedTime">
            <summary>
            Elapsed time from start to stop.
            If timing hasn't been stopped yet, returns TimeSpan.Zero
            </summary>
        </member>
        <member name="T:Amazon.Runtime.IMetricsFormatter">
            <summary>
            User supplied type to perform metrics formatting. 
            </summary>
        </member>
        <member name="M:Amazon.Runtime.IMetricsFormatter.FormatMetrics(Amazon.Runtime.IRequestMetrics)">
            <summary>
            Produce custom formatting for SDK metrics.
            </summary>
            <remarks>
            If defined, this method will be called for every request made by the SDK. 
            </remarks>
            <param name="metrics">An instance of IRequestMetrics produced by the SDK</param>
            <returns>formatted string representation of the metrics</returns>
        </member>
        <member name="T:Amazon.Runtime.Metric">
            <summary>
            Predefined request metrics that are collected by the SDK.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.ResponseMetadata">
            <summary>
            Information about the request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ResponseMetadata.RequestId">
            <summary>
            Gets and sets the RequestId property.
            ID that uniquely identifies a request. Amazon keeps track of request IDs. If you have a question about a request, include the request ID in your correspondence.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.SignatureException">
            <summary>
            This exception is thrown if there are problems signing the request.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.StreamTransferProgressArgs.#ctor(System.Int64,System.Int64,System.Int64)">
            <summary>
            The constructor takes the number of
            currently transferred bytes and the
            total number of bytes to be transferred
            </summary>
            <param name="incrementTransferred">The number of bytes transferred since last event</param>
            <param name="transferred">The number of bytes transferred</param>
            <param name="total">The total number of bytes to be transferred</param>
        </member>
        <member name="P:Amazon.Runtime.StreamTransferProgressArgs.PercentDone">
            <summary>
            Gets the percentage of transfer completed
            </summary>
        </member>
        <member name="P:Amazon.Runtime.StreamTransferProgressArgs.IncrementTransferred">
            <summary>
            Gets the number of bytes transferred since last event
            </summary>
        </member>
        <member name="P:Amazon.Runtime.StreamTransferProgressArgs.TransferredBytes">
            <summary>
            Gets the number of bytes transferred
            </summary>
        </member>
        <member name="P:Amazon.Runtime.StreamTransferProgressArgs.TotalBytes">
            <summary>
            Gets the total number of bytes to be transferred
            </summary>
        </member>
        <member name="M:Amazon.Runtime.StreamTransferProgressArgs.ToString">
            <summary>
            Returns a string representation of this object
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.AnonymousAWSCredentials">
            <summary>
            Anonymous credentials.
            Using these credentials, the client does not sign the request.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.AnonymousAWSCredentials.GetCredentials">
            <summary>
            Returns an instance of ImmutableCredentials for this instance
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.AssumeRoleAWSCredentials">
            <summary>
            AWS Credentials that automatically refresh by calling AssumeRole on
            the Amazon Security Token Service.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AssumeRoleAWSCredentials.SourceCredentials">
            <summary>
            The credentials of the user that will be used to call AssumeRole.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AssumeRoleAWSCredentials.RoleArn">
            <summary>
            The Amazon Resource Name (ARN) of the role to assume.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AssumeRoleAWSCredentials.RoleSessionName">
            <summary>
            An identifier for the assumed role session.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AssumeRoleAWSCredentials.Options">
            <summary>
            Options to be used in the call to AssumeRole.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.AssumeRoleAWSCredentials.#ctor(Amazon.Runtime.AWSCredentials,System.String,System.String)">
            <summary>
            Constructs an AssumeRoleAWSCredentials object.
            </summary>
            <param name="sourceCredentials">The credentials of the user that will be used to call AssumeRole.</param>
            <param name="roleArn">The Amazon Resource Name (ARN) of the role to assume.</param>
            <param name="roleSessionName">An identifier for the assumed role session.</param>
        </member>
        <member name="M:Amazon.Runtime.AssumeRoleAWSCredentials.#ctor(Amazon.Runtime.AWSCredentials,System.String,System.String,Amazon.Runtime.AssumeRoleAWSCredentialsOptions)">
            <summary>
            Constructs an AssumeRoleAWSCredentials object.
            </summary>
            <param name="sourceCredentials">The credentials of the user that will be used to call AssumeRole.</param>
            <param name="roleArn">The Amazon Resource Name (ARN) of the role to assume.</param>
            <param name="roleSessionName">An identifier for the assumed role session.</param>
            <param name="options">Options to be used in the call to AssumeRole.</param>
        </member>
        <member name="P:Amazon.Runtime.AssumeRoleAWSCredentialsOptions.ExternalId">
            <summary>
            A unique identifier that is used by third parties when assuming roles in their customers' accounts.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AssumeRoleAWSCredentialsOptions.Policy">
            <summary>
             An IAM policy in JSON format.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AssumeRoleAWSCredentialsOptions.DurationSeconds">
            <summary>
            The length of time in seconds before the credentials will expire.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AssumeRoleAWSCredentialsOptions.MfaSerialNumber">
            <summary>
            The identification number of the MFA device that is associated with the user who is making the assume-role call.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AssumeRoleAWSCredentialsOptions.MfaTokenCode">
            <summary>
            The value provided by the MFA device, if the trust policy of the role being assumed requires MFA.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AssumeRoleAWSCredentialsOptions.MfaTokenCodeCallback">
            <summary>
            A callback that's used to obtain the MFA token code when the AssumeRoleAWSCredentials are refreshed.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.AssumeRoleImmutableCredentials">
            <summary>
            Immutable representation of AWS credentials obtained as a result of
            authenticating with the AssumeRole method of the Security Token Service.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.AssumeRoleImmutableCredentials.Expiration">
            <summary>
            The expiry time of the credentials, obtained from the AssumeRoleResponse.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.AssumeRoleImmutableCredentials.#ctor(System.String,System.String,System.String,System.DateTime)">
            <summary>
            Constructs an instance with supplied keys, token, and expiration.
            </summary>
            <param name="awsAccessKeyId">The AccessKey for the credentials.</param>
            <param name="awsSecretAccessKey">The SecretKey for the credentials.</param>
            <param name="token">The security token for the credentials.</param>
            <param name="expiration">The expiration time for the credentials.</param>
        </member>
        <member name="M:Amazon.Runtime.AssumeRoleImmutableCredentials.Copy">
            <summary>
            Get a copy of this AssumeRoleImmutableCredentials object.
            </summary>
            <returns>A copy of this object.</returns>
        </member>
        <member name="T:Amazon.Runtime.AWSCredentials">
            <summary>
            Abstract class that represents a credentials object for AWS services.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.AWSCredentials.GetCredentials">
            <summary>
            Returns a copy of ImmutableCredentials
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.AWSCredentials.Validate">
            <summary>
            Called by AmazonServiceClient to validate the credential state
            on client construction.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.BasicAWSCredentials">
            <summary>
            Basic set of credentials consisting of an AccessKey and SecretKey
            </summary>
        </member>
        <member name="M:Amazon.Runtime.BasicAWSCredentials.#ctor(System.String,System.String)">
            <summary>
            Constructs a BasicAWSCredentials object for the specified accessKey and secretKey.
            </summary>
            <param name="accessKey"></param>
            <param name="secretKey"></param>
        </member>
        <member name="M:Amazon.Runtime.BasicAWSCredentials.GetCredentials">
            <summary>
            Returns an instance of ImmutableCredentials for this instance
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.ImmutableCredentials">
            <summary>
            Immutable representation of AWS credentials.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ImmutableCredentials.AccessKey">
            <summary>
            Gets the AccessKey property for the current credentials.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ImmutableCredentials.SecretKey">
            <summary>
            Gets the SecretKey property for the current credentials.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ImmutableCredentials.Token">
            <summary>
            Gets the Token property for the current credentials.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.ImmutableCredentials.UseToken">
            <summary>
            Gets the UseToken property for the current credentials.
            Specifies if Token property is non-emtpy.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.ImmutableCredentials.#ctor(System.String,System.String,System.String)">
            <summary>
            Constructs an ImmutableCredentials object with supplied accessKey, secretKey.
            </summary>
            <param name="awsAccessKeyId"></param>
            <param name="awsSecretAccessKey"></param>
            <param name="token">Optional. Can be set to null or empty for non-session credentials.</param>
        </member>
        <member name="M:Amazon.Runtime.ImmutableCredentials.Copy">
            <summary>
            Returns a copy of the current credentials.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.RefreshingAWSCredentials">
            <summary>
            Abstract class for automatically refreshing AWS credentials
            </summary>
        </member>
        <member name="T:Amazon.Runtime.RefreshingAWSCredentials.CredentialsRefreshState">
            <summary>
            Refresh state container consisting of credentials
            and the date of the their expiration
            </summary>
        </member>
        <member name="P:Amazon.Runtime.RefreshingAWSCredentials.PreemptExpiryTime">
            <summary>
            The time before actual expiration to expire the credentials.        
            Property cannot be set to a negative TimeSpan.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.RefreshingAWSCredentials.GetCredentials">
            <summary>
            Returns an instance of ImmutableCredentials for this instance
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.RefreshingAWSCredentials.GenerateNewCredentials">
            <summary>
            When overridden in a derived class, generates new credentials and new expiration date.
            
            Called on first credentials request and when expiration date is in the past.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.RefreshingAWSCredentials.GenerateNewCredentialsAsync">
            <summary>
            When overridden in a derived class, generates new credentials and new expiration date.
            
            Called on first credentials request and when expiration date is in the past.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.RefreshingAWSCredentials.ClearCredentials">
            <summary>
            Clears currently-stored credentials, forcing the next GetCredentials call to generate new credentials.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.SAMLImmutableCredentials">
            <summary>
            Immutable representation of AWS credentials obtained as a result of
            authenticating against a SAML-supporting federated identity provider.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.SAMLImmutableCredentials.Expires">
            <summary>
            The expiry time of the credentials, obtained from the AssumeRoleWithSAML response.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.SAMLImmutableCredentials.Subject">
            <summary>
            The value of the NameID element in the Subject element of the SAML assertion, as returned in the
            AssumeRoleWithSAML response from the Security Token Service.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.SAMLImmutableCredentials.#ctor(System.String,System.String,System.String,System.DateTime,System.String)">
            <summary>
            Constructs an instance with supplied keys and SAML assertion data.
            </summary>
            <param name="awsAccessKeyId"></param>
            <param name="awsSecretAccessKey"></param>
            <param name="token"></param>
            <param name="expires"></param>
            <param name="subject"></param>
        </member>
        <member name="M:Amazon.Runtime.SAMLImmutableCredentials.#ctor(Amazon.Runtime.ImmutableCredentials,System.DateTime,System.String)">
            <summary>
            Constructs an instance with supplied keys and SAML assertion data.
            </summary>
            <param name="credentials"></param>
            <param name="subject"></param>
            <param name="expires"></param>
        </member>
        <member name="M:Amazon.Runtime.SAMLImmutableCredentials.Copy">
            <summary>
            Returns a copy of the current credentials.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.SAMLImmutableCredentials.ToJson">
            <summary>
            Serializes the instance to a json-format string for external storage
            </summary>
            <returns>The serialized object in json</returns>
        </member>
        <member name="M:Amazon.Runtime.SAMLImmutableCredentials.FromJson(System.String)">
            <summary>
            Instantiates an instance from persisted json data
            </summary>
            <param name="json">The serialized data</param>
            <returns>Deserialized instance corresponding to the json data</returns>
        </member>
        <member name="T:Amazon.Runtime.SessionAWSCredentials">
            <summary>
            Session credentials consisting of AccessKey, SecretKey and Token
            </summary>
        </member>
        <member name="M:Amazon.Runtime.SessionAWSCredentials.#ctor(System.String,System.String,System.String)">
            <summary>
            Constructs a SessionAWSCredentials object for the specified accessKey, secretKey.
            </summary>
            <param name="awsAccessKeyId"></param>
            <param name="awsSecretAccessKey"></param>
            <param name="token"></param>
        </member>
        <member name="M:Amazon.Runtime.SessionAWSCredentials.GetCredentials">
            <summary>
            Returns an instance of ImmutableCredentials for this instance
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.InstanceProfileAWSCredentials">
            <summary>
            Credentials that are retrieved from the Instance Profile service on an EC2 instance
            </summary>
        </member>
        <member name="M:Amazon.Runtime.InstanceProfileAWSCredentials.#ctor(System.String)">
            <summary>
            Constructs a InstanceProfileAWSCredentials object for specific role
            </summary>
            <param name="role">Role to use</param>
        </member>
        <member name="M:Amazon.Runtime.InstanceProfileAWSCredentials.#ctor">
            <summary>
            Constructs a InstanceProfileAWSCredentials object for the first found role
            </summary>
        </member>
        <member name="M:Amazon.Runtime.InstanceProfileAWSCredentials.GetAvailableRoles">
            <summary>
            Retrieves a list of all roles available through current InstanceProfile service
            
            InstanceProfileAWSCredentials is no longer supported in PCL.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.EventStreamErrorCodeException">
            <summary>
            Superclass for Exceptions that come over the event stream, or to wrap other kinds of generic exceptions.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.EventStreamErrorCodeException.ErrorCode">
            <summary>
            The numeric code identifier for the type of error.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamErrorCodeException.#ctor(System.Int32)">
            <summary>
            Constructs an EventStreamError.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamErrorCodeException.#ctor(System.Int32,System.String)">
            <summary>
            Constructs an EventStreamError.
            </summary>
            <param name="errorCode">The error code.</param>
            <param name="message">The error message.</param>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.EventStreamEventReceivedArgs`1">
            <summary>
            Event args that contain an <see cref="T:Amazon.Runtime.EventStreams.Internal.IEventStreamEvent"/>.
            </summary>
            <typeparam name="T">The <see cref="T:Amazon.Runtime.EventStreams.Internal.IEventStreamEvent"/></typeparam>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.EventStreamEventReceivedArgs`1.EventStreamEvent">
            <summary>
            The EventStream Event.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamEventReceivedArgs`1.#ctor(`0)">
            <summary>
            Constructs an EventStreamEventReceivedArgs.
            </summary>
            <param name="eventStreamEvent">The EventStream Event.</param>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.EventStreamExceptionReceivedArgs`1">
            <summary>
            Event args that contain an <see cref="P:Amazon.Runtime.EventStreams.EventStreamExceptionReceivedArgs`1.EventStreamException"/>.
            </summary>
            <typeparam name="T">The <see cref="P:Amazon.Runtime.EventStreams.EventStreamExceptionReceivedArgs`1.EventStreamException"/>.</typeparam>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.EventStreamExceptionReceivedArgs`1.EventStreamException">
            <summary>
            The EventStream Exception.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamExceptionReceivedArgs`1.#ctor(`0)">
            <summary>
            Constructs an EventStreamExceptionReceivedArgs.
            </summary>
            <param name="eventStreamException">The EventStream Exception.</param>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.EventStreamHeaderType">
            <summary>
             Supported Header types for vnd.amazon.event-stream
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.IEventStreamHeader.Name">
            <summary>
            Name for the Header. Maximum of 255 bytes.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.IEventStreamHeader.HeaderType">
            <summary>
            Header type id
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.GetWireSize">
            <summary>
            Computes the amount of memory neccesary to serialize this Header.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.WriteToBuffer(System.Byte[],System.Int32)">
            <summary>
            Writes this Header to buffer starting at offset
            Keep in mind, this API assumes buffer is large enough
            for the operation.
            </summary>
            <param name="buffer">buffer to serialize this Header to</param>
            <param name="offset">offset to begin writing at.</param>
            <returns>
            the new offset.
            </returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.AsBool">
            <summary>
            Returns the current value as a bool
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.SetBool(System.Boolean)">
            <summary>
            Sets the current value
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.AsByte">
            <summary>
            Returns the current value as a byte
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.SetByte(System.Byte)">
            <summary>
            Sets the current value
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.AsInt16">
            <summary>
            Gets the current value as a 16 bit integer. (Host Order).
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.SetInt16(System.Int16)">
            <summary>
            Sets the current value. (Host Order)
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.AsInt32">
            <summary>
            Returns the current value as a 32 bit integer. (Host Order)
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.SetInt32(System.Int32)">
            <summary>
            Sets the current value
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.AsInt64">
            <summary>
            returns the current value as a 64-bit integer. (Host Order)
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.SetInt64(System.Int64)">
            <summary>
            Sets the current value. (Host Order)
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.AsByteBuf">
            <summary>
            Returns the current value as a byte buffer.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.SetByteBuf(System.Byte[])">
            <summary>
            Sets the current value. Max length is 2^15 - 1
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.AsString">
            <summary>
            Returns the current value as a utf-8 string.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.SetString(System.String)">
            <summary>
            Sets the current value. Utf-8 encoded. Max byte size is 2^16 - 1
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.AsTimestamp">
            <summary>
            Gets the current value as a DateTime.
            Note: You do not need to compensate for unix epoch on this API.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.SetTimestamp(System.DateTime)">
            <summary>
            Sets the current value.
            Note: You do not need to compensate for unix epoch on this API.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.AsUUID">
            <summary>
            Returns the current value as a Guid (UUID)
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamHeader.SetUUID(System.Guid)">
            <summary>
            Sets the current value
            </summary>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.EventStreamHeader">
            <summary>
            Header Data for an EventStreamMessage
            
            Each header is of format:
            [name len] (1) | [utf-8 name] (v)
            [type (1)]
            [value (v)]
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.EventStreamHeader.Name">
            <summary>
            Name for the Header. Maximum of 255 bytes.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.EventStreamHeader.HeaderType">
            <summary>
            Header type id
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.#ctor(System.String)">
            <summary>
            Initializes this Header with name.
            </summary>
            <param name="name">utf-8 string, max length is 255</param>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.FromBuffer(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Deserializes the header in buffer.
            </summary>
            <param buffer="buffer">buffer constaining the header</param>
            <param offset="offset">index to start from in the buffer.</param>
            <param offset="newOffset">updated offset with new value for offset</param>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.WriteToBuffer(System.Byte[],System.Int32)">
            <summary>
            Writes this Header to buffer starting at offset
            Keep in mind, this API assumes buffer is large enough
            for the operation.
            </summary>
            <param name="buffer">buffer to serialize this Header to</param>
            <param name="offset">offset to begin writing at.</param>
            <returns>
            the new offset.
            </returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.GetWireSize">
            <summary>
            Computes the amount of memory neccesary to serialize this Header.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.AsBool">
            <summary>
            Returns the current value as a bool
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.SetBool(System.Boolean)">
            <summary>
            Sets the current value
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.AsByte">
            <summary>
            Returns the current value as a byte
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.SetByte(System.Byte)">
            <summary>
            Sets the current value
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.AsInt16">
            <summary>
            Gets the current value as a 16 bit integer. (Host Order).
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.SetInt16(System.Int16)">
            <summary>
            Sets the current value. (Host Order)
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.AsInt32">
            <summary>
            Returns the current value as a 32 bit integer. (Host Order)
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.SetInt32(System.Int32)">
            <summary>
            Sets the current value
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.AsInt64">
            <summary>
            returns the current value as a 64-bit integer. (Host Order)
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.SetInt64(System.Int64)">
            <summary>
            Sets the current value. (Host Order)
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.AsByteBuf">
            <summary>
            Returns the current value as a byte buffer.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.SetByteBuf(System.Byte[])">
            <summary>
            Sets the current value. Max length is 2^15 - 1
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.AsString">
            <summary>
            Returns the current value as a utf-8 string.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.SetString(System.String)">
            <summary>
            Sets the current value. Utf-8 encoded. Max byte size is 2^16 - 1
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.AsTimestamp">
            <summary>
            Gets the current value as a DateTime.
            Note: You do not need to compensate for unix epoch on this API.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.SetTimestamp(System.DateTime)">
            <summary>
            Sets the current value.
            Note: You do not need to compensate for unix epoch on this API.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.AsUUID">
            <summary>
            Returns the current value as a Guid (UUID)
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamHeader.SetUUID(System.Guid)">
            <summary>
            Sets the current value
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.IEventStreamMessage.Headers">
            <summary>
            Headers for the message. Can be null.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.IEventStreamMessage.Payload">
            <summary>
            Payload for the message. Can be null.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.IEventStreamMessage.ToByteArray">
            <summary>
            Converts a message into a byte buffer (usually for network transmission).
            </summary>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.EventStreamMessage">
            <summary>
            Message is a single datagram. The format is as follows:
            [ total length (4) ] | [ headers_length (4)] | [ prelude crc(4)]
            [ headers (v)]       
            [ payload (v)}
            [ trailing crc ]
            
            CRCs use the CRC32 algorithm.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.EventStreams.EventStreamMessage.ContentType">
            <summary>
            Content type for EventStreams.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.EventStreamMessage.Headers">
            <summary>
            Headers for the message. Can be null.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.EventStreamMessage.Payload">
            <summary>
            Payload for the message. Can be null.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamMessage.#ctor(System.Collections.Generic.List{Amazon.Runtime.EventStreams.IEventStreamHeader},System.Byte[])">
            <summary>
            Initialize a message with headers and a payload.
            </summary>
            <param name="headers">list of headers for the message, can be null.</param>
            <param name="payload">payload for the message, can be null.</param>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamMessage.FromBuffer(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Builds a message from buffer.
            </summary>
            <param name="buffer">buffer to read</param>
            <param name="offset">offset to start reading</param>
            <param name="length">buffer length.</param>
            <returns>
            parsed instance of EventStreamMessage. Doesn't return null,
            does throw if CRCs don't match.
            </returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamMessage.ToByteArray">
            <summary>
            Converts a message into a byte buffer (usually for network transmission).
            </summary>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.EventStreamValidationException">
            <summary>
            Defines exceptions that arise from an <see cref="T:Amazon.Runtime.EventStreams.EventStreamMessage"/> not conforming to specification.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamValidationException.#ctor">
            <summary>
            Constructs an EventStreamValidationException.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamValidationException.#ctor(System.String)">
            <summary>
            Constructs an EventStreamValidationException.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.EventStreamValidationException.#ctor(System.String,System.Exception)">
            <summary>
            Constructs an EventStreamValidationException.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.UnknownEventStreamException">
            <summary>
            This exception is thrown if an exception is retrieved from the event stream and should be modeled,
            but a generator function for the strongly-typed exception is not defined.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.UnknownEventStreamException.ExceptionType">
            <summary>
            The exception type.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.UnknownEventStreamException.#ctor(System.String)">
            <summary>
            Constructs an EventStreamException.
            </summary>
            <param name="exceptionType">The exception type.</param>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.Internal.IEnumerableEventStream`2">
            <summary>
            The contract for the <see cref="T:Amazon.Runtime.EventStreams.Internal.EnumerableEventStream`2"/>.
            </summary>
            <typeparam name="T">An implementation of IEventStreamEvent (e.g. IS3Event).</typeparam>
            <typeparam name="TE">An implementation of EventStreamException (e.g. S3EventStreamException).</typeparam>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.Internal.EnumerableEventStream`2">
            <summary>
            A subclass of <see cref="T:Amazon.Runtime.EventStreams.Internal.EventStream`2" /> that enables an enumerable interface for interacting with Events.
            </summary>
            <typeparam name="T">An implementation of IEventStreamEvent (e.g. IS3Event).</typeparam>
            <typeparam name="TE">An implementation of EventStreamException (e.g. S3EventStreamException).</typeparam>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.EnumerableEventStream`2.IsEnumerated">
            <summary>
            Flag if the stream was chosen to be enumerated.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EnumerableEventStream`2.#ctor(System.IO.Stream)">
            <summary>
            A Stream of Events. Events can be retrieved from this stream by either
            <list type="bullet">
              <item><description>attaching handlers to listen events, and then call StartProcessing <i>or</i></description></item>
              <item><description>enumerating over the events.</description></item>
            </list>
            <para></para>
            These options should be treated as mutually exclusive.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EnumerableEventStream`2.#ctor(System.IO.Stream,Amazon.Runtime.EventStreams.Internal.IEventStreamDecoder)">
            <summary>
            A Stream of Events. Events can be retrieved from this stream by either
            <list type="bullet">
              <item><description>attaching handlers to listen events, and then call StartProcessing <i>or</i></description></item>
              <item><description>enumerating over the events.</description></item>
            </list>
            <para></para>
            These options should be treated as mutually exclusive.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EnumerableEventStream`2.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>An enumerator that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EnumerableEventStream`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EnumerableEventStream`2.StartProcessing">
            <summary>
            Starts the background thread to start reading events from the network stream.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.Internal.IEventStream`2">
            <summary>
            The contract for the <see cref="T:Amazon.Runtime.EventStreams.Internal.EventStream`2"/>.
            </summary>
            <typeparam name="T">An implementation of IEventStreamEvent (e.g. IS3Event).</typeparam>
            <typeparam name="TE">An implementation of EventStreamException (e.g. S3EventStreamException).</typeparam>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.IEventStream`2.BufferSize">
            <summary>
            The size of the buffer for reading from the network stream.
            </summary>
        </member>
        <member name="E:Amazon.Runtime.EventStreams.Internal.IEventStream`2.EventReceived">
            <summary>
            Fires when an event is received.
            </summary>
        </member>
        <member name="E:Amazon.Runtime.EventStreams.Internal.IEventStream`2.ExceptionReceived">
            <summary>
            Fired when an exception or error is raised.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.IEventStream`2.StartProcessing">
            <summary>
            Starts the background thread to start reading events from the network stream.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.Internal.EventStream`2">
            <summary>
            The superclass for all EventStreams. It contains the common processing logic needed to retreive events from a network Stream. It
            also contains the mechanisms needed to have a background loop raise events.
            </summary>
            <typeparam name="T">An implementation of IEventStreamEvent (e.g. IS3Event).</typeparam>
            <typeparam name="TE">An implementation of EventStreamException (e.g. S3EventStreamException).</typeparam>
        </member>
        <member name="F:Amazon.Runtime.EventStreams.Internal.EventStream`2.UnknownEventKey">
            <summary>
            "Unique" key for unknown event lookup.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.EventStreams.Internal.EventStream`2.HeaderMessageType">
            <summary>
            Header key for message type.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.EventStreams.Internal.EventStream`2.HeaderEventType">
            <summary>
            Header key for event type.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.EventStreams.Internal.EventStream`2.HeaderExceptionType">
            <summary>
            Header key for exception type.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.EventStreams.Internal.EventStream`2.HeaderErrorCode">
            <summary>
            Header key for error code.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.EventStreams.Internal.EventStream`2.HeaderErrorMessage">
            <summary>
            Header key for error message.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.EventStreams.Internal.EventStream`2.EventHeaderMessageTypeValue">
            <summary>
            Value of <see cref="F:Amazon.Runtime.EventStreams.Internal.EventStream`2.HeaderMessageType"/> when the message is an event.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.EventStreams.Internal.EventStream`2.ExceptionHeaderMessageTypeValue">
            <summary>
            Value of <see cref="F:Amazon.Runtime.EventStreams.Internal.EventStream`2.HeaderMessageType"/> when the message is an exception.
            </summary>
        </member>
        <member name="F:Amazon.Runtime.EventStreams.Internal.EventStream`2.ErrorHeaderMessageTypeValue">
            <summary>
            Value of <see cref="F:Amazon.Runtime.EventStreams.Internal.EventStream`2.HeaderMessageType"/> when the message is an error.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.EventStream`2.BufferSize">
            <summary>
            The size of the buffer for reading from the network stream.
            Default is 8192.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.EventStream`2.NetworkStream">
            <summary>
            The underlying stream to read events from.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.EventStream`2.Decoder">
            <summary>
            Responsible for decoding events from sequences of bytes.
            </summary>
        </member>
        <member name="E:Amazon.Runtime.EventStreams.Internal.EventStream`2.EventReceived">
            <summary>
            Fires when an event is recieved.
            </summary>
        </member>
        <member name="E:Amazon.Runtime.EventStreams.Internal.EventStream`2.ExceptionReceived">
            <summary>
            Fired when an exception or error is raised.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.EventStream`2.EventMapping">
            <summary>
            The mapping of event message to a generator function to construct the matching Event Stream event.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.EventStream`2.ExceptionMapping">
            <summary>
            The mapping of event message to a generator function to construct the matching Event Stream exception.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.EventStream`2.IsProcessing">
            <summary>
            Whether the Stream is currently being processed.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStream`2.#ctor(System.IO.Stream)">
            <summary>
            A Stream of Events. Events can be retrieved from this stream by attaching handlers to listen events, and then calling StartProcessing.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStream`2.#ctor(System.IO.Stream,Amazon.Runtime.EventStreams.Internal.IEventStreamDecoder)">
            <summary>
            A Stream of Events. Events can be retrieved from this stream by attaching handlers to listen events, and then calling StartProcessing.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStream`2.ConvertMessageToEvent(Amazon.Runtime.EventStreams.EventStreamMessage)">
            <summary>
            Converts an EventStreamMessage to an event.
            </summary>
            <param name="eventStreamMessage">The event stream message to be converted.</param>
            <returns>The event</returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStream`2.Process">
            <summary>
            Abstraction for cross-framework initiation of the background thread.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStream`2.ProcessLoop">
            <summary>
            The background thread main loop. It will constantly read from the network stream until IsProcessing is false, or an error occurs.
            <para></para>
            This stub exists due to FXCop.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStream`2.ProcessLoop(System.Object)">
            <summary>
            The background thread main loop. It will constantly read from the network stream until IsProcessing is false, or an error occurs.
            </summary>
            <param name="state">Needed for 3.5 support. Not used.</param>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStream`2.ReadFromStream(System.Byte[])">
            <summary>
            Reads from the stream into the buffer. It then passes the buffer to the decoder, which raises an event for
            each message it decodes.
            </summary>
            <param name="buffer">The buffer to store the read bytes from the stream.</param>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStream`2.WrapException(System.Exception)">
            <summary>
            Wraps exceptions in an outer exception so they can be passed to event handlers. If the Exception is already of a compatable type,
            the method returns what it was given.
            </summary>
            <param name="ex">The exception to wrap.</param>
            <returns>An exception of type TE</returns>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStream`2.StartProcessing">
            <summary>
            Starts the background thread to start reading events from the network stream.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStream`2.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStream`2.Dispose(System.Boolean)">
            <summary>
            Disposes the resources of this stream.
            </summary>
            <param name="disposing">Should dispose of unmanged resources.</param>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.Internal.EventStreamMessageReceivedEventArgs">
            <summary>
            Event Arguments for EventStreamDecoder.MessageReceived.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.EventStreamMessageReceivedEventArgs.Message">
            <summary>
            Received message.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.EventStreamMessageReceivedEventArgs.Context">
            <summary>
            Additional object context
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStreamMessageReceivedEventArgs.#ctor(Amazon.Runtime.EventStreams.EventStreamMessage)">
            <summary>
            Initialize this with message
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStreamMessageReceivedEventArgs.#ctor(Amazon.Runtime.EventStreams.EventStreamMessage,System.Object)">
            <summary>
            Initialize this with message and object conetext
            </summary>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.Internal.EventStreamDecoderIllegalStateException">
            <summary>
            Exception thrown when a request is made of the Decoder, but the internal state
            machine is an invalid state. This is usually the result of an interanl exception
            being thrown during parsing of the network stream.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.Internal.IEventStreamDecoder">
            <summary>
            Contract for EventStreamDecoder
            </summary>
        </member>
        <member name="E:Amazon.Runtime.EventStreams.Internal.IEventStreamDecoder.MessageReceived">
            <summary>
            Invoked anytime an EventStreamMessage is found.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.IEventStreamDecoder.ProcessData(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Processes data starting at offset up to length.
            Invokes MessageRecieved for each message encountered.
            If an exception is thrown, this object is not safe for reuse.
            </summary>
            <param name="data">buffer to read</param>
            <param name="offset">offset in buffer to start reading.</param>
            <param name="length">length of data.</param>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.Internal.EventStreamDecoder">
            <summary>
            Streaming decoder for listening for incoming EventStreamMessage datagrams.
            </summary>
        </member>
        <member name="E:Amazon.Runtime.EventStreams.Internal.EventStreamDecoder.MessageReceived">
            <summary>
            Invoked anytime an EventStreamMessage is found.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.EventStreamDecoder.MessageReceivedContext">
            <summary>
            Object data (optional) that can be passed to the event handler for MessageReceived.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStreamDecoder.#ctor">
            <summary>
            Default constructor. Initializes internal _state machine.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStreamDecoder.ProcessData(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Processes data starting at offset up to length.
            Invokes MessageRecieved for each message encountered.
            If an exception is thrown, this object is not safe for reuse.
            </summary>
            <param name="data">buffer to read</param>
            <param name="offset">offset in buffer to start reading.</param>
            <param name="length">length of data.</param>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.Internal.EventStreamException">
            <summary>
            Superclass for Exceptions that come over the event stream, or to wrap other kinds of generic exceptions.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStreamException.#ctor">
            <summary>
            Constructs an EventStreamException.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStreamException.#ctor(System.String)">
            <summary>
            Constructs an EventStreamException.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.EventStreamException.#ctor(System.String,System.Exception)">
            <summary>
            Constructs an EventStreamException.
            </summary>
            <param name="message">The exception message.</param>
            <param name="innerException">The inner exception.</param>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.Internal.IEventStreamEvent">
            <summary>
            The contract for EventStream events.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.Internal.IEventStreamTerminalEvent">
            <summary>
            The contract for EventStream terminal events.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.Internal.UnknownEventStreamEvent">
            <summary>
            This Event is returned if an event is retrieved from the event stream, but a generator function
            for the event is not defined. This can occur if the SDK is not updated after a new event type is introduced.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.UnknownEventStreamEvent.ReceivedMessage">
            <summary>
            The Message recieved from the event stream before conversion.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.EventStreams.Internal.UnknownEventStreamEvent.EventType">
            <summary>
            The event type.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.UnknownEventStreamEvent.#ctor">
            <summary>
            Constructs an UnknownEventStreamEvent.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.Internal.UnknownEventStreamEvent.#ctor(Amazon.Runtime.EventStreams.IEventStreamMessage,System.String)">
            <summary>
            Constructs an UnknownEventStreamEvent.
            </summary>
            <param name="receivedMessage">The Message recieved from the event stream before conversion.</param>
            <param name="eventType">The event type.</param>
        </member>
        <member name="T:Amazon.Runtime.EventStreams.UnknownEventStreamMessageTypeException">
            <summary>
            Signals that an an <see cref="T:Amazon.Runtime.EventStreams.EventStreamMessage"/> has an unknown message type. These exceptions whould be
            swallowed to allow future expansion of the EventStream specification without breaking existing clients.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.EventStreams.UnknownEventStreamMessageTypeException.#ctor">
            <summary>
            Constructs an UnknownEventStreamMessageTypeException.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.IPipelineHandler">
            <summary>
            Interface for a handler in a pipeline.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IPipelineHandler.Logger">
            <summary>
            The logger used to log messages.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IPipelineHandler.InnerHandler">
            <summary>
            The inner handler which is called after the current 
            handler completes it's processing.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IPipelineHandler.OuterHandler">
            <summary>
            The outer handler which encapsulates the current handler.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.IPipelineHandler.InvokeSync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Contains the processing logic for a synchronous request invocation.
            This method should call InnerHandler.InvokeSync to continue processing of the
            request by the pipeline, unless it's a terminating handler.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.IPipelineHandler.InvokeAsync``1(Amazon.Runtime.IExecutionContext)">
            <summary>
            Contains the processing logic for an asynchronous request invocation.
            This method should call InnerHandler.InvokeSync to continue processing of the
            request by the pipeline, unless it's a terminating handler.
            </summary>
            <typeparam name="T">The response type for the current request.</typeparam>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <returns>A task that represents the asynchronous operation.</returns>
        </member>
        <member name="T:Amazon.Runtime.IExceptionHandler">
            <summary>
            The interface for an exception handler.
            </summary>    
        </member>
        <member name="M:Amazon.Runtime.IExceptionHandler.Handle(Amazon.Runtime.IExecutionContext,System.Exception)">
            <summary>
            Handles an exception for the given execution context.
            </summary>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <param name="exception">The exception to handle.</param>
            <returns>
            Returns a boolean value which indicates if the original exception
            should be rethrown.
            This method can also throw a new exception to replace the original exception.
            </returns>
        </member>
        <member name="T:Amazon.Runtime.IExceptionHandler`1">
            <summary>
            The interface for an exception handler with a generic parameter for the exception type.
            </summary>
            <typeparam name="T">The exception type.</typeparam>
        </member>
        <member name="M:Amazon.Runtime.IExceptionHandler`1.HandleException(Amazon.Runtime.IExecutionContext,`0)">
            <summary>
            Handles an exception for the given execution context.
            </summary>
            <param name="executionContext">The execution context, it contains the
            request and response context.</param>
            <param name="exception">The exception to handle.</param>
            <returns>
            Returns a boolean value which indicates if the original exception
            should be rethrown.
            This method can also throw a new exception to replace the original exception.
            </returns>
        </member>
        <member name="T:Amazon.Runtime.IHttpRequestFactory`1">
            <summary>
            The interface for a HTTP request factory.
            </summary>
            <typeparam name="TRequestContent">The type used by the underlying HTTP API to represent the request body.</typeparam>
        </member>
        <member name="M:Amazon.Runtime.IHttpRequestFactory`1.CreateHttpRequest(System.Uri)">
            <summary>
            Creates an HTTP request for the given URI.
            </summary>
            <param name="requestUri">The request URI.</param>
            <returns>An HTTP request.</returns>
        </member>
        <member name="T:Amazon.Runtime.IHttpRequest`1">
            <summary>
            The interface for an HTTP request that is agnostic of the underlying HTTP API.
            </summary>
            <typeparam name="TRequestContent">The type used by the underlying HTTP API to represent the HTTP request content.</typeparam>
        </member>
        <member name="P:Amazon.Runtime.IHttpRequest`1.Method">
            <summary>
            The HTTP method or verb.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.IHttpRequest`1.RequestUri">
            <summary>
            The request URI.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.IHttpRequest`1.ConfigureRequest(Amazon.Runtime.IRequestContext)">
            <summary>
            Configures a request as per the request context.
            </summary>
            <param name="requestContext">The request context.</param>
        </member>
        <member name="M:Amazon.Runtime.IHttpRequest`1.SetRequestHeaders(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Sets the headers on the request.
            </summary>
            <param name="headers">A dictionary of header names and values.</param>
        </member>
        <member name="M:Amazon.Runtime.IHttpRequest`1.GetRequestContent">
            <summary>
            Gets a handle to the request content.
            </summary>
            <returns>The request content.</returns>
        </member>
        <member name="M:Amazon.Runtime.IHttpRequest`1.GetResponse">
            <summary>
            Returns the HTTP response.
            </summary>
            <returns>The HTTP response.</returns>
        </member>
        <member name="M:Amazon.Runtime.IHttpRequest`1.WriteToRequestBody(`0,System.IO.Stream,System.Collections.Generic.IDictionary{System.String,System.String},Amazon.Runtime.IRequestContext)">
            <summary>
            Writes a stream to the request body.
            </summary>
            <param name="requestContent">The destination where the content stream is written.</param>
            <param name="contentStream">The content stream to be written.</param>
            <param name="contentHeaders">HTTP content headers.</param>
            <param name="requestContext">The request context.</param>
        </member>
        <member name="M:Amazon.Runtime.IHttpRequest`1.WriteToRequestBody(`0,System.Byte[],System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Writes a byte array to the request body.
            </summary>
            <param name="requestContent">The destination where the content stream is written.</param>
            <param name="content">The content stream to be written.</param>
            <param name="contentHeaders">HTTP content headers.</param>
        </member>
        <member name="M:Amazon.Runtime.IHttpRequest`1.SetupProgressListeners(System.IO.Stream,System.Int64,System.Object,System.EventHandler{Amazon.Runtime.StreamTransferProgressArgs})">
            <summary>
            Sets up the progress listeners
            </summary>
            <param name="originalStream">The content stream</param>
            <param name="progressUpdateInterval">The interval at which progress needs to be published</param>
            <param name="sender">The objects which is trigerring the progress changes</param>
            <param name="callback">The callback which will be invoked when the progress changed event is trigerred</param>
            <returns>an <see cref="T:Amazon.Runtime.Internal.Util.EventStream"/> object, incase the progress is setup, else returns the original stream</returns>
        </member>
        <member name="M:Amazon.Runtime.IHttpRequest`1.Abort">
            <summary>
            Aborts the HTTP request.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.IHttpRequest`1.GetRequestContentAsync">
            <summary>
            Gets a handle to the request content.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.IHttpRequest`1.GetResponseAsync(System.Threading.CancellationToken)">
            <summary>
            Returns the HTTP response.
            </summary>
            <param name="cancellationToken">A cancellation token that can be used to cancel the asynchronous operation.</param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.IHttpClientFactory">
            <summary>
            A factory which creates HTTP clients.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.IHttpClientFactory.CreateHttpClient(Amazon.Runtime.IClientConfig)">
            <summary>
            Create and configure an HttpClient.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.HttpRequestMessageFactory">
            <summary>
            A factory which creates HTTP requests which uses System.Net.Http.HttpClient.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.HttpRequestMessageFactory.#ctor(Amazon.Runtime.IClientConfig)">
            <summary>
            The constructor for HttpRequestMessageFactory.
            </summary>
            <param name="clientConfig">Configuration setting for a client.</param>
        </member>
        <member name="M:Amazon.Runtime.HttpRequestMessageFactory.CreateHttpRequest(System.Uri)">
            <summary>
            Creates an HTTP request for the given URI.
            </summary>
            <param name="requestUri">The request URI.</param>
            <returns>An HTTP request.</returns>
        </member>
        <member name="M:Amazon.Runtime.HttpRequestMessageFactory.Dispose">
            <summary>
            Disposes the HttpRequestMessageFactory.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.HttpRequestMessageFactory.Dispose(System.Boolean)">
            <summary>
            Dispose the factory
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:Amazon.Runtime.HttpRequestMessageFactory.CreateManagedHttpClient(Amazon.Runtime.IClientConfig)">
            <summary>
            Create and configure a managed HttpClient instance.
            The use of HttpClientHandler in the constructor for HttpClient implicitly creates a managed HttpClient.
            If a native HttpClient is required the SDK user must implement IHttpClientFactory and provide an
            instance via IClientConfig.HttpClientFactory (only available in PCL).
            </summary>
            <param name="clientConfig"></param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.HttpClientCache">
            <summary>
            A cache of HttpClient objects. The GetNextClient method does a round robin cycle through the clients
            to distribute the load even across.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.HttpClientCache.#ctor(System.Net.Http.HttpClient[])">
            <summary>
            Constructs a container for a cache of HttpClient objects
            </summary>
            <param name="clients">The HttpClient to cache</param>
        </member>
        <member name="M:Amazon.Runtime.HttpClientCache.GetNextClient">
            <summary>
            Returns the next HttpClient using a round robin rotation. It is expected that individual clients will be used
            by more then one Thread.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.HttpClientCache.Dispose">
            <summary>
            Disposes the HttpClientCache.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.HttpClientCache.Dispose(System.Boolean)">
            <summary>
            Dispose the HttpClientCache
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="T:Amazon.Runtime.HttpWebRequestMessage">
            <summary>
            HTTP request wrapper for System.Net.Http.HttpRequestMessage.
            </summary>    
        </member>
        <member name="F:Amazon.Runtime.HttpWebRequestMessage.ContentHeaderNames">
            <summary>
            Set of content header names.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.#ctor(System.Net.Http.HttpClient,System.Uri,Amazon.Runtime.IClientConfig)">
            <summary>
            The constructor for HttpWebRequestMessage.
            </summary>
            <param name="httpClient">The HttpClient used to make the request.</param>
            <param name="requestUri">The request URI.</param>
            <param name="config">The service client config.</param>
        </member>
        <member name="P:Amazon.Runtime.HttpWebRequestMessage.HttpClient">
            <summary>
            The underlying HttpClient
            </summary>
        </member>
        <member name="P:Amazon.Runtime.HttpWebRequestMessage.Request">
            <summary>
            The underlying HTTP web request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.HttpWebRequestMessage.Method">
            <summary>
            The HTTP method or verb.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.HttpWebRequestMessage.RequestUri">
            <summary>
            The request URI.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.ConfigureRequest(Amazon.Runtime.IRequestContext)">
            <summary>
            Configures a request as per the request context.
            </summary>
            <param name="requestContext">The request context.</param>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.SetRequestHeaders(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Sets the headers on the request.
            </summary>
            <param name="headers">A dictionary of header names and values.</param>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.GetRequestContent">
            <summary>
            Gets a handle to the request content.
            </summary>
            <returns>The request content.</returns>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.GetResponse">
            <summary>
            Returns the HTTP response.
            </summary>
            <returns>The HTTP response.</returns>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.Abort">
            <summary>
            Aborts the HTTP request.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.GetResponseAsync(System.Threading.CancellationToken)">
            <summary>
            Returns the HTTP response.
            </summary>
            <param name="cancellationToken">A cancellation token that can be used to cancel the asynchronous operation.</param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.WriteToRequestBody(System.Net.Http.HttpContent,System.IO.Stream,System.Collections.Generic.IDictionary{System.String,System.String},Amazon.Runtime.IRequestContext)">
            <summary>
            Writes a stream to the request body.
            </summary>
            <param name="requestContent">The destination where the content stream is written.</param>
            <param name="contentStream">The content stream to be written.</param>
            <param name="contentHeaders">HTTP content headers.</param>
            <param name="requestContext">The request context.</param>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.WriteToRequestBody(System.Net.Http.HttpContent,System.Byte[],System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Writes a byte array to the request body.
            </summary>
            <param name="requestContent">The destination where the content stream is written.</param>
            <param name="content">The content stream to be written.</param>
            <param name="contentHeaders">HTTP content headers.</param>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.GetRequestContentAsync">
            <summary>
            Gets a handle to the request content.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.Dispose">
            <summary>
            Disposes the HttpWebRequestMessage.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.Dispose(System.Boolean)">
            <summary>
            
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:Amazon.Runtime.HttpWebRequestMessage.SetupProgressListeners(System.IO.Stream,System.Int64,System.Object,System.EventHandler{Amazon.Runtime.StreamTransferProgressArgs})">
            <summary>
            Sets up the progress listeners
            </summary>
            <param name="originalStream">The content stream</param>
            <param name="progressUpdateInterval">The interval at which progress needs to be published</param>
            <param name="sender">The objects which is trigerring the progress changes</param>
            <param name="callback">The callback which will be invoked when the progress changed event is trigerred</param>
            <returns>an <see cref="T:Amazon.Runtime.Internal.Util.EventStream"/> object, incase the progress is setup, else returns the original stream</returns>
        </member>
        <member name="T:Amazon.Runtime.RetryPolicy">
            <summary>
            A retry policy specifies all aspects of retry behavior. This includes conditions when the request should be retried,
            checks of retry limit, preparing the request before retry and introducing delay (backoff) before retries.
            </summary>
            <summary>
            A retry policy specifies all aspects of retry behavior. This includes conditions when the request should be retried,
            checks of retry limit, preparing the request before retry and introducing delay (backoff) before retries.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.RetryPolicy.MaxRetries">
            <summary>
            Maximum number of retries to be performed.
            This does not count the initial request.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.RetryPolicy.Logger">
            <summary>
            The logger used to log messages.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.RetryPolicy.Retry(Amazon.Runtime.IExecutionContext,System.Exception)">
            <summary>
            Checks if a retry should be performed with the given execution context and exception.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
            <param name="exception">The exception thrown after issuing the request.</param>
            <returns>Returns true if the request should be retried, else false. The exception is retried if it matches with clockskew error codes.</returns>
        </member>
        <member name="M:Amazon.Runtime.RetryPolicy.CanRetry(Amazon.Runtime.IExecutionContext)">
            <summary>
            Returns true if the request is in a state where it can be retried, else false.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
            <returns>Returns true if the request is in a state where it can be retried, else false.</returns>
        </member>
        <member name="M:Amazon.Runtime.RetryPolicy.RetryForException(Amazon.Runtime.IExecutionContext,System.Exception)">
            <summary>
            Return true if the request should be retried for the given exception.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
            <param name="exception">The exception thrown by the previous request.</param>
            <returns>Return true if the request should be retried.</returns>
        </member>
        <member name="M:Amazon.Runtime.RetryPolicy.RetryLimitReached(Amazon.Runtime.IExecutionContext)">
            <summary>
            Checks if the retry limit is reached.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
            <returns>Return false if the request can be retried, based on number of retries.</returns>
        </member>
        <member name="M:Amazon.Runtime.RetryPolicy.WaitBeforeRetry(Amazon.Runtime.IExecutionContext)">
            <summary>
            Waits before retrying a request.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.RetryPolicy.NotifySuccess(Amazon.Runtime.IExecutionContext)">
            <summary>
            Virtual method that gets called on a successful request response.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.RetryPolicy.OnRetry(Amazon.Runtime.IExecutionContext)">
            <summary>
            Virtual method that gets called before a retry request is initiated. The value 
            returned is True by default(retry throttling feature is disabled).
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="M:Amazon.Runtime.RetryPolicy.OnRetry(Amazon.Runtime.IExecutionContext,System.Boolean)">
            <summary>
            Virtual method that gets called before a retry request is initiated. The value 
            returned is True by default(retry throttling feature is disabled).
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
            <param name="bypassAcquireCapacity">true to bypass any attempt to acquire capacity on a retry</param>
        </member>
        <member name="M:Amazon.Runtime.RetryPolicy.RetryAsync(Amazon.Runtime.IExecutionContext,System.Exception)">
            <summary>
            Checks if a retry should be performed with the given execution context and exception.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
            <param name="exception">The exception throw after issuing the request.</param>
            <returns>Returns true if the request should be retried, else false. The exception is retried if it matches with clockskew error codes.</returns>
        </member>
        <member name="M:Amazon.Runtime.RetryPolicy.RetryForExceptionAsync(Amazon.Runtime.IExecutionContext,System.Exception)">
            <summary>
            Return true if the request should be retried for the given exception.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
            <param name="exception">The exception thrown by the previous request.</param>
            <returns>Return true if the request should be retried.</returns>
        </member>
        <member name="M:Amazon.Runtime.RetryPolicy.WaitBeforeRetryAsync(Amazon.Runtime.IExecutionContext)">
            <summary>
            Waits before retrying a request.
            </summary>
            <param name="executionContext">The execution context which contains both the
            requests and response context.</param>
        </member>
        <member name="T:Amazon.Runtime.SharedInterfaces.ICoreAmazonKMS">
            <summary>
            ICoreAmazonKMS is not meant to be used directly. It defines Key Management Service
            with basic .NET types and allows other services to be able to use the service as
            a runtime dependency. This interface is implemented by the AmazonKeyManagementServiceClient
            defined in the AWSSDK.KeyManagementService assembly.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.SharedInterfaces.GenerateDataKeyResult">
            <summary>
            The result of the GenerateDataKey and GenerateDataKeyAsync operations.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.SharedInterfaces.GenerateDataKeyResult.KeyPlaintext">
            <summary>
            The plaintext for the data key.
            </summary>
        </member>
        <member name="P:Amazon.Runtime.SharedInterfaces.GenerateDataKeyResult.KeyCiphertext">
            <summary>
            The ciphertext for the data key.
            </summary>
        </member>
        <member name="T:Amazon.Runtime.SharedInterfaces.ICoreAmazonS3">
            <summary>
            ICoreAmazonS3 is not meant to use directly. It defines S3 with basic .NET types
            and allows other services to be able to use S3 as a runtime dependency. This interface
            is implemented by the AmazonS3Client defined in the S3 assembly.
            </summary>
            <summary>
            ICoreAmazonS3 is not meant to use directly. It defines S3 with basic .NET types
            and allows other services to be able to use S3 as a runtime dependency. This interface
            is implemented by the AmazonS3Client defined in the S3 assembly.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonS3.GeneratePreSignedURL(System.String,System.String,System.DateTime,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Generate a presigned URL.
            </summary>
            <param name="bucketName"></param>
            <param name="objectKey"></param>
            <param name="expiration"></param>
            <param name="additionalProperties"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonS3.GetAllObjectKeysAsync(System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Get all the object keys for the particular bucket and key prefix.
            </summary>
            <param name="bucketName"></param>
            <param name="prefix"></param>
            <param name="additionalProperties"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonS3.UploadObjectFromStreamAsync(System.String,System.String,System.IO.Stream,System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            Upload an object from a stream.
            </summary>
            <param name="bucketName"></param>
            <param name="objectKey"></param>
            <param name="stream"></param>
            <param name="additionalProperties"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonS3.DeleteAsync(System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            Delete an object.
            </summary>
            <param name="bucketName"></param>
            <param name="objectKey"></param>
            <param name="additionalProperties"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonS3.DeletesAsync(System.String,System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            Delete an object.
            </summary>
            <param name="bucketName"></param>
            <param name="objectKeys"></param>
            <param name="additionalProperties"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonS3.GetObjectStreamAsync(System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            Open a stream to an object in S3.
            </summary>
            <param name="bucketName"></param>
            <param name="objectKey"></param>
            <param name="additionalProperties"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonS3.UploadObjectFromFilePathAsync(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            Upload an object from a file path.
            </summary>
            <param name="bucketName"></param>
            <param name="objectKey"></param>
            <param name="filepath"></param>
            <param name="additionalProperties"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonS3.DownloadToFilePathAsync(System.String,System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.CancellationToken)">
            <summary>
            Download an object in S3 to a file path.
            </summary>
            <param name="bucketName"></param>
            <param name="objectKey"></param>
            <param name="filepath"></param>
            <param name="additionalProperties"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonS3.MakeObjectPublicAsync(System.String,System.String,System.Boolean)">
            <summary>
            Set the ACL on the object to public readable.
            </summary>
            <param name="bucketName"></param>
            <param name="objectKey"></param>
            <param name="enable"></param>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonS3.EnsureBucketExistsAsync(System.String)">
            <summary>
            Check to see if the bucket exists and if it doesn't create the bucket.
            </summary>
            <param name="bucketName"></param>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonS3.DoesS3BucketExistAsync(System.String)">
            <summary>
            Check to see if the bucket exists.
            </summary>
            <param name="bucketName"></param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.SharedInterfaces.ICoreAmazonSQS">
            <summary>
            ICoreAmazonSQS is not meant to use directly. It defines SQS with basic .NET types
            and allows other services to be able to use SQS as a runtime dependency. This interface
            is implemented by the AmazonSQSClient defined in the SQS assembly.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonSQS.GetAttributesAsync(System.String)">
            <summary>
            Get the attributes for the queue identified by the queue URL asynchronously.
            </summary>
            <param name="queueUrl">The queue URL to get attributes for.</param>
            <returns>A Task containing the result of a dictionary of attributes for the queue.</returns>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonSQS.SetAttributesAsync(System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Set the attributes on the queue identified by the queue URL asynchronously.
            </summary>
            <param name="queueUrl">The queue URL to set the attributues.</param>
            <param name="attributes">The attributes to set.</param>
            <returns>A Task</returns>
        </member>
        <member name="T:Amazon.Runtime.SharedInterfaces.ICoreAmazonSTS">
            <summary>
            ICoreAmazonSTS is not meant to use directly. It defines Security Token
            service with basic .NET types and allows other services to be able to use the service as 
            a runtime dependency. This interface is implemented by the AmazonSecurityTokenServiceClient 
            defined in the AWSSDK.SecurityToken assembly.
            </summary>
        </member>
        <member name="M:Amazon.Runtime.SharedInterfaces.ICoreAmazonSTS.CredentialsFromAssumeRoleAuthentication(System.String,System.String,Amazon.Runtime.AssumeRoleAWSCredentialsOptions)">
            <summary>
            <para>
            This method is used internally to access the Amazon Security Token
            service within other service assemblies.
            Please use AmazonSecurityTokenServiceClient to access the Amazon Security Token
            service instead.
            </para>
            Use Amazon Security Token Service to assume a role.
            <remarks>
            Proxy settings that are required for the HTTPS and STS calls made during the authentication/credential
            generation process are supported and should have been configured on the STS ClientConfig instance
            associated with the STS client instance exposing this interface.
            </remarks>
            </summary>
            <param name="roleArn">The Amazon Resource Name (ARN) of the role to assume.</param>
            <param name="roleSessionName"> An identifier for the assumed role session.</param>
            <param name="options">Options to be used in the call to AssumeRole.</param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Runtime.SharedInterfaces.Internal.CoreAmazonKMS">
             <summary>
             CoreAmazonKMS is not meant to use directly. It implements ICoreAmazonKMS
             and allows other service clients to be able to use the KMS service as a runtime dependency.
            
             This class waits until a method is actually called on the ICoreAmazonKMS interface
             before requiring the KMS assembly to be present and loaded.
             </summary>
        </member>
        <member name="T:Amazon.Util.ProxyConfig">
            <summary>
            Settings for configuring a proxy for the SDK to use.
            </summary>
        </member>
        <member name="P:Amazon.Util.ProxyConfig.Host">
            <summary>
            The host name or IP address of the proxy server.
            </summary>
        </member>
        <member name="P:Amazon.Util.ProxyConfig.Port">
            <summary>
            The port number of the proxy.
            </summary>
        </member>
        <member name="P:Amazon.Util.ProxyConfig.Username">
            <summary>
            The username to authenticate with the proxy server.
            </summary>
        </member>
        <member name="P:Amazon.Util.ProxyConfig.Password">
            <summary>
            The password to authenticate with the proxy server.
            </summary>
        </member>
        <member name="P:Amazon.Util.ProxyConfig.BypassList">
            <summary>
            Collection of one or more regular expressions denoting addresses
            for which the proxy will not be used.
            </summary>
            <remarks>
             For more information on bypass lists 
             see https://msdn.microsoft.com/en-us/library/system.net.webproxy.bypasslist%28v=vs.110%29.aspx.
            </remarks>
        </member>
        <member name="P:Amazon.Util.ProxyConfig.BypassOnLocal">
            <summary>
            If true requests to local addresses bypass the configured
            proxy.
            </summary>
        </member>
        <member name="T:Amazon.Util.LoggingConfig">
            <summary>
            Settings for logging in the SDK.
            </summary>
        </member>
        <member name="P:Amazon.Util.LoggingConfig.LogTo">
            <summary>
            Logging destination.
            </summary>
        </member>
        <member name="P:Amazon.Util.LoggingConfig.LogResponses">
            <summary>
            When to log responses.
            </summary>
        </member>
        <member name="P:Amazon.Util.LoggingConfig.LogResponsesSizeLimit">
            <summary>        
            Gets or sets the size limit in bytes for logged responses.
            If logging for response body is enabled, logged response
            body is limited to this size. The default limit is 1KB.
            </summary>
        </member>
        <member name="P:Amazon.Util.LoggingConfig.LogMetrics">
            <summary>
            Whether or not to log SDK metrics.
            </summary>
        </member>
        <member name="P:Amazon.Util.LoggingConfig.LogMetricsCustomFormatter">
            <summary>
            A custom formatter added through Configuration
            </summary>
        </member>
        <member name="T:Amazon.Util.AWSPublicIpAddressRanges">
            <summary>
            This class can be used to discover the public address ranges for AWS. The 
            information is retrieved from the publicly accessible 
            https://ip-ranges.amazonaws.com/ip-ranges.json file.
            </summary>
            <remarks>
            The information in this file is generated from our internal system-of-record and 
            is authoritative. You can expect it to change several times per week and should 
            poll accordingly. 
            </remarks>
        </member>
        <member name="F:Amazon.Util.AWSPublicIpAddressRanges.GlobalRegionIdentifier">
            <summary>
            Region identifier string for ROUTE53 and CLOUDFRONT ranges
            </summary>
        </member>
        <member name="P:Amazon.Util.AWSPublicIpAddressRanges.ServiceKeys">
            <summary>
            Collection of service keys found in the data file.
            </summary>
        </member>
        <member name="P:Amazon.Util.AWSPublicIpAddressRanges.CreateDate">
            <summary>
            The publication date and time of the file that was downloaded and parsed.
            </summary>
        </member>
        <member name="P:Amazon.Util.AWSPublicIpAddressRanges.AllAddressRanges">
            <summary>
            Collection of all public IP ranges.
            </summary>
        </member>
        <member name="M:Amazon.Util.AWSPublicIpAddressRanges.AddressRangesByServiceKey(System.String)">
            <summary>
            Filtered collection of public IP ranges for the given service key
            </summary>
        </member>
        <member name="M:Amazon.Util.AWSPublicIpAddressRanges.AddressRangesByRegion(System.String)">
            <summary>
            Filtered collection of public IP ranges for the given region (us-east-1 etc) or GLOBAL.
            </summary>
        </member>
        <member name="M:Amazon.Util.AWSPublicIpAddressRanges.Load">
            <summary>
            Downloads the most recent copy of the endpoint address file and
            parses it to a collection of address range objects.
            </summary>
        </member>
        <member name="M:Amazon.Util.AWSPublicIpAddressRanges.Load(System.Net.IWebProxy)">
            <summary>
            Downloads the most recent copy of the endpoint address file and
            parses it to a collection of address range objects.
            </summary>
        </member>
        <member name="T:Amazon.Util.AWSPublicIpAddressRange">
            <summary>
            Represents the IP address range for a single region and service key.
            </summary>
        </member>
        <member name="P:Amazon.Util.AWSPublicIpAddressRange.IpPrefix">
            <summary>
            The public IPv4 or Ipv6 address range, in CIDR notation.
            </summary>
        </member>
        <member name="P:Amazon.Util.AWSPublicIpAddressRange.IpAddressFormat">
            <summary>
            Indicates ipv4 or v6 format of the address
            </summary>
        </member>
        <member name="P:Amazon.Util.AWSPublicIpAddressRange.Region">
            <summary>
            The AWS region or GLOBAL for edge locations. Note that the CLOUDFRONT and ROUTE53 
            ranges are GLOBAL.
            </summary>
        </member>
        <member name="P:Amazon.Util.AWSPublicIpAddressRange.Service">
            <summary>
            The subset of IP address ranges. Specify AMAZON to get all IP address ranges 
            (for example, the ranges in the EC2 subset are also in the AMAZON subset). Note 
            that some IP address ranges are only in the AMAZON subset.
            </summary>
            <remarks>
            Valid values for the service key include "AMAZON", "EC2", "ROUTE53", 
            "ROUTE53_HEALTHCHECKS", and "CLOUDFRONT." If you need to know all of 
            the ranges and don't care about the service, use the "AMAZON" entries. 
            The other entries are subsets of this one. Also, some of the services, 
            such as S3, are represented in "AMAZON" and do not have an entry that 
            is specific to the service. We plan to add additional values over time; 
            code accordingly! 
            </remarks>
        </member>
        <member name="T:Amazon.Util.AWSSDKUtils">
            <summary>
            This class defines utilities and constants that can be used by 
            all the client libraries of the SDK.
            </summary>
        </member>
        <member name="F:Amazon.Util.AWSSDKUtils.UserAgentHeader">
            <summary>
            The user agent string header
            </summary>
        </member>
        <member name="F:Amazon.Util.AWSSDKUtils.ValidUrlCharacters">
            <summary>
            The Set of accepted and valid Url characters per RFC3986. 
            Characters outside of this set will be encoded.
            </summary>
        </member>
        <member name="F:Amazon.Util.AWSSDKUtils.ValidUrlCharactersRFC1738">
            <summary>
            The Set of accepted and valid Url characters per RFC1738. 
            Characters outside of this set will be encoded.
            </summary>
        </member>
        <member name="F:Amazon.Util.AWSSDKUtils.ValidPathCharacters">
            <summary>
            The set of accepted and valid Url path characters per RFC3986.
            </summary>
        </member>
        <member name="F:Amazon.Util.AWSSDKUtils.UrlEncodedContent">
            <summary>
            The string representing Url Encoded Content in HTTP requests
            </summary>
        </member>
        <member name="F:Amazon.Util.AWSSDKUtils.GMTDateFormat">
            <summary>
            The GMT Date Format string. Used when parsing date objects
            </summary>
        </member>
        <member name="F:Amazon.Util.AWSSDKUtils.ISO8601DateFormat">
            <summary>
            The ISO8601Date Format string. Used when parsing date objects
            </summary>
        </member>
        <member name="F:Amazon.Util.AWSSDKUtils.ISO8601DateFormatNoMS">
            <summary>
            The ISO8601Date Format string. Used when parsing date objects
            </summary>
        </member>
        <member name="F:Amazon.Util.AWSSDKUtils.ISO8601BasicDateTimeFormat">
            <summary>
            The ISO8601 Basic date/time format string. Used when parsing date objects
            </summary>
        </member>
        <member name="F:Amazon.Util.AWSSDKUtils.ISO8601BasicDateFormat">
            <summary>
            The ISO8601 basic date format. Used during AWS4 signature computation.
            </summary>
        </member>
        <member name="F:Amazon.Util.AWSSDKUtils.RFC822DateFormat">
            <summary>
            The RFC822Date Format string. Used when parsing date objects
            </summary>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.GetExtension(System.String)">
            <summary>
            Returns an extension of a path.
            This has the same behavior as System.IO.Path.GetExtension, but does not
            check the path for invalid characters.
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.GetParametersAsString(Amazon.Runtime.Internal.IRequest)">
            Convert request parameters to Url encoded query string
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.GetParametersAsString(Amazon.Runtime.Internal.ParameterCollection)">
            Convert Dictionary of parameters to Url encoded query string
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.CanonicalizeResourcePath(System.Uri,System.String)">
            <summary>
            Returns the canonicalized resource path for the service endpoint with single URL encoded path segments.
            </summary>
            <param name="endpoint">Endpoint URL for the request</param>
            <param name="resourcePath">Resource path for the request</param>
            <remarks>
            If resourcePath begins or ends with slash, the resulting canonicalized
            path will follow suit.
            </remarks>
            <returns>Canonicalized resource path for the endpoint</returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.CanonicalizeResourcePath(System.Uri,System.String,System.Boolean)">
            <summary>
            Returns the canonicalized resource path for the service endpoint
            </summary>
            <param name="endpoint">Endpoint URL for the request</param>
            <param name="resourcePath">Resource path for the request</param>
            <param name="detectPreEncode">If true pre URL encode path segments if necessary.
            S3 is currently the only service that does not expect pre URL encoded segments.</param>
            <remarks>
            If resourcePath begins or ends with slash, the resulting canonicalized
            path will follow suit.
            </remarks>
            <returns>Canonicalized resource path for the endpoint</returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.Join(System.Collections.Generic.List{System.String})">
            <summary>
            Returns a new string created by joining each of the strings in the
            specified list together, with a comma between them.
            </summary>
            <parma name="strings">The list of strings to join into a single, comma delimited
            string list.</parma>
            <returns> A new string created by joining each of the strings in the
            specified list together, with a comma between strings.</returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.DetermineRegion(System.String)">
            <summary>
            Attempt to infer the region for a service request based on the endpoint
            </summary>
            <param name="url">Endpoint to the service to be called</param>
            <returns>
            Region parsed from the endpoint; DefaultRegion (or DefaultGovRegion) 
            if it cannot be determined/is not explicit
            </returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.DetermineService(System.String)">
            <summary>
            Attempt to infer the service name for a request (in short form, eg 'iam') from the
            service endpoint.
            </summary>
            <param name="url">Endpoint to the service to be called</param>
            <returns>
            Short-form name of the service parsed from the endpoint; empty string if it cannot 
            be determined
            </returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.ConvertFromUnixEpochSeconds(System.Int32)">
            <summary>
            Utility method for converting Unix epoch seconds to DateTime structure.
            </summary>
            <param name="seconds">The number of seconds since January 1, 1970.</param>
            <returns>Converted DateTime structure</returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.ToHex(System.Byte[],System.Boolean)">
            <summary>
            Helper function to format a byte array into string
            </summary>
            <param name="data">The data blob to process</param>
            <param name="lowercase">If true, returns hex digits in lower case form</param>
            <returns>String version of the data</returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.InvokeInBackground``1(System.EventHandler{``0},``0,System.Object)">
            <summary>
            Calls a specific EventHandler in a background thread
            </summary>
            <param name="handler"></param>
            <param name="args"></param>
            <param name="sender"></param>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.ParseQueryParameters(System.String)">
            <summary>
            Parses a query string of a URL and returns the parameters as a string-to-string dictionary.
            </summary>
            <param name="url"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.GenerateMemoryStreamFromString(System.String)">
            <summary>
            Utility method for converting a string to a MemoryStream.
            </summary>
            <param name="s"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.CopyStream(System.IO.Stream,System.IO.Stream)">
            <summary>
            Utility method for copy the contents of the source stream to the destination stream.
            </summary>
            <param name="source"></param>
            <param name="destination"></param>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.CopyStream(System.IO.Stream,System.IO.Stream,System.Int32)">
            <summary>
            Utility method for copy the contents of the source stream to the destination stream.
            </summary>
            <param name="source"></param>
            <param name="destination"></param>
            <param name="bufferSize"></param>
        </member>
        <member name="P:Amazon.Util.AWSSDKUtils.FormattedCurrentTimestampGMT">
            <summary>
            Formats the current date as a GMT timestamp
            </summary>
            <returns>A GMT formatted string representation
            of the current date and time
            </returns>
        </member>
        <member name="P:Amazon.Util.AWSSDKUtils.FormattedCurrentTimestampISO8601">
            <summary>
            Formats the current date as ISO 8601 timestamp
            </summary>
            <returns>An ISO 8601 formatted string representation
            of the current date and time
            </returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.GetFormattedTimestampISO8601(System.Int32)">
            <summary>
            Gets the ISO8601 formatted timestamp that is minutesFromNow
            in the future.
            </summary>
            <param name="minutesFromNow">The number of minutes from the current instant
            for which the timestamp is needed.</param>
            <returns>The ISO8601 formatted future timestamp.</returns>
        </member>
        <member name="P:Amazon.Util.AWSSDKUtils.FormattedCurrentTimestampRFC822">
            <summary>
            Formats the current date as ISO 8601 timestamp
            </summary>
            <returns>An ISO 8601 formatted string representation
            of the current date and time
            </returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.GetFormattedTimestampRFC822(System.Int32)">
            <summary>
            Gets the RFC822 formatted timestamp that is minutesFromNow
            in the future.
            </summary>
            <param name="minutesFromNow">The number of minutes from the current instant
            for which the timestamp is needed.</param>
            <returns>The ISO8601 formatted future timestamp.</returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.UrlEncode(System.String,System.Boolean)">
            <summary>
            URL encodes a string per RFC3986. If the path property is specified,
            the accepted path characters {/+:} are not encoded.
            </summary>
            <param name="data">The string to encode</param>
            <param name="path">Whether the string is a URL path or not</param>
            <returns>The encoded string</returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.UrlEncode(System.Int32,System.String,System.Boolean)">
            <summary>
            URL encodes a string per the specified RFC. If the path property is specified,
            the accepted path characters {/+:} are not encoded.
            </summary>
            <param name="rfcNumber">RFC number determing safe characters</param>
            <param name="data">The string to encode</param>
            <param name="path">Whether the string is a URL path or not</param>
            <returns>The encoded string</returns>
            <remarks>
            Currently recognised RFC versions are 1738 (Dec '94) and 3986 (Jan '05). 
            If the specified RFC is not recognised, 3986 is used by default.
            </remarks>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.ProtectEncodedSlashUrlEncode(System.String,System.Boolean)">
            <summary>
            URL encodes a string per the specified RFC with the exception of preserving the encoding of previously encoded slashes.
            If the path property is specified, the accepted path characters {/+:} are not encoded. 
            </summary>
            <param name="data">The string to encode</param>
            <param name="path">Whether the string is a URL path or not</param>
            <returns>The encoded string with any previously encoded %2F preserved</returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.BytesToHexString(System.Byte[])">
            <summary>
            Convert bytes to a hex string
            </summary>
            <param name="value">Bytes to convert.</param>
            <returns>Hexadecimal string representing the byte array.</returns>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.HexStringToBytes(System.String)">
            <summary>
            Convert a hex string to bytes
            </summary>
            <param name="hex">Hexadecimal string</param>
            <returns>Byte array corresponding to the hex string.</returns>
        </member>
        <member name="P:Amazon.Util.AWSSDKUtils.CorrectedUtcNow">
            <summary>
            Returns DateTime.UtcNow + ManualClockCorrection when
            <seealso cref="P:Amazon.AWSConfigs.ManualClockCorrection"/> is set.
            This value should be used instead of DateTime.UtcNow to factor in manual clock correction
            </summary>
        </member>
        <member name="M:Amazon.Util.AWSSDKUtils.HasBidiControlCharacters(System.String)">
            <summary>
            Returns true if the string has any bidirectional control characters.
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Util.CircularReferenceTracking">
            <summary>
            Object to track circular references in nested types.
            At each level of nesting, make a call to Track to retrieve Tracker,
            a tracking object implementing the IDisposable interface.
            Dispose of this tracker when leaving the context of the tracked object.
            </summary>
        </member>
        <member name="T:Amazon.Util.CircularReferenceTracking.Tracker">
            <summary>
            Tracker. Must be disposed.
            </summary>
        </member>
        <member name="M:Amazon.Util.CircularReferenceTracking.Tracker.Dispose(System.Boolean)">
            <summary>
            Implements the Dispose pattern
            </summary>
            <param name="disposing">Whether this object is being disposed via a call to Dispose
            or garbage collected.</param>
        </member>
        <member name="M:Amazon.Util.CircularReferenceTracking.Tracker.Dispose">
            <summary>
            Disposes of all managed and unmanaged resources.
            </summary>
        </member>
        <member name="M:Amazon.Util.CircularReferenceTracking.Track(System.Object)">
            <summary>
            Adds the current target to a reference list and returns a tracker.
            The tracker removes the target from the reference list when the
            tracker is disposed.
            </summary>
            <param name="target"></param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Util.Internal.RootConfig">
            <summary>
            Root AWS config
            </summary>
        </member>
        <member name="T:Amazon.Util.AWSHttpClient">
            <summary>
            AwsHttpClient is a wrapper around HttpClient class of the System.Net.Http library.
            The wrapper has been added so as to remove System.Net.Http dependencies from the Services
            and migrate it to a Core level dependecy only. This avoids version clashes when a service 
            and the Core depends on different versions of the System.Net.Http library. This is a Service 
            level utility method
            </summary>
        </member>
        <member name="M:Amazon.Util.AWSHttpClient.#ctor">
            <summary>
            AWSHttpClient wrapper that is wrapped around
            HttpClient default wrapper.
            </summary>
        </member>
        <member name="P:Amazon.Util.AWSHttpClient.BaseAddress">
            <summary>
            AWSHttpClient BaseAddress property that is wrapper for
            HttpClient BaseAddress property.
            </summary>
        </member>
        <member name="P:Amazon.Util.AWSHttpClient.Timeout">
            <summary>
            AWSHttpClient Timeout property that is wrapper for
            HttpClient Timeout property.
            </summary>
        </member>
        <member name="P:Amazon.Util.AWSHttpClient.MaxResponseContentBufferSize">
            <summary>
            AWSHttpClient MaxResponseContentBufferSize property that is wrapper for
            HttpClient MaxResponseContentBufferSize property.
            </summary>
        </member>
        <member name="M:Amazon.Util.AWSHttpClient.GetStreamAsync(System.String)">
            <summary>
            AWSHttpClient GetStreamAsync that accepts the requester's URI
            and make a HttpClient.GetStreamAsync call.
            </summary>
            <param name="requestUri">Requester Uri</param>
        </member>
        <member name="M:Amazon.Util.AWSHttpClient.PutRequestUriAsync(System.String,Amazon.Util.AWSStreamContent,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Wrapper method that accepts a request uri, stream content and headers and makes a sendAsync call.
            </summary>
            <param name="requestUri"></param>
            <param name="content"></param>
        </member>
        <member name="M:Amazon.Util.AWSHttpClient.GetResponseHeadersAsync(System.String,System.String)">
            <summary>
            Wrapper for HttpClient's HttpRequestMessage and SendAsync methods.
            </summary>
            <param name="httpMethodValue"></param>
            <param name="url"></param>
            <returns>Returns response headers information- header name,value and status code</returns>
        </member>
        <member name="M:Amazon.Util.AWSHttpClient.Dispose">
            <summary>
            Wrapper for HttpClient Dispose.
            </summary>
        </member>
        <member name="M:Amazon.Util.AWSHttpClient.IsHttpInnerException(System.Exception)">
            <summary>
            Method that checks if the passed exception is of type
            HttpRequestException
            </summary>
            <param name="exception"></param>
            <returns></returns>
        </member>
        <member name="T:Amazon.Util.AWSStreamContent">
            <summary>
            AWSStreamContent is a wrapper for StreamContent class in
            the System.Net.Http library.The wrapper has been added so as to 
            remove System.Net.Http dependencies from the Services
            and migrate it to a Core level dependecy only. This avoids version clashes when a service 
            and the Core depends on different versions of the System.Net.Http library. This is a Service 
            level utility method
            </summary>
        </member>
        <member name="M:Amazon.Util.AWSStreamContent.#ctor(System.IO.Stream)">
            <summary>
            Wrapper for StreamContent constructor
            for stream content
            </summary>
            <param name="content"></param>
        </member>
        <member name="M:Amazon.Util.AWSStreamContent.#ctor(System.IO.Stream,System.Int32)">
            <summary>
            Wrapper for StreamContent constructor
            for stream content and bufferSize
            </summary>
            <param name="content"></param>
            <param name="bufferSize"></param>
        </member>
        <member name="M:Amazon.Util.AWSStreamContent.RemoveHttpContentHeader(System.String)">
            <summary>
            Wrapper to allow Services to remove StreamContent Headers
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Util.AWSStreamContent.AddHttpContentHeader(System.String,System.String)">
            <summary>
            Wrapper to allow Services to add StreamContent Headers
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Util.AWSStreamContent.Dispose">
            <summary>
            Wrapper for StreamContent Dispose.
            </summary>
        </member>
        <member name="P:Amazon.Internal.RegionEndpointProviderV2.Proxy">
            <summary>
            Allows to configure the proxy used for HTTP requests. The default value is null.
            </summary>
        </member>
        <member name="T:Amazon.Internal.RegionEndpointProviderV2.RegionEndpoint">
            <summary>
            This class contains the endpoints available to the AWS clients.  The static constants representing the 
            regions can be used while constructing the AWS client instead of looking up the exact endpoint URL.
            </summary>
        </member>
        <member name="M:Amazon.Internal.RegionEndpointProviderV2.RegionEndpoint.GetEndpointForService(System.String,System.Boolean)">
            <summary>
            Gets the endpoint for a service in a region.
            </summary>
            <param name="serviceName">The services system name.</param>
            <param name="dualStack">If true returns the endpoint for dualstack</param>
            <exception cref="T:System.ArgumentException">Thrown when the request service does not have a valid endpoint in the region.</exception>
            <returns></returns>
        </member>
        <member name="P:Amazon.Internal.RegionEndpointProviderV2.RegionEndpoint.EnumerableAllRegions">
            <summary>
            Enumerate through all the regions.
            </summary>
        </member>
        <member name="M:Amazon.Internal.RegionEndpointProviderV2.RegionEndpoint.GetBySystemName(System.String)">
            <summary>
            Gets the region based on its system name like "us-west-1"
            </summary>
            <param name="systemName">The system name of the service like "us-west-1"</param>
            <returns></returns>
        </member>
        <member name="M:Amazon.Internal.RegionEndpointProviderV2.RegionEndpoint.UnloadEndpointDefinitions">
            <summary>
            This is a testing method and should not be called by production applications.
            </summary>
        </member>
        <member name="P:Amazon.Internal.RegionEndpointProviderV2.RegionEndpoint.SystemName">
            <summary>
            Gets the system name of a region.
            </summary>
        </member>
        <member name="P:Amazon.Internal.RegionEndpointProviderV2.RegionEndpoint.DisplayName">
            <summary>
            Gets the display name of a region.
            </summary>
        </member>
        <member name="M:Amazon.Internal.RegionEndpointProviderV3.GetNonstandardRegionEndpoint(System.String)">
            <summary>
            This region name is non-standard.  Search the whole endpoints.json file to
            determine the partition this region is in.
            </summary>
        </member>
        <member name="T:Amazon.RegionEndpoint">
            <summary>
            This class contains region information used to lazily compute the service endpoints. The static constants representing the 
            regions can be used while constructing the AWS client instead of looking up the exact endpoint URL.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.USEast1">
            <summary>
            The US East (Virginia) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.USEast2">
            <summary>
            The US East (Ohio) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.USWest1">
            <summary>
            The US West (N. California) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.USWest2">
            <summary>
            The US West (Oregon) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.EUNorth1">
            <summary>
            The EU North (Stockholm) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.EUWest1">
            <summary>
            The EU West (Ireland) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.EUWest2">
            <summary>
            The EU West (London) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.EUWest3">
            <summary>
            The EU West (Paris) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.EUCentral1">
            <summary>
            The EU Central (Frankfurt) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.APNortheast1">
            <summary>
            The Asia Pacific (Tokyo) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.APNortheast2">
            <summary>
            The Asia Pacific (Seoul) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.APNortheast3">
            <summary>
            The Asia Pacific (Osaka-Local) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.APSouth1">
            <summary>
            The Asia Pacific (Mumbai) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.APSoutheast1">
            <summary>
            The Asia Pacific (Singapore) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.APSoutheast2">
            <summary>
            The Asia Pacific (Sydney) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.SAEast1">
            <summary>
            The South America (Sao Paulo) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.USGovCloudEast1">
            <summary>
            The US GovCloud East (Virginia) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.USGovCloudWest1">
            <summary>
            The US GovCloud West (Oregon) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.CNNorth1">
            <summary>
            The China (Beijing) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.CNNorthWest1">
            <summary>
            The China (Ningxia) endpoint.
            </summary>
        </member>
        <member name="F:Amazon.RegionEndpoint.CACentral1">
            <summary>
            The Canada (Central) endpoint.
            </summary>
        </member>
        <member name="P:Amazon.RegionEndpoint.EnumerableAllRegions">
            <summary>
            Enumerate through all the regions.
            </summary>
        </member>
        <member name="M:Amazon.RegionEndpoint.GetBySystemName(System.String)">
            <summary>
            Gets the region based on its system name like "us-west-1"
            </summary>
            <param name="systemName">The system name of the service like "us-west-1"</param>
            <returns></returns>
        </member>
        <member name="P:Amazon.RegionEndpoint.SystemName">
            <summary>
            Gets the system name of a region.
            </summary>
        </member>
        <member name="P:Amazon.RegionEndpoint.DisplayName">
            <summary>
            Gets the display name of a region.
            </summary>
        </member>
        <member name="M:Amazon.RegionEndpoint.GetEndpointForService(System.String)">
            <summary>
            Gets the endpoint for a service in a region.
            </summary>
            <param name="serviceName">
            The services system name. Service system names can be obtained from the
            RegionEndpointServiceName member of the ClientConfig-derived class for the service.
            </param>
            <param>
            For forwards compatibility, if the service being requested for isn't known in the region, this method 
            will generate an endpoint using the AWS endpoint heuristics. In this case, it is not guaranteed the
            endpoint will point to a valid service endpoint.
            </param>
            <returns></returns>
        </member>
        <member name="M:Amazon.RegionEndpoint.GetEndpointForService(System.String,System.Boolean)">
            <summary>
            Gets the endpoint for a service in a region, optionally selecting a dualstack compatible endpoint.
            </summary>
            <param name="serviceName">
            The services system name. Service system names can be obtained from the
            RegionEndpointServiceName member of the ClientConfig-derived class for the service.
            </param>
            <param name="dualStack">
            If true a dualstack endpoint is returned. It is the user's responsibility to verify that the given service
            supports a dualstack endpoint for the region.
            </param>
            <param>
            For forwards compatibility, if the service being requested for isn't known in the region, this method 
            will generate an endpoint using the AWS endpoint heuristics. In this case, it is not guaranteed the
            endpoint will point to a valid service endpoint.
            </param>
            <returns></returns>
        </member>
        <member name="T:Amazon.RegionEndpoint.Endpoint">
            <summary>
            This class defines an endpoints hostname and which protocols it supports.
            </summary>
        </member>
        <member name="P:Amazon.RegionEndpoint.Endpoint.Hostname">
            <summary>
            Gets the hostname for the service.
            </summary>
        </member>
        <member name="P:Amazon.RegionEndpoint.Endpoint.AuthRegion">
            <summary>
            The authentication region to be used in request signing.
            </summary>
        </member>
        <member name="P:Amazon.RegionEndpoint.Endpoint.SignatureVersionOverride">
            <summary>
            This property is only set for S3 endpoints.  For all other services this property returns null.
            For S3 endpoints, if the endpoint supports signature version 2 this property will be "2", otherwise it will be "4".
            </summary>
        </member>
        <member name="T:ThirdParty.Ionic.Zlib.CRC32">
            <summary>
            Calculates a 32bit Cyclic Redundancy Checksum (CRC) using the
            same polynomial used by Zip. This type is used internally by DotNetZip; it is generally not used directly
            by applications wishing to create, read, or manipulate zip archive files.
            </summary>
        </member>
        <member name="P:ThirdParty.Ionic.Zlib.CRC32.TotalBytesRead">
            <summary>
            indicates the total number of bytes read on the CRC stream.
            This is used when writing the ZipDirEntry when compressing files.
            </summary>
        </member>
        <member name="P:ThirdParty.Ionic.Zlib.CRC32.Crc32Result">
            <summary>
            Indicates the current CRC for all blocks slurped in.
            </summary>
        </member>
        <member name="M:ThirdParty.Ionic.Zlib.CRC32.GetCrc32(System.IO.Stream)">
            <summary>
            Returns the CRC32 for the specified stream.
            </summary>
            <param name="input">The stream over which to calculate the CRC32</param>
            <returns>the CRC32 calculation</returns>
        </member>
        <member name="M:ThirdParty.Ionic.Zlib.CRC32.GetCrc32AndCopy(System.IO.Stream,System.IO.Stream)">
            <summary>
            Returns the CRC32 for the specified stream, and writes the input into the output stream.
            </summary>
            <param name="input">The stream over which to calculate the CRC32</param>
            <param name="output">The stream into which to deflate the input</param>
            <returns>the CRC32 calculation</returns>
        </member>
        <member name="M:ThirdParty.Ionic.Zlib.CRC32.ComputeCrc32(System.Int32,System.Byte)">
            <summary>
            Get the CRC32 for the given (word,byte) combo. 
            This is a computation defined by PKzip.
            </summary>
            <param name="W">The word to start with.</param>
            <param name="B">The byte to combine it with.</param>
            <returns>The CRC-ized result.</returns>
        </member>
        <member name="M:ThirdParty.Ionic.Zlib.CRC32.SlurpBlock(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Update the value for the running CRC32 using the given block of bytes.
            This is useful when using the CRC32() class in a Stream.
            </summary>
            <param name="block">block of bytes to slurp</param>
            <param name="offset">starting point in the block</param>
            <param name="count">how many bytes within the block to slurp</param>
        </member>
        <member name="T:ThirdParty.Ionic.Zlib.CrcCalculatorStream">
             <summary>
             A Stream that calculates a CRC32 (a checksum) on all bytes read, 
             or on all bytes written.
             </summary>
            
             <remarks>
             <para>
             This class can be used to verify the CRC of a ZipEntry when reading from a stream, 
             or to calculate a CRC when writing to a stream.  The stream should be used to either 
             read, or write, but not both.  If you intermix reads and writes, the results are
             not defined. 
             </para>
             <para>This class is intended primarily for use internally by the DotNetZip library.</para>
             </remarks>
        </member>
        <member name="P:ThirdParty.Ionic.Zlib.CrcCalculatorStream.TotalBytesSlurped">
             <summary>
             Gets the total number of bytes run through the CRC32 calculator.
             </summary>
            
             <remarks>
             This is either the total number of bytes read, or the total number
             of bytes written, depending on the direction of this stream.
             </remarks>
        </member>
        <member name="M:ThirdParty.Ionic.Zlib.CrcCalculatorStream.#ctor(System.IO.Stream)">
            <summary>
            The constructor.
            </summary>
            <param name="stream">The underlying stream</param>
        </member>
        <member name="M:ThirdParty.Ionic.Zlib.CrcCalculatorStream.#ctor(System.IO.Stream,System.Int64)">
            <summary>
            The constructor.
            </summary>
            <param name="stream">The underlying stream</param>
            <param name="length">The length of the stream to slurp</param>
        </member>
        <member name="P:ThirdParty.Ionic.Zlib.CrcCalculatorStream.Crc32">
            <summary>
            Provides the current CRC for all blocks slurped in.
            </summary>
        </member>
        <member name="M:ThirdParty.Ionic.Zlib.CrcCalculatorStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Read from the stream
            </summary>
            <param name="buffer">the buffer to read</param>
            <param name="offset">the offset at which to start</param>
            <param name="count">the number of bytes to read</param>
            <returns>the number of bytes actually read</returns>
        </member>
        <member name="M:ThirdParty.Ionic.Zlib.CrcCalculatorStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Write to the stream. 
            </summary>
            <param name="buffer">the buffer from which to write</param>
            <param name="offset">the offset at which to start writing</param>
            <param name="count">the number of bytes to write</param>
        </member>
        <member name="P:ThirdParty.Ionic.Zlib.CrcCalculatorStream.CanRead">
            <summary>
            Indicates whether the stream supports reading. 
            </summary>
        </member>
        <member name="P:ThirdParty.Ionic.Zlib.CrcCalculatorStream.CanSeek">
            <summary>
            Indicates whether the stream supports seeking. 
            </summary>
        </member>
        <member name="P:ThirdParty.Ionic.Zlib.CrcCalculatorStream.CanWrite">
            <summary>
            Indicates whether the stream supports writing. 
            </summary>
        </member>
        <member name="M:ThirdParty.Ionic.Zlib.CrcCalculatorStream.Flush">
            <summary>
            Flush the stream.
            </summary>
        </member>
        <member name="P:ThirdParty.Ionic.Zlib.CrcCalculatorStream.Length">
            <summary>
            Not implemented.
            </summary>
        </member>
        <member name="P:ThirdParty.Ionic.Zlib.CrcCalculatorStream.Position">
            <summary>
            Not implemented.
            </summary>
        </member>
        <member name="M:ThirdParty.Ionic.Zlib.CrcCalculatorStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Not implemented.
            </summary>
            <param name="offset">N/A</param>
            <param name="origin">N/A</param>
            <returns>N/A</returns>
        </member>
        <member name="M:ThirdParty.Ionic.Zlib.CrcCalculatorStream.SetLength(System.Int64)">
            <summary>
            Not implemented.
            </summary>
            <param name="value">N/A</param>
        </member>
    </members>
</doc>
