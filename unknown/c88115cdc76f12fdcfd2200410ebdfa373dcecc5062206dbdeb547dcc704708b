﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Runtime.Serialization;

namespace Purolator.SmartSort.Service.MessageContracts
{
    [DataContract(Name = "RepositionPackageResponse", Namespace = ServiceNamespace.DataType, IsReference = false)] 
    public class RepositionPackageResponse : DeviceResponseBase
    {       
    }
}
