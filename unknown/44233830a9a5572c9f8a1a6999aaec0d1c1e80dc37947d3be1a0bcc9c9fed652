﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Business.Common.Exception;
using Purolator.SmartSort.Service.Contracts.Common;
using Purolator.SmartSort.Service.Common;
using Purolator.SmartSort.Service.DataContracts;
using System.Runtime.Serialization;

namespace Purolator.SmartSort.Service.MessageContracts
{
    [DataContract(Name = "ShipmentEventResponse", Namespace = ServiceNamespace.DataType, IsReference = false)]      
    //[MessageContract(IsWrapped = false)]
    public class ShipmentEventResponse
    {
        //[MessageBodyMember(Name = "isSuccess", Order = ServiceDataMember.MemberStartNumber + 0)]              
        [DataMember(Name = "isSuccess", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]
        public bool isSuccess { get; set; }                            
    }
}
