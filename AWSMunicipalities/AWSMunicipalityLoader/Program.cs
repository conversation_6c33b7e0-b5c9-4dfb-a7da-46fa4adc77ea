﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Purolator.SmartSort.ChannelIntegration.Clients.AWS;
using Purolator.SmartSort.Data.Access;
using Purolator.SmartSort.Common;

namespace AWSMunicipalityLoader
{
    class Program
    {
        static void Main(string[] args)
        {
            bool success = false;
            try
            {
                AWS awsClient = new AWS();
                string result = awsClient.GetAlternateMunicipalities();

                MunicipalitiesRepository repository = new MunicipalitiesRepository(Constants.DB_CONNECTION_NAME);

                success = repository.UpdateMunicipalities(result);
            }
            catch (Exception ex)
            {
                Logger.Error("Error in loading municipalities", LogCategories.AWS, ex);
            }
            if (success)
            {
                Console.Out.Write("Success");
            }
            else
            {
                Console.Out.Write("Failed");
            }
        }
    }
}
