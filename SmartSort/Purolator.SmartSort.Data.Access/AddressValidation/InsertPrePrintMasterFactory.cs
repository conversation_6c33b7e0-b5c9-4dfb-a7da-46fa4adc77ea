﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Data.Access.Generic;
using Entities = Purolator.SmartSort.Business.Entities;


namespace Purolator.SmartSort.Data.Access
{
    public class InsertPrePrintMasterFactory : IInsertFactory<Entities.PrePrintMaster>
    {
        public InsertPrePrintMasterFactory()
        {
        }

        public DbCommand ConstructInsertCommand(Database db, Entities.PrePrintMaster prePrintMaster)
        {
            DbCommand command = db.GetStoredProcCommand("dbo.InsertPrePrintMaster");
            db.AddInParameter(command, "@PreprintStgID", DbType.Int32, prePrintMaster.PrePrintStgId);
            db.AddInParameter(command, "@DestinationStreetNumber", DbType.String, prePrintMaster.DestinationStreetNumber);
            db.AddInParameter(command, "@DestinationStreetNumSuf", DbType.String, prePrintMaster.DestinationStreetNumSuf);
            db.AddInParameter(command, "@DestinationStreetName", DbType.String, prePrintMaster.DestinationStreetName);
            db.AddInParameter(command, "@DestinationUnitNumber", DbType.String, prePrintMaster.DestinationUnitNumber);
            db.AddInParameter(command, "@DestinationStreetType", DbType.String, prePrintMaster.DestinationStreetType);
            db.AddInParameter(command, "@DestinationStreetDirection", DbType.String, prePrintMaster.DestinationStreetDirection);
            db.AddInParameter(command, "@DestinationCity", DbType.String, prePrintMaster.DestinationCity);
            db.AddInParameter(command, "@DestinationProvince", DbType.String, prePrintMaster.DestinationProvince);
            db.AddInParameter(command, "@DestinationPostalCode", DbType.String, prePrintMaster.DestinationPostalCode);
            db.AddInParameter(command, "@DestinationCompanyName", DbType.String, prePrintMaster.DestinationCompanyName);
            db.AddInParameter(command, "@STATUS_CODE", DbType.String, prePrintMaster.StatusCd);
            return command;
        }

        public void SetNewID(Database db, DbCommand command, Entities.PrePrintMaster prePrintMaster)
        {
            try
            {
                System.Int32 id1 = (System.Int32)(db.GetParameterValue(command, "PreprintStgID"));
                prePrintMaster.PrePrintStgId = id1;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                case "PreprintStgID":
                    return "PreprintStgID";
                case "DestinationStreetNumber":
                    return "DestinationStreetNumber";
                case "DestinationStreetNumSuf":
                    return "DestinationStreetNumSuf";
                case "DestinationStreetName":
                    return "DestinationStreetName";
                case "DestinationUnitNumber":
                    return "DestinationUnitNumber";
                case "DestinationStreetType":
                    return "DestinationStreetType";
                case "DestinationStreetDirection":
                    return "DestinationStreetDirection";
                case "DestinationCity":
                    return "DestinationCity";
                case "DestinationProvince":
                    return "DestinationProvince";
                case "DestinationPostalCode":
                    return "DestinationPostalCode";
                case "DestinationCompanyName":
                    return "DestinationCompanyName";
                case "StatusCd":
                    return "StatusCd";
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }

    }
}
