﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    internal class GetPrePrintDataForInfohubFactory : IDomainObjectFactory<PrePrintDetails>
    {
        public PrePrintDetails Construct(IDataReader reader)
        {
            PrePrintDetails prePrintDetails = new PrePrintDetails();

            int prePrintStgIDIndex = reader.GetOrdinal("PrePrintStgID");
            if (!reader.IsDBNull(prePrintStgIDIndex))
            {
                prePrintDetails.PrePrintStgId = reader.GetInt32(prePrintStgIDIndex);
            }

            int PreprintOrderIDIndex = reader.GetOrdinal("PreprintOrderID");
            if (!reader.IsDBNull(PreprintOrderIDIndex))
            {
                prePrintDetails.PreprintOrderID = reader.GetInt32(PreprintOrderIDIndex);
            }

            int NumberOrderedIndex = reader.GetOrdinal("NumberOrdered");
            if (!reader.IsDBNull(NumberOrderedIndex))
            {
                prePrintDetails.NumberOrdered = reader.GetInt32(NumberOrderedIndex);
            }

            int StartPinIndex = reader.GetOrdinal("StartPin");
            if (!reader.IsDBNull(StartPinIndex))
            {
                prePrintDetails.StartPin = reader.GetString(StartPinIndex);
            }

            int EndPinIndex = reader.GetOrdinal("EndPin");
            if (!reader.IsDBNull(StartPinIndex))
            {
                prePrintDetails.EndPin = reader.GetString(EndPinIndex);
            }

            int TransportModeIndex = reader.GetOrdinal("TransportMode");
            if (!reader.IsDBNull(TransportModeIndex))
            {
                prePrintDetails.TransportMode = reader.GetString(TransportModeIndex);
            }

            int ServiceTimeIndex = reader.GetOrdinal("ServiceTime");
            if (!reader.IsDBNull(ServiceTimeIndex))
            {
                prePrintDetails.ServiceTime = reader.GetString(ServiceTimeIndex);
            }

            int PackageTypeIndex = reader.GetOrdinal("PackageType");
            if (!reader.IsDBNull(PackageTypeIndex))
            {
                prePrintDetails.PackageType = reader.GetString(PackageTypeIndex);
            }

            int DangerousGoodsIndex = reader.GetOrdinal("DangerousGoodsClass");
            if (!reader.IsDBNull(DangerousGoodsIndex))
            {
                prePrintDetails.DangerousGoodsClass = reader.GetString(DangerousGoodsIndex);
            }

            int CustomerNameIndex = reader.GetOrdinal("CustomerName");
            if (!reader.IsDBNull(CustomerNameIndex))
            {
                prePrintDetails.CustomerName = reader.GetString(CustomerNameIndex);
                if(prePrintDetails.CustomerName != null)
                {
                    prePrintDetails.CustomerName = prePrintDetails.CustomerName.Trim();
                }
            }

            int ReceiverAddressIndex = reader.GetOrdinal("ReceiverAddress");
            if (!reader.IsDBNull(ReceiverAddressIndex))
            {
                prePrintDetails.ReceiverAddress = reader.GetString(ReceiverAddressIndex);
                if (prePrintDetails.ReceiverAddress != null)
                {
                    prePrintDetails.ReceiverAddress = prePrintDetails.ReceiverAddress.Trim();
                }
            }

            int ReceiverUnitNumberIndex = reader.GetOrdinal("ReceiverUnitNumber");
            if (!reader.IsDBNull(ReceiverUnitNumberIndex))
            {
                prePrintDetails.ReceiverUnitNumber = reader.GetString(ReceiverUnitNumberIndex);
                if (prePrintDetails.ReceiverUnitNumber != null)
                {
                    prePrintDetails.ReceiverUnitNumber = prePrintDetails.ReceiverUnitNumber.Trim();
                }
            }  
            int cityIndex = reader.GetOrdinal("City");
            if (!reader.IsDBNull(cityIndex))
            {
                prePrintDetails.City = reader.GetString(cityIndex);
                if (prePrintDetails.City != null)
                {
                    prePrintDetails.City = prePrintDetails.City.Trim();
                }
            }

            int provinceIndex = reader.GetOrdinal("Province");
            if (!reader.IsDBNull(provinceIndex))
            {
                prePrintDetails.Province = reader.GetString(provinceIndex);
            }

            int postalCodeIndex = reader.GetOrdinal("PostalCode");
            if (!reader.IsDBNull(postalCodeIndex))
            {
                prePrintDetails.PostalCode = reader.GetString(postalCodeIndex);
                if (prePrintDetails.PostalCode != null)
                {
                    prePrintDetails.PostalCode = prePrintDetails.PostalCode.Trim();
                }
            }

            int countryIndex = reader.GetOrdinal("Country");
            if (!reader.IsDBNull(countryIndex))
            {
                prePrintDetails.Country = reader.GetString(countryIndex);
            }

            int CreateDateTimeIndex = reader.GetOrdinal("CreateDateTime");
            if (!reader.IsDBNull(CreateDateTimeIndex))
            {
                prePrintDetails.CreateDateTime = reader.GetDateTime(CreateDateTimeIndex);
            }

            int ProcessedFlagIndex = reader.GetOrdinal("ProcessedFlag");
            if (!reader.IsDBNull(ProcessedFlagIndex))
            {
                prePrintDetails.ProcessedFlag = reader.GetString(ProcessedFlagIndex);
            }

            int ConsumptionCounterIndex = reader.GetOrdinal("ConsumptionCounter");
            if (!reader.IsDBNull(ConsumptionCounterIndex))
            {
                prePrintDetails.ConsumptionCounter = reader.GetInt32(ConsumptionCounterIndex);
            }

            int AWSStreetNumberIndex = reader.GetOrdinal("AWSStreetNumber");
            if (!reader.IsDBNull(AWSStreetNumberIndex))
            {
                prePrintDetails.AWSStreetNumber = reader.GetString(AWSStreetNumberIndex);
            }

            int AWSStreetNumSufIndex = reader.GetOrdinal("AWSStreetNumSuf");
            if (!reader.IsDBNull(AWSStreetNumSufIndex))
            {
                prePrintDetails.AWSStreetNumSuf = reader.GetString(AWSStreetNumSufIndex);
            }

            int AWSStreetNameIndex = reader.GetOrdinal("AWSStreetName");
            if (!reader.IsDBNull(AWSStreetNameIndex))
            {
                prePrintDetails.AWSStreetName = reader.GetString(AWSStreetNameIndex);
            }

            int AWSUnitNumberIndex = reader.GetOrdinal("AWSUnitNumber");
            if (!reader.IsDBNull(AWSUnitNumberIndex))
            {
                prePrintDetails.AWSUnitNumber = reader.GetString(AWSUnitNumberIndex);
            }

            int AWSStreetTypeIndex = reader.GetOrdinal("AWSStreetType");
            if (!reader.IsDBNull(AWSStreetTypeIndex))
            {
                prePrintDetails.AWSStreetType = reader.GetString(AWSStreetTypeIndex);
            }

            int AWSStreetDirectionIndex = reader.GetOrdinal("AWSStreetDirection");
            if (!reader.IsDBNull(AWSStreetDirectionIndex))
            {
                prePrintDetails.AWSStreetDirection = reader.GetString(AWSStreetDirectionIndex);
            }

            int AWSCityIndex = reader.GetOrdinal("AWSCity");
            if (!reader.IsDBNull(AWSCityIndex))
            {
                prePrintDetails.AWSCity = reader.GetString(AWSCityIndex);
            }

            int AWSProvinceIndex = reader.GetOrdinal("AWSProvince");
            if (!reader.IsDBNull(AWSProvinceIndex))
            {
                prePrintDetails.AWSProvince = reader.GetString(AWSProvinceIndex);
            }

            int AWSPostalCodeIndex = reader.GetOrdinal("AWSPostalCode");
            if (!reader.IsDBNull(AWSPostalCodeIndex))
            {
                prePrintDetails.AWSPostalCode = reader.GetString(AWSPostalCodeIndex);
            }

            int AWSCompanyNameIndex = reader.GetOrdinal("AWSCompanyName");
            if (!reader.IsDBNull(AWSCompanyNameIndex))
            {
                prePrintDetails.AWSCompanyName = reader.GetString(AWSCompanyNameIndex);
            }

            int DestinationTerminalIndex = reader.GetOrdinal("DestinationTerminal");
            if (!reader.IsDBNull(DestinationTerminalIndex))
            {
                prePrintDetails.DestinationTerminal = reader.GetString(DestinationTerminalIndex);
            }

            int PurolatorAVStatusIndex = reader.GetOrdinal("PurolatorAVStatus");
            if (!reader.IsDBNull(PurolatorAVStatusIndex))
            {
                prePrintDetails.PurolatorAVStatus = reader.GetString(PurolatorAVStatusIndex);
            }

            return prePrintDetails;
        }
    }
}

