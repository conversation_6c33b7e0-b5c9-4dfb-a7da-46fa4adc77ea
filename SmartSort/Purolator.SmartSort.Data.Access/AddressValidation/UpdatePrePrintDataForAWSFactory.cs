﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Data.Access.Generic;
using Purolator.SmartSort.Common;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class UpdatePrePrintDataForAWSFactory : IUpdateFactory<Entities.PrePrintDetailsUpdate>
    {
        public UpdatePrePrintDataForAWSFactory()
        {
        }

        public DbCommand ConstructUpdateCommand(Database db, Entities.PrePrintDetailsUpdate prePrintDetailsUpdate)
        {
            DbCommand command = db.GetStoredProcCommand("dbo.UpdatePrePrintDataForAWS");
            db.AddInParameter(command, "@PreprintStgID", DbType.Int32, prePrintDetailsUpdate.PrePrintStgId);
            if (prePrintDetailsUpdate.Status.Equals(Entities.PrePrintDetailsStatusEnum.New))
            {
                db.AddInParameter(command, "@StatusCD", DbType.String, 'Q' );
            }
            else if (prePrintDetailsUpdate.Status.Equals(Entities.PrePrintDetailsStatusEnum.Complete))
            {
                db.AddInParameter(command, "@StatusCD", DbType.String, 'C');
            }
            else if (prePrintDetailsUpdate.Status.Equals(Entities.PrePrintDetailsStatusEnum.Error))
            {
                db.AddInParameter(command, "@StatusCD", DbType.String, 'E');
            }
            else if (prePrintDetailsUpdate.Status.Equals(Entities.PrePrintDetailsStatusEnum.Running))
            {
                db.AddInParameter(command, "@StatusCD", DbType.String, 'P');
            }
            return command;
        }

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
