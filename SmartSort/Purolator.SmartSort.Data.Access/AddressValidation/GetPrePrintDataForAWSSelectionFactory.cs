﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Data.Access.Generic;

namespace Purolator.SmartSort.Data.Access
{
    class GetPrePrintDataForAWSSelectionFactory : ISelectionFactory<GetPrePrintDataForAWSIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, GetPrePrintDataForAWSIdentity getPrePrintDataForAWSIdentity)
        {
            DbCommand command = null;
            if (null != db && null != getPrePrintDataForAWSIdentity)
            {
                command = db.GetStoredProcCommand("dbo.GetPrePrintDataForAWS");
                db.AddInParameter(command, "@ServerName", DbType.String, getPrePrintDataForAWSIdentity.ServerName );
                db.AddInParameter(command, "@MaxAmountRecords", DbType.Int32, getPrePrintDataForAWSIdentity.FetchAmount);
                
            }
            return command;
        }

     
        public void SetParameterValue(Database db, DbCommand command, ref GetPrePrintDataForAWSIdentity getPrePrintDataForAWSIdentity)
        { }

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
