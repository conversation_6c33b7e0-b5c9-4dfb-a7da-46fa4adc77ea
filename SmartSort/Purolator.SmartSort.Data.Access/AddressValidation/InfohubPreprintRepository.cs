﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Data.SqlClient;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class InfohubPreprintRepository : Repository<Entities.PrePrintDetails>
    {
        public InfohubPreprintRepository(string databaseName) : base(databaseName) { }

        public Entities.PrePrintDetails GetPrePrint(int id)
        {
            ISelectionFactory<GetPrePrintDataIdentity> selectionFactory = new GetPrePrintDataForInfohubSelectionFactory();
            try
            {
                GetPrePrintDataIdentity identity = new GetPrePrintDataIdentity(id);
                return base.FindOne(selectionFactory, new GetPrePrintDataForInfohubFactory(), identity);
            }
            catch (SqlException ex)
            {
                Purolator.SmartSort.Common.Logger.Error("Error in AddressValidationRepository()", Purolator.SmartSort.Common.LogCategories.ADDRESS_VALIDATION, ex);
            }
            return null;

        }
    }   
}
