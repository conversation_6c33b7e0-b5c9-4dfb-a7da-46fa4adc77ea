﻿using System;
using System.Data;
using System.Data.Common;
using System.Collections.Generic;
using System.Data.SqlClient;
using Purolator.SmartSort.Common;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class MunicipalitiesRepository : Repository<string>
    {
        private string databaseName;

        public MunicipalitiesRepository(string databaseName)
            : base(databaseName)
        {
            this.databaseName = databaseName;
        }

        public bool UpdateMunicipalities(string municipalitiesXMLString)
        {
            UpdateMunicipalitiesFactory factory = new UpdateMunicipalitiesFactory();
            try
            {
                DbCommand outCommand = null;

                base.Save(factory, municipalitiesXMLString, out outCommand);
                bool isSuccess = false;
                if (outCommand != null)
                {
                    string outValue = (string)outCommand.Parameters["@SuccessFlag"].Value;
                    isSuccess = outValue == "Y";
                }
                return isSuccess;
            }
            catch (SqlException ex)
            {
                Logger.Error("Error in UpdateMunicipalities()", LogCategories.ADDRESS_VALIDATION, ex);
            }

            return false;
        }
    }
}
