﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Data.SqlClient;
using Purolator.SmartSort.Common;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class PrePrintMasterRepository : Repository<Entities.PrePrintMaster>
    {
        private string databaseName;

        public PrePrintMasterRepository(string databaseName)
            : base(databaseName)
        {
            this.databaseName = databaseName;
        }

        public void Add(Entities.PrePrintMaster prePrintMaster)
        {
            InsertPrePrintMasterFactory insertFactory = new InsertPrePrintMasterFactory();
            try
            {
                base.Add(insertFactory, prePrintMaster);
            }
            catch (SqlException ex)
            {
                Logger.Error("Error in PrePrintMasterRepository()", LogCategories.ADDRESS_VALIDATION, ex);
            }
        }
    }
}
