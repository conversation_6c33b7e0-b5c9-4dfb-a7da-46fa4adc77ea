﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    internal class GetPrePrintDataForAWSFactory : IDomainObjectFactory<PrePrintDetails>
    {
        public PrePrintDetails Construct(IDataReader reader)
        {
            PrePrintDetails prePrintDetails = new PrePrintDetails();

            int prePrintStgIDIndex = reader.GetOrdinal("PrePrintStgID");
            if (!reader.IsDBNull(prePrintStgIDIndex))
            {
                prePrintDetails.PrePrintStgId = reader.GetInt32(prePrintStgIDIndex);
            }

            int line1Index = reader.GetOrdinal("Line1");
            if (!reader.IsDBNull(line1Index))
            {
                prePrintDetails.Line1 = reader.GetString(line1Index);
            }

            int line2Index = reader.GetOrdinal("Line2");
            if (!reader.IsDBNull(line2Index))
            {
                prePrintDetails.Line2 = reader.GetString(line2Index);
            }

            int cityIndex = reader.GetOrdinal("City");
            if (!reader.IsDBNull(cityIndex))
            {
                prePrintDetails.City = reader.GetString(cityIndex);
            }

            int provinceIndex = reader.GetOrdinal("Province");
            if (!reader.IsDBNull(provinceIndex))
            {
                prePrintDetails.Province = reader.GetString(provinceIndex);
            }

            int postalCodeIndex = reader.GetOrdinal("PostalCode");
            if (!reader.IsDBNull(postalCodeIndex))
            {
                prePrintDetails.PostalCode = reader.GetString(postalCodeIndex);
            }

            int countryIndex = reader.GetOrdinal("Country");
            if (!reader.IsDBNull(countryIndex))
            {
                prePrintDetails.Country = reader.GetString(countryIndex);
            }

            int avsFinishIndex = reader.GetOrdinal("AVSFinish");
            if (!reader.IsDBNull(avsFinishIndex))
            {
                string avsFinishStr = reader.GetString(avsFinishIndex);
                prePrintDetails.AVSFinished = avsFinishStr != null && avsFinishStr.Equals("1");
            }

            return prePrintDetails;
        }
    }
}