﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Data.Access.Generic;

namespace Purolator.SmartSort.Data.Access
{
    class GetPrePrintDataForInfohubSelectionFactory : ISelectionFactory<GetPrePrintDataIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, GetPrePrintDataIdentity identity)
        {
            DbCommand command = null;
            if (null != db && null != identity)
            {
                command = db.GetStoredProcCommand("[dbo].[GetPrePrintRecord]");
                db.AddInParameter(command, "@PreprintStgID ", DbType.Int32, identity.PreprintStgID);
                
                
            }
            return command;
        }

     
        public void SetParameterValue(Database db, DbCommand command, ref GetPrePrintDataIdentity getPrePrintDataForAWSIdentity)
        { }

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
