﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Data.SqlClient;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class AddressValidationRepository : Repository<Entities.PrePrintDetails>
    {
        public AddressValidationRepository(string databaseName) : base(databaseName) { }

        public List<Entities.PrePrintDetails> GetPrePrintDataForAWS(string serverName, int fetchAmount)
        {
            ISelectionFactory<GetPrePrintDataForAWSIdentity> selectionFactory = new GetPrePrintDataForAWSSelectionFactory();
            try
            {
                GetPrePrintDataForAWSIdentity getPrePrintDataForAWSIdentity = new GetPrePrintDataForAWSIdentity(serverName, fetchAmount);
                return base.Find(selectionFactory, new GetPrePrintDataForAWSFactory(), getPrePrintDataForAWSIdentity );
            }
            catch (SqlException ex)
            {
                Purolator.SmartSort.Common.Logger.Error("Error in AddressValidationRepository()", Purolator.SmartSort.Common.LogCategories.ADDRESS_VALIDATION, ex);
            }
            return new List<Entities.PrePrintDetails>();

        }
    }   
}
