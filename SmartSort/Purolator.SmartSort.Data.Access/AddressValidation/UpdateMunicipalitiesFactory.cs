﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Data.Access.Generic;
using Purolator.SmartSort.Common;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class UpdateMunicipalitiesFactory : IUpdateFactory<string>
    {
        public UpdateMunicipalitiesFactory()
        {
        }

        public DbCommand ConstructUpdateCommand(Database db, string municipalitiesXMLString)
        {
            DbCommand command = db.GetStoredProcCommand("dbo.InsertCityAlternativeName");
            db.AddInParameter(command, "@xml", DbType.Xml, municipalitiesXMLString);
            db.AddOutParameter(command, "@SuccessFlag", DbType.String, 1);
           
            return command;
        }

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
