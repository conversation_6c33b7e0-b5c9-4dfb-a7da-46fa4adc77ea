﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Data.Access
{
    public class GetPrePrintDataForAWSIdentity
    {        
        public GetPrePrintDataForAWSIdentity(string serverName, int fetchAmount)
        {
            this.ServerName = serverName;
            this.FetchAmount = fetchAmount;
        }

        public string ServerName {get; set;}
        public int FetchAmount { get; set; }
    }
}

