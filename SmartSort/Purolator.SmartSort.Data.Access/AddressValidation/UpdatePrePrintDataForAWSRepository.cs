﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Data.SqlClient;
using Purolator.SmartSort.Common;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class UpdatePrePrintDataForAWSRepository: Repository<Entities.PrePrintDetailsUpdate>
    {
        private string databaseName;

        public UpdatePrePrintDataForAWSRepository(string databaseName)
            : base(databaseName)
        {
            this.databaseName = databaseName;
        }


        public void Save(Entities.PrePrintDetailsUpdate prePrintDetailsUpdate)
        {
            UpdatePrePrintDataForAWSFactory updateFactory = new UpdatePrePrintDataForAWSFactory();
            try
            {
                base.Save(updateFactory, prePrintDetailsUpdate);
            }
            catch (SqlException ex)
            {
                Logger.Error("Error in UpdatePrePrintDataForAWSRepository()", LogCategories.ADDRESS_VALIDATION, ex);
            }
        }


    }
}
