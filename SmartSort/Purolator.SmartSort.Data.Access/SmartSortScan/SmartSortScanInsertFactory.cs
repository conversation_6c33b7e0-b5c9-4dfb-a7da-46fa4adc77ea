﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Entities = Purolator.SmartSort.Business.Entities;
using System.Globalization;
using Purolator.SmartSort.Serializer;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Data.Access
{
    public class SmartSortScanInsertFactory : IInsertFactory<Entities.SmartSortScan>
    {

        public DbCommand ConstructInsertCommand(Database db, Entities.SmartSortScan smartSortScan)
        {
           
            DbCommand command = db.GetStoredProcCommand("dbo.InsertSSLog");


            
            
            string xmldata = Serializer.Serializer.Serialize<Entities.SmartSortScanLog>(smartSortScan.SSLog);
            
            
            //XmlSerializer s = new XmlSerializer(smartSortScan.SSLog.GetType());

            //XmlSerializerNamespaces ns = new XmlSerializerNamespaces();
            //ns.Add("", "");

            //s.Serialize(xmlWriter, objectToSerialize, ns);
            //s.Serialize(


            db.AddInParameter(command, "doc", DbType.Xml, xmldata);

            db.AddOutParameter(command, "status", DbType.Boolean,10);

            return command;
        }
        
        public void SetNewID(Database db, DbCommand command, Entities.SmartSortScan smartSortScan)
        {
            if (db.GetParameterValue(command, "status").GetType() != typeof(System.DBNull))
            {             
                smartSortScan.SSLog.Status = (Boolean)db.GetParameterValue(command, "status");              
            }
        }

        
        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
