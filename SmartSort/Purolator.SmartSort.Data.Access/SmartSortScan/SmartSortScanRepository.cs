﻿using System;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class SmartSortScanRepository : Repository<Entities.SmartSortScan>
    {
        public SmartSortScanRepository(string databaseName) : base(databaseName) { }

        public void InsertSmartSortScan(Entities.SmartSortScan smartSortScan)
        {
            //IInsertFactory<Entities.SmartSortScan> insertFactory = new SmartSortScanInsertFactory();
            IInsertFactory<Entities.SmartSortScan> insertFactory = new SmartSortScanInsertFactory();
            
            try
            {
                // map
                base.Add(insertFactory, smartSortScan);
                

              
            }
            catch (SqlException ex)
            {
               // throw ex;
                //do not throw exeption as we need to test response as partial is allowed
                smartSortScan.SSLog.ErrorMessage = ex.Message;
            }
        }
    }
}
