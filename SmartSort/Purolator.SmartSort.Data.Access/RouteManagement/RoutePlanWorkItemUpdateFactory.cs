﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Business.Entities;
using System.Globalization;
using Purolator.SmartSort.Serializer;

namespace Purolator.SmartSort.Data.Access.RouteManagement
{
    public class RoutePlanWorkItemUpdateFactory : IUpdateFactory<RoutePrintWorkItem>
    {
        public DbCommand ConstructUpdateCommand(Database db, RoutePrintWorkItem identity)
        {
            DbCommand command = null;
            if (null != db && null != identity)
            {
                command = db.GetStoredProcCommand("[dbo].[RoutePlanPrintUpdateData]");  
                db.AddInParameter(command, "@RoutePlanPrintREQID", DbType.Int32, identity.RequestID);
                db.AddInParameter(command, "@RoutePlanPDF", DbType.String, identity.RoutePlanPDF);
                db.AddInParameter(command, "@SplitPlanPDF", DbType.String, identity.SplitPlanPDF);
                db.AddInParameter(command, "@PrintStatus", DbType.String, identity.Status);                
            }
            return command;
        }

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }

    }
}
