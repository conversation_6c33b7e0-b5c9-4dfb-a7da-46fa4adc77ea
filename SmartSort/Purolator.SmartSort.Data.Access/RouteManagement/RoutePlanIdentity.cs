﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Data.Access.RouteManagement
{
    public class RoutePlanIdentity
    {
        public string Terminal { get; set; }
        public int RoutePlanVersionId { get; set; }

        public RoutePlanIdentity(string terminal, int routePlanVersionId)
        {
            this.Terminal = terminal;
            this.RoutePlanVersionId = routePlanVersionId;
        }
    }
}
