﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Business.Entities;
using System.Globalization;
using Purolator.SmartSort.Serializer;

namespace Purolator.SmartSort.Data.Access.RouteManagement
{
    public class RoutePlanWorkItemResetFactory : IUpdateFactory<RoutePrintWorkItem>
    {
        public DbCommand ConstructUpdateCommand(Database db, RoutePrintWorkItem identity)
        {
            DbCommand command = null;
            if (null != db && null != identity)
            {
                command = db.GetStoredProcCommand("[dbo].[RoutePlanPrintResetData]");                
                db.AddInParameter(command, "@server_name", DbType.String, identity.ServerName);
            }
            return command;
        }

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }

    }
}
