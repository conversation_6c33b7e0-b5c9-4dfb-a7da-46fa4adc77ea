﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Entities = Purolator.SmartSort.Business.Entities;
using System.Globalization;
using Purolator.SmartSort.Serializer;

namespace Purolator.SmartSort.Data.Access.RouteManagement
{
    public class RoutePlanWorkItemSelectionFactory : ISelectionFactory<RoutePlanWorkItemIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, RoutePlanWorkItemIdentity identity)
        {
            DbCommand command = null;
            if (null != db && null != identity)
            {
                command = db.GetStoredProcCommand("[dbo].[RoutePlanPrintGetData]");
                db.AddInParameter(command, "@fetchNo", DbType.String, identity.Rows);
                db.AddInParameter(command, "@server_name", DbType.String, identity.ServerName);
            }
            return command;
        }

        public void SetParameterValue(Database db, DbCommand command, ref RoutePlanWorkItemIdentity identity)
        { }

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }

    }
}
