﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Business.Entities;
using System.Globalization;
using Purolator.SmartSort.Serializer;

namespace Purolator.SmartSort.Data.Access.RouteManagement
{
    public class SplitPlanPrintRepository : Repository<SplitPlanPrint>
    {
        public SplitPlanPrintRepository(string databaseName) : base(databaseName) { }

        public SplitPlanPrint GetSplitPlan(int routePlanVersionID)
        {
            ISelectionFactory<RoutePlanIdentity> selectionFactory = new SplitPlanPrintSelectionFactory();
            RoutePlanIdentity routePlanIdentity = new RoutePlanIdentity(null, routePlanVersionID);
            return FindOne(selectionFactory, new SplitPlanDomainFactory(), routePlanIdentity);            
        }
    }

    internal class SplitPlanDomainFactory : IDomainObjectFactory<SplitPlanPrint>
    {
        public SplitPlanPrint Construct(IDataReader reader)
        {
            SplitPlanPrint result = new SplitPlanPrint();
            if (reader[0] != null)
            {
                try
                {
                    string xml = reader[0].ToString();
                    result = Serializer.Serializer.Deserialize<SplitPlanPrint>(xml);
                }
                catch (Exception ex)
                {
                    // LOG                    
                }
            }
            return result;
        }
    }
}
