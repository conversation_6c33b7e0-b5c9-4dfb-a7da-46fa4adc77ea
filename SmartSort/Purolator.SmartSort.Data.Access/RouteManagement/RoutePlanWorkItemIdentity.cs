﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Data.Access.RouteManagement
{
    public class RoutePlanWorkItemIdentity
    {
        public string ServerName { get; set; }
        public int Rows { get; set; }

        public RoutePlanWorkItemIdentity(string serverName, int rows)
        {
            this.ServerName = serverName;
            this.Rows = rows;
        }
    }
}
