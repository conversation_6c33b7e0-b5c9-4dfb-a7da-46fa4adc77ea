﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Business.Entities;
using System.Globalization;
using Purolator.SmartSort.Serializer;

namespace Purolator.SmartSort.Data.Access.RouteManagement
{
    public class RoutePlanPrintRepository : Repository<RoutePlanPrint>
    {
        public RoutePlanPrintRepository(string databaseName) : base(databaseName) { }

        public RoutePlanPrint GetRoutePlan(int routePlanVersionID)
        {
            ISelectionFactory<RoutePlanIdentity> selectionFactory = new RoutePlanPrintSelectionFactory();
            RoutePlanIdentity routePlanIdentity = new RoutePlanIdentity(null, routePlanVersionID);
            return FindOne(selectionFactory, new RoutePlanDomainFactory(), routePlanIdentity);            
        }
    }

    internal class RoutePlanDomainFactory : IDomainObjectFactory<RoutePlanPrint>
    {
        public RoutePlanPrint Construct(IDataReader reader)
        {
            RoutePlanPrint result = new RoutePlanPrint();
            if (reader[0] != null)
            {
                try
                {
                    result = Serializer.Serializer.Deserialize<RoutePlanPrint>(reader[0].ToString());
                }
                catch (Exception ex)
                {
                    // LOG                    
                }
            }
            return result;
        }
    }
}
