﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Business.Entities;
using System.Globalization;
using Purolator.SmartSort.Serializer;

namespace Purolator.SmartSort.Data.Access.RouteManagement
{
    public class RoutePlanServiceRepository : Repository<RoutePrintWorkItem>
    {
        public RoutePlanServiceRepository(string databaseName) : base(databaseName) { }

        public List<RoutePrintWorkItem> GetWorkItems(string serverName, int rows)
        {
            var selectionFactory = new RoutePlanWorkItemSelectionFactory();
            RoutePlanWorkItemIdentity identity = new RoutePlanWorkItemIdentity(serverName, rows);
            return Find(selectionFactory, new RoutePlanWorkItemDomainFactory(), identity);            
        }

        public void ResetWorkItems(string serverName)
        {
            var resetFactory = new RoutePlanWorkItemResetFactory();
            RoutePrintWorkItem identity = new RoutePrintWorkItem();
            identity.ServerName = serverName;            
            Save(resetFactory, identity);
        }

        public void UpdateWorkItem(RoutePrintWorkItem item)
        {
            var updateFactory = new RoutePlanWorkItemUpdateFactory();
            Save(updateFactory, item);
        }
    }

    internal class RoutePlanWorkItemDomainFactory : IDomainObjectFactory<RoutePrintWorkItem>
    {
        public RoutePrintWorkItem Construct(IDataReader reader)
        {
            RoutePrintWorkItem result = new RoutePrintWorkItem();

            int index = reader.GetOrdinal("RoutePlanPrintREQID");
            if (!reader.IsDBNull(index))
            {
                result.RequestID = reader.GetInt32(index);
            }

            index = reader.GetOrdinal("RoutePlanVersionID");
            if (!reader.IsDBNull(index))
            {
                result.RoutePlanVersionID = reader.GetInt32(index);
            }

            index = reader.GetOrdinal("RoutePlanPDF");
            if (!reader.IsDBNull(index))
            {
                result.RoutePlanPDF = reader.GetString(index);
            }

            index = reader.GetOrdinal("SplitPlanPDF");
            if (!reader.IsDBNull(index))
            {
                result.SplitPlanPDF = reader.GetString(index);
            }

            return result;
        }
    }
}
