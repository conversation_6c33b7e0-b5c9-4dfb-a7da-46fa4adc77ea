﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Entities = Purolator.SmartSort.Business.Entities;
using System.Globalization;
using Purolator.SmartSort.Serializer;

namespace Purolator.SmartSort.Data.Access.RouteManagement
{
    public class RoutePlanPrintSelectionFactory : ISelectionFactory<RoutePlanIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, RoutePlanIdentity routePlan)
        {
            DbCommand command = null;
            if (null != db && null != routePlan)
            {
                command = db.GetStoredProcCommand("[dbo].[LoadChartV2]");                
                db.AddInParameter(command, "@RoutePlanVersionId", DbType.String, routePlan.RoutePlanVersionId);
            }
            command.CommandTimeout = 600;
            return command;
        }

        public void SetParameterValue(Database db, DbCommand command, ref RoutePlanIdentity identity)
        { }

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }

    }
}
