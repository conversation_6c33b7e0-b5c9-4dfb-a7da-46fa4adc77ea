﻿using System;
using System.Data;
using System.Data.SqlClient;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class ShipmentEventRepository : Repository<Entities.ShipmentEventStaging>
    {
        public ShipmentEventRepository(string databaseName) : base(databaseName) { }

        public void InsertShipmentEvent(Entities.ShipmentEventStaging shipEvent)
        {
            IInsertFactory<Entities.ShipmentEventStaging> insertFactory = new ShipmentEventInsertFactory();
            try
            {
                // map
                base.Add(insertFactory, shipEvent);
            }
            catch (SqlException ex)
            {
                throw ex;
            }
        }
    }
}
