﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Entities = Purolator.SmartSort.Business.Entities;
using System.Globalization;
using Purolator.SmartSort.Serializer;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Data.Access
{
    public class ShipmentEventInsertFactory : IInsertFactory<Entities.ShipmentEventStaging>
    {

        public DbCommand ConstructInsertCommand(Database db, Entities.ShipmentEventStaging shipEvent)
        {
           
            DbCommand command = db.GetStoredProcCommand("dbo.InsertSAPMessageSTG");
            XmlSerializerNamespaces ns = new XmlSerializerNamespaces();
            ns.Add("", "");
            string xmldata = Serializer.Serializer.Serialize<Entities.ShipmentEventStaging>(shipEvent, ns);

            db.AddInParameter(command, "doc", DbType.Xml, xmldata);            
           
            return command;
        }

        // Not used
        public void SetNewID(Database db, DbCommand command, Entities.ShipmentEventStaging shipEvent)
        {
        }

        
        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
