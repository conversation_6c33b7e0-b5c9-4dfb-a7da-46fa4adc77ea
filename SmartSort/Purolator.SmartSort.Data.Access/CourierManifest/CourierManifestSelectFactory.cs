﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Data.Access.Generic;

namespace Purolator.SmartSort.Data.Access
{
    class CourierManifestSelectFactory : ISelectionFactory<CourierManifestIdentity>
    {


        public DbCommand ConstructSelectCommand(Database db, CourierManifestIdentity courierManifest)
        {
            DbCommand command = null;
            if (null != db && null != courierManifest)
            {
                command = db.GetStoredProcCommand("dbo.GetCourierManifest_XML");
                db.AddInParameter(command, "@CallStack", DbType.String, courierManifest.CallStack);
                db.AddInParameter(command, "@Terminal", DbType.String, courierManifest.Terminal);
                db.AddInParameter(command, "@RouteNumber", DbType.String, courierManifest.Route);
                db.AddInParameter(command, "@ManifestDate", DbType.Date, courierManifest.SelectedDate);                
            }
            return command;
        }



     

        public void SetParameterValue(Database db, DbCommand command, ref CourierManifestIdentity courierManifest)
        { }

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
