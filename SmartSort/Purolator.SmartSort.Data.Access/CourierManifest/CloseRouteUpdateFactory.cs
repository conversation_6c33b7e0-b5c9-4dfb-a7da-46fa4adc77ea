﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Data.Access.Generic;
using Entities = Purolator.SmartSort.Business.Entities;


namespace Purolator.SmartSort.Data.Access
{
    public class CloseRouteUpdateFactory : IUpdateFactory<Entities.LoadDeviationEvent>
    {
        public DbCommand ConstructUpdateCommand(Database db, Entities.LoadDeviationEvent deviationEvent)
        {
            DbCommand command = db.GetStoredProcCommand("dbo.CloseRoutes");
            db.AddInParameter(command, "@Terminal ", DbType.String, deviationEvent.Terminal);
            db.AddInParameter(command, "@DeviceID ", DbType.String, deviationEvent.DeviceID);
            db.AddInParameter(command, "@Datetimeoffset", DbType.DateTimeOffset, deviationEvent.ScanDateTime);
            db.AddInParameter(command, "@Routes", DbType.String, deviationEvent.Route);
            db.AddInParameter(command, "@Date", DbType.Date, deviationEvent.ScanDateTime.Date);
            db.AddOutParameter(command, "@Status", DbType.Boolean, 1);

            return command;
        }

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
