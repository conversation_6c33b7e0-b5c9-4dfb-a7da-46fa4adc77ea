﻿using System;
using System.Data;
using System.Data.SqlClient;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data.Common;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class CourierManifestRepository : Repository<Entities.CourierManifest>
    {
        public CourierManifestRepository(string dbName) : base(dbName) {}

        public Entities.CourierManifest GetCourierManifest(string terminal, DateTime selectedDate, string route)
        {
            ISelectionFactory<CourierManifestIdentity> selectionFactory = new CourierManifestSelectFactory();

            CourierManifestIdentity cmIdentity = new CourierManifestIdentity();

            cmIdentity.Terminal = terminal;
            cmIdentity.Route = route;
            cmIdentity.SelectedDate = selectedDate;            

            return base.FindOne(selectionFactory, new CourierManifestDomainFactory(), cmIdentity);

        }

        public bool isServiceUp()
        {
            return isDBUp();
        }

    }

    internal class CourierManifestDomainFactory : IDomainObjectFactory<Entities.CourierManifest>
    {
        public Entities.CourierManifest Construct(IDataReader reader)
        {
            Entities.CourierManifest cm = new Entities.CourierManifest();
            if (reader[0] != null)
            {
                try
                {
                    cm = Serializer.Serializer.Deserialize<Entities.CourierManifest>(reader[0].ToString());
                }
                catch (Exception ex)
                {
                    // LOG                    
                }
            }
            return cm;
        }
    }
}
