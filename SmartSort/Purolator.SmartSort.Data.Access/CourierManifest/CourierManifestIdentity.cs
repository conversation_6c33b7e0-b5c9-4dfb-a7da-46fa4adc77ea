﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Data.Access
{
    public class CourierManifestIdentity
    {
        public string Terminal { get; set; }

        public string CallStack { get; set; }

        public DateTime SelectedDate { get; set; }

        public string Route { get; set; }

        public string DeviceId { get; set; }
    }
}
