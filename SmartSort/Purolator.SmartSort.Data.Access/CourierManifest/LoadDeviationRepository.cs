﻿using System;
using System.Data;
using System.Data.SqlClient;
using Purolator.SmartSort.Common;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class LoadDeviationRepository : Repository<Entities.LoadDeviationEvent>
    {
        public LoadDeviationRepository(string databaseName) : base(databaseName) { }

        public void CloseRoute(Entities.LoadDeviationEvent devEvent)
        {
            IUpdateFactory<Entities.LoadDeviationEvent> updateFactory = new CloseRouteUpdateFactory();
            Save(updateFactory, devEvent);          
        }

        public void LoadToAlternateTruck(Entities.LoadDeviationEvent devEvent)
        {
            IUpdateFactory<Entities.LoadDeviationEvent> updateFactory = new LoadAlternateTruckUpdateFactory();
            Save(updateFactory, devEvent);          
        }

        public void RepositionPackage(Entities.LoadDeviationEvent devEvent)
        {
            IUpdateFactory<Entities.LoadDeviationEvent> updateFactory = new RepositionPackageUpdateFactory();
            Save(updateFactory, devEvent);          
        }

        public void UnloadFromTruck(Entities.LoadDeviationEvent devEvent)
        {
            IUpdateFactory<Entities.LoadDeviationEvent> updateFactory = new UnloadTruckUpdateFactory();
            Save(updateFactory, devEvent);          
        }



        private void Save(IUpdateFactory<Entities.LoadDeviationEvent> updateFactory, Entities.LoadDeviationEvent devEvent)
        {
            DateTime startTime = DateTime.Now;
            try
            {
                base.Save(updateFactory, devEvent);

            }
            catch (SqlException ex)
            {
                throw ex;
            }
            finally
            {
                DateTime endTime = DateTime.Now;
                double seconds = (endTime - startTime).TotalSeconds;
                if (seconds > 10)
                {
                    string pinText = string.Empty;
                    if (!string.IsNullOrWhiteSpace(devEvent.PIN))
                    {
                        pinText = "for pin: " + devEvent.PIN;
                    }
                    Logger.Info(updateFactory .GetType().FullName + "saving to DB took: " + seconds + " seconds " + pinText , LogCategories.COURIER_MANIFEST);
                }
            }
        }


        public void IsServiceUp(Entities.LoadDeviationEvent devEvent)
        {
            CourierManifestSelectFactory selFactory = new CourierManifestSelectFactory();
            //selFactory.
        }

    }
}
