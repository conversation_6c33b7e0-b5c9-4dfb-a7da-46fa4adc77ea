﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Data.Access.Generic;
using Entities = Purolator.SmartSort.Business.Entities;



namespace Purolator.SmartSort.Data.Access
{
    public class LoadAlternateTruckUpdateFactory : IUpdateFactory<Entities.LoadDeviationEvent>
    {
        public DbCommand ConstructUpdateCommand(Database db, Entities.LoadDeviationEvent deviationEvent)
        {
            DbCommand command = db.GetStoredProcCommand("dbo.CmLoadAltTruck");
            db.AddInParameter(command, "@Terminal ", DbType.String, deviationEvent.Terminal);
            db.AddInParameter(command, "@PDTDeviceID ", DbType.String, deviationEvent.DeviceID);
            db.AddInParameter(command, "@ScanDateTime", DbType.DateTimeOffset, deviationEvent.ScanDateTime);
            db.AddInParameter(command, "@PIN", DbType.String, deviationEvent.PIN);
            db.AddInParameter(command, "@Route", DbType.String, deviationEvent.Route);
            db.AddInParameter(command, "@Shelf", DbType.String, deviationEvent.Shelf);
            command.CommandTimeout = 120;
            return command;
        }

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
