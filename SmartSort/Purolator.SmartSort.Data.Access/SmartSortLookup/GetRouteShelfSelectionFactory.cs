﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Data.Access.Generic;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Data.Access;

namespace Purolator.SmartSort.Data.Access
{
    class GetRouteShelfSelectionFactory : ISelectionFactory<GetRouteShelfIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, GetRouteShelfIdentity getRouteShelfIdentity)
        {
          
            DbCommand command = null;
            if (null != db && null != getRouteShelfIdentity)
            {
                command = db.GetStoredProcCommand("dbo.SSLookupPin");

                string xmldata = Serializer.Serializer.Serialize<Entities.SmartSortLookup>(getRouteShelfIdentity.SmartSortLookup);

                db.AddInParameter(command, "doc", DbType.Xml, xmldata);                
                db.AddOutParameter(command, "Status", DbType.String, 255);

            }
            
            return command;
        }

        public void SetParameterValue(Database db, DbCommand command, ref Purolator.SmartSort.Data.Access.GetRouteShelfIdentity routeDetails)        
        {

            if (db.GetParameterValue(command, "status").GetType() != typeof(System.DBNull))
            {
                if (!String.IsNullOrEmpty((String)db.GetParameterValue(command, "status")))
                {
                    routeDetails.SmartSortLookup.Status = (String)db.GetParameterValue(command, "status");
                }        
               
            }

        }
       

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
