﻿using System;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class SmartSortLookupLogFactory : Repository<Entities.LookupWSLog>
    {
        public SmartSortLookupLogFactory(string databaseName) : base(databaseName) { }

        public void InsertSmartSortScan(Entities.LookupWSLog log)
        {
            IInsertFactory<Entities.LookupWSLog> insertFactory = new SmartSortLookupLogInsertFactory();

            try
            {                
                base.Add(insertFactory, log);
            }
            catch (SqlException ex)
            {
                throw ex;
            }
        }
    
    }
}
