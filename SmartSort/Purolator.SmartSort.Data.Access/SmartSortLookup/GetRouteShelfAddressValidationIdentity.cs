﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Data.Access
{
    public class GetRouteShelfAddressValidationIdentity
    {
        SmartSort.Business.Entities.SmartSortLookupAddressValidation _smartSortLookupAddressValidation;

        public GetRouteShelfAddressValidationIdentity(SmartSort.Business.Entities.SmartSortLookupAddressValidation smartSortLookupAddressValidation)
        {            
            this.SmartSortLookupAddressValidation = smartSortLookupAddressValidation;
        }
    
        public SmartSort.Business.Entities.SmartSortLookupAddressValidation SmartSortLookupAddressValidation
        {
            get { return _smartSortLookupAddressValidation; }
            set { _smartSortLookupAddressValidation = value; }
        }
        
    }
}
