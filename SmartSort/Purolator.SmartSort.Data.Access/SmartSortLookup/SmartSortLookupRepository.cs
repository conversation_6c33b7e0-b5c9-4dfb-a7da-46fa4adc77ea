﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class SmartSortLookupRepository : Repository<Entities.RouteDetails>
    {
        public SmartSortLookupRepository(string databaseName) : base(databaseName) { }

        public List<Entities.RouteDetails> GetRouteDetails(SmartSort.Business.Entities.SmartSortLookup smartSortLookup)
        {            
            ISelectionFactory<GetRouteShelfIdentity> selectionFactory = new GetRouteShelfSelectionFactory();
            
            try
            {                
                GetRouteShelfIdentity getRouteShelfIdentity = new GetRouteShelfIdentity(smartSortLookup);                             
                return base.Find(selectionFactory, new GetRouteShelfDomainObjectFactory(), getRouteShelfIdentity);      
              
            }
            catch (SqlException ex)
            {
                Purolator.SmartSort.Common.Logger.Error("Error in SmartSortLookupRepository()", Purolator.SmartSort.Common.LogCategories.LOOKUP_SERVICE, ex);
                throw ex;
            }         
        }
       
    }
}
