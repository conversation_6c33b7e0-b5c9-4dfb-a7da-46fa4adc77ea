﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Entities = Purolator.SmartSort.Business.Entities;
using System.Globalization;
using Purolator.SmartSort.Serializer;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Data.Access
{
    public class SmartSortLookupLogInsertFactory : IInsertFactory<Entities.LookupWSLog>
    {

        public DbCommand ConstructInsertCommand(Database db, Entities.LookupWSLog log)
        {

            DbCommand command = db.GetStoredProcCommand("dbo.InsertSSlwsLog");


            db.AddInParameter(command, "@DeviceID", DbType.String, log.RequestId);
            db.AddInParameter(command, "@piecepin", DbType.String, log.PIN);
            db.AddInParameter(command, "@Request", DbType.String, log.Request);
            db.AddInParameter(command, "@Response", DbType.String, log.Response);
            db.AddInParameter(command, "@callDuration", DbType.Int32, log.CallDuration);

            return command;
        }


        public void SetNewID(Database db, DbCommand command, Entities.LookupWSLog log)
        {            
        }
        
        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
