﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class SmartSortLookupAddressValidationRepository : Repository<Entities.RouteDetailsAddressValidation>
    {
        public SmartSortLookupAddressValidationRepository(string databaseName) : base(databaseName) { }

     
        public List<Entities.RouteDetailsAddressValidation> GetRouteDetailsAddressValidation(SmartSort.Business.Entities.SmartSortLookupAddressValidation smartSortLookupAddressValidation)
        {
            ISelectionFactory<GetRouteShelfAddressValidationIdentity> selectionFactory = new GetRouteShelfAddressValidationSelectionFactory();

            try
            {
                GetRouteShelfAddressValidationIdentity getRouteShelfIdentityAddressValidation = new GetRouteShelfAddressValidationIdentity(smartSortLookupAddressValidation);

                return base.Find(selectionFactory, new GetRouteShelfAddressValidationDomainObjectFactory(), getRouteShelfIdentityAddressValidation);
                
            }
            catch (SqlException ex)
            {                
                Purolator.SmartSort.Common.Logger.Error("Error in SmartSortLookupAddressValidationRepository()", Purolator.SmartSort.Common.LogCategories.LOOKUP_SERVICE, ex);
                throw ex;

            }
            return new List<Entities.RouteDetailsAddressValidation>();

        }
    
    }
}
