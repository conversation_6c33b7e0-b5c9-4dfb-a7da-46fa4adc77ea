﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Data.Access
{
    public class GetRouteShelfIdentity
    {
        SmartSort.Business.Entities.SmartSortLookup _smartSortLookup;

        public GetRouteShelfIdentity(SmartSort.Business.Entities.SmartSortLookup smartSortLookup)
        {            
            this.SmartSortLookup = smartSortLookup;

        }

    
        public SmartSort.Business.Entities.SmartSortLookup SmartSortLookup
        {
              get { return _smartSortLookup; }
            set { _smartSortLookup = value; }
        }
        
    }
}
