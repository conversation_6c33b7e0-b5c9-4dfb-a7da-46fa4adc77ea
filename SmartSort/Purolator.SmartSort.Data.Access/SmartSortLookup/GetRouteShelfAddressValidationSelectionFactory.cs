﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Data.Access.Generic;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Data.Access;

namespace Purolator.SmartSort.Data.Access
{

    class GetRouteShelfAddressValidationSelectionFactory : ISelectionFactory<GetRouteShelfAddressValidationIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, GetRouteShelfAddressValidationIdentity getRouteShelfAddressValidationIdentity)
        {
          
            DbCommand command = null;
            if (null != db && null != getRouteShelfAddressValidationIdentity)
            {
                command = db.GetStoredProcCommand("dbo.SSLookupPinAV");

                string xmldata = Serializer.Serializer.Serialize<Entities.SmartSortLookupAddressValidation>(getRouteShelfAddressValidationIdentity.SmartSortLookupAddressValidation);

                db.AddInParameter(command, "doc", DbType.Xml, xmldata);

                db.AddOutParameter(command, "Status", DbType.String, 255);
            }
            
            return command;
        }

        public void SetParameterValue(Database db, DbCommand command, ref Purolator.SmartSort.Data.Access.GetRouteShelfAddressValidationIdentity routeDetailsAddressValidation)        
        {

            if (db.GetParameterValue(command, "status").GetType() != typeof(System.DBNull))
            {
                if (!String.IsNullOrEmpty((String)db.GetParameterValue(command, "status")))
                {
                    routeDetailsAddressValidation.SmartSortLookupAddressValidation.Status = (String)db.GetParameterValue(command, "status");
                }
            }

        }

    

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
