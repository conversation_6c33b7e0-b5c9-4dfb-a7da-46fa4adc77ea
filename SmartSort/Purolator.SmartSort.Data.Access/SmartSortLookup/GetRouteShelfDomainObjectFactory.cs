﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    internal class GetRouteShelfDomainObjectFactory : IDomainObjectFactory<RouteDetails>
    {
        public RouteDetails Construct(IDataReader reader)
        {
            RouteDetails routeDetails = new RouteDetails();
                     
            int routeIndex = reader.GetOrdinal("Route");
            if (!reader.IsDBNull(routeIndex))
            {
                routeDetails.Route = reader.GetString(routeIndex);
            }

            int shelfIndex = reader.GetOrdinal("Shelf");
            if (!reader.IsDBNull(shelfIndex))
            {
                routeDetails.Shelf = reader.GetString(shelfIndex);
            }

            int truckShelfOverrideIndex = reader.GetOrdinal("TruckShelfOverride");
            if (!reader.IsDBNull(truckShelfOverrideIndex))
            {
                routeDetails.TruckShelfOverride = reader.GetString(truckShelfOverrideIndex);
            }

            int deliverySeqIDIndex = reader.GetOrdinal("DeliverySeqID");
            if (!reader.IsDBNull(deliverySeqIDIndex))
            {
                try
                {
                    routeDetails.DeliverySeqID = "" + reader.GetInt32(deliverySeqIDIndex);
                }
                catch (Exception ex)
                {
                    routeDetails.DeliverySeqID = "0";
                }
            }

            int primarySortIndex = reader.GetOrdinal("PrimarySort");
            if (!reader.IsDBNull(primarySortIndex))
            {
                routeDetails.PrimarySort = reader.GetString(primarySortIndex);
            }

            int beltSideIndex = reader.GetOrdinal("BeltSide");
            if (!reader.IsDBNull(beltSideIndex))
            {
                routeDetails.BeltSide = reader.GetString(beltSideIndex);
            }
                     
            return routeDetails;
        }
    }
}