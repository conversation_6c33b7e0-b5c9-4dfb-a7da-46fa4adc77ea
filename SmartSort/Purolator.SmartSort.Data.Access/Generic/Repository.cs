﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data;
using System.Data.Common;


namespace Purolator.SmartSort.Data.Access
{
    public class Repository<TDomainObject>
    {        
        private string databaseName;
        private Database db;

        public Repository(string databaseName)
        {
            DatabaseProviderFactory factory = new DatabaseProviderFactory();
            this.databaseName = databaseName;
            this.db = factory.Create(databaseName);
        }

        public Repository(Database database)
        {
            this.db = database;
        }

        /// <summary>
        /// Find all objects that match the given criteria.
        /// </summary>
        /// <typeparam name="TIdentity">Type of object used to identify
        /// the objects to find.</typeparam>
        /// <param name="selectionFactory">Factory object that can turn the
        /// identity object into the appropriate DbCommand.</param>
        /// <param name="domainObjectFactory">Factory object that can turn the
        /// returned result set into a domain object.</param>
        /// <param name="identity">Object that identifies which items to get.</param>
        /// <returns></returns>
        public List<TDomainObject> Find<TIdentity>(
            ISelectionFactory<TIdentity> selectionFactory,
            IDomainObjectFactory<TDomainObject> domainObjectFactory,
            TIdentity identity)
        {
            List<TDomainObject> results = new List<TDomainObject>();

            
            using (DbCommand command = selectionFactory.ConstructSelectCommand(db, identity))
            {
                using (IDataReader rdr = db.ExecuteReader(command))
                {
                    while (rdr.Read())
                    {                     
                        results.Add(domainObjectFactory.Construct(rdr));                        
                    }
                 
                }

                selectionFactory.SetParameterValue(db, command, ref identity);
            }
            
           

            return results;
        }

       


        /// <summary>
        /// Find the first / only object that matches the given criteria.
        /// </summary>
        /// <typeparam name="TIdentity">Type of object used to identity the domain object desired.</typeparam>
        /// <param name="selectionFactory">Factory object that can turn
        /// the identity object into the appropriate DbCommand.</param>
        /// <param name="domainObjectFactory">Factory object that can turn the
        /// returned result set into a domain object.</param>
        /// <param name="identity">Object that identifies which item to get.</param>
        /// <returns>The domain object requested, or null if not found.</returns>
        public TDomainObject FindOne<TIdentity>(
            ISelectionFactory<TIdentity> selectionFactory,
            IDomainObjectFactory<TDomainObject> domainObjectFactory,
            TIdentity identity)
        {
            TDomainObject result = default(TDomainObject);
            using (DbCommand command = selectionFactory.ConstructSelectCommand(db, identity))
            {
                using (IDataReader rdr = db.ExecuteReader(command))
                {
                    if (rdr.Read())
                    {
                        result = domainObjectFactory.Construct(rdr);
                    }                                 
                }                
                
            }
            
            return result;
        }


        /// <summary>
        /// Find the first / only object that matches the given criteria.
        /// </summary>
        /// <typeparam name="TIdentity">Type of object used to identity the domain object desired.</typeparam>
        /// <param name="selectionFactory">Factory object that can turn
        /// the identity object into the appropriate DbCommand.</param>
        /// <param name="domainObjectFactory">Factory object that can turn the
        /// returned result set into a domain object.</param>
        /// <param name="identity">Object that identifies which item to get.</param>
        /// <returns>The domain object requested, or null if not found.</returns>
        public TDomainObject FindOne<TIdentity>(
            ISelectionFactory<TIdentity> selectionFactory,
            IDomainObjectFactory<TDomainObject> domainObjectFactory,
            ref TIdentity identity)
        {
            TDomainObject result = default(TDomainObject);
            using (DbCommand command = selectionFactory.ConstructSelectCommand(db, identity))
            {
                using (IDataReader rdr = db.ExecuteReader(command))
                {
                    if (rdr.Read())
                    {
                        result = domainObjectFactory.Construct(rdr);
                    }
                }

                selectionFactory.SetParameterValue(db, command, ref identity);


            }

            return result;
        }

        protected bool isDBUp()
        {

            using (DbCommand command = db.GetStoredProcCommand("dbo.DBCheck"))
            {
                using (IDataReader rdr = db.ExecuteReader(command))
                {
                    if (rdr.Read())
                    {
                        return true;
                    }
                }
            }
            return false;
        }



        /// <summary>
        /// Insert the given object into the database.
        /// </summary>
        /// <param name="insertFactory">Factory used to create the command.</param>
        /// <param name="domainObj">Domain object to insert</param>
        public void Add(IInsertFactory<TDomainObject> insertFactory, TDomainObject domainObj)
        {
            using (DbCommand command = insertFactory.ConstructInsertCommand(db, domainObj))
            {
                db.ExecuteNonQuery(command);
                insertFactory.SetNewID(db, command, domainObj);
            }
        }

        public void Add(IInsertFactory<TDomainObject> insertFactory, TDomainObject domainObj, DbTransaction trans)
        {
            using (DbCommand command = insertFactory.ConstructInsertCommand(db, domainObj))
            {
                db.ExecuteNonQuery(command, trans);
                insertFactory.SetNewID(db, command, domainObj);
            }
        }

        public void Remove<TIdentityObject>(IDeleteFactory<TIdentityObject> deleteFactory,
            TIdentityObject identityObj)
        {
            using (DbCommand command = deleteFactory.ConstructDeleteCommand(db, identityObj))
            {
                db.ExecuteNonQuery(command);
            }
        }

        public void Save(IUpdateFactory<TDomainObject> updateFactory, TDomainObject domainObj)
        {
            using (DbCommand command = updateFactory.ConstructUpdateCommand(db, domainObj))
            {
                db.ExecuteNonQuery(command);
            }
        }

        public void Save(IUpdateFactory<TDomainObject> updateFactory, TDomainObject domainObj, out DbCommand outValue)
        {
            using (DbCommand command = updateFactory.ConstructUpdateCommand(db, domainObj))
            {
                db.ExecuteNonQuery(command);
                outValue = command;               
            }
        }


        public void Save(IUpdateFactory<TDomainObject> updateFactory, TDomainObject domainObj, DbTransaction trans)
        {
            using (DbCommand command = updateFactory.ConstructUpdateCommand(db, domainObj))
            {
                db.ExecuteNonQuery(command, trans);
            }
        }
    }
}
