﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Purolator.SmartSort.Data.Access.Generic;

namespace Purolator.SmartSort.Data.Access
{
    [Serializable]
    public class RepositoryFailureException : RepositoryException
    {
        public RepositoryFailureException()
        {
        }

        public RepositoryFailureException(string message)
            : base(message)
        {
        }

        public RepositoryFailureException(string message, Exception inner)
            : base(message, inner)
        {
        }

        public RepositoryFailureException(Exception inner)
            : base(string.Format(CultureInfo.CurrentCulture, GenericResources.DefaultMessage), inner)
        {
        }

        protected RepositoryFailureException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
    }
}
