﻿using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Purolator.SmartSort.Data.Access
{
    public interface IInsertFactory<TDomainObject> : IDbToBusinessEntityNameMapper
    {
        /// <summary>
        /// Generate the insert command.
        /// </summary>
        /// <param name="db">Entlib Database object to generate command for.</param>
        /// <param name="domainObj">Domain object to insert.</param>
        /// <returns>Initialized DbCommand object.</returns>
        DbCommand ConstructInsertCommand(Database db, TDomainObject domainObj);

        /// <summary>
        /// Read the ID of the newly inserted object out of the command
        /// and set it in the domain object.
        /// </summary>
        /// <param name="db">EntLib database object that the command was generated with.</param>
        /// <param name="command">Successfully executed command that now holds the id. This should be the same command object that was returned from ConstructInsertCommand.</param>
        /// <param name="domainObj">Domain object that was passed in to ConstructInsertCommand. This will have the ID assigned by the database set.</param>
        void SetNewID(Database db, DbCommand command, TDomainObject domainObj);
    }
}
