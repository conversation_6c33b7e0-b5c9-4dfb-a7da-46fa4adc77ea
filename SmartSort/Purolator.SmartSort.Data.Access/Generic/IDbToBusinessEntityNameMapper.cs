﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Purolator.SmartSort.Data.Access
{
    public interface IDbToBusinessEntityNameMapper
    {
        /// <summary>
        /// Maps a field name in the database (usually a parameter name for a stored proc)
        /// to the corresponding business entity property name.
        /// </summary>
        /// <remarks>This method is intended for error message handling, not for reflection.</remarks>
        /// <param name="dbParameter">Name of field/parameter that the database knows about.</param>
        /// <returns>Corresponding business entity field name.</returns>
        string MapDbParameterToBusinessEntityProperty(string dbParameter);
    }
}
