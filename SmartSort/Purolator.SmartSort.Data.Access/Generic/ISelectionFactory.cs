﻿using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace Purolator.SmartSort.Data.Access
{
    public interface ISelectionFactory<TIdentityObject> : IDbToBusinessEntityNameMapper
    {
        DbCommand ConstructSelectCommand(Database db, TIdentityObject idObject);

        void SetParameterValue(Database db, DbCommand command, ref TIdentityObject idObject);
                
    }
}
