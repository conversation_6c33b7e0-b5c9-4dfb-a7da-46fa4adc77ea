﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using Purolator.SmartSort.Data.Access.Generic;

namespace Purolator.SmartSort.Data.Access
{
    [Serializable]
    public class RepositoryValidationException : RepositoryException
    {
        private string failedField;
        private string failureReason;

        public RepositoryValidationException(string failedField, string failureReason)
            : this(failedField, failureReason, null)
        {
        }

        public RepositoryValidationException(string failedField, string failureReason, Exception inner)
            : base(string.Format(CultureInfo.CurrentCulture, GenericResources.ValidationFailed, failedField, failureReason), inner)
        {
            this.failedField = failedField;
            this.failureReason = failureReason;
        }

        public string FailedField
        {
            get { return failedField; }
        }

        public string FailureReason
        {
            get { return failureReason; }
        }

        #region Standard Exception Constructors
        public RepositoryValidationException()
        {
        }

        public RepositoryValidationException(string message)
            : base(message)
        {
        }

        public RepositoryValidationException(string message, Exception inner)
            : base(message, inner)
        {
        }

        protected RepositoryValidationException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }


        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
        }
        #endregion
    }
}
