﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace Purolator.SmartSort.Data.Access
{
    [Serializable]
    public class ConcurrencyViolationException : RepositoryException
    {
        public ConcurrencyViolationException()
        {
        }

        public ConcurrencyViolationException(string message)
            : base(message)
        {
        }

        public ConcurrencyViolationException(string message, Exception inner)
            : base(message, inner)
        {
        }

        protected ConcurrencyViolationException(SerializationInfo info, StreamingContext context)
            : base(info, context)
        {
        }
    }
}
