﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1FD9CDB9-E4B1-4AA5-AB52-2D3C6E03262D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Purolator.SmartSort.Data.Access</RootNamespace>
    <AssemblyName>Purolator.SmartSort.Data.Access</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>..\SmartSortRuleSet.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Common, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.Common.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Data, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.Data.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Data.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AddressValidation\InfohubPreprintRepository.cs" />
    <Compile Include="AddressValidation\AddressValidationRepository.cs" />
    <Compile Include="AddressValidation\GetPrePrintDataIdentity.cs" />
    <Compile Include="AddressValidation\GetPrePrintDataForInfohubSelectionFactory.cs" />
    <Compile Include="AddressValidation\GetPrePrintDataForInfohubFactory .cs" />
    <Compile Include="AddressValidation\MunicipalitiesRepository.cs" />
    <Compile Include="AddressValidation\UpdateMunicipalitiesFactory.cs" />
    <Compile Include="AddressValidation\InsertPrePrintMasterFactory.cs" />
    <Compile Include="AddressValidation\GetPrePrintDataForAWSFactory.cs" />
    <Compile Include="AddressValidation\GetPrePrintDataForAWSSelectionFactory.cs" />
    <Compile Include="AddressValidation\GetPrePrintDataForAWSIdentity.cs" />
    <Compile Include="AddressValidation\PrePrintMasterRepository.cs" />
    <Compile Include="AddressValidation\UpdatePrePrintDataForAWSFactory.cs" />
    <Compile Include="AddressValidation\UpdatePrePrintDataForAWSRepository.cs" />
    <Compile Include="CourierManifest\CloseRouteUpdateFactory.cs" />
    <Compile Include="CourierManifest\CourierManifestIdentity.cs" />
    <Compile Include="CourierManifest\CourierManifestRepository.cs" />
    <Compile Include="CourierManifest\RepositionPackageUpdateFactory.cs" />
    <Compile Include="CourierManifest\UnloadTruckUpdateFactory.cs" />
    <Compile Include="CourierManifest\LoadDeviationRepository.cs" />
    <Compile Include="CourierManifest\CourierManifestSelectFactory.cs" />
    <Compile Include="CourierManifest\LoadAlternateTruckUpdateFactory.cs" />
    <Compile Include="GenericResources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>GenericResources.resx</DependentUpon>
    </Compile>
    <Compile Include="Generic\ConcurrencyViolationException.cs" />
    <Compile Include="Generic\GenericResources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>GenericResources.resx</DependentUpon>
    </Compile>
    <Compile Include="Generic\IDbToBusinessEntityNameMapper.cs" />
    <Compile Include="Generic\IDeleteFactory.cs" />
    <Compile Include="Generic\IDomainObjectFactory.cs" />
    <Compile Include="Generic\IInsertFactory.cs" />
    <Compile Include="Generic\ISelectionFactory.cs" />
    <Compile Include="Generic\IUpdateFactory.cs" />
    <Compile Include="Generic\NullableIdentity.cs" />
    <Compile Include="Generic\Repository.cs" />
    <Compile Include="Generic\RepositoryException.cs" />
    <Compile Include="Generic\RepositoryFailureException.cs" />
    <Compile Include="Generic\RepositoryValidationException.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RouteManagement\SplitPlanPrintSelectionFactory.cs" />
    <Compile Include="RouteManagement\SplitPlanPrintRepository.cs" />
    <Compile Include="RouteManagement\RoutePlanWorkItemUpdateFactory.cs" />
    <Compile Include="RouteManagement\RoutePlanWorkItemResetFactory.cs" />
    <Compile Include="RouteManagement\RoutePlanWorkItemSelectionFactory.cs" />
    <Compile Include="RouteManagement\RoutePlanWorkItemIdentity.cs" />
    <Compile Include="RouteManagement\RoutePlanServiceRepository.cs" />
    <Compile Include="RouteManagement\RoutePlanPrintSelectionFactory.cs" />
    <Compile Include="RouteManagement\RoutePlanPrintRepository.cs" />
    <Compile Include="RouteManagement\RoutePlanIdentity.cs" />
    <Compile Include="ShipmentEvent\ShipmentEventInsertFactory.cs" />
    <Compile Include="ShipmentEvent\ShipmentEventRepository.cs" />
    <Compile Include="SmartSortLookup\SmartSortLookupLogFactory.cs" />
    <Compile Include="SmartSortLookup\SmartSortLookupLogInsertFactory.cs" />
    <Compile Include="SmartSortTimeZone\SmartSortTimeZoneDomainObjectFactory.cs" />
    <Compile Include="SmartSortTimeZone\SmartSortTimeZoneSelectFactory.cs" />
    <Compile Include="SmartSortTimeZone\SmartSortTimeZoneIdentity.cs" />
    <Compile Include="SmartSortTimeZone\SmartSortTimeZoneRepository.cs" />
    <Compile Include="SmartSortLookup\SmartSortLookupAddressValidationRepository.cs" />
    <Compile Include="SmartSortLookup\GetRouteShelfAddressValidationSelectionFactory.cs" />
    <Compile Include="SmartSortLookup\GetRouteShelfAddressValidationDomainObjectFactory.cs" />
    <Compile Include="SmartSortLookup\GetRouteShelfAddressValidationIdentity.cs" />
    <Compile Include="SmartSortLookup\GetRouteShelfDomainObjectFactory.cs" />
    <Compile Include="SmartSortLookup\GetRouteShelfSelectionFactory.cs" />
    <Compile Include="SmartSortLookup\GetRouteShelfIdentity.cs" />
    <Compile Include="SmartSortLookup\SmartSortLookupRepository.cs" />
    <Compile Include="SmartSortScan\SmartSortScanRepository.cs" />
    <Compile Include="SmartSortScan\SmartSortScanInsertFactory.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Purolator.SmartSort.Business.Entities\Purolator.SmartSort.Business.Entities.csproj">
      <Project>{862103a3-8dab-46ee-8890-2f192a71ed66}</Project>
      <Name>Purolator.SmartSort.Business.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Common\Purolator.SmartSort.Common.csproj">
      <Project>{93213bc1-5510-457f-b09b-94acb33a232f}</Project>
      <Name>Purolator.SmartSort.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Serializer\Purolator.SmartSort.Serializer.csproj">
      <Project>{96339c2a-f60d-4599-b2c2-bba5813245e9}</Project>
      <Name>Purolator.SmartSort.Serializer</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="GenericResources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>GenericResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Generic\GenericResources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>GenericResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>