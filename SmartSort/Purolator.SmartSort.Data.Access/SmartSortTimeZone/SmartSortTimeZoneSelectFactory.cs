﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Data;
using Purolator.SmartSort.Data.Access.Generic;

namespace Purolator.SmartSort.Data.Access
{
    class SmartSortTimeZoneSelectFactory : ISelectionFactory<SmartSortTimeZoneIdentity>
    {


        public DbCommand ConstructSelectCommand(Database db, SmartSortTimeZoneIdentity smartSortTimeZone)
        {
            DbCommand command = null;
            if (null != db && null != smartSortTimeZone)
            {
                command = db.GetStoredProcCommand("dbo.GetTimeZone");
                db.AddInParameter(command, "@Terminal", DbType.String, smartSortTimeZone.Terminal);                
                db.AddInParameter(command, "@DateTime", DbType.Date, smartSortTimeZone.DateTime);
                db.AddOutParameter(command, "@TimeZoneOffset", DbType.String, 255);

            }
            return command;
        }
      
        public void SetParameterValue(Database db, DbCommand command, ref SmartSortTimeZoneIdentity smartSortTimeZone)
        {
            if (db.GetParameterValue(command, "TimeZoneOffset").GetType() != typeof(System.DBNull))
            {
                if (!String.IsNullOrEmpty((String)db.GetParameterValue(command, "TimeZoneOffset")))
                {
                    smartSortTimeZone.TimeZoneOffset = (String)db.GetParameterValue(command, "TimeZoneOffset");
                }
            }
        }

      

        public string MapDbParameterToBusinessEntityProperty(string dbParameter)
        {
            switch (dbParameter)
            {
                default:
                    throw new ArgumentException(string.Format(CultureInfo.CurrentCulture, GenericResources.InvalidParameterName), dbParameter);
            }
        }
    }
}
