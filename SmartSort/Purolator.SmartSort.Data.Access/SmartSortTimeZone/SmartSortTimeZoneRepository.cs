﻿using System;
using System.Data;
using System.Data.SqlClient;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data.Common;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    public class SmartSortTimeZoneRepository : Repository<Entities.SmartSortTimeZone>
    {
        public SmartSortTimeZoneRepository(string dbName) : base(dbName) { }
        
        public Entities.SmartSortTimeZone GetTimeZoneSync(Entities.SmartSortTimeZone sstz)
        {
            ISelectionFactory<SmartSortTimeZoneIdentity> selectionFactory = new SmartSortTimeZoneSelectFactory();
            SmartSortTimeZoneIdentity sstzIdentity = new SmartSortTimeZoneIdentity();

            sstzIdentity.Terminal = sstz.Terminal;
            sstzIdentity.DateTime = sstz.DateTime;
            base.FindOne(selectionFactory, new SmartSortTimeZoneDomainFactory(), ref sstzIdentity);
            
            if (sstzIdentity.TimeZoneOffset != null)
            {
                sstz.TimeZoneOffset = sstzIdentity.TimeZoneOffset;
            }
            return sstz;

        }

        public bool isServiceUp()
        {
            return isDBUp();
        }

    }

     

    internal class SmartSortTimeZoneDomainFactory : IDomainObjectFactory<Entities.SmartSortTimeZone>
    {
        public Entities.SmartSortTimeZone Construct(IDataReader reader)
        {
            Entities.SmartSortTimeZone cm = new Entities.SmartSortTimeZone();
            if (reader[0] != null)
            {
                try
                {
                    cm = Serializer.Serializer.Deserialize<Entities.SmartSortTimeZone>(reader[0].ToString());
                }
                catch (Exception ex)
                {
                    // LOG                    
                }
            }
            return cm;
        }
    }

}
