﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Data.Access
{
    internal class SmartSortTimeZoneDomainObjectFactory : IDomainObjectFactory<SmartSortTimeZone>
    {
        public SmartSortTimeZone Construct(IDataReader reader)
        {
            SmartSortTimeZone sstz = new SmartSortTimeZone();

            int timeZoneOffsetIndex = reader.GetOrdinal("TimeZoneOffset");
            if (!reader.IsDBNull(timeZoneOffsetIndex))
            {
                sstz.TimeZoneOffset = reader.GetString(timeZoneOffsetIndex);       
            }

            /*int shelfIndex = reader.GetOrdinal("Shelf");
            if (!reader.IsDBNull(shelfIndex))
            {
                routeDetailsAddressValidation.Shelf = reader.GetString(shelfIndex);
            }

            int truckShelfOverrideIndex = reader.GetOrdinal("TruckShelfOverride");
            if (!reader.IsDBNull(truckShelfOverrideIndex))
            {
                routeDetailsAddressValidation.TruckShelfOverride = reader.GetString(truckShelfOverrideIndex);
            }

            int deliverySeqIDIndex = reader.GetOrdinal("DeliverySeqID");
            if (!reader.IsDBNull(deliverySeqIDIndex))
            {
                routeDetailsAddressValidation.DeliverySeqID = reader.GetString(deliverySeqIDIndex);
            }

            int primarySortIndex = reader.GetOrdinal("PrimarySort");
            if (!reader.IsDBNull(primarySortIndex))
            {
                routeDetailsAddressValidation.PrimarySort = reader.GetString(primarySortIndex);
            }

            int beltSideIndex = reader.GetOrdinal("BeltSide");
            if (!reader.IsDBNull(beltSideIndex))
            {
                routeDetailsAddressValidation.BeltSide = reader.GetString(beltSideIndex);
            }*/

            return sstz;
        }
    }
}