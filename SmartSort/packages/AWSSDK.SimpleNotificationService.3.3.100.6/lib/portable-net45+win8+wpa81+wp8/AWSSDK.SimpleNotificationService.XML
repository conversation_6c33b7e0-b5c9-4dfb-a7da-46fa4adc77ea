<?xml version="1.0"?>
<doc>
    <assembly>
        <name>AWSSDK.SimpleNotificationService</name>
    </assembly>
    <members>
        <member name="T:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient">
             <summary>
             Implementation for accessing SimpleNotificationService
            
             Amazon Simple Notification Service 
             <para>
             Amazon Simple Notification Service (Amazon SNS) is a web service that enables you
             to build distributed web-enabled applications. Applications can use Amazon SNS to
             easily push real-time notification messages to interested subscribers over multiple
             delivery protocols. For more information about this product see <a href="http://aws.amazon.com/sns/">http://aws.amazon.com/sns</a>.
             For detailed information about Amazon SNS features and their associated API calls,
             see the <a href="http://docs.aws.amazon.com/sns/latest/dg/">Amazon SNS Developer Guide</a>.
             
             </para>
              
             <para>
             We also provide SDKs that enable you to access Amazon SNS from your preferred programming
             language. The SDKs contain functionality that automatically takes care of tasks such
             as: cryptographically signing your service requests, retrying requests, and handling
             error responses. For a list of available SDKs, go to <a href="http://aws.amazon.com/tools/">Tools
             for Amazon Web Services</a>. 
             </para>
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.AddSQSPermission(Amazon.Auth.AccessControlPolicy.Policy,System.String,System.String)">
            <summary>
            Add statement to the policy that gives the sns topic access to send a message to the queue.
            </summary>
            <param name="policy"></param>
            <param name="topicArn"></param>
            <param name="sqsQueueArn"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.HasSQSPermission(Amazon.Auth.AccessControlPolicy.Policy,System.String,System.String)">
            <summary>
            Check to see if the policy for the queue has already given permission to the topic.
            </summary>
            <param name="policy"></param>
            <param name="topicArn"></param>
            <param name="sqsQueueArn"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.TopicNameMatcher(System.String,System.String)">
            <summary>
            Verifies that the ARN for the topic matches the topic name
            </summary>
            <param name="topicArn"></param>
            <param name="topicName"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.GetNewPolicyAndStatementForTopicAttributes(System.Collections.Generic.Dictionary{System.String,System.String},System.String,System.String,Amazon.Auth.AccessControlPolicy.Policy@,Amazon.Auth.AccessControlPolicy.Statement@)">
            <summary>
            Helper method for AuthorizeS3ToPublishAsync()
            </summary>
            <param name="attributes"></param>
            <param name="topicArn"></param>
            <param name="bucket"></param>
            <param name="policy"></param>
            <param name="statement"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.SubscribeQueueAsync(System.String,Amazon.Runtime.SharedInterfaces.ICoreAmazonSQS,System.String)">
            <summary>
            Subscribes an existing Amazon SQS queue to an existing Amazon SNS topic asynchronously.
            <para>
            The policy applied to the SQS queue is similar to this:
            <code>
            {
            	"Version" : "2008-10-17",
            	"Statement" : [{
            	    "Sid" : "topic-subscription-arn:aws:sns:us-west-2:599109622955:myTopic",
            		"Effect" : "Allow",
            		"Principal" : "*",
            		"Action" : ["sqs:SendMessage"],
            		"Resource":["arn:aws:sqs:us-west-2:599109622955:myQueue"],
            		"Condition" : {
            			"ArnLike":{
            				"aws:SourceArn":["arn:aws:sns:us-west-2:599109622955:myTopic"]
            			}
            		}
                }]
            }
            </code>
            </para>
            <para>
            There might be a small time period immediately after
            subscribing the SQS queue to the SNS topic and updating the SQS queue's
            policy, where messages are not able to be delivered to the queue. After a
            moment, the new queue policy will propagate and the queue will be able to
            receive messages. This delay only occurs immediately after initially
            subscribing the queue.
            </para>
            </summary>
            <param name="topicArn">The topic to subscribe to</param>
            <param name="sqsClient">The SQS client used to get attributes and set the policy on the SQS queue.</param>
            <param name="sqsQueueUrl">The queue to add a subscription to.</param>
            <returns>The subscription ARN as returned by Amazon SNS when the queue is 
            successfully subscribed to the topic.</returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.SubscribeQueueToTopicsAsync(System.Collections.Generic.IList{System.String},Amazon.Runtime.SharedInterfaces.ICoreAmazonSQS,System.String)">
            <summary>
            Subscribes an existing Amazon SQS queue to existing Amazon SNS topics asynchronously.
            <para>
            The policy applied to the SQS queue is similar to this:
            <code>
            {
            	"Version" : "2008-10-17",
            	"Statement" : [{
            	    "Sid" : "topic-subscription-arn:aws:sns:us-west-2:599109622955:myTopic",
            		"Effect" : "Allow",
            		"Principal" : "*",
            		"Action" : ["sqs:SendMessage"],
            		"Resource":["arn:aws:sqs:us-west-2:599109622955:myQueue"],
            		"Condition" : {
            			"ArnLike":{
            				"aws:SourceArn":["arn:aws:sns:us-west-2:599109622955:myTopic"]
            			}
            		}
                }]
            }
            </code>
            </para>
            <para>
            There might be a small time period immediately after
            subscribing the SQS queue to the SNS topic and updating the SQS queue's
            policy, where messages are not able to be delivered to the queue. After a
            moment, the new queue policy will propagate and the queue will be able to
            receive messages. This delay only occurs immediately after initially
            subscribing the queue.
            </para>
            </summary>
            <param name="topicArns">The topics to subscribe to</param>
            <param name="sqsClient">The SQS client used to get attributes and set the policy on the SQS queue.</param>
            <param name="sqsQueueUrl">The queue to add a subscription to.</param>
            <returns>The mapping of topic ARNs to subscription ARNs as returned by Amazon SNS when the queue is 
            successfully subscribed to each topic.</returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.FindTopicAsync(System.String)">
            <summary>
            Finds an existing Amazon SNS topic by iterating all SNS topics until a match is found asynchronously.
            <para>
            The ListTopics method is used to fetch upto 100 SNS topics at a time until a SNS topic 
            with an TopicArn that matches <paramref name="topicName"/> is found.
            </para>
            </summary>
            <param name="topicName">The name of the topic find</param>
            <returns>A Task containing the matched SNS topic.</returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.AuthorizeS3ToPublishAsync(System.String,System.String)">
            <summary>
            This is a utility method which updates the policy of a topic to allow the
            S3 bucket to publish events to it.
            </summary>
            <param name="topicArn">The topic that will have its policy updated.</param>
            <param name="bucket">The bucket that will be given access to publish from.</param>
            /// <returns>A Task</returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.#ctor(Amazon.Runtime.AWSCredentials)">
            <summary>
            Constructs AmazonSimpleNotificationServiceClient with AWS Credentials
            </summary>
            <param name="credentials">AWS Credentials</param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.#ctor(Amazon.Runtime.AWSCredentials,Amazon.RegionEndpoint)">
            <summary>
            Constructs AmazonSimpleNotificationServiceClient with AWS Credentials
            </summary>
            <param name="credentials">AWS Credentials</param>
            <param name="region">The region to connect.</param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.#ctor(Amazon.Runtime.AWSCredentials,Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceConfig)">
            <summary>
            Constructs AmazonSimpleNotificationServiceClient with AWS Credentials and an
            AmazonSimpleNotificationServiceClient Configuration object.
            </summary>
            <param name="credentials">AWS Credentials</param>
            <param name="clientConfig">The AmazonSimpleNotificationServiceClient Configuration Object</param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.#ctor(System.String,System.String)">
            <summary>
            Constructs AmazonSimpleNotificationServiceClient with AWS Access Key ID and AWS Secret Key
            </summary>
            <param name="awsAccessKeyId">AWS Access Key ID</param>
            <param name="awsSecretAccessKey">AWS Secret Access Key</param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.#ctor(System.String,System.String,Amazon.RegionEndpoint)">
            <summary>
            Constructs AmazonSimpleNotificationServiceClient with AWS Access Key ID and AWS Secret Key
            </summary>
            <param name="awsAccessKeyId">AWS Access Key ID</param>
            <param name="awsSecretAccessKey">AWS Secret Access Key</param>
            <param name="region">The region to connect.</param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.#ctor(System.String,System.String,Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceConfig)">
            <summary>
            Constructs AmazonSimpleNotificationServiceClient with AWS Access Key ID, AWS Secret Key and an
            AmazonSimpleNotificationServiceClient Configuration object. 
            </summary>
            <param name="awsAccessKeyId">AWS Access Key ID</param>
            <param name="awsSecretAccessKey">AWS Secret Access Key</param>
            <param name="clientConfig">The AmazonSimpleNotificationServiceClient Configuration Object</param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.#ctor(System.String,System.String,System.String)">
            <summary>
            Constructs AmazonSimpleNotificationServiceClient with AWS Access Key ID and AWS Secret Key
            </summary>
            <param name="awsAccessKeyId">AWS Access Key ID</param>
            <param name="awsSecretAccessKey">AWS Secret Access Key</param>
            <param name="awsSessionToken">AWS Session Token</param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.#ctor(System.String,System.String,System.String,Amazon.RegionEndpoint)">
            <summary>
            Constructs AmazonSimpleNotificationServiceClient with AWS Access Key ID and AWS Secret Key
            </summary>
            <param name="awsAccessKeyId">AWS Access Key ID</param>
            <param name="awsSecretAccessKey">AWS Secret Access Key</param>
            <param name="awsSessionToken">AWS Session Token</param>
            <param name="region">The region to connect.</param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.#ctor(System.String,System.String,System.String,Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceConfig)">
            <summary>
            Constructs AmazonSimpleNotificationServiceClient with AWS Access Key ID, AWS Secret Key and an
            AmazonSimpleNotificationServiceClient Configuration object. 
            </summary>
            <param name="awsAccessKeyId">AWS Access Key ID</param>
            <param name="awsSecretAccessKey">AWS Secret Access Key</param>
            <param name="awsSessionToken">AWS Session Token</param>
            <param name="clientConfig">The AmazonSimpleNotificationServiceClient Configuration Object</param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.CreateSigner">
            <summary>
            Creates the signer for the service.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ServiceMetadata">
            <summary>
            Capture metadata for the service.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.Dispose(System.Boolean)">
            <summary>
            Disposes the service client.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.AddPermissionAsync(System.String,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            Adds a statement to a topic's access control policy, granting access for the specified
            AWS accounts to the specified actions.
            </summary>
            <param name="topicArn">The ARN of the topic whose access control policy you wish to modify.</param>
            <param name="label">A unique identifier for the new policy statement.</param>
            <param name="awsAccountId">The AWS account IDs of the users (principals) who will be given access to the specified actions. The users must have AWS accounts, but do not need to be signed up for this service.</param>
            <param name="actionName">The action you want to allow for the specified principal(s). Valid values: any Amazon SNS action name.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the AddPermission service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/AddPermission">REST API Reference for AddPermission Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.AddPermissionAsync(Amazon.SimpleNotificationService.Model.AddPermissionRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the AddPermission operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the AddPermission operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/AddPermission">REST API Reference for AddPermission Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.CheckIfPhoneNumberIsOptedOutAsync(Amazon.SimpleNotificationService.Model.CheckIfPhoneNumberIsOptedOutRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the CheckIfPhoneNumberIsOptedOut operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the CheckIfPhoneNumberIsOptedOut operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/CheckIfPhoneNumberIsOptedOut">REST API Reference for CheckIfPhoneNumberIsOptedOut Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ConfirmSubscriptionAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Verifies an endpoint owner's intent to receive messages by validating the token sent
            to the endpoint by an earlier <code>Subscribe</code> action. If the token is valid,
            the action creates a new subscription and returns its Amazon Resource Name (ARN).
            This call requires an AWS signature only when the <code>AuthenticateOnUnsubscribe</code>
            flag is set to "true".
            </summary>
            <param name="topicArn">The ARN of the topic for which you wish to confirm a subscription.</param>
            <param name="token">Short-lived token sent to an endpoint during the <code>Subscribe</code> action.</param>
            <param name="authenticateOnUnsubscribe">Disallows unauthenticated unsubscribes of the subscription. If the value of this parameter is <code>true</code> and the request has an AWS signature, then only the topic owner and the subscription owner can unsubscribe the endpoint. The unsubscribe action requires AWS authentication. </param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ConfirmSubscription service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.SubscriptionLimitExceededException">
            Indicates that the customer already owns the maximum allowed number of subscriptions.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ConfirmSubscription">REST API Reference for ConfirmSubscription Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ConfirmSubscriptionAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Verifies an endpoint owner's intent to receive messages by validating the token sent
            to the endpoint by an earlier <code>Subscribe</code> action. If the token is valid,
            the action creates a new subscription and returns its Amazon Resource Name (ARN).
            This call requires an AWS signature only when the <code>AuthenticateOnUnsubscribe</code>
            flag is set to "true".
            </summary>
            <param name="topicArn">The ARN of the topic for which you wish to confirm a subscription.</param>
            <param name="token">Short-lived token sent to an endpoint during the <code>Subscribe</code> action.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ConfirmSubscription service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.SubscriptionLimitExceededException">
            Indicates that the customer already owns the maximum allowed number of subscriptions.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ConfirmSubscription">REST API Reference for ConfirmSubscription Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ConfirmSubscriptionAsync(Amazon.SimpleNotificationService.Model.ConfirmSubscriptionRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ConfirmSubscription operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ConfirmSubscription operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ConfirmSubscription">REST API Reference for ConfirmSubscription Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.CreatePlatformApplicationAsync(Amazon.SimpleNotificationService.Model.CreatePlatformApplicationRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the CreatePlatformApplication operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the CreatePlatformApplication operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/CreatePlatformApplication">REST API Reference for CreatePlatformApplication Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.CreatePlatformEndpointAsync(Amazon.SimpleNotificationService.Model.CreatePlatformEndpointRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the CreatePlatformEndpoint operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the CreatePlatformEndpoint operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/CreatePlatformEndpoint">REST API Reference for CreatePlatformEndpoint Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.CreateTopicAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Creates a topic to which notifications can be published. Users can create at most
            100,000 topics. For more information, see <a href="http://aws.amazon.com/sns/">http://aws.amazon.com/sns</a>.
            This action is idempotent, so if the requester already owns a topic with the specified
            name, that topic's ARN is returned without creating a new topic.
            </summary>
            <param name="name">The name of the topic you want to create. Constraints: Topic names must be made up of only uppercase and lowercase ASCII letters, numbers, underscores, and hyphens, and must be between 1 and 256 characters long.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the CreateTopic service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.TopicLimitExceededException">
            Indicates that the customer already owns the maximum allowed number of topics.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/CreateTopic">REST API Reference for CreateTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.CreateTopicAsync(Amazon.SimpleNotificationService.Model.CreateTopicRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the CreateTopic operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the CreateTopic operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/CreateTopic">REST API Reference for CreateTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.DeleteEndpointAsync(Amazon.SimpleNotificationService.Model.DeleteEndpointRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the DeleteEndpoint operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the DeleteEndpoint operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/DeleteEndpoint">REST API Reference for DeleteEndpoint Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.DeletePlatformApplicationAsync(Amazon.SimpleNotificationService.Model.DeletePlatformApplicationRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the DeletePlatformApplication operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the DeletePlatformApplication operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/DeletePlatformApplication">REST API Reference for DeletePlatformApplication Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.DeleteTopicAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes a topic and all its subscriptions. Deleting a topic might prevent some messages
            previously sent to the topic from being delivered to subscribers. This action is idempotent,
            so deleting a topic that does not exist does not result in an error.
            </summary>
            <param name="topicArn">The ARN of the topic you want to delete.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the DeleteTopic service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/DeleteTopic">REST API Reference for DeleteTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.DeleteTopicAsync(Amazon.SimpleNotificationService.Model.DeleteTopicRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the DeleteTopic operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the DeleteTopic operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/DeleteTopic">REST API Reference for DeleteTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.GetEndpointAttributesAsync(Amazon.SimpleNotificationService.Model.GetEndpointAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the GetEndpointAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the GetEndpointAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetEndpointAttributes">REST API Reference for GetEndpointAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.GetPlatformApplicationAttributesAsync(Amazon.SimpleNotificationService.Model.GetPlatformApplicationAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the GetPlatformApplicationAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the GetPlatformApplicationAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetPlatformApplicationAttributes">REST API Reference for GetPlatformApplicationAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.GetSMSAttributesAsync(Amazon.SimpleNotificationService.Model.GetSMSAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the GetSMSAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the GetSMSAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetSMSAttributes">REST API Reference for GetSMSAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.GetSubscriptionAttributesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns all of the properties of a subscription.
            </summary>
            <param name="subscriptionArn">The ARN of the subscription whose properties you want to get.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the GetSubscriptionAttributes service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetSubscriptionAttributes">REST API Reference for GetSubscriptionAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.GetSubscriptionAttributesAsync(Amazon.SimpleNotificationService.Model.GetSubscriptionAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the GetSubscriptionAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the GetSubscriptionAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetSubscriptionAttributes">REST API Reference for GetSubscriptionAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.GetTopicAttributesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns all of the properties of a topic. Topic properties returned might differ based
            on the authorization of the user.
            </summary>
            <param name="topicArn">The ARN of the topic whose properties you want to get.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the GetTopicAttributes service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetTopicAttributes">REST API Reference for GetTopicAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.GetTopicAttributesAsync(Amazon.SimpleNotificationService.Model.GetTopicAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the GetTopicAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the GetTopicAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetTopicAttributes">REST API Reference for GetTopicAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListEndpointsByPlatformApplicationAsync(Amazon.SimpleNotificationService.Model.ListEndpointsByPlatformApplicationRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ListEndpointsByPlatformApplication operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ListEndpointsByPlatformApplication operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListEndpointsByPlatformApplication">REST API Reference for ListEndpointsByPlatformApplication Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListPhoneNumbersOptedOutAsync(Amazon.SimpleNotificationService.Model.ListPhoneNumbersOptedOutRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ListPhoneNumbersOptedOut operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ListPhoneNumbersOptedOut operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListPhoneNumbersOptedOut">REST API Reference for ListPhoneNumbersOptedOut Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListPlatformApplicationsAsync(System.Threading.CancellationToken)">
            <summary>
            Lists the platform application objects for the supported push notification services,
            such as APNS and GCM. The results for <code>ListPlatformApplications</code> are paginated
            and return a limited list of applications, up to 100. If additional records are available
            after the first page results, then a NextToken string will be returned. To receive
            the next page, you call <code>ListPlatformApplications</code> using the NextToken
            string received from the previous call. When there are no more records to return,
            NextToken will be null. For more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePush.html">Using
            Amazon SNS Mobile Push Notifications</a>. 
            
             
            <para>
            This action is throttled at 15 transactions per second (TPS).
            </para>
            </summary>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListPlatformApplications service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListPlatformApplications">REST API Reference for ListPlatformApplications Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListPlatformApplicationsAsync(Amazon.SimpleNotificationService.Model.ListPlatformApplicationsRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ListPlatformApplications operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ListPlatformApplications operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListPlatformApplications">REST API Reference for ListPlatformApplications Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListSubscriptionsAsync(System.Threading.CancellationToken)">
            <summary>
            Returns a list of the requester's subscriptions. Each call returns a limited list
            of subscriptions, up to 100. If there are more subscriptions, a <code>NextToken</code>
            is also returned. Use the <code>NextToken</code> parameter in a new <code>ListSubscriptions</code>
            call to get further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListSubscriptions service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListSubscriptions">REST API Reference for ListSubscriptions Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListSubscriptionsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns a list of the requester's subscriptions. Each call returns a limited list
            of subscriptions, up to 100. If there are more subscriptions, a <code>NextToken</code>
            is also returned. Use the <code>NextToken</code> parameter in a new <code>ListSubscriptions</code>
            call to get further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
            <param name="nextToken">Token returned by the previous <code>ListSubscriptions</code> request.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListSubscriptions service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListSubscriptions">REST API Reference for ListSubscriptions Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListSubscriptionsAsync(Amazon.SimpleNotificationService.Model.ListSubscriptionsRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ListSubscriptions operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ListSubscriptions operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListSubscriptions">REST API Reference for ListSubscriptions Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListSubscriptionsByTopicAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Returns a list of the subscriptions to a specific topic. Each call returns a limited
            list of subscriptions, up to 100. If there are more subscriptions, a <code>NextToken</code>
            is also returned. Use the <code>NextToken</code> parameter in a new <code>ListSubscriptionsByTopic</code>
            call to get further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
            <param name="topicArn">The ARN of the topic for which you wish to find subscriptions.</param>
            <param name="nextToken">Token returned by the previous <code>ListSubscriptionsByTopic</code> request.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListSubscriptionsByTopic service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListSubscriptionsByTopic">REST API Reference for ListSubscriptionsByTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListSubscriptionsByTopicAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns a list of the subscriptions to a specific topic. Each call returns a limited
            list of subscriptions, up to 100. If there are more subscriptions, a <code>NextToken</code>
            is also returned. Use the <code>NextToken</code> parameter in a new <code>ListSubscriptionsByTopic</code>
            call to get further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
            <param name="topicArn">The ARN of the topic for which you wish to find subscriptions.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListSubscriptionsByTopic service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListSubscriptionsByTopic">REST API Reference for ListSubscriptionsByTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListSubscriptionsByTopicAsync(Amazon.SimpleNotificationService.Model.ListSubscriptionsByTopicRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ListSubscriptionsByTopic operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ListSubscriptionsByTopic operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListSubscriptionsByTopic">REST API Reference for ListSubscriptionsByTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListTopicsAsync(System.Threading.CancellationToken)">
            <summary>
            Returns a list of the requester's topics. Each call returns a limited list of topics,
            up to 100. If there are more topics, a <code>NextToken</code> is also returned. Use
            the <code>NextToken</code> parameter in a new <code>ListTopics</code> call to get
            further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListTopics service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListTopics">REST API Reference for ListTopics Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListTopicsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns a list of the requester's topics. Each call returns a limited list of topics,
            up to 100. If there are more topics, a <code>NextToken</code> is also returned. Use
            the <code>NextToken</code> parameter in a new <code>ListTopics</code> call to get
            further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
            <param name="nextToken">Token returned by the previous <code>ListTopics</code> request.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListTopics service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListTopics">REST API Reference for ListTopics Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.ListTopicsAsync(Amazon.SimpleNotificationService.Model.ListTopicsRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ListTopics operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ListTopics operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListTopics">REST API Reference for ListTopics Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.OptInPhoneNumberAsync(Amazon.SimpleNotificationService.Model.OptInPhoneNumberRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the OptInPhoneNumber operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the OptInPhoneNumber operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/OptInPhoneNumber">REST API Reference for OptInPhoneNumber Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.PublishAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Sends a message to an Amazon SNS topic or sends a text message (SMS message) directly
            to a phone number. 
            
             
            <para>
            If you send a message to a topic, Amazon SNS delivers the message to each endpoint
            that is subscribed to the topic. The format of the message depends on the notification
            protocol for each subscribed endpoint.
            </para>
             
            <para>
            When a <code>messageId</code> is returned, the message has been saved and Amazon SNS
            will attempt to deliver it shortly.
            </para>
             
            <para>
            To use the <code>Publish</code> action for sending a message to a mobile endpoint,
            such as an app on a Kindle device or mobile phone, you must specify the EndpointArn
            for the TargetArn parameter. The EndpointArn is returned when making a call with the
            <code>CreatePlatformEndpoint</code> action. 
            </para>
             
            <para>
            For more information about formatting messages, see <a href="http://docs.aws.amazon.com/sns/latest/dg/mobile-push-send-custommessage.html">Send
            Custom Platform-Specific Payloads in Messages to Mobile Devices</a>. 
            </para>
            </summary>
            <param name="topicArn">The topic you want to publish to. If you don't specify a value for the <code>TopicArn</code> parameter, you must specify a value for the <code>PhoneNumber</code> or <code>TargetArn</code> parameters.</param>
            <param name="message">The message you want to send. <important> The <code>Message</code> parameter is always a string. If you set <code>MessageStructure</code> to <code>json</code>, you must string-encode the <code>Message</code> parameter. </important> If you are publishing to a topic and you want to send the same message to all transport protocols, include the text of the message as a String value. If you want to send different messages for each transport protocol, set the value of the <code>MessageStructure</code> parameter to <code>json</code> and use a JSON object for the <code>Message</code> parameter.  <p/> Constraints: <ul> <li> With the exception of SMS, messages must be UTF-8 encoded strings and at most 256 KB in size (262,144 bytes, not 262,144 characters). </li> <li> For SMS, each message can contain up to 140 characters. This character limit depends on the encoding schema. For example, an SMS message can contain 160 GSM characters, 140 ASCII characters, or 70 UCS-2 characters. If you publish a message that exceeds this size limit, Amazon SNS sends the message as multiple messages, each fitting within the size limit. Messages aren't truncated mid-word but are cut off at whole-word boundaries. The total size limit for a single SMS <code>Publish</code> action is 1,600 characters. </li> </ul> JSON-specific constraints: <ul> <li> Keys in the JSON object that correspond to supported transport protocols must have simple JSON string values. </li> <li> The values will be parsed (unescaped) before they are used in outgoing messages. </li> <li> Outbound notifications are JSON encoded (meaning that the characters will be reescaped for sending). </li> <li> Values have a minimum length of 0 (the empty string, "", is allowed). </li> <li> Values have a maximum length bounded by the overall message size (so, including multiple protocols may limit message sizes). </li> <li> Non-string values will cause the key to be ignored. </li> <li> Keys that do not correspond to supported transport protocols are ignored. </li> <li> Duplicate keys are not allowed. </li> <li> Failure to parse or validate any key or value in the message will cause the <code>Publish</code> call to return an error (no partial delivery). </li> </ul></param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the Publish service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.EndpointDisabledException">
            Exception error indicating endpoint disabled.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterValueException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSAccessDeniedException">
            The ciphertext references a key that doesn't exist or that you don't have access to.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSDisabledException">
            The request was rejected because the specified customer master key (CMK) isn't enabled.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSInvalidStateException">
            The request was rejected because the state of the specified resource isn't valid for
            this request. For more information, see <a href="http://docs.aws.amazon.com/kms/latest/developerguide/key-state.html">How
            Key State Affects Use of a Customer Master Key</a> in the <i>AWS Key Management Service
            Developer Guide</i>.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSNotFoundException">
            The request was rejected because the specified entity or resource can't be found.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSOptInRequiredException">
            The AWS access key ID needs a subscription for the service.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSThrottlingException">
            The request was denied due to request throttling. For more information about throttling,
            see <a href="http://docs.aws.amazon.com/kms/latest/developerguide/limits.html#requests-per-second">Limits</a>
            in the <i>AWS Key Management Service Developer Guide.</i>
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.PlatformApplicationDisabledException">
            Exception error indicating platform application disabled.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Publish">REST API Reference for Publish Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.PublishAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Sends a message to an Amazon SNS topic or sends a text message (SMS message) directly
            to a phone number. 
            
             
            <para>
            If you send a message to a topic, Amazon SNS delivers the message to each endpoint
            that is subscribed to the topic. The format of the message depends on the notification
            protocol for each subscribed endpoint.
            </para>
             
            <para>
            When a <code>messageId</code> is returned, the message has been saved and Amazon SNS
            will attempt to deliver it shortly.
            </para>
             
            <para>
            To use the <code>Publish</code> action for sending a message to a mobile endpoint,
            such as an app on a Kindle device or mobile phone, you must specify the EndpointArn
            for the TargetArn parameter. The EndpointArn is returned when making a call with the
            <code>CreatePlatformEndpoint</code> action. 
            </para>
             
            <para>
            For more information about formatting messages, see <a href="http://docs.aws.amazon.com/sns/latest/dg/mobile-push-send-custommessage.html">Send
            Custom Platform-Specific Payloads in Messages to Mobile Devices</a>. 
            </para>
            </summary>
            <param name="topicArn">The topic you want to publish to. If you don't specify a value for the <code>TopicArn</code> parameter, you must specify a value for the <code>PhoneNumber</code> or <code>TargetArn</code> parameters.</param>
            <param name="message">The message you want to send. <important> The <code>Message</code> parameter is always a string. If you set <code>MessageStructure</code> to <code>json</code>, you must string-encode the <code>Message</code> parameter. </important> If you are publishing to a topic and you want to send the same message to all transport protocols, include the text of the message as a String value. If you want to send different messages for each transport protocol, set the value of the <code>MessageStructure</code> parameter to <code>json</code> and use a JSON object for the <code>Message</code> parameter.  <p/> Constraints: <ul> <li> With the exception of SMS, messages must be UTF-8 encoded strings and at most 256 KB in size (262,144 bytes, not 262,144 characters). </li> <li> For SMS, each message can contain up to 140 characters. This character limit depends on the encoding schema. For example, an SMS message can contain 160 GSM characters, 140 ASCII characters, or 70 UCS-2 characters. If you publish a message that exceeds this size limit, Amazon SNS sends the message as multiple messages, each fitting within the size limit. Messages aren't truncated mid-word but are cut off at whole-word boundaries. The total size limit for a single SMS <code>Publish</code> action is 1,600 characters. </li> </ul> JSON-specific constraints: <ul> <li> Keys in the JSON object that correspond to supported transport protocols must have simple JSON string values. </li> <li> The values will be parsed (unescaped) before they are used in outgoing messages. </li> <li> Outbound notifications are JSON encoded (meaning that the characters will be reescaped for sending). </li> <li> Values have a minimum length of 0 (the empty string, "", is allowed). </li> <li> Values have a maximum length bounded by the overall message size (so, including multiple protocols may limit message sizes). </li> <li> Non-string values will cause the key to be ignored. </li> <li> Keys that do not correspond to supported transport protocols are ignored. </li> <li> Duplicate keys are not allowed. </li> <li> Failure to parse or validate any key or value in the message will cause the <code>Publish</code> call to return an error (no partial delivery). </li> </ul></param>
            <param name="subject">Optional parameter to be used as the "Subject" line when the message is delivered to email endpoints. This field will also be included, if present, in the standard JSON messages delivered to other endpoints. Constraints: Subjects must be ASCII text that begins with a letter, number, or punctuation mark; must not include line breaks or control characters; and must be less than 100 characters long.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the Publish service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.EndpointDisabledException">
            Exception error indicating endpoint disabled.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterValueException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSAccessDeniedException">
            The ciphertext references a key that doesn't exist or that you don't have access to.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSDisabledException">
            The request was rejected because the specified customer master key (CMK) isn't enabled.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSInvalidStateException">
            The request was rejected because the state of the specified resource isn't valid for
            this request. For more information, see <a href="http://docs.aws.amazon.com/kms/latest/developerguide/key-state.html">How
            Key State Affects Use of a Customer Master Key</a> in the <i>AWS Key Management Service
            Developer Guide</i>.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSNotFoundException">
            The request was rejected because the specified entity or resource can't be found.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSOptInRequiredException">
            The AWS access key ID needs a subscription for the service.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSThrottlingException">
            The request was denied due to request throttling. For more information about throttling,
            see <a href="http://docs.aws.amazon.com/kms/latest/developerguide/limits.html#requests-per-second">Limits</a>
            in the <i>AWS Key Management Service Developer Guide.</i>
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.PlatformApplicationDisabledException">
            Exception error indicating platform application disabled.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Publish">REST API Reference for Publish Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.PublishAsync(Amazon.SimpleNotificationService.Model.PublishRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the Publish operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the Publish operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Publish">REST API Reference for Publish Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.RemovePermissionAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Removes a statement from a topic's access control policy.
            </summary>
            <param name="topicArn">The ARN of the topic whose access control policy you wish to modify.</param>
            <param name="label">The unique label of the statement you want to remove.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the RemovePermission service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/RemovePermission">REST API Reference for RemovePermission Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.RemovePermissionAsync(Amazon.SimpleNotificationService.Model.RemovePermissionRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the RemovePermission operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the RemovePermission operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/RemovePermission">REST API Reference for RemovePermission Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.SetEndpointAttributesAsync(Amazon.SimpleNotificationService.Model.SetEndpointAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the SetEndpointAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the SetEndpointAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetEndpointAttributes">REST API Reference for SetEndpointAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.SetPlatformApplicationAttributesAsync(Amazon.SimpleNotificationService.Model.SetPlatformApplicationAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the SetPlatformApplicationAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the SetPlatformApplicationAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetPlatformApplicationAttributes">REST API Reference for SetPlatformApplicationAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.SetSMSAttributesAsync(Amazon.SimpleNotificationService.Model.SetSMSAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the SetSMSAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the SetSMSAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetSMSAttributes">REST API Reference for SetSMSAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.SetSubscriptionAttributesAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Allows a subscription owner to set an attribute of the subscription to a new value.
            </summary>
            <param name="subscriptionArn">The ARN of the subscription to modify.</param>
            <param name="attributeName">A map of attributes with their corresponding values. The following lists the names, descriptions, and values of the special request parameters that the <code>SetTopicAttributes</code> action uses: <ul> <li>  <code>DeliveryPolicy</code> – The policy that defines how Amazon SNS retries failed deliveries to HTTP/S endpoints. </li> <li>  <code>FilterPolicy</code> – The simple JSON object that lets your subscriber receive only a subset of messages, rather than receiving every message published to the topic. </li> <li>  <code>RawMessageDelivery</code> – When set to <code>true</code>, enables raw message delivery to Amazon SQS or HTTP/S endpoints. This eliminates the need for the endpoints to process JSON formatting, which is otherwise created for Amazon SNS metadata. </li> </ul></param>
            <param name="attributeValue">The new value for the attribute in JSON format.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the SetSubscriptionAttributes service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.FilterPolicyLimitExceededException">
            Indicates that the number of filter polices in your AWS account exceeds the limit.
            To add more filter polices, submit an SNS Limit Increase case in the AWS Support Center.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetSubscriptionAttributes">REST API Reference for SetSubscriptionAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.SetSubscriptionAttributesAsync(Amazon.SimpleNotificationService.Model.SetSubscriptionAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the SetSubscriptionAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the SetSubscriptionAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetSubscriptionAttributes">REST API Reference for SetSubscriptionAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.SetTopicAttributesAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Allows a topic owner to set an attribute of the topic to a new value.
            </summary>
            <param name="topicArn">The ARN of the topic to modify.</param>
            <param name="attributeName">A map of attributes with their corresponding values. The following lists the names, descriptions, and values of the special request parameters that the <code>SetTopicAttributes</code> action uses: <ul> <li>  <code>DeliveryPolicy</code> – The policy that defines how Amazon SNS retries failed deliveries to HTTP/S endpoints. </li> <li>  <code>DisplayName</code> – The display name to use for a topic with SMS subscriptions. </li> <li>  <code>Policy</code> – The policy that defines who can access your topic. By default, only the topic owner can publish or subscribe to the topic. </li> </ul></param>
            <param name="attributeValue">The new value for the attribute.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the SetTopicAttributes service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetTopicAttributes">REST API Reference for SetTopicAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.SetTopicAttributesAsync(Amazon.SimpleNotificationService.Model.SetTopicAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the SetTopicAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the SetTopicAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetTopicAttributes">REST API Reference for SetTopicAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.SubscribeAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Prepares to subscribe an endpoint by sending the endpoint a confirmation message.
            To actually create a subscription, the endpoint owner must call the <code>ConfirmSubscription</code>
            action with the token from the confirmation message. Confirmation tokens are valid
            for three days.
            
             
            <para>
            This action is throttled at 100 transactions per second (TPS).
            </para>
            </summary>
            <param name="topicArn">The ARN of the topic you want to subscribe to.</param>
            <param name="protocol">The protocol you want to use. Supported protocols include: <ul> <li>  <code>http</code> – delivery of JSON-encoded message via HTTP POST </li> <li>  <code>https</code> – delivery of JSON-encoded message via HTTPS POST </li> <li>  <code>email</code> – delivery of message via SMTP </li> <li>  <code>email-json</code> – delivery of JSON-encoded message via SMTP </li> <li>  <code>sms</code> – delivery of message via SMS </li> <li>  <code>sqs</code> – delivery of JSON-encoded message to an Amazon SQS queue </li> <li>  <code>application</code> – delivery of JSON-encoded message to an EndpointArn for a mobile app and device. </li> <li>  <code>lambda</code> – delivery of JSON-encoded message to an AWS Lambda function. </li> </ul></param>
            <param name="endpoint">The endpoint that you want to receive notifications. Endpoints vary by protocol: <ul> <li> For the <code>http</code> protocol, the endpoint is an URL beginning with "http://" </li> <li> For the <code>https</code> protocol, the endpoint is a URL beginning with "https://" </li> <li> For the <code>email</code> protocol, the endpoint is an email address </li> <li> For the <code>email-json</code> protocol, the endpoint is an email address </li> <li> For the <code>sms</code> protocol, the endpoint is a phone number of an SMS-enabled device </li> <li> For the <code>sqs</code> protocol, the endpoint is the ARN of an Amazon SQS queue </li> <li> For the <code>application</code> protocol, the endpoint is the EndpointArn of a mobile app and device. </li> <li> For the <code>lambda</code> protocol, the endpoint is the ARN of an AWS Lambda function. </li> </ul></param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the Subscribe service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.FilterPolicyLimitExceededException">
            Indicates that the number of filter polices in your AWS account exceeds the limit.
            To add more filter polices, submit an SNS Limit Increase case in the AWS Support Center.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.SubscriptionLimitExceededException">
            Indicates that the customer already owns the maximum allowed number of subscriptions.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Subscribe">REST API Reference for Subscribe Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.SubscribeAsync(Amazon.SimpleNotificationService.Model.SubscribeRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the Subscribe operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the Subscribe operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Subscribe">REST API Reference for Subscribe Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.UnsubscribeAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes a subscription. If the subscription requires authentication for deletion,
            only the owner of the subscription or the topic's owner can unsubscribe, and an AWS
            signature is required. If the <code>Unsubscribe</code> call does not require authentication
            and the requester is not the subscription owner, a final cancellation message is delivered
            to the endpoint, so that the endpoint owner can easily resubscribe to the topic if
            the <code>Unsubscribe</code> request was unintended.
            
             
            <para>
            This action is throttled at 100 transactions per second (TPS).
            </para>
            </summary>
            <param name="subscriptionArn">The ARN of the subscription to be deleted.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the Unsubscribe service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Unsubscribe">REST API Reference for Unsubscribe Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceClient.UnsubscribeAsync(Amazon.SimpleNotificationService.Model.UnsubscribeRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the Unsubscribe operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the Unsubscribe operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Unsubscribe">REST API Reference for Unsubscribe Operation</seealso>
        </member>
        <member name="T:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService">
             <summary>
             Interface for accessing SimpleNotificationService
            
             Amazon Simple Notification Service 
             <para>
             Amazon Simple Notification Service (Amazon SNS) is a web service that enables you
             to build distributed web-enabled applications. Applications can use Amazon SNS to
             easily push real-time notification messages to interested subscribers over multiple
             delivery protocols. For more information about this product see <a href="http://aws.amazon.com/sns/">http://aws.amazon.com/sns</a>.
             For detailed information about Amazon SNS features and their associated API calls,
             see the <a href="http://docs.aws.amazon.com/sns/latest/dg/">Amazon SNS Developer Guide</a>.
             
             </para>
              
             <para>
             We also provide SDKs that enable you to access Amazon SNS from your preferred programming
             language. The SDKs contain functionality that automatically takes care of tasks such
             as: cryptographically signing your service requests, retrying requests, and handling
             error responses. For a list of available SDKs, go to <a href="http://aws.amazon.com/tools/">Tools
             for Amazon Web Services</a>. 
             </para>
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.SubscribeQueueAsync(System.String,Amazon.Runtime.SharedInterfaces.ICoreAmazonSQS,System.String)">
            <summary>
            Subscribes an existing Amazon SQS queue to an existing Amazon SNS topic asynchronously.
            <para>
            The policy applied to the SQS queue is similar to this:
            <code>
            {
            	"Version" : "2008-10-17",
            	"Statement" : [{
            	    "Sid" : "topic-subscription-arn:aws:sns:us-west-2:599109622955:myTopic",
            		"Effect" : "Allow",
            		"Principal" : {
            			"AWS":["*"]
            		},
            		"Action" : ["sqs:SendMessage"],
            		"Resource":["arn:aws:sqs:us-west-2:599109622955:myQueue"],
            		"Condition" : {
            			"ArnLike":{
            				"aws:SourceArn":["arn:aws:sns:us-west-2:599109622955:myTopic"]
            			}
            		}
                }]
            }
            </code>
            </para>
            <para>
            There might be a small time period immediately after
            subscribing the SQS queue to the SNS topic and updating the SQS queue's
            policy, where messages are not able to be delivered to the queue. After a
            moment, the new queue policy will propagate and the queue will be able to
            receive messages. This delay only occurs immediately after initially
            subscribing the queue.
            </para>
            </summary>
            <param name="topicArn">The topic to subscribe to</param>
            <param name="sqsClient">The SQS client used to get attributes and set the policy on the SQS queue.</param>
            <param name="sqsQueueUrl">The queue to add a subscription to.</param>
            <returns>A Task containing the subscription ARN as returned by Amazon SNS when the queue is 
            successfully subscribed to the topic.</returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.SubscribeQueueToTopicsAsync(System.Collections.Generic.IList{System.String},Amazon.Runtime.SharedInterfaces.ICoreAmazonSQS,System.String)">
            <summary>
            Subscribes an existing Amazon SQS queue to existing Amazon SNS topics asynchronously.
            <para>
            The policy applied to the SQS queue is similar to this:
            <code>
            {
            	"Version" : "2008-10-17",
            	"Statement" : [{
            	    "Sid" : "topic-subscription-arn:aws:sns:us-west-2:599109622955:myTopic",
            		"Effect" : "Allow",
            		"Principal" : "*",
            		"Action" : ["sqs:SendMessage"],
            		"Resource":["arn:aws:sqs:us-west-2:599109622955:myQueue"],
            		"Condition" : {
            			"ArnLike":{
            				"aws:SourceArn":["arn:aws:sns:us-west-2:599109622955:myTopic"]
            			}
            		}
                }]
            }
            </code>
            </para>
            <para>
            There might be a small time period immediately after
            subscribing the SQS queue to the SNS topic and updating the SQS queue's
            policy, where messages are not able to be delivered to the queue. After a
            moment, the new queue policy will propagate and the queue will be able to
            receive messages. This delay only occurs immediately after initially
            subscribing the queue.
            </para>
            </summary>
            <param name="topicArns">The topics to subscribe to</param>
            <param name="sqsClient">The SQS client used to get attributes and set the policy on the SQS queue.</param>
            <param name="sqsQueueUrl">The queue to add a subscription to.</param>
            <returns>A Task containing the mapping of topic ARNs to subscription ARNs as returned by Amazon SNS wrapped when the queue is 
            successfully subscribed to each topic.</returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.FindTopicAsync(System.String)">
            <summary>
            Finds an existing Amazon SNS topic by iterating all SNS topics until a match is found asynchronously.
            <para>
            The ListTopics method is used to fetch upto 100 SNS topics at a time until a SNS topic 
            with an TopicArn that matches <paramref name="topicName"/> is found.
            </para>
            </summary>
            <param name="topicName">The name of the topic find</param>
            <returns>A Task containing the matched SNS topic.</returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.AuthorizeS3ToPublishAsync(System.String,System.String)">
            <summary>
            This is a utility method which updates the policy of a topic to allow the
            S3 bucket to publish events to it.
            </summary>
            <param name="topicArn">The topic that will have its policy updated.</param>
            <param name="bucket">The bucket that will be given access to publish from.</param>
            /// <returns>A Task</returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.AddPermissionAsync(System.String,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            Adds a statement to a topic's access control policy, granting access for the specified
            AWS accounts to the specified actions.
            </summary>
            <param name="topicArn">The ARN of the topic whose access control policy you wish to modify.</param>
            <param name="label">A unique identifier for the new policy statement.</param>
            <param name="awsAccountId">The AWS account IDs of the users (principals) who will be given access to the specified actions. The users must have AWS accounts, but do not need to be signed up for this service.</param>
            <param name="actionName">The action you want to allow for the specified principal(s). Valid values: any Amazon SNS action name.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the AddPermission service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/AddPermission">REST API Reference for AddPermission Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.AddPermissionAsync(Amazon.SimpleNotificationService.Model.AddPermissionRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the AddPermission operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the AddPermission operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/AddPermission">REST API Reference for AddPermission Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.CheckIfPhoneNumberIsOptedOutAsync(Amazon.SimpleNotificationService.Model.CheckIfPhoneNumberIsOptedOutRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the CheckIfPhoneNumberIsOptedOut operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the CheckIfPhoneNumberIsOptedOut operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/CheckIfPhoneNumberIsOptedOut">REST API Reference for CheckIfPhoneNumberIsOptedOut Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ConfirmSubscriptionAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Verifies an endpoint owner's intent to receive messages by validating the token sent
            to the endpoint by an earlier <code>Subscribe</code> action. If the token is valid,
            the action creates a new subscription and returns its Amazon Resource Name (ARN).
            This call requires an AWS signature only when the <code>AuthenticateOnUnsubscribe</code>
            flag is set to "true".
            </summary>
            <param name="topicArn">The ARN of the topic for which you wish to confirm a subscription.</param>
            <param name="token">Short-lived token sent to an endpoint during the <code>Subscribe</code> action.</param>
            <param name="authenticateOnUnsubscribe">Disallows unauthenticated unsubscribes of the subscription. If the value of this parameter is <code>true</code> and the request has an AWS signature, then only the topic owner and the subscription owner can unsubscribe the endpoint. The unsubscribe action requires AWS authentication. </param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ConfirmSubscription service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.SubscriptionLimitExceededException">
            Indicates that the customer already owns the maximum allowed number of subscriptions.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ConfirmSubscription">REST API Reference for ConfirmSubscription Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ConfirmSubscriptionAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Verifies an endpoint owner's intent to receive messages by validating the token sent
            to the endpoint by an earlier <code>Subscribe</code> action. If the token is valid,
            the action creates a new subscription and returns its Amazon Resource Name (ARN).
            This call requires an AWS signature only when the <code>AuthenticateOnUnsubscribe</code>
            flag is set to "true".
            </summary>
            <param name="topicArn">The ARN of the topic for which you wish to confirm a subscription.</param>
            <param name="token">Short-lived token sent to an endpoint during the <code>Subscribe</code> action.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ConfirmSubscription service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.SubscriptionLimitExceededException">
            Indicates that the customer already owns the maximum allowed number of subscriptions.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ConfirmSubscription">REST API Reference for ConfirmSubscription Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ConfirmSubscriptionAsync(Amazon.SimpleNotificationService.Model.ConfirmSubscriptionRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ConfirmSubscription operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ConfirmSubscription operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ConfirmSubscription">REST API Reference for ConfirmSubscription Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.CreatePlatformApplicationAsync(Amazon.SimpleNotificationService.Model.CreatePlatformApplicationRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the CreatePlatformApplication operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the CreatePlatformApplication operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/CreatePlatformApplication">REST API Reference for CreatePlatformApplication Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.CreatePlatformEndpointAsync(Amazon.SimpleNotificationService.Model.CreatePlatformEndpointRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the CreatePlatformEndpoint operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the CreatePlatformEndpoint operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/CreatePlatformEndpoint">REST API Reference for CreatePlatformEndpoint Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.CreateTopicAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Creates a topic to which notifications can be published. Users can create at most
            100,000 topics. For more information, see <a href="http://aws.amazon.com/sns/">http://aws.amazon.com/sns</a>.
            This action is idempotent, so if the requester already owns a topic with the specified
            name, that topic's ARN is returned without creating a new topic.
            </summary>
            <param name="name">The name of the topic you want to create. Constraints: Topic names must be made up of only uppercase and lowercase ASCII letters, numbers, underscores, and hyphens, and must be between 1 and 256 characters long.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the CreateTopic service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.TopicLimitExceededException">
            Indicates that the customer already owns the maximum allowed number of topics.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/CreateTopic">REST API Reference for CreateTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.CreateTopicAsync(Amazon.SimpleNotificationService.Model.CreateTopicRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the CreateTopic operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the CreateTopic operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/CreateTopic">REST API Reference for CreateTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.DeleteEndpointAsync(Amazon.SimpleNotificationService.Model.DeleteEndpointRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the DeleteEndpoint operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the DeleteEndpoint operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/DeleteEndpoint">REST API Reference for DeleteEndpoint Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.DeletePlatformApplicationAsync(Amazon.SimpleNotificationService.Model.DeletePlatformApplicationRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the DeletePlatformApplication operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the DeletePlatformApplication operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/DeletePlatformApplication">REST API Reference for DeletePlatformApplication Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.DeleteTopicAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes a topic and all its subscriptions. Deleting a topic might prevent some messages
            previously sent to the topic from being delivered to subscribers. This action is idempotent,
            so deleting a topic that does not exist does not result in an error.
            </summary>
            <param name="topicArn">The ARN of the topic you want to delete.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the DeleteTopic service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/DeleteTopic">REST API Reference for DeleteTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.DeleteTopicAsync(Amazon.SimpleNotificationService.Model.DeleteTopicRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the DeleteTopic operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the DeleteTopic operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/DeleteTopic">REST API Reference for DeleteTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.GetEndpointAttributesAsync(Amazon.SimpleNotificationService.Model.GetEndpointAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the GetEndpointAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the GetEndpointAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetEndpointAttributes">REST API Reference for GetEndpointAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.GetPlatformApplicationAttributesAsync(Amazon.SimpleNotificationService.Model.GetPlatformApplicationAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the GetPlatformApplicationAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the GetPlatformApplicationAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetPlatformApplicationAttributes">REST API Reference for GetPlatformApplicationAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.GetSMSAttributesAsync(Amazon.SimpleNotificationService.Model.GetSMSAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the GetSMSAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the GetSMSAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetSMSAttributes">REST API Reference for GetSMSAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.GetSubscriptionAttributesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns all of the properties of a subscription.
            </summary>
            <param name="subscriptionArn">The ARN of the subscription whose properties you want to get.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the GetSubscriptionAttributes service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetSubscriptionAttributes">REST API Reference for GetSubscriptionAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.GetSubscriptionAttributesAsync(Amazon.SimpleNotificationService.Model.GetSubscriptionAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the GetSubscriptionAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the GetSubscriptionAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetSubscriptionAttributes">REST API Reference for GetSubscriptionAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.GetTopicAttributesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns all of the properties of a topic. Topic properties returned might differ based
            on the authorization of the user.
            </summary>
            <param name="topicArn">The ARN of the topic whose properties you want to get.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the GetTopicAttributes service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetTopicAttributes">REST API Reference for GetTopicAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.GetTopicAttributesAsync(Amazon.SimpleNotificationService.Model.GetTopicAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the GetTopicAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the GetTopicAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/GetTopicAttributes">REST API Reference for GetTopicAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListEndpointsByPlatformApplicationAsync(Amazon.SimpleNotificationService.Model.ListEndpointsByPlatformApplicationRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ListEndpointsByPlatformApplication operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ListEndpointsByPlatformApplication operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListEndpointsByPlatformApplication">REST API Reference for ListEndpointsByPlatformApplication Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListPhoneNumbersOptedOutAsync(Amazon.SimpleNotificationService.Model.ListPhoneNumbersOptedOutRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ListPhoneNumbersOptedOut operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ListPhoneNumbersOptedOut operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListPhoneNumbersOptedOut">REST API Reference for ListPhoneNumbersOptedOut Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListPlatformApplicationsAsync(System.Threading.CancellationToken)">
            <summary>
            Lists the platform application objects for the supported push notification services,
            such as APNS and GCM. The results for <code>ListPlatformApplications</code> are paginated
            and return a limited list of applications, up to 100. If additional records are available
            after the first page results, then a NextToken string will be returned. To receive
            the next page, you call <code>ListPlatformApplications</code> using the NextToken
            string received from the previous call. When there are no more records to return,
            NextToken will be null. For more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePush.html">Using
            Amazon SNS Mobile Push Notifications</a>. 
            
             
            <para>
            This action is throttled at 15 transactions per second (TPS).
            </para>
            </summary>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListPlatformApplications service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListPlatformApplications">REST API Reference for ListPlatformApplications Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListPlatformApplicationsAsync(Amazon.SimpleNotificationService.Model.ListPlatformApplicationsRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ListPlatformApplications operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ListPlatformApplications operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListPlatformApplications">REST API Reference for ListPlatformApplications Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListSubscriptionsAsync(System.Threading.CancellationToken)">
            <summary>
            Returns a list of the requester's subscriptions. Each call returns a limited list
            of subscriptions, up to 100. If there are more subscriptions, a <code>NextToken</code>
            is also returned. Use the <code>NextToken</code> parameter in a new <code>ListSubscriptions</code>
            call to get further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListSubscriptions service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListSubscriptions">REST API Reference for ListSubscriptions Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListSubscriptionsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns a list of the requester's subscriptions. Each call returns a limited list
            of subscriptions, up to 100. If there are more subscriptions, a <code>NextToken</code>
            is also returned. Use the <code>NextToken</code> parameter in a new <code>ListSubscriptions</code>
            call to get further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
            <param name="nextToken">Token returned by the previous <code>ListSubscriptions</code> request.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListSubscriptions service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListSubscriptions">REST API Reference for ListSubscriptions Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListSubscriptionsAsync(Amazon.SimpleNotificationService.Model.ListSubscriptionsRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ListSubscriptions operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ListSubscriptions operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListSubscriptions">REST API Reference for ListSubscriptions Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListSubscriptionsByTopicAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Returns a list of the subscriptions to a specific topic. Each call returns a limited
            list of subscriptions, up to 100. If there are more subscriptions, a <code>NextToken</code>
            is also returned. Use the <code>NextToken</code> parameter in a new <code>ListSubscriptionsByTopic</code>
            call to get further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
            <param name="topicArn">The ARN of the topic for which you wish to find subscriptions.</param>
            <param name="nextToken">Token returned by the previous <code>ListSubscriptionsByTopic</code> request.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListSubscriptionsByTopic service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListSubscriptionsByTopic">REST API Reference for ListSubscriptionsByTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListSubscriptionsByTopicAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns a list of the subscriptions to a specific topic. Each call returns a limited
            list of subscriptions, up to 100. If there are more subscriptions, a <code>NextToken</code>
            is also returned. Use the <code>NextToken</code> parameter in a new <code>ListSubscriptionsByTopic</code>
            call to get further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
            <param name="topicArn">The ARN of the topic for which you wish to find subscriptions.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListSubscriptionsByTopic service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListSubscriptionsByTopic">REST API Reference for ListSubscriptionsByTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListSubscriptionsByTopicAsync(Amazon.SimpleNotificationService.Model.ListSubscriptionsByTopicRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ListSubscriptionsByTopic operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ListSubscriptionsByTopic operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListSubscriptionsByTopic">REST API Reference for ListSubscriptionsByTopic Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListTopicsAsync(System.Threading.CancellationToken)">
            <summary>
            Returns a list of the requester's topics. Each call returns a limited list of topics,
            up to 100. If there are more topics, a <code>NextToken</code> is also returned. Use
            the <code>NextToken</code> parameter in a new <code>ListTopics</code> call to get
            further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListTopics service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListTopics">REST API Reference for ListTopics Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListTopicsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns a list of the requester's topics. Each call returns a limited list of topics,
            up to 100. If there are more topics, a <code>NextToken</code> is also returned. Use
            the <code>NextToken</code> parameter in a new <code>ListTopics</code> call to get
            further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
            <param name="nextToken">Token returned by the previous <code>ListTopics</code> request.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the ListTopics service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListTopics">REST API Reference for ListTopics Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.ListTopicsAsync(Amazon.SimpleNotificationService.Model.ListTopicsRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the ListTopics operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the ListTopics operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/ListTopics">REST API Reference for ListTopics Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.OptInPhoneNumberAsync(Amazon.SimpleNotificationService.Model.OptInPhoneNumberRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the OptInPhoneNumber operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the OptInPhoneNumber operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/OptInPhoneNumber">REST API Reference for OptInPhoneNumber Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.PublishAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Sends a message to an Amazon SNS topic or sends a text message (SMS message) directly
            to a phone number. 
            
             
            <para>
            If you send a message to a topic, Amazon SNS delivers the message to each endpoint
            that is subscribed to the topic. The format of the message depends on the notification
            protocol for each subscribed endpoint.
            </para>
             
            <para>
            When a <code>messageId</code> is returned, the message has been saved and Amazon SNS
            will attempt to deliver it shortly.
            </para>
             
            <para>
            To use the <code>Publish</code> action for sending a message to a mobile endpoint,
            such as an app on a Kindle device or mobile phone, you must specify the EndpointArn
            for the TargetArn parameter. The EndpointArn is returned when making a call with the
            <code>CreatePlatformEndpoint</code> action. 
            </para>
             
            <para>
            For more information about formatting messages, see <a href="http://docs.aws.amazon.com/sns/latest/dg/mobile-push-send-custommessage.html">Send
            Custom Platform-Specific Payloads in Messages to Mobile Devices</a>. 
            </para>
            </summary>
            <param name="topicArn">The topic you want to publish to. If you don't specify a value for the <code>TopicArn</code> parameter, you must specify a value for the <code>PhoneNumber</code> or <code>TargetArn</code> parameters.</param>
            <param name="message">The message you want to send. <important> The <code>Message</code> parameter is always a string. If you set <code>MessageStructure</code> to <code>json</code>, you must string-encode the <code>Message</code> parameter. </important> If you are publishing to a topic and you want to send the same message to all transport protocols, include the text of the message as a String value. If you want to send different messages for each transport protocol, set the value of the <code>MessageStructure</code> parameter to <code>json</code> and use a JSON object for the <code>Message</code> parameter.  <p/> Constraints: <ul> <li> With the exception of SMS, messages must be UTF-8 encoded strings and at most 256 KB in size (262,144 bytes, not 262,144 characters). </li> <li> For SMS, each message can contain up to 140 characters. This character limit depends on the encoding schema. For example, an SMS message can contain 160 GSM characters, 140 ASCII characters, or 70 UCS-2 characters. If you publish a message that exceeds this size limit, Amazon SNS sends the message as multiple messages, each fitting within the size limit. Messages aren't truncated mid-word but are cut off at whole-word boundaries. The total size limit for a single SMS <code>Publish</code> action is 1,600 characters. </li> </ul> JSON-specific constraints: <ul> <li> Keys in the JSON object that correspond to supported transport protocols must have simple JSON string values. </li> <li> The values will be parsed (unescaped) before they are used in outgoing messages. </li> <li> Outbound notifications are JSON encoded (meaning that the characters will be reescaped for sending). </li> <li> Values have a minimum length of 0 (the empty string, "", is allowed). </li> <li> Values have a maximum length bounded by the overall message size (so, including multiple protocols may limit message sizes). </li> <li> Non-string values will cause the key to be ignored. </li> <li> Keys that do not correspond to supported transport protocols are ignored. </li> <li> Duplicate keys are not allowed. </li> <li> Failure to parse or validate any key or value in the message will cause the <code>Publish</code> call to return an error (no partial delivery). </li> </ul></param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the Publish service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.EndpointDisabledException">
            Exception error indicating endpoint disabled.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterValueException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSAccessDeniedException">
            The ciphertext references a key that doesn't exist or that you don't have access to.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSDisabledException">
            The request was rejected because the specified customer master key (CMK) isn't enabled.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSInvalidStateException">
            The request was rejected because the state of the specified resource isn't valid for
            this request. For more information, see <a href="http://docs.aws.amazon.com/kms/latest/developerguide/key-state.html">How
            Key State Affects Use of a Customer Master Key</a> in the <i>AWS Key Management Service
            Developer Guide</i>.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSNotFoundException">
            The request was rejected because the specified entity or resource can't be found.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSOptInRequiredException">
            The AWS access key ID needs a subscription for the service.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSThrottlingException">
            The request was denied due to request throttling. For more information about throttling,
            see <a href="http://docs.aws.amazon.com/kms/latest/developerguide/limits.html#requests-per-second">Limits</a>
            in the <i>AWS Key Management Service Developer Guide.</i>
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.PlatformApplicationDisabledException">
            Exception error indicating platform application disabled.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Publish">REST API Reference for Publish Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.PublishAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Sends a message to an Amazon SNS topic or sends a text message (SMS message) directly
            to a phone number. 
            
             
            <para>
            If you send a message to a topic, Amazon SNS delivers the message to each endpoint
            that is subscribed to the topic. The format of the message depends on the notification
            protocol for each subscribed endpoint.
            </para>
             
            <para>
            When a <code>messageId</code> is returned, the message has been saved and Amazon SNS
            will attempt to deliver it shortly.
            </para>
             
            <para>
            To use the <code>Publish</code> action for sending a message to a mobile endpoint,
            such as an app on a Kindle device or mobile phone, you must specify the EndpointArn
            for the TargetArn parameter. The EndpointArn is returned when making a call with the
            <code>CreatePlatformEndpoint</code> action. 
            </para>
             
            <para>
            For more information about formatting messages, see <a href="http://docs.aws.amazon.com/sns/latest/dg/mobile-push-send-custommessage.html">Send
            Custom Platform-Specific Payloads in Messages to Mobile Devices</a>. 
            </para>
            </summary>
            <param name="topicArn">The topic you want to publish to. If you don't specify a value for the <code>TopicArn</code> parameter, you must specify a value for the <code>PhoneNumber</code> or <code>TargetArn</code> parameters.</param>
            <param name="message">The message you want to send. <important> The <code>Message</code> parameter is always a string. If you set <code>MessageStructure</code> to <code>json</code>, you must string-encode the <code>Message</code> parameter. </important> If you are publishing to a topic and you want to send the same message to all transport protocols, include the text of the message as a String value. If you want to send different messages for each transport protocol, set the value of the <code>MessageStructure</code> parameter to <code>json</code> and use a JSON object for the <code>Message</code> parameter.  <p/> Constraints: <ul> <li> With the exception of SMS, messages must be UTF-8 encoded strings and at most 256 KB in size (262,144 bytes, not 262,144 characters). </li> <li> For SMS, each message can contain up to 140 characters. This character limit depends on the encoding schema. For example, an SMS message can contain 160 GSM characters, 140 ASCII characters, or 70 UCS-2 characters. If you publish a message that exceeds this size limit, Amazon SNS sends the message as multiple messages, each fitting within the size limit. Messages aren't truncated mid-word but are cut off at whole-word boundaries. The total size limit for a single SMS <code>Publish</code> action is 1,600 characters. </li> </ul> JSON-specific constraints: <ul> <li> Keys in the JSON object that correspond to supported transport protocols must have simple JSON string values. </li> <li> The values will be parsed (unescaped) before they are used in outgoing messages. </li> <li> Outbound notifications are JSON encoded (meaning that the characters will be reescaped for sending). </li> <li> Values have a minimum length of 0 (the empty string, "", is allowed). </li> <li> Values have a maximum length bounded by the overall message size (so, including multiple protocols may limit message sizes). </li> <li> Non-string values will cause the key to be ignored. </li> <li> Keys that do not correspond to supported transport protocols are ignored. </li> <li> Duplicate keys are not allowed. </li> <li> Failure to parse or validate any key or value in the message will cause the <code>Publish</code> call to return an error (no partial delivery). </li> </ul></param>
            <param name="subject">Optional parameter to be used as the "Subject" line when the message is delivered to email endpoints. This field will also be included, if present, in the standard JSON messages delivered to other endpoints. Constraints: Subjects must be ASCII text that begins with a letter, number, or punctuation mark; must not include line breaks or control characters; and must be less than 100 characters long.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the Publish service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.EndpointDisabledException">
            Exception error indicating endpoint disabled.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterValueException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSAccessDeniedException">
            The ciphertext references a key that doesn't exist or that you don't have access to.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSDisabledException">
            The request was rejected because the specified customer master key (CMK) isn't enabled.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSInvalidStateException">
            The request was rejected because the state of the specified resource isn't valid for
            this request. For more information, see <a href="http://docs.aws.amazon.com/kms/latest/developerguide/key-state.html">How
            Key State Affects Use of a Customer Master Key</a> in the <i>AWS Key Management Service
            Developer Guide</i>.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSNotFoundException">
            The request was rejected because the specified entity or resource can't be found.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSOptInRequiredException">
            The AWS access key ID needs a subscription for the service.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.KMSThrottlingException">
            The request was denied due to request throttling. For more information about throttling,
            see <a href="http://docs.aws.amazon.com/kms/latest/developerguide/limits.html#requests-per-second">Limits</a>
            in the <i>AWS Key Management Service Developer Guide.</i>
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.PlatformApplicationDisabledException">
            Exception error indicating platform application disabled.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Publish">REST API Reference for Publish Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.PublishAsync(Amazon.SimpleNotificationService.Model.PublishRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the Publish operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the Publish operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Publish">REST API Reference for Publish Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.RemovePermissionAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Removes a statement from a topic's access control policy.
            </summary>
            <param name="topicArn">The ARN of the topic whose access control policy you wish to modify.</param>
            <param name="label">The unique label of the statement you want to remove.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the RemovePermission service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/RemovePermission">REST API Reference for RemovePermission Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.RemovePermissionAsync(Amazon.SimpleNotificationService.Model.RemovePermissionRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the RemovePermission operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the RemovePermission operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/RemovePermission">REST API Reference for RemovePermission Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.SetEndpointAttributesAsync(Amazon.SimpleNotificationService.Model.SetEndpointAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the SetEndpointAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the SetEndpointAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetEndpointAttributes">REST API Reference for SetEndpointAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.SetPlatformApplicationAttributesAsync(Amazon.SimpleNotificationService.Model.SetPlatformApplicationAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the SetPlatformApplicationAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the SetPlatformApplicationAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetPlatformApplicationAttributes">REST API Reference for SetPlatformApplicationAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.SetSMSAttributesAsync(Amazon.SimpleNotificationService.Model.SetSMSAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the SetSMSAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the SetSMSAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetSMSAttributes">REST API Reference for SetSMSAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.SetSubscriptionAttributesAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Allows a subscription owner to set an attribute of the subscription to a new value.
            </summary>
            <param name="subscriptionArn">The ARN of the subscription to modify.</param>
            <param name="attributeName">A map of attributes with their corresponding values. The following lists the names, descriptions, and values of the special request parameters that the <code>SetTopicAttributes</code> action uses: <ul> <li>  <code>DeliveryPolicy</code> – The policy that defines how Amazon SNS retries failed deliveries to HTTP/S endpoints. </li> <li>  <code>FilterPolicy</code> – The simple JSON object that lets your subscriber receive only a subset of messages, rather than receiving every message published to the topic. </li> <li>  <code>RawMessageDelivery</code> – When set to <code>true</code>, enables raw message delivery to Amazon SQS or HTTP/S endpoints. This eliminates the need for the endpoints to process JSON formatting, which is otherwise created for Amazon SNS metadata. </li> </ul></param>
            <param name="attributeValue">The new value for the attribute in JSON format.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the SetSubscriptionAttributes service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.FilterPolicyLimitExceededException">
            Indicates that the number of filter polices in your AWS account exceeds the limit.
            To add more filter polices, submit an SNS Limit Increase case in the AWS Support Center.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetSubscriptionAttributes">REST API Reference for SetSubscriptionAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.SetSubscriptionAttributesAsync(Amazon.SimpleNotificationService.Model.SetSubscriptionAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the SetSubscriptionAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the SetSubscriptionAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetSubscriptionAttributes">REST API Reference for SetSubscriptionAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.SetTopicAttributesAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Allows a topic owner to set an attribute of the topic to a new value.
            </summary>
            <param name="topicArn">The ARN of the topic to modify.</param>
            <param name="attributeName">A map of attributes with their corresponding values. The following lists the names, descriptions, and values of the special request parameters that the <code>SetTopicAttributes</code> action uses: <ul> <li>  <code>DeliveryPolicy</code> – The policy that defines how Amazon SNS retries failed deliveries to HTTP/S endpoints. </li> <li>  <code>DisplayName</code> – The display name to use for a topic with SMS subscriptions. </li> <li>  <code>Policy</code> – The policy that defines who can access your topic. By default, only the topic owner can publish or subscribe to the topic. </li> </ul></param>
            <param name="attributeValue">The new value for the attribute.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the SetTopicAttributes service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetTopicAttributes">REST API Reference for SetTopicAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.SetTopicAttributesAsync(Amazon.SimpleNotificationService.Model.SetTopicAttributesRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the SetTopicAttributes operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the SetTopicAttributes operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/SetTopicAttributes">REST API Reference for SetTopicAttributes Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.SubscribeAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Prepares to subscribe an endpoint by sending the endpoint a confirmation message.
            To actually create a subscription, the endpoint owner must call the <code>ConfirmSubscription</code>
            action with the token from the confirmation message. Confirmation tokens are valid
            for three days.
            
             
            <para>
            This action is throttled at 100 transactions per second (TPS).
            </para>
            </summary>
            <param name="topicArn">The ARN of the topic you want to subscribe to.</param>
            <param name="protocol">The protocol you want to use. Supported protocols include: <ul> <li>  <code>http</code> – delivery of JSON-encoded message via HTTP POST </li> <li>  <code>https</code> – delivery of JSON-encoded message via HTTPS POST </li> <li>  <code>email</code> – delivery of message via SMTP </li> <li>  <code>email-json</code> – delivery of JSON-encoded message via SMTP </li> <li>  <code>sms</code> – delivery of message via SMS </li> <li>  <code>sqs</code> – delivery of JSON-encoded message to an Amazon SQS queue </li> <li>  <code>application</code> – delivery of JSON-encoded message to an EndpointArn for a mobile app and device. </li> <li>  <code>lambda</code> – delivery of JSON-encoded message to an AWS Lambda function. </li> </ul></param>
            <param name="endpoint">The endpoint that you want to receive notifications. Endpoints vary by protocol: <ul> <li> For the <code>http</code> protocol, the endpoint is an URL beginning with "http://" </li> <li> For the <code>https</code> protocol, the endpoint is a URL beginning with "https://" </li> <li> For the <code>email</code> protocol, the endpoint is an email address </li> <li> For the <code>email-json</code> protocol, the endpoint is an email address </li> <li> For the <code>sms</code> protocol, the endpoint is a phone number of an SMS-enabled device </li> <li> For the <code>sqs</code> protocol, the endpoint is the ARN of an Amazon SQS queue </li> <li> For the <code>application</code> protocol, the endpoint is the EndpointArn of a mobile app and device. </li> <li> For the <code>lambda</code> protocol, the endpoint is the ARN of an AWS Lambda function. </li> </ul></param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the Subscribe service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.FilterPolicyLimitExceededException">
            Indicates that the number of filter polices in your AWS account exceeds the limit.
            To add more filter polices, submit an SNS Limit Increase case in the AWS Support Center.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.SubscriptionLimitExceededException">
            Indicates that the customer already owns the maximum allowed number of subscriptions.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Subscribe">REST API Reference for Subscribe Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.SubscribeAsync(Amazon.SimpleNotificationService.Model.SubscribeRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the Subscribe operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the Subscribe operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Subscribe">REST API Reference for Subscribe Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.UnsubscribeAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Deletes a subscription. If the subscription requires authentication for deletion,
            only the owner of the subscription or the topic's owner can unsubscribe, and an AWS
            signature is required. If the <code>Unsubscribe</code> call does not require authentication
            and the requester is not the subscription owner, a final cancellation message is delivered
            to the endpoint, so that the endpoint owner can easily resubscribe to the topic if
            the <code>Unsubscribe</code> request was unintended.
            
             
            <para>
            This action is throttled at 100 transactions per second (TPS).
            </para>
            </summary>
            <param name="subscriptionArn">The ARN of the subscription to be deleted.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            
            <returns>The response from the Unsubscribe service method, as returned by SimpleNotificationService.</returns>
            <exception cref="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            Indicates that the user has been denied access to the requested resource.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            Indicates an internal service error.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            Indicates that a request parameter does not comply with the associated constraints.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            The credential signature isn't valid. You must use an HTTPS endpoint and sign your
            request using Signature Version 4.
            </exception>
            <exception cref="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            Indicates that the requested resource does not exist.
            </exception>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Unsubscribe">REST API Reference for Unsubscribe Operation</seealso>
        </member>
        <member name="M:Amazon.SimpleNotificationService.IAmazonSimpleNotificationService.UnsubscribeAsync(Amazon.SimpleNotificationService.Model.UnsubscribeRequest,System.Threading.CancellationToken)">
            <summary>
            Initiates the asynchronous execution of the Unsubscribe operation.
            </summary>
            
            <param name="request">Container for the necessary parameters to execute the Unsubscribe operation.</param>
            <param name="cancellationToken">
                A cancellation token that can be used by other objects or threads to receive notice of cancellation.
            </param>
            <returns>The task object representing the asynchronous operation.</returns>
            <seealso href="http://docs.aws.amazon.com/goto/WebAPI/sns-2010-03-31/Unsubscribe">REST API Reference for Unsubscribe Operation</seealso>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Util.Message">
            <summary>
            This class reads in JSON formatted Amazon SNS messages into Message objects. The messages can also be verified using the IsMessageSignatureValid operation.
            </summary>
        </member>
        <member name="F:Amazon.SimpleNotificationService.Util.Message.MESSAGE_TYPE_SUBSCRIPTION_CONFIRMATION">
            <summary>
            The value of the Type property for a subscription confirmation message
            </summary>
        </member>
        <member name="F:Amazon.SimpleNotificationService.Util.Message.MESSAGE_TYPE_UNSUBSCRIPTION_CONFIRMATION">
            <summary>
            The value of the Type property for a unsubscribe confirmation message
            </summary>
        </member>
        <member name="F:Amazon.SimpleNotificationService.Util.Message.MESSAGE_TYPE_NOTIFICATION">
            <summary>
            The value of the Type property for a notification message
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Util.Message.ParseMessage(System.String)">
            <summary>
            Parses the JSON message from Amazon SNS into the Message object.
            </summary>
            <param name="messageText">The JSON text from an Amazon SNS message</param>
            <returns>The Message object with properties set from the JSON document</returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.MessageId">
            <summary>
            Gets a Universally Unique Identifier, unique for each message published. For a notification that Amazon SNS resends during a retry, the message ID of the original message is used.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.MessageText">
            <summary>
            Gets the MessageText value specified when the notification was published to the topic.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.Signature">
            <summary>
            Gets the Base64-encoded "SHA1withRSA" signature of the Message, MessageId, Subject (if present), Type, Timestamp, and TopicArn values.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.SignatureVersion">
            <summary>
            Gets the Version of the Amazon SNS signature used.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.SigningCertURL">
            <summary>
            Gets the URL to the certificate that was used to sign the message.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.Subject">
            <summary>
            Gets the Subject parameter specified when the notification was published to the topic. Note that this is an optional parameter. 
            If no Subject was specified, then this name/value pair does not appear in this JSON document.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.SubscribeURL">
            <summary>
            Gets the URL that you must visit in order to re-confirm the subscription. Alternatively, you can instead use the Token with the ConfirmSubscription action to re-confirm the subscription.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.Timestamp">
            <summary>
            Gets the time (GMT) when the notification was published.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.Token">
            <summary>
            Gets a value you can use with the ConfirmSubscription action to re-confirm the subscription. Alternatively, you can simply visit the SubscribeURL.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.TopicArn">
            <summary>
            Gets the Amazon Resource Name (ARN) for the topic.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.Type">
            <summary>
            Gets the type of message. Possible values are Notification, SubscriptionConfirmation, and UnsubscribeConfirmation.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.IsSubscriptionType">
            <summary>
            Returns true if the message type is a subscription confirmation.
            </summary>
            <returns>True if the message type is a subscription confirmation.</returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.IsUnsubscriptionType">
            <summary>
            Returns true if the message type is a unsubscribe confirmation.
            </summary>
            <returns>True if the message type is a unsubscribe confirmation.</returns>        
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.IsNotificationType">
            <summary>
            Returns true if the message type is a notification message.
            </summary>
            <returns>True if the message type is a notification message.</returns>        
        </member>
        <member name="P:Amazon.SimpleNotificationService.Util.Message.UnsubscribeURL">
            <summary>
            Gets a URL that you can use to unsubscribe the endpoint from this topic. If you visit this URL, Amazon SNS unsubscribes the endpoint and stops sending notifications to this endpoint.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Util.Message.ValidateCertUrl(System.String)">
            <summary>
            Verifies that the signing certificate url is from a recognizable source. 
            Returns the cert url if it cen be verified, otherwise throws an exception.
            </summary>
            <param name="certUrl"></param>
            <returns></returns>
        </member>
        <member name="T:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceConfig">
            <summary>
            Configuration for accessing Amazon SimpleNotificationService service
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceConfig.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceConfig.RegionEndpointServiceName">
            <summary>
            The constant used to lookup in the region hash the endpoint.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceConfig.ServiceVersion">
            <summary>
            Gets the ServiceVersion property.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceConfig.UserAgent">
            <summary>
            Gets the value of UserAgent property.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceException">
            <summary>
             Common exception for the SimpleNotificationService service.
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceException.#ctor(System.String)">
            <summary>
            Construct instance of AmazonSimpleNotificationServiceException
            </summary>
            <param name="message"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of AmazonSimpleNotificationServiceException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceException.#ctor(System.Exception)">
            <summary>
            Construct instance of AmazonSimpleNotificationServiceException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of AmazonSimpleNotificationServiceException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of AmazonSimpleNotificationServiceException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Internal.AmazonSimpleNotificationServiceMetadata">
            <summary>
            Service metadata for  Amazon SimpleNotificationService service
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Internal.AmazonSimpleNotificationServiceMetadata.ServiceId">
            <summary>
            Gets the value of the Service Id.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Internal.AmazonSimpleNotificationServiceMetadata.OperationNameMapping">
            <summary>
            Gets the dictionary that gives mapping of renamed operations
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.AddPermissionRequest">
            <summary>
            Container for the parameters to the AddPermission operation.
            Adds a statement to a topic's access control policy, granting access for the specified
            AWS accounts to the specified actions.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.AddPermissionRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.AddPermissionRequest.#ctor(System.String,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String})">
            <summary>
            Instantiates AddPermissionRequest with the parameterized properties
            </summary>
            <param name="topicArn">The ARN of the topic whose access control policy you wish to modify.</param>
            <param name="label">A unique identifier for the new policy statement.</param>
            <param name="awsAccountId">The AWS account IDs of the users (principals) who will be given access to the specified actions. The users must have AWS accounts, but do not need to be signed up for this service.</param>
            <param name="actionName">The action you want to allow for the specified principal(s). Valid values: any Amazon SNS action name.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.AddPermissionRequest.ActionName">
            <summary>
            Gets and sets the property ActionName. 
            <para>
            The action you want to allow for the specified principal(s).
            </para>
             
            <para>
            Valid values: any Amazon SNS action name.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.AddPermissionRequest.AWSAccountId">
            <summary>
            Gets and sets the property AWSAccountId. 
            <para>
            The AWS account IDs of the users (principals) who will be given access to the specified
            actions. The users must have AWS accounts, but do not need to be signed up for this
            service.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.AddPermissionRequest.Label">
            <summary>
            Gets and sets the property Label. 
            <para>
            A unique identifier for the new policy statement.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.AddPermissionRequest.TopicArn">
            <summary>
            Gets and sets the property TopicArn. 
            <para>
            The ARN of the topic whose access control policy you wish to modify.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.AddPermissionResponse">
            <summary>
            This is the response object from the AddPermission operation.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.AuthorizationErrorException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.AuthorizationErrorException.#ctor(System.String)">
            <summary>
            Constructs a new AuthorizationErrorException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.AuthorizationErrorException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of AuthorizationErrorException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.AuthorizationErrorException.#ctor(System.Exception)">
            <summary>
            Construct instance of AuthorizationErrorException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.AuthorizationErrorException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of AuthorizationErrorException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.AuthorizationErrorException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of AuthorizationErrorException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.CheckIfPhoneNumberIsOptedOutRequest">
            <summary>
            Container for the parameters to the CheckIfPhoneNumberIsOptedOut operation.
            Accepts a phone number and indicates whether the phone holder has opted out of receiving
            SMS messages from your account. You cannot send SMS messages to a number that is opted
            out.
            
             
            <para>
            To resume sending messages, you can opt in the number by using the <code>OptInPhoneNumber</code>
            action.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CheckIfPhoneNumberIsOptedOutRequest.PhoneNumber">
            <summary>
            Gets and sets the property PhoneNumber. 
            <para>
            The phone number for which you want to check the opt out status.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.CheckIfPhoneNumberIsOptedOutResponse">
            <summary>
            The response from the <code>CheckIfPhoneNumberIsOptedOut</code> action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CheckIfPhoneNumberIsOptedOutResponse.IsOptedOut">
            <summary>
            Gets and sets the property IsOptedOut. 
            <para>
            Indicates whether the phone number is opted out:
            </para>
             <ul> <li> 
            <para>
             <code>true</code> – The phone number is opted out, meaning you cannot publish SMS
            messages to it.
            </para>
             </li> <li> 
            <para>
             <code>false</code> – The phone number is opted in, meaning you can publish SMS messages
            to it.
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ConfirmSubscriptionRequest">
            <summary>
            Container for the parameters to the ConfirmSubscription operation.
            Verifies an endpoint owner's intent to receive messages by validating the token sent
            to the endpoint by an earlier <code>Subscribe</code> action. If the token is valid,
            the action creates a new subscription and returns its Amazon Resource Name (ARN).
            This call requires an AWS signature only when the <code>AuthenticateOnUnsubscribe</code>
            flag is set to "true".
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ConfirmSubscriptionRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ConfirmSubscriptionRequest.#ctor(System.String,System.String)">
            <summary>
            Instantiates ConfirmSubscriptionRequest with the parameterized properties
            </summary>
            <param name="topicArn">The ARN of the topic for which you wish to confirm a subscription.</param>
            <param name="token">Short-lived token sent to an endpoint during the <code>Subscribe</code> action.</param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ConfirmSubscriptionRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Instantiates ConfirmSubscriptionRequest with the parameterized properties
            </summary>
            <param name="topicArn">The ARN of the topic for which you wish to confirm a subscription.</param>
            <param name="token">Short-lived token sent to an endpoint during the <code>Subscribe</code> action.</param>
            <param name="authenticateOnUnsubscribe">Disallows unauthenticated unsubscribes of the subscription. If the value of this parameter is <code>true</code> and the request has an AWS signature, then only the topic owner and the subscription owner can unsubscribe the endpoint. The unsubscribe action requires AWS authentication. </param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ConfirmSubscriptionRequest.AuthenticateOnUnsubscribe">
            <summary>
            Gets and sets the property AuthenticateOnUnsubscribe. 
            <para>
            Disallows unauthenticated unsubscribes of the subscription. If the value of this parameter
            is <code>true</code> and the request has an AWS signature, then only the topic owner
            and the subscription owner can unsubscribe the endpoint. The unsubscribe action requires
            AWS authentication. 
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ConfirmSubscriptionRequest.Token">
            <summary>
            Gets and sets the property Token. 
            <para>
            Short-lived token sent to an endpoint during the <code>Subscribe</code> action.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ConfirmSubscriptionRequest.TopicArn">
            <summary>
            Gets and sets the property TopicArn. 
            <para>
            The ARN of the topic for which you wish to confirm a subscription.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ConfirmSubscriptionResponse">
            <summary>
            Response for ConfirmSubscriptions action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ConfirmSubscriptionResponse.SubscriptionArn">
            <summary>
            Gets and sets the property SubscriptionArn. 
            <para>
            The ARN of the created subscription.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.CreatePlatformApplicationRequest">
            <summary>
            Container for the parameters to the CreatePlatformApplication operation.
            Creates a platform application object for one of the supported push notification services,
            such as APNS and GCM, to which devices and mobile apps may register. You must specify
            PlatformPrincipal and PlatformCredential attributes when using the <code>CreatePlatformApplication</code>
            action. The PlatformPrincipal is received from the notification service. For APNS/APNS_SANDBOX,
            PlatformPrincipal is "SSL certificate". For GCM, PlatformPrincipal is not applicable.
            For ADM, PlatformPrincipal is "client id". The PlatformCredential is also received
            from the notification service. For WNS, PlatformPrincipal is "Package Security Identifier".
            For MPNS, PlatformPrincipal is "TLS certificate". For Baidu, PlatformPrincipal is
            "API key".
            
             
            <para>
            For APNS/APNS_SANDBOX, PlatformCredential is "private key". For GCM, PlatformCredential
            is "API key". For ADM, PlatformCredential is "client secret". For WNS, PlatformCredential
            is "secret key". For MPNS, PlatformCredential is "private key". For Baidu, PlatformCredential
            is "secret key". The PlatformApplicationArn that is returned when using <code>CreatePlatformApplication</code>
            is then used as an attribute for the <code>CreatePlatformEndpoint</code> action. For
            more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePush.html">Using
            Amazon SNS Mobile Push Notifications</a>. For more information about obtaining the
            PlatformPrincipal and PlatformCredential for each of the supported push notification
            services, see <a href="http://docs.aws.amazon.com/sns/latest/dg/mobile-push-apns.html">Getting
            Started with Apple Push Notification Service</a>, <a href="http://docs.aws.amazon.com/sns/latest/dg/mobile-push-adm.html">Getting
            Started with Amazon Device Messaging</a>, <a href="http://docs.aws.amazon.com/sns/latest/dg/mobile-push-baidu.html">Getting
            Started with Baidu Cloud Push</a>, <a href="http://docs.aws.amazon.com/sns/latest/dg/mobile-push-gcm.html">Getting
            Started with Google Cloud Messaging for Android</a>, <a href="http://docs.aws.amazon.com/sns/latest/dg/mobile-push-mpns.html">Getting
            Started with MPNS</a>, or <a href="http://docs.aws.amazon.com/sns/latest/dg/mobile-push-wns.html">Getting
            Started with WNS</a>. 
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CreatePlatformApplicationRequest.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            For a list of attributes, see <a href="http://docs.aws.amazon.com/sns/latest/api/API_SetPlatformApplicationAttributes.html">SetPlatformApplicationAttributes</a>
            
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CreatePlatformApplicationRequest.Name">
            <summary>
            Gets and sets the property Name. 
            <para>
            Application names must be made up of only uppercase and lowercase ASCII letters, numbers,
            underscores, hyphens, and periods, and must be between 1 and 256 characters long.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CreatePlatformApplicationRequest.Platform">
            <summary>
            Gets and sets the property Platform. 
            <para>
            The following platforms are supported: ADM (Amazon Device Messaging), APNS (Apple
            Push Notification Service), APNS_SANDBOX, and GCM (Google Cloud Messaging).
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.CreatePlatformApplicationResponse">
            <summary>
            Response from CreatePlatformApplication action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CreatePlatformApplicationResponse.PlatformApplicationArn">
            <summary>
            Gets and sets the property PlatformApplicationArn. 
            <para>
            PlatformApplicationArn is returned.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.CreatePlatformEndpointRequest">
            <summary>
            Container for the parameters to the CreatePlatformEndpoint operation.
            Creates an endpoint for a device and mobile app on one of the supported push notification
            services, such as GCM and APNS. <code>CreatePlatformEndpoint</code> requires the PlatformApplicationArn
            that is returned from <code>CreatePlatformApplication</code>. The EndpointArn that
            is returned when using <code>CreatePlatformEndpoint</code> can then be used by the
            <code>Publish</code> action to send a message to a mobile app or by the <code>Subscribe</code>
            action for subscription to a topic. The <code>CreatePlatformEndpoint</code> action
            is idempotent, so if the requester already owns an endpoint with the same device token
            and attributes, that endpoint's ARN is returned without creating a new endpoint. For
            more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePush.html">Using
            Amazon SNS Mobile Push Notifications</a>. 
            
             
            <para>
            When using <code>CreatePlatformEndpoint</code> with Baidu, two attributes must be
            provided: ChannelId and UserId. The token field must also contain the ChannelId. For
            more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePushBaiduEndpoint.html">Creating
            an Amazon SNS Endpoint for Baidu</a>. 
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CreatePlatformEndpointRequest.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            For a list of attributes, see <a href="http://docs.aws.amazon.com/sns/latest/api/API_SetEndpointAttributes.html">SetEndpointAttributes</a>.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CreatePlatformEndpointRequest.CustomUserData">
            <summary>
            Gets and sets the property CustomUserData. 
            <para>
            Arbitrary user data to associate with the endpoint. Amazon SNS does not use this data.
            The data must be in UTF-8 format and less than 2KB.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CreatePlatformEndpointRequest.PlatformApplicationArn">
            <summary>
            Gets and sets the property PlatformApplicationArn. 
            <para>
            PlatformApplicationArn returned from CreatePlatformApplication is used to create a
            an endpoint.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CreatePlatformEndpointRequest.Token">
            <summary>
            Gets and sets the property Token. 
            <para>
            Unique identifier created by the notification service for an app on a device. The
            specific name for Token will vary, depending on which notification service is being
            used. For example, when using APNS as the notification service, you need the device
            token. Alternatively, when using GCM or ADM, the device token equivalent is called
            the registration ID.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.CreatePlatformEndpointResponse">
            <summary>
            Response from CreateEndpoint action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CreatePlatformEndpointResponse.EndpointArn">
            <summary>
            Gets and sets the property EndpointArn. 
            <para>
            EndpointArn returned from CreateEndpoint action.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.CreateTopicRequest">
            <summary>
            Container for the parameters to the CreateTopic operation.
            Creates a topic to which notifications can be published. Users can create at most
            100,000 topics. For more information, see <a href="http://aws.amazon.com/sns/">http://aws.amazon.com/sns</a>.
            This action is idempotent, so if the requester already owns a topic with the specified
            name, that topic's ARN is returned without creating a new topic.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.CreateTopicRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.CreateTopicRequest.#ctor(System.String)">
            <summary>
            Instantiates CreateTopicRequest with the parameterized properties
            </summary>
            <param name="name">The name of the topic you want to create. Constraints: Topic names must be made up of only uppercase and lowercase ASCII letters, numbers, underscores, and hyphens, and must be between 1 and 256 characters long.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CreateTopicRequest.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            A map of attributes with their corresponding values.
            </para>
             
            <para>
            The following lists the names, descriptions, and values of the special request parameters
            that the <code>CreateTopic</code> action uses:
            </para>
             <ul> <li> 
            <para>
             <code>DeliveryPolicy</code> – The policy that defines how Amazon SNS retries failed
            deliveries to HTTP/S endpoints.
            </para>
             </li> <li> 
            <para>
             <code>DisplayName</code> – The display name to use for a topic with SMS subscriptions.
            </para>
             </li> <li> 
            <para>
             <code>Policy</code> – The policy that defines who can access your topic. By default,
            only the topic owner can publish or subscribe to the topic.
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CreateTopicRequest.Name">
            <summary>
            Gets and sets the property Name. 
            <para>
            The name of the topic you want to create.
            </para>
             
            <para>
            Constraints: Topic names must be made up of only uppercase and lowercase ASCII letters,
            numbers, underscores, and hyphens, and must be between 1 and 256 characters long.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.CreateTopicResponse">
            <summary>
            Response from CreateTopic action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.CreateTopicResponse.TopicArn">
            <summary>
            Gets and sets the property TopicArn. 
            <para>
            The Amazon Resource Name (ARN) assigned to the created topic.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.DeleteEndpointRequest">
            <summary>
            Container for the parameters to the DeleteEndpoint operation.
            Deletes the endpoint for a device and mobile app from Amazon SNS. This action is idempotent.
            For more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePush.html">Using
            Amazon SNS Mobile Push Notifications</a>. 
            
             
            <para>
            When you delete an endpoint that is also subscribed to a topic, then you must also
            unsubscribe the endpoint from the topic.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.DeleteEndpointRequest.EndpointArn">
            <summary>
            Gets and sets the property EndpointArn. 
            <para>
            EndpointArn of endpoint to delete.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.DeleteEndpointResponse">
            <summary>
            This is the response object from the DeleteEndpoint operation.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.DeletePlatformApplicationRequest">
            <summary>
            Container for the parameters to the DeletePlatformApplication operation.
            Deletes a platform application object for one of the supported push notification services,
            such as APNS and GCM. For more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePush.html">Using
            Amazon SNS Mobile Push Notifications</a>.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.DeletePlatformApplicationRequest.PlatformApplicationArn">
            <summary>
            Gets and sets the property PlatformApplicationArn. 
            <para>
            PlatformApplicationArn of platform application object to delete.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.DeletePlatformApplicationResponse">
            <summary>
            This is the response object from the DeletePlatformApplication operation.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.DeleteTopicRequest">
            <summary>
            Container for the parameters to the DeleteTopic operation.
            Deletes a topic and all its subscriptions. Deleting a topic might prevent some messages
            previously sent to the topic from being delivered to subscribers. This action is idempotent,
            so deleting a topic that does not exist does not result in an error.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.DeleteTopicRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.DeleteTopicRequest.#ctor(System.String)">
            <summary>
            Instantiates DeleteTopicRequest with the parameterized properties
            </summary>
            <param name="topicArn">The ARN of the topic you want to delete.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.DeleteTopicRequest.TopicArn">
            <summary>
            Gets and sets the property TopicArn. 
            <para>
            The ARN of the topic you want to delete.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.DeleteTopicResponse">
            <summary>
            This is the response object from the DeleteTopic operation.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Endpoint">
            <summary>
            Endpoint for mobile app and device.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Endpoint.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            Attributes for endpoint.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Endpoint.EndpointArn">
            <summary>
            Gets and sets the property EndpointArn. 
            <para>
            EndpointArn for mobile app and device.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.EndpointDisabledException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.EndpointDisabledException.#ctor(System.String)">
            <summary>
            Constructs a new EndpointDisabledException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.EndpointDisabledException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of EndpointDisabledException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.EndpointDisabledException.#ctor(System.Exception)">
            <summary>
            Construct instance of EndpointDisabledException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.EndpointDisabledException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of EndpointDisabledException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.EndpointDisabledException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of EndpointDisabledException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.FilterPolicyLimitExceededException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.FilterPolicyLimitExceededException.#ctor(System.String)">
            <summary>
            Constructs a new FilterPolicyLimitExceededException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.FilterPolicyLimitExceededException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of FilterPolicyLimitExceededException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.FilterPolicyLimitExceededException.#ctor(System.Exception)">
            <summary>
            Construct instance of FilterPolicyLimitExceededException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.FilterPolicyLimitExceededException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of FilterPolicyLimitExceededException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.FilterPolicyLimitExceededException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of FilterPolicyLimitExceededException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.GetEndpointAttributesRequest">
            <summary>
            Container for the parameters to the GetEndpointAttributes operation.
            Retrieves the endpoint attributes for a device on one of the supported push notification
            services, such as GCM and APNS. For more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePush.html">Using
            Amazon SNS Mobile Push Notifications</a>.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.GetEndpointAttributesRequest.EndpointArn">
            <summary>
            Gets and sets the property EndpointArn. 
            <para>
            EndpointArn for GetEndpointAttributes input.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.GetEndpointAttributesResponse">
            <summary>
            Response from GetEndpointAttributes of the EndpointArn.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.GetEndpointAttributesResponse.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            Attributes include the following:
            </para>
             <ul> <li> 
            <para>
             <code>CustomUserData</code> – arbitrary user data to associate with the endpoint.
            Amazon SNS does not use this data. The data must be in UTF-8 format and less than
            2KB.
            </para>
             </li> <li> 
            <para>
             <code>Enabled</code> – flag that enables/disables delivery to the endpoint. Amazon
            SNS will set this to false when a notification service indicates to Amazon SNS that
            the endpoint is invalid. Users can set it back to true, typically after updating Token.
            </para>
             </li> <li> 
            <para>
             <code>Token</code> – device token, also referred to as a registration id, for an
            app and mobile device. This is returned from the notification service when an app
            and mobile device are registered with the notification service.
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.GetPlatformApplicationAttributesRequest">
            <summary>
            Container for the parameters to the GetPlatformApplicationAttributes operation.
            Retrieves the attributes of the platform application object for the supported push
            notification services, such as APNS and GCM. For more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePush.html">Using
            Amazon SNS Mobile Push Notifications</a>.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.GetPlatformApplicationAttributesRequest.PlatformApplicationArn">
            <summary>
            Gets and sets the property PlatformApplicationArn. 
            <para>
            PlatformApplicationArn for GetPlatformApplicationAttributesInput.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.GetPlatformApplicationAttributesResponse">
            <summary>
            Response for GetPlatformApplicationAttributes action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.GetPlatformApplicationAttributesResponse.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            Attributes include the following:
            </para>
             <ul> <li> 
            <para>
             <code>EventEndpointCreated</code> – Topic ARN to which EndpointCreated event notifications
            should be sent.
            </para>
             </li> <li> 
            <para>
             <code>EventEndpointDeleted</code> – Topic ARN to which EndpointDeleted event notifications
            should be sent.
            </para>
             </li> <li> 
            <para>
             <code>EventEndpointUpdated</code> – Topic ARN to which EndpointUpdate event notifications
            should be sent.
            </para>
             </li> <li> 
            <para>
             <code>EventDeliveryFailure</code> – Topic ARN to which DeliveryFailure event notifications
            should be sent upon Direct Publish delivery failure (permanent) to one of the application's
            endpoints.
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.GetSMSAttributesRequest">
            <summary>
            Container for the parameters to the GetSMSAttributes operation.
            Returns the settings for sending SMS messages from your account.
            
             
            <para>
            These settings are set with the <code>SetSMSAttributes</code> action.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.GetSMSAttributesRequest.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            A list of the individual attribute names, such as <code>MonthlySpendLimit</code>,
            for which you want values.
            </para>
             
            <para>
            For all attribute names, see <a href="http://docs.aws.amazon.com/sns/latest/api/API_SetSMSAttributes.html">SetSMSAttributes</a>.
            </para>
             
            <para>
            If you don't use this parameter, Amazon SNS returns all SMS attributes.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.GetSMSAttributesResponse">
            <summary>
            The response from the <code>GetSMSAttributes</code> request.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.GetSMSAttributesResponse.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            The SMS attribute names and their values.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.GetSubscriptionAttributesRequest">
            <summary>
            Container for the parameters to the GetSubscriptionAttributes operation.
            Returns all of the properties of a subscription.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.GetSubscriptionAttributesRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.GetSubscriptionAttributesRequest.#ctor(System.String)">
            <summary>
            Instantiates GetSubscriptionAttributesRequest with the parameterized properties
            </summary>
            <param name="subscriptionArn">The ARN of the subscription whose properties you want to get.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.GetSubscriptionAttributesRequest.SubscriptionArn">
            <summary>
            Gets and sets the property SubscriptionArn. 
            <para>
            The ARN of the subscription whose properties you want to get.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.GetSubscriptionAttributesResponse">
            <summary>
            Response for GetSubscriptionAttributes action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.GetSubscriptionAttributesResponse.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            A map of the subscription's attributes. Attributes in this map include the following:
            </para>
             <ul> <li> 
            <para>
             <code>ConfirmationWasAuthenticated</code> – <code>true</code> if the subscription
            confirmation request was authenticated.
            </para>
             </li> <li> 
            <para>
             <code>DeliveryPolicy</code> – The JSON serialization of the subscription's delivery
            policy.
            </para>
             </li> <li> 
            <para>
             <code>EffectiveDeliveryPolicy</code> – The JSON serialization of the effective delivery
            policy that takes into account the topic delivery policy and account system defaults.
            </para>
             </li> <li> 
            <para>
             <code>FilterPolicy</code> – The filter policy JSON that is assigned to the subscription.
            </para>
             </li> <li> 
            <para>
             <code>Owner</code> – The AWS account ID of the subscription's owner.
            </para>
             </li> <li> 
            <para>
             <code>PendingConfirmation</code> – <code>true</code> if the subscription hasn't been
            confirmed. To confirm a pending subscription, call the <code>ConfirmSubscription</code>
            action with a confirmation token.
            </para>
             </li> <li> 
            <para>
             <code>RawMessageDelivery</code> – <code>true</code> if raw message delivery is enabled
            for the subscription. Raw messages are free of JSON formatting and can be sent to
            HTTP/S and Amazon SQS endpoints.
            </para>
             </li> <li> 
            <para>
             <code>SubscriptionArn</code> – The subscription's ARN.
            </para>
             </li> <li> 
            <para>
             <code>TopicArn</code> – The topic ARN that the subscription is associated with.
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.GetTopicAttributesRequest">
            <summary>
            Container for the parameters to the GetTopicAttributes operation.
            Returns all of the properties of a topic. Topic properties returned might differ based
            on the authorization of the user.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.GetTopicAttributesRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.GetTopicAttributesRequest.#ctor(System.String)">
            <summary>
            Instantiates GetTopicAttributesRequest with the parameterized properties
            </summary>
            <param name="topicArn">The ARN of the topic whose properties you want to get.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.GetTopicAttributesRequest.TopicArn">
            <summary>
            Gets and sets the property TopicArn. 
            <para>
            The ARN of the topic whose properties you want to get.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.GetTopicAttributesResponse">
            <summary>
            Response for GetTopicAttributes action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.GetTopicAttributesResponse.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            A map of the topic's attributes. Attributes in this map include the following:
            </para>
             <ul> <li> 
            <para>
             <code>TopicArn</code> – the topic's ARN
            </para>
             </li> <li> 
            <para>
             <code>Owner</code> – the AWS account ID of the topic's owner
            </para>
             </li> <li> 
            <para>
             <code>Policy</code> – the JSON serialization of the topic's access control policy
            </para>
             </li> <li> 
            <para>
             <code>DisplayName</code> – the human-readable name used in the "From" field for notifications
            to email and email-json endpoints
            </para>
             </li> <li> 
            <para>
             <code>SubscriptionsPending</code> – the number of subscriptions pending confirmation
            on this topic
            </para>
             </li> <li> 
            <para>
             <code>SubscriptionsConfirmed</code> – the number of confirmed subscriptions on this
            topic
            </para>
             </li> <li> 
            <para>
             <code>SubscriptionsDeleted</code> – the number of deleted subscriptions on this topic
            </para>
             </li> <li> 
            <para>
             <code>DeliveryPolicy</code> – the JSON serialization of the topic's delivery policy
            </para>
             </li> <li> 
            <para>
             <code>EffectiveDeliveryPolicy</code> – the JSON serialization of the effective delivery
            policy that takes into account system defaults
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.InternalErrorException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InternalErrorException.#ctor(System.String)">
            <summary>
            Constructs a new InternalErrorException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InternalErrorException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of InternalErrorException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InternalErrorException.#ctor(System.Exception)">
            <summary>
            Construct instance of InternalErrorException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InternalErrorException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of InternalErrorException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InternalErrorException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of InternalErrorException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.InvalidParameterException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidParameterException.#ctor(System.String)">
            <summary>
            Constructs a new InvalidParameterException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidParameterException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of InvalidParameterException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidParameterException.#ctor(System.Exception)">
            <summary>
            Construct instance of InvalidParameterException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidParameterException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of InvalidParameterException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidParameterException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of InvalidParameterException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.InvalidParameterValueException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidParameterValueException.#ctor(System.String)">
            <summary>
            Constructs a new InvalidParameterValueException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidParameterValueException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of InvalidParameterValueException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidParameterValueException.#ctor(System.Exception)">
            <summary>
            Construct instance of InvalidParameterValueException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidParameterValueException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of InvalidParameterValueException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidParameterValueException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of InvalidParameterValueException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.InvalidSecurityException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidSecurityException.#ctor(System.String)">
            <summary>
            Constructs a new InvalidSecurityException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidSecurityException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of InvalidSecurityException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidSecurityException.#ctor(System.Exception)">
            <summary>
            Construct instance of InvalidSecurityException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidSecurityException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of InvalidSecurityException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.InvalidSecurityException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of InvalidSecurityException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.KMSAccessDeniedException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSAccessDeniedException.#ctor(System.String)">
            <summary>
            Constructs a new KMSAccessDeniedException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSAccessDeniedException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of KMSAccessDeniedException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSAccessDeniedException.#ctor(System.Exception)">
            <summary>
            Construct instance of KMSAccessDeniedException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSAccessDeniedException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of KMSAccessDeniedException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSAccessDeniedException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of KMSAccessDeniedException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.KMSDisabledException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSDisabledException.#ctor(System.String)">
            <summary>
            Constructs a new KMSDisabledException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSDisabledException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of KMSDisabledException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSDisabledException.#ctor(System.Exception)">
            <summary>
            Construct instance of KMSDisabledException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSDisabledException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of KMSDisabledException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSDisabledException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of KMSDisabledException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.KMSInvalidStateException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSInvalidStateException.#ctor(System.String)">
            <summary>
            Constructs a new KMSInvalidStateException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSInvalidStateException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of KMSInvalidStateException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSInvalidStateException.#ctor(System.Exception)">
            <summary>
            Construct instance of KMSInvalidStateException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSInvalidStateException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of KMSInvalidStateException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSInvalidStateException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of KMSInvalidStateException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.KMSNotFoundException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSNotFoundException.#ctor(System.String)">
            <summary>
            Constructs a new KMSNotFoundException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSNotFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of KMSNotFoundException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSNotFoundException.#ctor(System.Exception)">
            <summary>
            Construct instance of KMSNotFoundException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSNotFoundException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of KMSNotFoundException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSNotFoundException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of KMSNotFoundException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.KMSOptInRequiredException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSOptInRequiredException.#ctor(System.String)">
            <summary>
            Constructs a new KMSOptInRequiredException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSOptInRequiredException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of KMSOptInRequiredException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSOptInRequiredException.#ctor(System.Exception)">
            <summary>
            Construct instance of KMSOptInRequiredException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSOptInRequiredException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of KMSOptInRequiredException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSOptInRequiredException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of KMSOptInRequiredException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.KMSThrottlingException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSThrottlingException.#ctor(System.String)">
            <summary>
            Constructs a new KMSThrottlingException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSThrottlingException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of KMSThrottlingException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSThrottlingException.#ctor(System.Exception)">
            <summary>
            Construct instance of KMSThrottlingException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSThrottlingException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of KMSThrottlingException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.KMSThrottlingException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of KMSThrottlingException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ListEndpointsByPlatformApplicationRequest">
            <summary>
            Container for the parameters to the ListEndpointsByPlatformApplication operation.
            Lists the endpoints and endpoint attributes for devices in a supported push notification
            service, such as GCM and APNS. The results for <code>ListEndpointsByPlatformApplication</code>
            are paginated and return a limited list of endpoints, up to 100. If additional records
            are available after the first page results, then a NextToken string will be returned.
            To receive the next page, you call <code>ListEndpointsByPlatformApplication</code>
            again using the NextToken string received from the previous call. When there are no
            more records to return, NextToken will be null. For more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePush.html">Using
            Amazon SNS Mobile Push Notifications</a>. 
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListEndpointsByPlatformApplicationRequest.NextToken">
            <summary>
            Gets and sets the property NextToken. 
            <para>
            NextToken string is used when calling ListEndpointsByPlatformApplication action to
            retrieve additional records that are available after the first page results.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListEndpointsByPlatformApplicationRequest.PlatformApplicationArn">
            <summary>
            Gets and sets the property PlatformApplicationArn. 
            <para>
            PlatformApplicationArn for ListEndpointsByPlatformApplicationInput action.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ListEndpointsByPlatformApplicationResponse">
            <summary>
            Response for ListEndpointsByPlatformApplication action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListEndpointsByPlatformApplicationResponse.Endpoints">
            <summary>
            Gets and sets the property Endpoints. 
            <para>
            Endpoints returned for ListEndpointsByPlatformApplication action.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListEndpointsByPlatformApplicationResponse.NextToken">
            <summary>
            Gets and sets the property NextToken. 
            <para>
            NextToken string is returned when calling ListEndpointsByPlatformApplication action
            if additional records are available after the first page results.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ListPhoneNumbersOptedOutRequest">
            <summary>
            Container for the parameters to the ListPhoneNumbersOptedOut operation.
            Returns a list of phone numbers that are opted out, meaning you cannot send SMS messages
            to them.
            
             
            <para>
            The results for <code>ListPhoneNumbersOptedOut</code> are paginated, and each page
            returns up to 100 phone numbers. If additional phone numbers are available after the
            first page of results, then a <code>NextToken</code> string will be returned. To receive
            the next page, you call <code>ListPhoneNumbersOptedOut</code> again using the <code>NextToken</code>
            string received from the previous call. When there are no more records to return,
            <code>NextToken</code> will be null.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListPhoneNumbersOptedOutRequest.NextToken">
            <summary>
            Gets and sets the property NextToken. 
            <para>
            A <code>NextToken</code> string is used when you call the <code>ListPhoneNumbersOptedOut</code>
            action to retrieve additional records that are available after the first page of results.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ListPhoneNumbersOptedOutResponse">
            <summary>
            The response from the <code>ListPhoneNumbersOptedOut</code> action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListPhoneNumbersOptedOutResponse.NextToken">
            <summary>
            Gets and sets the property NextToken. 
            <para>
            A <code>NextToken</code> string is returned when you call the <code>ListPhoneNumbersOptedOut</code>
            action if additional records are available after the first page of results.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListPhoneNumbersOptedOutResponse.PhoneNumbers">
            <summary>
            Gets and sets the property PhoneNumbers. 
            <para>
            A list of phone numbers that are opted out of receiving SMS messages. The list is
            paginated, and each page can contain up to 100 phone numbers.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ListPlatformApplicationsRequest">
            <summary>
            Container for the parameters to the ListPlatformApplications operation.
            Lists the platform application objects for the supported push notification services,
            such as APNS and GCM. The results for <code>ListPlatformApplications</code> are paginated
            and return a limited list of applications, up to 100. If additional records are available
            after the first page results, then a NextToken string will be returned. To receive
            the next page, you call <code>ListPlatformApplications</code> using the NextToken
            string received from the previous call. When there are no more records to return,
            NextToken will be null. For more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePush.html">Using
            Amazon SNS Mobile Push Notifications</a>. 
            
             
            <para>
            This action is throttled at 15 transactions per second (TPS).
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListPlatformApplicationsRequest.NextToken">
            <summary>
            Gets and sets the property NextToken. 
            <para>
            NextToken string is used when calling ListPlatformApplications action to retrieve
            additional records that are available after the first page results.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ListPlatformApplicationsResponse">
            <summary>
            Response for ListPlatformApplications action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListPlatformApplicationsResponse.NextToken">
            <summary>
            Gets and sets the property NextToken. 
            <para>
            NextToken string is returned when calling ListPlatformApplications action if additional
            records are available after the first page results.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListPlatformApplicationsResponse.PlatformApplications">
            <summary>
            Gets and sets the property PlatformApplications. 
            <para>
            Platform applications returned when calling ListPlatformApplications action.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ListSubscriptionsByTopicRequest">
            <summary>
            Container for the parameters to the ListSubscriptionsByTopic operation.
            Returns a list of the subscriptions to a specific topic. Each call returns a limited
            list of subscriptions, up to 100. If there are more subscriptions, a <code>NextToken</code>
            is also returned. Use the <code>NextToken</code> parameter in a new <code>ListSubscriptionsByTopic</code>
            call to get further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ListSubscriptionsByTopicRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ListSubscriptionsByTopicRequest.#ctor(System.String)">
            <summary>
            Instantiates ListSubscriptionsByTopicRequest with the parameterized properties
            </summary>
            <param name="topicArn">The ARN of the topic for which you wish to find subscriptions.</param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ListSubscriptionsByTopicRequest.#ctor(System.String,System.String)">
            <summary>
            Instantiates ListSubscriptionsByTopicRequest with the parameterized properties
            </summary>
            <param name="topicArn">The ARN of the topic for which you wish to find subscriptions.</param>
            <param name="nextToken">Token returned by the previous <code>ListSubscriptionsByTopic</code> request.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListSubscriptionsByTopicRequest.NextToken">
            <summary>
            Gets and sets the property NextToken. 
            <para>
            Token returned by the previous <code>ListSubscriptionsByTopic</code> request.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListSubscriptionsByTopicRequest.TopicArn">
            <summary>
            Gets and sets the property TopicArn. 
            <para>
            The ARN of the topic for which you wish to find subscriptions.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ListSubscriptionsByTopicResponse">
            <summary>
            Response for ListSubscriptionsByTopic action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListSubscriptionsByTopicResponse.NextToken">
            <summary>
            Gets and sets the property NextToken. 
            <para>
            Token to pass along to the next <code>ListSubscriptionsByTopic</code> request. This
            element is returned if there are more subscriptions to retrieve.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListSubscriptionsByTopicResponse.Subscriptions">
            <summary>
            Gets and sets the property Subscriptions. 
            <para>
            A list of subscriptions.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ListSubscriptionsRequest">
            <summary>
            Container for the parameters to the ListSubscriptions operation.
            Returns a list of the requester's subscriptions. Each call returns a limited list
            of subscriptions, up to 100. If there are more subscriptions, a <code>NextToken</code>
            is also returned. Use the <code>NextToken</code> parameter in a new <code>ListSubscriptions</code>
            call to get further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ListSubscriptionsRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ListSubscriptionsRequest.#ctor(System.String)">
            <summary>
            Instantiates ListSubscriptionsRequest with the parameterized properties
            </summary>
            <param name="nextToken">Token returned by the previous <code>ListSubscriptions</code> request.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListSubscriptionsRequest.NextToken">
            <summary>
            Gets and sets the property NextToken. 
            <para>
            Token returned by the previous <code>ListSubscriptions</code> request.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ListSubscriptionsResponse">
            <summary>
            Response for ListSubscriptions action
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListSubscriptionsResponse.NextToken">
            <summary>
            Gets and sets the property NextToken. 
            <para>
            Token to pass along to the next <code>ListSubscriptions</code> request. This element
            is returned if there are more subscriptions to retrieve.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListSubscriptionsResponse.Subscriptions">
            <summary>
            Gets and sets the property Subscriptions. 
            <para>
            A list of subscriptions.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ListTopicsRequest">
            <summary>
            Container for the parameters to the ListTopics operation.
            Returns a list of the requester's topics. Each call returns a limited list of topics,
            up to 100. If there are more topics, a <code>NextToken</code> is also returned. Use
            the <code>NextToken</code> parameter in a new <code>ListTopics</code> call to get
            further results.
            
             
            <para>
            This action is throttled at 30 transactions per second (TPS).
            </para>
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ListTopicsRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ListTopicsRequest.#ctor(System.String)">
            <summary>
            Instantiates ListTopicsRequest with the parameterized properties
            </summary>
            <param name="nextToken">Token returned by the previous <code>ListTopics</code> request.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListTopicsRequest.NextToken">
            <summary>
            Gets and sets the property NextToken. 
            <para>
            Token returned by the previous <code>ListTopics</code> request.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ListTopicsResponse">
            <summary>
            Response for ListTopics action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListTopicsResponse.NextToken">
            <summary>
            Gets and sets the property NextToken. 
            <para>
            Token to pass along to the next <code>ListTopics</code> request. This element is returned
            if there are additional topics to retrieve.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.ListTopicsResponse.Topics">
            <summary>
            Gets and sets the property Topics. 
            <para>
            A list of topic ARNs.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.MessageAttributeValue">
            <summary>
            The user-specified message attribute value. For string data types, the value attribute
            has the same restrictions on the content as the message body. For more information,
            see <a href="http://docs.aws.amazon.com/sns/latest/api/API_Publish.html">Publish</a>.
            
             
            <para>
            Name, type, and value must not be empty or null. In addition, the message body should
            not be empty or null. All parts of the message attribute, including name, type, and
            value, are included in the message size restriction, which is currently 256 KB (262,144
            bytes). For more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMessageAttributes.html">Using
            Amazon SNS Message Attributes</a>.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.MessageAttributeValue.BinaryValue">
            <summary>
            Gets and sets the property BinaryValue. 
            <para>
            Binary type attributes can store any binary data, for example, compressed data, encrypted
            data, or images.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.MessageAttributeValue.DataType">
            <summary>
            Gets and sets the property DataType. 
            <para>
            Amazon SNS supports the following logical data types: String, String.Array, Number,
            and Binary. For more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMessageAttributes.html#SNSMessageAttributes.DataTypes">Message
            Attribute Data Types</a>.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.MessageAttributeValue.StringValue">
            <summary>
            Gets and sets the property StringValue. 
            <para>
            Strings are Unicode with UTF8 binary encoding. For a list of code values, see <a href="http://en.wikipedia.org/wiki/ASCII#ASCII_printable_characters">http://en.wikipedia.org/wiki/ASCII#ASCII_printable_characters</a>.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.NotFoundException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.NotFoundException.#ctor(System.String)">
            <summary>
            Constructs a new NotFoundException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.NotFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of NotFoundException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.NotFoundException.#ctor(System.Exception)">
            <summary>
            Construct instance of NotFoundException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.NotFoundException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of NotFoundException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.NotFoundException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of NotFoundException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.OptInPhoneNumberRequest">
            <summary>
            Container for the parameters to the OptInPhoneNumber operation.
            Use this request to opt in a phone number that is opted out, which enables you to
            resume sending SMS messages to the number.
            
             
            <para>
            You can opt in a phone number only once every 30 days.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.OptInPhoneNumberRequest.PhoneNumber">
            <summary>
            Gets and sets the property PhoneNumber. 
            <para>
            The phone number to opt in.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.OptInPhoneNumberResponse">
            <summary>
            The response for the OptInPhoneNumber action.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.PlatformApplication">
            <summary>
            Platform application object.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.PlatformApplication.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            Attributes for platform application object.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.PlatformApplication.PlatformApplicationArn">
            <summary>
            Gets and sets the property PlatformApplicationArn. 
            <para>
            PlatformApplicationArn for platform application object.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.PlatformApplicationDisabledException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.PlatformApplicationDisabledException.#ctor(System.String)">
            <summary>
            Constructs a new PlatformApplicationDisabledException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.PlatformApplicationDisabledException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of PlatformApplicationDisabledException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.PlatformApplicationDisabledException.#ctor(System.Exception)">
            <summary>
            Construct instance of PlatformApplicationDisabledException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.PlatformApplicationDisabledException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of PlatformApplicationDisabledException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.PlatformApplicationDisabledException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of PlatformApplicationDisabledException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.PublishRequest">
            <summary>
            Container for the parameters to the Publish operation.
            Sends a message to an Amazon SNS topic or sends a text message (SMS message) directly
            to a phone number. 
            
             
            <para>
            If you send a message to a topic, Amazon SNS delivers the message to each endpoint
            that is subscribed to the topic. The format of the message depends on the notification
            protocol for each subscribed endpoint.
            </para>
             
            <para>
            When a <code>messageId</code> is returned, the message has been saved and Amazon SNS
            will attempt to deliver it shortly.
            </para>
             
            <para>
            To use the <code>Publish</code> action for sending a message to a mobile endpoint,
            such as an app on a Kindle device or mobile phone, you must specify the EndpointArn
            for the TargetArn parameter. The EndpointArn is returned when making a call with the
            <code>CreatePlatformEndpoint</code> action. 
            </para>
             
            <para>
            For more information about formatting messages, see <a href="http://docs.aws.amazon.com/sns/latest/dg/mobile-push-send-custommessage.html">Send
            Custom Platform-Specific Payloads in Messages to Mobile Devices</a>. 
            </para>
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.PublishRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.PublishRequest.#ctor(System.String,System.String)">
            <summary>
            Instantiates PublishRequest with the parameterized properties
            </summary>
            <param name="topicArn">The topic you want to publish to. If you don't specify a value for the <code>TopicArn</code> parameter, you must specify a value for the <code>PhoneNumber</code> or <code>TargetArn</code> parameters.</param>
            <param name="message">The message you want to send. <important> The <code>Message</code> parameter is always a string. If you set <code>MessageStructure</code> to <code>json</code>, you must string-encode the <code>Message</code> parameter. </important> If you are publishing to a topic and you want to send the same message to all transport protocols, include the text of the message as a String value. If you want to send different messages for each transport protocol, set the value of the <code>MessageStructure</code> parameter to <code>json</code> and use a JSON object for the <code>Message</code> parameter.  <p/> Constraints: <ul> <li> With the exception of SMS, messages must be UTF-8 encoded strings and at most 256 KB in size (262,144 bytes, not 262,144 characters). </li> <li> For SMS, each message can contain up to 140 characters. This character limit depends on the encoding schema. For example, an SMS message can contain 160 GSM characters, 140 ASCII characters, or 70 UCS-2 characters. If you publish a message that exceeds this size limit, Amazon SNS sends the message as multiple messages, each fitting within the size limit. Messages aren't truncated mid-word but are cut off at whole-word boundaries. The total size limit for a single SMS <code>Publish</code> action is 1,600 characters. </li> </ul> JSON-specific constraints: <ul> <li> Keys in the JSON object that correspond to supported transport protocols must have simple JSON string values. </li> <li> The values will be parsed (unescaped) before they are used in outgoing messages. </li> <li> Outbound notifications are JSON encoded (meaning that the characters will be reescaped for sending). </li> <li> Values have a minimum length of 0 (the empty string, "", is allowed). </li> <li> Values have a maximum length bounded by the overall message size (so, including multiple protocols may limit message sizes). </li> <li> Non-string values will cause the key to be ignored. </li> <li> Keys that do not correspond to supported transport protocols are ignored. </li> <li> Duplicate keys are not allowed. </li> <li> Failure to parse or validate any key or value in the message will cause the <code>Publish</code> call to return an error (no partial delivery). </li> </ul></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.PublishRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Instantiates PublishRequest with the parameterized properties
            </summary>
            <param name="topicArn">The topic you want to publish to. If you don't specify a value for the <code>TopicArn</code> parameter, you must specify a value for the <code>PhoneNumber</code> or <code>TargetArn</code> parameters.</param>
            <param name="message">The message you want to send. <important> The <code>Message</code> parameter is always a string. If you set <code>MessageStructure</code> to <code>json</code>, you must string-encode the <code>Message</code> parameter. </important> If you are publishing to a topic and you want to send the same message to all transport protocols, include the text of the message as a String value. If you want to send different messages for each transport protocol, set the value of the <code>MessageStructure</code> parameter to <code>json</code> and use a JSON object for the <code>Message</code> parameter.  <p/> Constraints: <ul> <li> With the exception of SMS, messages must be UTF-8 encoded strings and at most 256 KB in size (262,144 bytes, not 262,144 characters). </li> <li> For SMS, each message can contain up to 140 characters. This character limit depends on the encoding schema. For example, an SMS message can contain 160 GSM characters, 140 ASCII characters, or 70 UCS-2 characters. If you publish a message that exceeds this size limit, Amazon SNS sends the message as multiple messages, each fitting within the size limit. Messages aren't truncated mid-word but are cut off at whole-word boundaries. The total size limit for a single SMS <code>Publish</code> action is 1,600 characters. </li> </ul> JSON-specific constraints: <ul> <li> Keys in the JSON object that correspond to supported transport protocols must have simple JSON string values. </li> <li> The values will be parsed (unescaped) before they are used in outgoing messages. </li> <li> Outbound notifications are JSON encoded (meaning that the characters will be reescaped for sending). </li> <li> Values have a minimum length of 0 (the empty string, "", is allowed). </li> <li> Values have a maximum length bounded by the overall message size (so, including multiple protocols may limit message sizes). </li> <li> Non-string values will cause the key to be ignored. </li> <li> Keys that do not correspond to supported transport protocols are ignored. </li> <li> Duplicate keys are not allowed. </li> <li> Failure to parse or validate any key or value in the message will cause the <code>Publish</code> call to return an error (no partial delivery). </li> </ul></param>
            <param name="subject">Optional parameter to be used as the "Subject" line when the message is delivered to email endpoints. This field will also be included, if present, in the standard JSON messages delivered to other endpoints. Constraints: Subjects must be ASCII text that begins with a letter, number, or punctuation mark; must not include line breaks or control characters; and must be less than 100 characters long.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.PublishRequest.Message">
            <summary>
            Gets and sets the property Message. 
            <para>
            The message you want to send.
            </para>
             <important> 
            <para>
            The <code>Message</code> parameter is always a string. If you set <code>MessageStructure</code>
            to <code>json</code>, you must string-encode the <code>Message</code> parameter.
            </para>
             </important> 
            <para>
            If you are publishing to a topic and you want to send the same message to all transport
            protocols, include the text of the message as a String value. If you want to send
            different messages for each transport protocol, set the value of the <code>MessageStructure</code>
            parameter to <code>json</code> and use a JSON object for the <code>Message</code>
            parameter. 
            </para>
              
            <para>
            Constraints:
            </para>
             <ul> <li> 
            <para>
            With the exception of SMS, messages must be UTF-8 encoded strings and at most 256
            KB in size (262,144 bytes, not 262,144 characters).
            </para>
             </li> <li> 
            <para>
            For SMS, each message can contain up to 140 characters. This character limit depends
            on the encoding schema. For example, an SMS message can contain 160 GSM characters,
            140 ASCII characters, or 70 UCS-2 characters.
            </para>
             
            <para>
            If you publish a message that exceeds this size limit, Amazon SNS sends the message
            as multiple messages, each fitting within the size limit. Messages aren't truncated
            mid-word but are cut off at whole-word boundaries.
            </para>
             
            <para>
            The total size limit for a single SMS <code>Publish</code> action is 1,600 characters.
            </para>
             </li> </ul> 
            <para>
            JSON-specific constraints:
            </para>
             <ul> <li> 
            <para>
            Keys in the JSON object that correspond to supported transport protocols must have
            simple JSON string values.
            </para>
             </li> <li> 
            <para>
            The values will be parsed (unescaped) before they are used in outgoing messages.
            </para>
             </li> <li> 
            <para>
            Outbound notifications are JSON encoded (meaning that the characters will be reescaped
            for sending).
            </para>
             </li> <li> 
            <para>
            Values have a minimum length of 0 (the empty string, "", is allowed).
            </para>
             </li> <li> 
            <para>
            Values have a maximum length bounded by the overall message size (so, including multiple
            protocols may limit message sizes).
            </para>
             </li> <li> 
            <para>
            Non-string values will cause the key to be ignored.
            </para>
             </li> <li> 
            <para>
            Keys that do not correspond to supported transport protocols are ignored.
            </para>
             </li> <li> 
            <para>
            Duplicate keys are not allowed.
            </para>
             </li> <li> 
            <para>
            Failure to parse or validate any key or value in the message will cause the <code>Publish</code>
            call to return an error (no partial delivery).
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.PublishRequest.MessageAttributes">
            <summary>
            Gets and sets the property MessageAttributes. 
            <para>
            Message attributes for Publish action.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.PublishRequest.MessageStructure">
            <summary>
            Gets and sets the property MessageStructure. 
            <para>
            Set <code>MessageStructure</code> to <code>json</code> if you want to send a different
            message for each protocol. For example, using one publish action, you can send a short
            message to your SMS subscribers and a longer message to your email subscribers. If
            you set <code>MessageStructure</code> to <code>json</code>, the value of the <code>Message</code>
            parameter must: 
            </para>
             <ul> <li> 
            <para>
            be a syntactically valid JSON object; and
            </para>
             </li> <li> 
            <para>
            contain at least a top-level JSON key of "default" with a value that is a string.
            </para>
             </li> </ul> 
            <para>
            You can define other top-level keys that define the message you want to send to a
            specific transport protocol (e.g., "http").
            </para>
             
            <para>
            For information about sending different messages for each protocol using the AWS Management
            Console, go to <a href="http://docs.aws.amazon.com/sns/latest/gsg/Publish.html#sns-message-formatting-by-protocol">Create
            Different Messages for Each Protocol</a> in the <i>Amazon Simple Notification Service
            Getting Started Guide</i>. 
            </para>
             
            <para>
            Valid value: <code>json</code> 
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.PublishRequest.PhoneNumber">
            <summary>
            Gets and sets the property PhoneNumber. 
            <para>
            The phone number to which you want to deliver an SMS message. Use E.164 format.
            </para>
             
            <para>
            If you don't specify a value for the <code>PhoneNumber</code> parameter, you must
            specify a value for the <code>TargetArn</code> or <code>TopicArn</code> parameters.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.PublishRequest.Subject">
            <summary>
            Gets and sets the property Subject. 
            <para>
            Optional parameter to be used as the "Subject" line when the message is delivered
            to email endpoints. This field will also be included, if present, in the standard
            JSON messages delivered to other endpoints.
            </para>
             
            <para>
            Constraints: Subjects must be ASCII text that begins with a letter, number, or punctuation
            mark; must not include line breaks or control characters; and must be less than 100
            characters long.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.PublishRequest.TargetArn">
            <summary>
            Gets and sets the property TargetArn. 
            <para>
            Either TopicArn or EndpointArn, but not both.
            </para>
             
            <para>
            If you don't specify a value for the <code>TargetArn</code> parameter, you must specify
            a value for the <code>PhoneNumber</code> or <code>TopicArn</code> parameters.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.PublishRequest.TopicArn">
            <summary>
            Gets and sets the property TopicArn. 
            <para>
            The topic you want to publish to.
            </para>
             
            <para>
            If you don't specify a value for the <code>TopicArn</code> parameter, you must specify
            a value for the <code>PhoneNumber</code> or <code>TargetArn</code> parameters.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.PublishResponse">
            <summary>
            Response for Publish action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.PublishResponse.MessageId">
            <summary>
            Gets and sets the property MessageId. 
            <para>
            Unique identifier assigned to the published message.
            </para>
             
            <para>
            Length Constraint: Maximum 100 characters
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.RemovePermissionRequest">
            <summary>
            Container for the parameters to the RemovePermission operation.
            Removes a statement from a topic's access control policy.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.RemovePermissionRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.RemovePermissionRequest.#ctor(System.String,System.String)">
            <summary>
            Instantiates RemovePermissionRequest with the parameterized properties
            </summary>
            <param name="topicArn">The ARN of the topic whose access control policy you wish to modify.</param>
            <param name="label">The unique label of the statement you want to remove.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.RemovePermissionRequest.Label">
            <summary>
            Gets and sets the property Label. 
            <para>
            The unique label of the statement you want to remove.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.RemovePermissionRequest.TopicArn">
            <summary>
            Gets and sets the property TopicArn. 
            <para>
            The ARN of the topic whose access control policy you wish to modify.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.RemovePermissionResponse">
            <summary>
            This is the response object from the RemovePermission operation.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SetEndpointAttributesRequest">
            <summary>
            Container for the parameters to the SetEndpointAttributes operation.
            Sets the attributes for an endpoint for a device on one of the supported push notification
            services, such as GCM and APNS. For more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePush.html">Using
            Amazon SNS Mobile Push Notifications</a>.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SetEndpointAttributesRequest.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            A map of the endpoint attributes. Attributes in this map include the following:
            </para>
             <ul> <li> 
            <para>
             <code>CustomUserData</code> – arbitrary user data to associate with the endpoint.
            Amazon SNS does not use this data. The data must be in UTF-8 format and less than
            2KB.
            </para>
             </li> <li> 
            <para>
             <code>Enabled</code> – flag that enables/disables delivery to the endpoint. Amazon
            SNS will set this to false when a notification service indicates to Amazon SNS that
            the endpoint is invalid. Users can set it back to true, typically after updating Token.
            </para>
             </li> <li> 
            <para>
             <code>Token</code> – device token, also referred to as a registration id, for an
            app and mobile device. This is returned from the notification service when an app
            and mobile device are registered with the notification service.
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SetEndpointAttributesRequest.EndpointArn">
            <summary>
            Gets and sets the property EndpointArn. 
            <para>
            EndpointArn used for SetEndpointAttributes action.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SetEndpointAttributesResponse">
            <summary>
            This is the response object from the SetEndpointAttributes operation.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SetPlatformApplicationAttributesRequest">
            <summary>
            Container for the parameters to the SetPlatformApplicationAttributes operation.
            Sets the attributes of the platform application object for the supported push notification
            services, such as APNS and GCM. For more information, see <a href="http://docs.aws.amazon.com/sns/latest/dg/SNSMobilePush.html">Using
            Amazon SNS Mobile Push Notifications</a>. For information on configuring attributes
            for message delivery status, see <a href="http://docs.aws.amazon.com/sns/latest/dg/sns-msg-status.html">Using
            Amazon SNS Application Attributes for Message Delivery Status</a>.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SetPlatformApplicationAttributesRequest.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            A map of the platform application attributes. Attributes in this map include the following:
            </para>
             <ul> <li> 
            <para>
             <code>PlatformCredential</code> – The credential received from the notification service.
            For APNS/APNS_SANDBOX, PlatformCredential is private key. For GCM, PlatformCredential
            is "API key". For ADM, PlatformCredential is "client secret".
            </para>
             </li> <li> 
            <para>
             <code>PlatformPrincipal</code> – The principal received from the notification service.
            For APNS/APNS_SANDBOX, PlatformPrincipal is SSL certificate. For GCM, PlatformPrincipal
            is not applicable. For ADM, PlatformPrincipal is "client id".
            </para>
             </li> <li> 
            <para>
             <code>EventEndpointCreated</code> – Topic ARN to which EndpointCreated event notifications
            should be sent.
            </para>
             </li> <li> 
            <para>
             <code>EventEndpointDeleted</code> – Topic ARN to which EndpointDeleted event notifications
            should be sent.
            </para>
             </li> <li> 
            <para>
             <code>EventEndpointUpdated</code> – Topic ARN to which EndpointUpdate event notifications
            should be sent.
            </para>
             </li> <li> 
            <para>
             <code>EventDeliveryFailure</code> – Topic ARN to which DeliveryFailure event notifications
            should be sent upon Direct Publish delivery failure (permanent) to one of the application's
            endpoints.
            </para>
             </li> <li> 
            <para>
             <code>SuccessFeedbackRoleArn</code> – IAM role ARN used to give Amazon SNS write
            access to use CloudWatch Logs on your behalf.
            </para>
             </li> <li> 
            <para>
             <code>FailureFeedbackRoleArn</code> – IAM role ARN used to give Amazon SNS write
            access to use CloudWatch Logs on your behalf.
            </para>
             </li> <li> 
            <para>
             <code>SuccessFeedbackSampleRate</code> – Sample rate percentage (0-100) of successfully
            delivered messages.
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SetPlatformApplicationAttributesRequest.PlatformApplicationArn">
            <summary>
            Gets and sets the property PlatformApplicationArn. 
            <para>
            PlatformApplicationArn for SetPlatformApplicationAttributes action.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SetPlatformApplicationAttributesResponse">
            <summary>
            This is the response object from the SetPlatformApplicationAttributes operation.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SetSMSAttributesRequest">
            <summary>
            Container for the parameters to the SetSMSAttributes operation.
            Use this request to set the default settings for sending SMS messages and receiving
            daily SMS usage reports.
            
             
            <para>
            You can override some of these settings for a single message when you use the <code>Publish</code>
            action with the <code>MessageAttributes.entry.N</code> parameter. For more information,
            see <a href="http://docs.aws.amazon.com/sns/latest/dg/sms_publish-to-phone.html">Sending
            an SMS Message</a> in the <i>Amazon SNS Developer Guide</i>.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SetSMSAttributesRequest.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            The default settings for sending SMS messages from your account. You can set values
            for the following attribute names:
            </para>
             
            <para>
             <code>MonthlySpendLimit</code> – The maximum amount in USD that you are willing to
            spend each month to send SMS messages. When Amazon SNS determines that sending an
            SMS message would incur a cost that exceeds this limit, it stops sending SMS messages
            within minutes.
            </para>
             <important> 
            <para>
            Amazon SNS stops sending SMS messages within minutes of the limit being crossed. During
            that interval, if you continue to send SMS messages, you will incur costs that exceed
            your limit.
            </para>
             </important> 
            <para>
            By default, the spend limit is set to the maximum allowed by Amazon SNS. If you want
            to raise the limit, submit an <a href="https://console.aws.amazon.com/support/home#/case/create?issueType=service-limit-increase&amp;limitType=service-code-sns">SNS
            Limit Increase case</a>. For <b>New limit value</b>, enter your desired monthly spend
            limit. In the <b>Use Case Description</b> field, explain that you are requesting an
            SMS monthly spend limit increase.
            </para>
             
            <para>
             <code>DeliveryStatusIAMRole</code> – The ARN of the IAM role that allows Amazon SNS
            to write logs about SMS deliveries in CloudWatch Logs. For each SMS message that you
            send, Amazon SNS writes a log that includes the message price, the success or failure
            status, the reason for failure (if the message failed), the message dwell time, and
            other information.
            </para>
             
            <para>
             <code>DeliveryStatusSuccessSamplingRate</code> – The percentage of successful SMS
            deliveries for which Amazon SNS will write logs in CloudWatch Logs. The value can
            be an integer from 0 - 100. For example, to write logs only for failed deliveries,
            set this value to <code>0</code>. To write logs for 10% of your successful deliveries,
            set it to <code>10</code>.
            </para>
             
            <para>
             <code>DefaultSenderID</code> – A string, such as your business brand, that is displayed
            as the sender on the receiving device. Support for sender IDs varies by country. The
            sender ID can be 1 - 11 alphanumeric characters, and it must contain at least one
            letter.
            </para>
             
            <para>
             <code>DefaultSMSType</code> – The type of SMS message that you will send by default.
            You can assign the following values:
            </para>
             <ul> <li> 
            <para>
             <code>Promotional</code> – (Default) Noncritical messages, such as marketing messages.
            Amazon SNS optimizes the message delivery to incur the lowest cost.
            </para>
             </li> <li> 
            <para>
             <code>Transactional</code> – Critical messages that support customer transactions,
            such as one-time passcodes for multi-factor authentication. Amazon SNS optimizes the
            message delivery to achieve the highest reliability.
            </para>
             </li> </ul> 
            <para>
             <code>UsageReportS3Bucket</code> – The name of the Amazon S3 bucket to receive daily
            SMS usage reports from Amazon SNS. Each day, Amazon SNS will deliver a usage report
            as a CSV file to the bucket. The report includes the following information for each
            SMS message that was successfully delivered by your account:
            </para>
             <ul> <li> 
            <para>
            Time that the message was published (in UTC)
            </para>
             </li> <li> 
            <para>
            Message ID
            </para>
             </li> <li> 
            <para>
            Destination phone number
            </para>
             </li> <li> 
            <para>
            Message type
            </para>
             </li> <li> 
            <para>
            Delivery status
            </para>
             </li> <li> 
            <para>
            Message price (in USD)
            </para>
             </li> <li> 
            <para>
            Part number (a message is split into multiple parts if it is too long for a single
            message)
            </para>
             </li> <li> 
            <para>
            Total number of parts
            </para>
             </li> </ul> 
            <para>
            To receive the report, the bucket must have a policy that allows the Amazon SNS service
            principle to perform the <code>s3:PutObject</code> and <code>s3:GetBucketLocation</code>
            actions.
            </para>
             
            <para>
            For an example bucket policy and usage report, see <a href="http://docs.aws.amazon.com/sns/latest/dg/sms_stats.html">Monitoring
            SMS Activity</a> in the <i>Amazon SNS Developer Guide</i>.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SetSMSAttributesResponse">
            <summary>
            The response for the SetSMSAttributes action.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SetSubscriptionAttributesRequest">
            <summary>
            Container for the parameters to the SetSubscriptionAttributes operation.
            Allows a subscription owner to set an attribute of the subscription to a new value.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.SetSubscriptionAttributesRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.SetSubscriptionAttributesRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Instantiates SetSubscriptionAttributesRequest with the parameterized properties
            </summary>
            <param name="subscriptionArn">The ARN of the subscription to modify.</param>
            <param name="attributeName">A map of attributes with their corresponding values. The following lists the names, descriptions, and values of the special request parameters that the <code>SetTopicAttributes</code> action uses: <ul> <li>  <code>DeliveryPolicy</code> – The policy that defines how Amazon SNS retries failed deliveries to HTTP/S endpoints. </li> <li>  <code>FilterPolicy</code> – The simple JSON object that lets your subscriber receive only a subset of messages, rather than receiving every message published to the topic. </li> <li>  <code>RawMessageDelivery</code> – When set to <code>true</code>, enables raw message delivery to Amazon SQS or HTTP/S endpoints. This eliminates the need for the endpoints to process JSON formatting, which is otherwise created for Amazon SNS metadata. </li> </ul></param>
            <param name="attributeValue">The new value for the attribute in JSON format.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SetSubscriptionAttributesRequest.AttributeName">
            <summary>
            Gets and sets the property AttributeName. 
            <para>
            A map of attributes with their corresponding values.
            </para>
             
            <para>
            The following lists the names, descriptions, and values of the special request parameters
            that the <code>SetTopicAttributes</code> action uses:
            </para>
             <ul> <li> 
            <para>
             <code>DeliveryPolicy</code> – The policy that defines how Amazon SNS retries failed
            deliveries to HTTP/S endpoints.
            </para>
             </li> <li> 
            <para>
             <code>FilterPolicy</code> – The simple JSON object that lets your subscriber receive
            only a subset of messages, rather than receiving every message published to the topic.
            </para>
             </li> <li> 
            <para>
             <code>RawMessageDelivery</code> – When set to <code>true</code>, enables raw message
            delivery to Amazon SQS or HTTP/S endpoints. This eliminates the need for the endpoints
            to process JSON formatting, which is otherwise created for Amazon SNS metadata.
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SetSubscriptionAttributesRequest.AttributeValue">
            <summary>
            Gets and sets the property AttributeValue. 
            <para>
            The new value for the attribute in JSON format.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SetSubscriptionAttributesRequest.SubscriptionArn">
            <summary>
            Gets and sets the property SubscriptionArn. 
            <para>
            The ARN of the subscription to modify.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SetSubscriptionAttributesResponse">
            <summary>
            This is the response object from the SetSubscriptionAttributes operation.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SetTopicAttributesRequest">
            <summary>
            Container for the parameters to the SetTopicAttributes operation.
            Allows a topic owner to set an attribute of the topic to a new value.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.SetTopicAttributesRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.SetTopicAttributesRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Instantiates SetTopicAttributesRequest with the parameterized properties
            </summary>
            <param name="topicArn">The ARN of the topic to modify.</param>
            <param name="attributeName">A map of attributes with their corresponding values. The following lists the names, descriptions, and values of the special request parameters that the <code>SetTopicAttributes</code> action uses: <ul> <li>  <code>DeliveryPolicy</code> – The policy that defines how Amazon SNS retries failed deliveries to HTTP/S endpoints. </li> <li>  <code>DisplayName</code> – The display name to use for a topic with SMS subscriptions. </li> <li>  <code>Policy</code> – The policy that defines who can access your topic. By default, only the topic owner can publish or subscribe to the topic. </li> </ul></param>
            <param name="attributeValue">The new value for the attribute.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SetTopicAttributesRequest.AttributeName">
            <summary>
            Gets and sets the property AttributeName. 
            <para>
            A map of attributes with their corresponding values.
            </para>
             
            <para>
            The following lists the names, descriptions, and values of the special request parameters
            that the <code>SetTopicAttributes</code> action uses:
            </para>
             <ul> <li> 
            <para>
             <code>DeliveryPolicy</code> – The policy that defines how Amazon SNS retries failed
            deliveries to HTTP/S endpoints.
            </para>
             </li> <li> 
            <para>
             <code>DisplayName</code> – The display name to use for a topic with SMS subscriptions.
            </para>
             </li> <li> 
            <para>
             <code>Policy</code> – The policy that defines who can access your topic. By default,
            only the topic owner can publish or subscribe to the topic.
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SetTopicAttributesRequest.AttributeValue">
            <summary>
            Gets and sets the property AttributeValue. 
            <para>
            The new value for the attribute.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SetTopicAttributesRequest.TopicArn">
            <summary>
            Gets and sets the property TopicArn. 
            <para>
            The ARN of the topic to modify.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SetTopicAttributesResponse">
            <summary>
            This is the response object from the SetTopicAttributes operation.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SubscribeRequest">
            <summary>
            Container for the parameters to the Subscribe operation.
            Prepares to subscribe an endpoint by sending the endpoint a confirmation message.
            To actually create a subscription, the endpoint owner must call the <code>ConfirmSubscription</code>
            action with the token from the confirmation message. Confirmation tokens are valid
            for three days.
            
             
            <para>
            This action is throttled at 100 transactions per second (TPS).
            </para>
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.SubscribeRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.SubscribeRequest.#ctor(System.String,System.String,System.String)">
            <summary>
            Instantiates SubscribeRequest with the parameterized properties
            </summary>
            <param name="topicArn">The ARN of the topic you want to subscribe to.</param>
            <param name="protocol">The protocol you want to use. Supported protocols include: <ul> <li>  <code>http</code> – delivery of JSON-encoded message via HTTP POST </li> <li>  <code>https</code> – delivery of JSON-encoded message via HTTPS POST </li> <li>  <code>email</code> – delivery of message via SMTP </li> <li>  <code>email-json</code> – delivery of JSON-encoded message via SMTP </li> <li>  <code>sms</code> – delivery of message via SMS </li> <li>  <code>sqs</code> – delivery of JSON-encoded message to an Amazon SQS queue </li> <li>  <code>application</code> – delivery of JSON-encoded message to an EndpointArn for a mobile app and device. </li> <li>  <code>lambda</code> – delivery of JSON-encoded message to an AWS Lambda function. </li> </ul></param>
            <param name="endpoint">The endpoint that you want to receive notifications. Endpoints vary by protocol: <ul> <li> For the <code>http</code> protocol, the endpoint is an URL beginning with "http://" </li> <li> For the <code>https</code> protocol, the endpoint is a URL beginning with "https://" </li> <li> For the <code>email</code> protocol, the endpoint is an email address </li> <li> For the <code>email-json</code> protocol, the endpoint is an email address </li> <li> For the <code>sms</code> protocol, the endpoint is a phone number of an SMS-enabled device </li> <li> For the <code>sqs</code> protocol, the endpoint is the ARN of an Amazon SQS queue </li> <li> For the <code>application</code> protocol, the endpoint is the EndpointArn of a mobile app and device. </li> <li> For the <code>lambda</code> protocol, the endpoint is the ARN of an AWS Lambda function. </li> </ul></param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SubscribeRequest.Attributes">
            <summary>
            Gets and sets the property Attributes. 
            <para>
            A map of attributes with their corresponding values.
            </para>
             
            <para>
            The following lists the names, descriptions, and values of the special request parameters
            that the <code>SetTopicAttributes</code> action uses:
            </para>
             <ul> <li> 
            <para>
             <code>DeliveryPolicy</code> – The policy that defines how Amazon SNS retries failed
            deliveries to HTTP/S endpoints.
            </para>
             </li> <li> 
            <para>
             <code>FilterPolicy</code> – The simple JSON object that lets your subscriber receive
            only a subset of messages, rather than receiving every message published to the topic.
            </para>
             </li> <li> 
            <para>
             <code>RawMessageDelivery</code> – When set to <code>true</code>, enables raw message
            delivery to Amazon SQS or HTTP/S endpoints. This eliminates the need for the endpoints
            to process JSON formatting, which is otherwise created for Amazon SNS metadata.
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SubscribeRequest.Endpoint">
            <summary>
            Gets and sets the property Endpoint. 
            <para>
            The endpoint that you want to receive notifications. Endpoints vary by protocol:
            </para>
             <ul> <li> 
            <para>
            For the <code>http</code> protocol, the endpoint is an URL beginning with "http://"
            </para>
             </li> <li> 
            <para>
            For the <code>https</code> protocol, the endpoint is a URL beginning with "https://"
            </para>
             </li> <li> 
            <para>
            For the <code>email</code> protocol, the endpoint is an email address
            </para>
             </li> <li> 
            <para>
            For the <code>email-json</code> protocol, the endpoint is an email address
            </para>
             </li> <li> 
            <para>
            For the <code>sms</code> protocol, the endpoint is a phone number of an SMS-enabled
            device
            </para>
             </li> <li> 
            <para>
            For the <code>sqs</code> protocol, the endpoint is the ARN of an Amazon SQS queue
            </para>
             </li> <li> 
            <para>
            For the <code>application</code> protocol, the endpoint is the EndpointArn of a mobile
            app and device.
            </para>
             </li> <li> 
            <para>
            For the <code>lambda</code> protocol, the endpoint is the ARN of an AWS Lambda function.
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SubscribeRequest.Protocol">
            <summary>
            Gets and sets the property Protocol. 
            <para>
            The protocol you want to use. Supported protocols include:
            </para>
             <ul> <li> 
            <para>
             <code>http</code> – delivery of JSON-encoded message via HTTP POST
            </para>
             </li> <li> 
            <para>
             <code>https</code> – delivery of JSON-encoded message via HTTPS POST
            </para>
             </li> <li> 
            <para>
             <code>email</code> – delivery of message via SMTP
            </para>
             </li> <li> 
            <para>
             <code>email-json</code> – delivery of JSON-encoded message via SMTP
            </para>
             </li> <li> 
            <para>
             <code>sms</code> – delivery of message via SMS
            </para>
             </li> <li> 
            <para>
             <code>sqs</code> – delivery of JSON-encoded message to an Amazon SQS queue
            </para>
             </li> <li> 
            <para>
             <code>application</code> – delivery of JSON-encoded message to an EndpointArn for
            a mobile app and device.
            </para>
             </li> <li> 
            <para>
             <code>lambda</code> – delivery of JSON-encoded message to an AWS Lambda function.
            </para>
             </li> </ul>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SubscribeRequest.ReturnSubscriptionArn">
            <summary>
            Gets and sets the property ReturnSubscriptionArn. 
            <para>
            Sets whether the response from the <code>Subscribe</code> request includes the subscription
            ARN, even if the subscription is not yet confirmed.
            </para>
             
            <para>
            If you set this parameter to <code>false</code>, the response includes the ARN for
            confirmed subscriptions, but it includes an ARN value of "pending subscription" for
            subscriptions that are not yet confirmed. A subscription becomes confirmed when the
            subscriber calls the <code>ConfirmSubscription</code> action with a confirmation token.
            </para>
             
            <para>
            If you set this parameter to <code>true</code>, the response includes the ARN in all
            cases, even if the subscription is not yet confirmed.
            </para>
             
            <para>
            The default value is <code>false</code>.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SubscribeRequest.TopicArn">
            <summary>
            Gets and sets the property TopicArn. 
            <para>
            The ARN of the topic you want to subscribe to.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SubscribeResponse">
            <summary>
            Response for Subscribe action.
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.SubscribeResponse.SubscriptionArn">
            <summary>
            Gets and sets the property SubscriptionArn. 
            <para>
            The ARN of the subscription if it is confirmed, or the string "pending confirmation"
            if the subscription requires confirmation. However, if the API request parameter <code>ReturnSubscriptionArn</code>
            is true, then the value is always the subscription ARN, even if the subscription requires
            confirmation.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Subscription">
            <summary>
            A wrapper type for the attributes of an Amazon SNS subscription.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Subscription.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Subscription.Endpoint">
            <summary>
            Gets and sets the property Endpoint. 
            <para>
            The subscription's endpoint (format depends on the protocol).
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Subscription.Owner">
            <summary>
            Gets and sets the property Owner. 
            <para>
            The subscription's owner.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Subscription.Protocol">
            <summary>
            Gets and sets the property Protocol. 
            <para>
            The subscription's protocol.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Subscription.SubscriptionArn">
            <summary>
            Gets and sets the property SubscriptionArn. 
            <para>
            The subscription's ARN.
            </para>
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Subscription.TopicArn">
            <summary>
            Gets and sets the property TopicArn. 
            <para>
            The ARN of the subscription's topic.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.SubscriptionLimitExceededException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.SubscriptionLimitExceededException.#ctor(System.String)">
            <summary>
            Constructs a new SubscriptionLimitExceededException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.SubscriptionLimitExceededException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of SubscriptionLimitExceededException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.SubscriptionLimitExceededException.#ctor(System.Exception)">
            <summary>
            Construct instance of SubscriptionLimitExceededException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.SubscriptionLimitExceededException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of SubscriptionLimitExceededException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.SubscriptionLimitExceededException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of SubscriptionLimitExceededException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.ThrottledException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ThrottledException.#ctor(System.String)">
            <summary>
            Constructs a new ThrottledException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ThrottledException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of ThrottledException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ThrottledException.#ctor(System.Exception)">
            <summary>
            Construct instance of ThrottledException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ThrottledException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of ThrottledException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.ThrottledException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of ThrottledException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Topic">
            <summary>
            A wrapper type for the topic's Amazon Resource Name (ARN). To retrieve a topic's attributes,
            use <code>GetTopicAttributes</code>.
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Topic.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Topic.TopicArn">
            <summary>
            Gets and sets the property TopicArn. 
            <para>
            The topic's ARN.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.TopicLimitExceededException">
            <summary>
             SimpleNotificationService exception
             </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.TopicLimitExceededException.#ctor(System.String)">
            <summary>
            Constructs a new TopicLimitExceededException with the specified error
            message.
            </summary>
            <param name="message">
            Describes the error encountered.
            </param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.TopicLimitExceededException.#ctor(System.String,System.Exception)">
            <summary>
            Construct instance of TopicLimitExceededException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.TopicLimitExceededException.#ctor(System.Exception)">
            <summary>
            Construct instance of TopicLimitExceededException
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.TopicLimitExceededException.#ctor(System.String,System.Exception,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of TopicLimitExceededException
            </summary>
            <param name="message"></param>
            <param name="innerException"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.TopicLimitExceededException.#ctor(System.String,Amazon.Runtime.ErrorType,System.String,System.String,System.Net.HttpStatusCode)">
            <summary>
            Construct instance of TopicLimitExceededException
            </summary>
            <param name="message"></param>
            <param name="errorType"></param>
            <param name="errorCode"></param>
            <param name="requestId"></param>
            <param name="statusCode"></param>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.UnsubscribeRequest">
            <summary>
            Container for the parameters to the Unsubscribe operation.
            Deletes a subscription. If the subscription requires authentication for deletion,
            only the owner of the subscription or the topic's owner can unsubscribe, and an AWS
            signature is required. If the <code>Unsubscribe</code> call does not require authentication
            and the requester is not the subscription owner, a final cancellation message is delivered
            to the endpoint, so that the endpoint owner can easily resubscribe to the topic if
            the <code>Unsubscribe</code> request was unintended.
            
             
            <para>
            This action is throttled at 100 transactions per second (TPS).
            </para>
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.UnsubscribeRequest.#ctor">
            <summary>
            Empty constructor used to set  properties independently even when a simple constructor is available
            </summary>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.UnsubscribeRequest.#ctor(System.String)">
            <summary>
            Instantiates UnsubscribeRequest with the parameterized properties
            </summary>
            <param name="subscriptionArn">The ARN of the subscription to be deleted.</param>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.UnsubscribeRequest.SubscriptionArn">
            <summary>
            Gets and sets the property SubscriptionArn. 
            <para>
            The ARN of the subscription to be deleted.
            </para>
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.UnsubscribeResponse">
            <summary>
            This is the response object from the Unsubscribe operation.
            </summary>
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.AddPermissionRequestMarshaller">
            <summary>
            AddPermission Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.AddPermissionRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.AddPermissionRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.AddPermissionRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.AddPermissionRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.AddPermissionResponseUnmarshaller">
            <summary>
            Response Unmarshaller for AddPermission operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.AddPermissionResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.AddPermissionResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.AddPermissionResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CheckIfPhoneNumberIsOptedOutRequestMarshaller">
            <summary>
            CheckIfPhoneNumberIsOptedOut Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CheckIfPhoneNumberIsOptedOutRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CheckIfPhoneNumberIsOptedOutRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.CheckIfPhoneNumberIsOptedOutRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CheckIfPhoneNumberIsOptedOutRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CheckIfPhoneNumberIsOptedOutResponseUnmarshaller">
            <summary>
            Response Unmarshaller for CheckIfPhoneNumberIsOptedOut operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CheckIfPhoneNumberIsOptedOutResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CheckIfPhoneNumberIsOptedOutResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CheckIfPhoneNumberIsOptedOutResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ConfirmSubscriptionRequestMarshaller">
            <summary>
            ConfirmSubscription Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ConfirmSubscriptionRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ConfirmSubscriptionRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.ConfirmSubscriptionRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ConfirmSubscriptionRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ConfirmSubscriptionResponseUnmarshaller">
            <summary>
            Response Unmarshaller for ConfirmSubscription operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ConfirmSubscriptionResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ConfirmSubscriptionResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ConfirmSubscriptionResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformApplicationRequestMarshaller">
            <summary>
            CreatePlatformApplication Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformApplicationRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformApplicationRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.CreatePlatformApplicationRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformApplicationRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformApplicationResponseUnmarshaller">
            <summary>
            Response Unmarshaller for CreatePlatformApplication operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformApplicationResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformApplicationResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformApplicationResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformEndpointRequestMarshaller">
            <summary>
            CreatePlatformEndpoint Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformEndpointRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformEndpointRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.CreatePlatformEndpointRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformEndpointRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformEndpointResponseUnmarshaller">
            <summary>
            Response Unmarshaller for CreatePlatformEndpoint operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformEndpointResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformEndpointResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreatePlatformEndpointResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreateTopicRequestMarshaller">
            <summary>
            CreateTopic Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreateTopicRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreateTopicRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.CreateTopicRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreateTopicRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreateTopicResponseUnmarshaller">
            <summary>
            Response Unmarshaller for CreateTopic operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreateTopicResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreateTopicResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.CreateTopicResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteEndpointRequestMarshaller">
            <summary>
            DeleteEndpoint Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteEndpointRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteEndpointRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.DeleteEndpointRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteEndpointRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteEndpointResponseUnmarshaller">
            <summary>
            Response Unmarshaller for DeleteEndpoint operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteEndpointResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteEndpointResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteEndpointResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeletePlatformApplicationRequestMarshaller">
            <summary>
            DeletePlatformApplication Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeletePlatformApplicationRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeletePlatformApplicationRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.DeletePlatformApplicationRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeletePlatformApplicationRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeletePlatformApplicationResponseUnmarshaller">
            <summary>
            Response Unmarshaller for DeletePlatformApplication operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeletePlatformApplicationResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeletePlatformApplicationResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeletePlatformApplicationResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteTopicRequestMarshaller">
            <summary>
            DeleteTopic Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteTopicRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteTopicRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.DeleteTopicRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteTopicRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteTopicResponseUnmarshaller">
            <summary>
            Response Unmarshaller for DeleteTopic operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteTopicResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteTopicResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.DeleteTopicResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.EndpointUnmarshaller">
            <summary>
            Response Unmarshaller for Endpoint Object
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.EndpointUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.EndpointUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.EndpointUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetEndpointAttributesRequestMarshaller">
            <summary>
            GetEndpointAttributes Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetEndpointAttributesRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetEndpointAttributesRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.GetEndpointAttributesRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetEndpointAttributesRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetEndpointAttributesResponseUnmarshaller">
            <summary>
            Response Unmarshaller for GetEndpointAttributes operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetEndpointAttributesResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetEndpointAttributesResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetEndpointAttributesResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetPlatformApplicationAttributesRequestMarshaller">
            <summary>
            GetPlatformApplicationAttributes Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetPlatformApplicationAttributesRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetPlatformApplicationAttributesRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.GetPlatformApplicationAttributesRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetPlatformApplicationAttributesRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetPlatformApplicationAttributesResponseUnmarshaller">
            <summary>
            Response Unmarshaller for GetPlatformApplicationAttributes operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetPlatformApplicationAttributesResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetPlatformApplicationAttributesResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetPlatformApplicationAttributesResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSMSAttributesRequestMarshaller">
            <summary>
            GetSMSAttributes Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSMSAttributesRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSMSAttributesRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.GetSMSAttributesRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSMSAttributesRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSMSAttributesResponseUnmarshaller">
            <summary>
            Response Unmarshaller for GetSMSAttributes operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSMSAttributesResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSMSAttributesResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSMSAttributesResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSubscriptionAttributesRequestMarshaller">
            <summary>
            GetSubscriptionAttributes Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSubscriptionAttributesRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSubscriptionAttributesRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.GetSubscriptionAttributesRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSubscriptionAttributesRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSubscriptionAttributesResponseUnmarshaller">
            <summary>
            Response Unmarshaller for GetSubscriptionAttributes operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSubscriptionAttributesResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSubscriptionAttributesResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetSubscriptionAttributesResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetTopicAttributesRequestMarshaller">
            <summary>
            GetTopicAttributes Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetTopicAttributesRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetTopicAttributesRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.GetTopicAttributesRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetTopicAttributesRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetTopicAttributesResponseUnmarshaller">
            <summary>
            Response Unmarshaller for GetTopicAttributes operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetTopicAttributesResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetTopicAttributesResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.GetTopicAttributesResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListEndpointsByPlatformApplicationRequestMarshaller">
            <summary>
            ListEndpointsByPlatformApplication Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListEndpointsByPlatformApplicationRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListEndpointsByPlatformApplicationRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.ListEndpointsByPlatformApplicationRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListEndpointsByPlatformApplicationRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListEndpointsByPlatformApplicationResponseUnmarshaller">
            <summary>
            Response Unmarshaller for ListEndpointsByPlatformApplication operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListEndpointsByPlatformApplicationResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListEndpointsByPlatformApplicationResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListEndpointsByPlatformApplicationResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPhoneNumbersOptedOutRequestMarshaller">
            <summary>
            ListPhoneNumbersOptedOut Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPhoneNumbersOptedOutRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPhoneNumbersOptedOutRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.ListPhoneNumbersOptedOutRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPhoneNumbersOptedOutRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPhoneNumbersOptedOutResponseUnmarshaller">
            <summary>
            Response Unmarshaller for ListPhoneNumbersOptedOut operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPhoneNumbersOptedOutResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPhoneNumbersOptedOutResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPhoneNumbersOptedOutResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPlatformApplicationsRequestMarshaller">
            <summary>
            ListPlatformApplications Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPlatformApplicationsRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPlatformApplicationsRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.ListPlatformApplicationsRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPlatformApplicationsRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPlatformApplicationsResponseUnmarshaller">
            <summary>
            Response Unmarshaller for ListPlatformApplications operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPlatformApplicationsResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPlatformApplicationsResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListPlatformApplicationsResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsByTopicRequestMarshaller">
            <summary>
            ListSubscriptionsByTopic Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsByTopicRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsByTopicRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.ListSubscriptionsByTopicRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsByTopicRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsByTopicResponseUnmarshaller">
            <summary>
            Response Unmarshaller for ListSubscriptionsByTopic operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsByTopicResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsByTopicResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsByTopicResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsRequestMarshaller">
            <summary>
            ListSubscriptions Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.ListSubscriptionsRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsResponseUnmarshaller">
            <summary>
            Response Unmarshaller for ListSubscriptions operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListSubscriptionsResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListTopicsRequestMarshaller">
            <summary>
            ListTopics Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListTopicsRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListTopicsRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.ListTopicsRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListTopicsRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListTopicsResponseUnmarshaller">
            <summary>
            Response Unmarshaller for ListTopics operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListTopicsResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListTopicsResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.ListTopicsResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.OptInPhoneNumberRequestMarshaller">
            <summary>
            OptInPhoneNumber Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.OptInPhoneNumberRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.OptInPhoneNumberRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.OptInPhoneNumberRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.OptInPhoneNumberRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.OptInPhoneNumberResponseUnmarshaller">
            <summary>
            Response Unmarshaller for OptInPhoneNumber operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.OptInPhoneNumberResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.OptInPhoneNumberResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.OptInPhoneNumberResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.PlatformApplicationUnmarshaller">
            <summary>
            Response Unmarshaller for PlatformApplication Object
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.PlatformApplicationUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.PlatformApplicationUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.PlatformApplicationUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.PublishRequestMarshaller">
            <summary>
            Publish Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.PublishRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.PublishRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.PublishRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.PublishRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.PublishResponseUnmarshaller">
            <summary>
            Response Unmarshaller for Publish operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.PublishResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.PublishResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.PublishResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.RemovePermissionRequestMarshaller">
            <summary>
            RemovePermission Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.RemovePermissionRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.RemovePermissionRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.RemovePermissionRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.RemovePermissionRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.RemovePermissionResponseUnmarshaller">
            <summary>
            Response Unmarshaller for RemovePermission operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.RemovePermissionResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.RemovePermissionResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.RemovePermissionResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetEndpointAttributesRequestMarshaller">
            <summary>
            SetEndpointAttributes Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetEndpointAttributesRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetEndpointAttributesRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.SetEndpointAttributesRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetEndpointAttributesRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetEndpointAttributesResponseUnmarshaller">
            <summary>
            Response Unmarshaller for SetEndpointAttributes operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetEndpointAttributesResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetEndpointAttributesResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetEndpointAttributesResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetPlatformApplicationAttributesRequestMarshaller">
            <summary>
            SetPlatformApplicationAttributes Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetPlatformApplicationAttributesRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetPlatformApplicationAttributesRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.SetPlatformApplicationAttributesRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetPlatformApplicationAttributesRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetPlatformApplicationAttributesResponseUnmarshaller">
            <summary>
            Response Unmarshaller for SetPlatformApplicationAttributes operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetPlatformApplicationAttributesResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetPlatformApplicationAttributesResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetPlatformApplicationAttributesResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSMSAttributesRequestMarshaller">
            <summary>
            SetSMSAttributes Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSMSAttributesRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSMSAttributesRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.SetSMSAttributesRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSMSAttributesRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSMSAttributesResponseUnmarshaller">
            <summary>
            Response Unmarshaller for SetSMSAttributes operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSMSAttributesResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSMSAttributesResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSMSAttributesResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSubscriptionAttributesRequestMarshaller">
            <summary>
            SetSubscriptionAttributes Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSubscriptionAttributesRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSubscriptionAttributesRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.SetSubscriptionAttributesRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSubscriptionAttributesRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSubscriptionAttributesResponseUnmarshaller">
            <summary>
            Response Unmarshaller for SetSubscriptionAttributes operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSubscriptionAttributesResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSubscriptionAttributesResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetSubscriptionAttributesResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetTopicAttributesRequestMarshaller">
            <summary>
            SetTopicAttributes Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetTopicAttributesRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetTopicAttributesRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.SetTopicAttributesRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetTopicAttributesRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetTopicAttributesResponseUnmarshaller">
            <summary>
            Response Unmarshaller for SetTopicAttributes operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetTopicAttributesResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetTopicAttributesResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SetTopicAttributesResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SubscribeRequestMarshaller">
            <summary>
            Subscribe Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SubscribeRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SubscribeRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.SubscribeRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SubscribeRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SubscribeResponseUnmarshaller">
            <summary>
            Response Unmarshaller for Subscribe operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SubscribeResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SubscribeResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SubscribeResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SubscriptionUnmarshaller">
            <summary>
            Response Unmarshaller for Subscription Object
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SubscriptionUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SubscriptionUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.SubscriptionUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.TopicUnmarshaller">
            <summary>
            Response Unmarshaller for Topic Object
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.TopicUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.TopicUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.JsonUnmarshallerContext)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.TopicUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.UnsubscribeRequestMarshaller">
            <summary>
            Unsubscribe Request Marshaller
            </summary>       
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.UnsubscribeRequestMarshaller.Marshall(Amazon.Runtime.AmazonWebServiceRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.UnsubscribeRequestMarshaller.Marshall(Amazon.SimpleNotificationService.Model.UnsubscribeRequest)">
            <summary>
            Marshaller the request object to the HTTP request.
            </summary>  
            <param name="publicRequest"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.UnsubscribeRequestMarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.UnsubscribeResponseUnmarshaller">
            <summary>
            Response Unmarshaller for Unsubscribe operation
            </summary>  
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.UnsubscribeResponseUnmarshaller.Unmarshall(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext)">
            <summary>
            Unmarshaller the response from the service to the response class.
            </summary>  
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.UnsubscribeResponseUnmarshaller.UnmarshallException(Amazon.Runtime.Internal.Transform.XmlUnmarshallerContext,System.Exception,System.Net.HttpStatusCode)">
            <summary>
            Unmarshaller error response to exception.
            </summary>  
            <param name="context"></param>
            <param name="innerException"></param>
            <param name="statusCode"></param>
            <returns></returns>
        </member>
        <member name="P:Amazon.SimpleNotificationService.Model.Internal.MarshallTransformations.UnsubscribeResponseUnmarshaller.Instance">
            <summary>
            Gets the singleton.
            </summary>  
        </member>
        <member name="T:Amazon.SimpleNotificationService.AmazonSimpleNotificationServiceRequest">
            <summary>
            Base class for SimpleNotificationService operation requests.
            </summary>
        </member>
    </members>
</doc>
