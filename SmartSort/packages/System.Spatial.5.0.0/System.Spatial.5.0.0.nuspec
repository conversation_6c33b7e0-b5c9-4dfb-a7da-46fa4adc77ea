<?xml version="1.0"?>
<package xmlns="http://schemas.microsoft.com/packaging/2010/07/nuspec.xsd">
  <metadata>
    <id>System.Spatial</id>
    <version>5.0.0</version>
    <title>System.Spatial</title>
    <authors>Microsoft Corporation</authors>
    <owners>Microsoft Corporation</owners>
    <licenseUrl>http://www.microsoft.com/download/en/details.aspx?id=29306</licenseUrl>
    <projectUrl>http://go.microsoft.com/fwlink/?LinkId=220868</projectUrl>
    <iconUrl>http://static.tumblr.com/hgchgxz/9ualgdf98/icon.png</iconUrl>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <description>Contains a number of classes and canonical methods that facilitate geography and geometry spatial operations. Targets .NET 4.0 or Silverlight 4.0. Localized for CHS, CHT, DEU, ESN, FRA, ITA, JPN, KOR and RUS.</description>
    <tags>wcf data services odata odatalib edmlib spatial ado.net ef entity framework open protocol wcfds wcfdataservices dataservices</tags>
  </metadata>
</package>