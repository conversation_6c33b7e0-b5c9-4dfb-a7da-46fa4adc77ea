<?xml version="1.0"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd">
  <metadata>
    <id>Microsoft.AspNet.Mvc</id>
    <version>5.0.0</version>
    <title>Microsoft ASP.NET MVC</title>
    <authors>Microsoft</authors>
    <owners>Microsoft</owners>
    <licenseUrl>http://www.microsoft.com/web/webpi/eula/aspnetcomponent_rtw_ENU.htm</licenseUrl>
    <projectUrl>http://www.asp.net/mvc</projectUrl>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <description>This package contains the runtime assemblies for ASP.NET MVC. ASP.NET MVC gives you a powerful, patterns-based way to build dynamic websites that enables a clean separation of concerns and that gives you full control over markup.</description>
    <summary>This package contains the runtime assemblies for ASP.NET MVC.</summary>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <language>en-US</language>
    <tags>Microsoft AspNet Mvc AspNetMvc</tags>
    <dependencies>
      <dependency id="Microsoft.AspNet.WebPages" version="3.0.0" />
      <dependency id="Microsoft.AspNet.Razor" version="3.0.0" />
    </dependencies>
  </metadata>
</package>