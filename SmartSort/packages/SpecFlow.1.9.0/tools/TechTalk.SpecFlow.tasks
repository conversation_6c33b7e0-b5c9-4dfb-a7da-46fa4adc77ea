﻿<?xml version="1.0" encoding="utf-8" ?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <SpecFlowTasksPath Condition="'$(SpecFlowTasksPath)'==''">specflow.exe</SpecFlowTasksPath>
  </PropertyGroup>

  <PropertyGroup>
    <!-- handle absolute / targets-relative tasks path -->
    <__SpecFlowTasksFullPath>$(SpecFlowTasksPath)</__SpecFlowTasksFullPath>
    <!-- handle relative tasks path -->
    <__SpecFlowTasksFullPath Condition="Exists('$(MSBuildProjectDirectory)\$(SpecFlowTasksPath)')"
      >$(MSBuildProjectDirectory)\$(SpecFlowTasksPath)</__SpecFlowTasksFullPath>
  </PropertyGroup>
  
  <UsingTask TaskName="TechTalk.SpecFlow.Tools.MsBuild.GenerateAll" AssemblyFile="$(__SpecFlowTasksFullPath)" />

</Project>
