﻿MICROSOFT ENTERPRISE LIBRARY
LOGGING APPLICATION BLOCK
6.0.1304.0

Summary: The Logging Application Block enables you to decouple your logging functionality from your application code. The block can route log entries synchronously or asynchronously to a Windows Event Log, a database, or a flat file. It can also generate an e-mail message containing the logging information or a message you can route through Windows Message Queuing (using a distributor service provided with the block). You can create a custom provider that sends the log entry to any other location or executes some other action.

The most up-to-date version of the release notes and known issues is available online:
http://aka.ms/el6release


Microsoft patterns & practices
http://microsoft.com/practices
