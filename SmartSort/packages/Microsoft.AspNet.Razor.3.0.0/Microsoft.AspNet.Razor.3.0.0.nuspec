<?xml version="1.0"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd">
  <metadata>
    <id>Microsoft.AspNet.Razor</id>
    <version>3.0.0</version>
    <title>Microsoft ASP.NET Razor</title>
    <authors>Microsoft</authors>
    <owners>Microsoft</owners>
    <licenseUrl>http://www.microsoft.com/web/webpi/eula/aspnetcomponent_rtw_ENU.htm</licenseUrl>
    <projectUrl>http://www.asp.net/web-pages</projectUrl>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <description>This package contains the runtime assemblies for ASP.NET Web Pages. ASP.NET Web Pages and the new Razor syntax provide a fast, terse, clean and lightweight way to combine server code with HTML to create dynamic web content.</description>
    <summary>This package contains the runtime assemblies for ASP.NET Web Pages.</summary>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <language>en-US</language>
    <tags>Microsoft AspNet WebPages AspNetWebPages Razor</tags>
  </metadata>
</package>