<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Practices.EnterpriseLibrary.Data</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.CachingMechanism">
            <devdoc>
            CachingMechanism provides caching support for stored procedure 
            parameter discovery and caching
            </devdoc>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.CachingMechanism.CloneParameters(System.Data.IDataParameter[])">
            <devdoc>
            Create and return a copy of the IDataParameter array.
            </devdoc>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.CachingMechanism.Clear">
            <devdoc>
            Empties all items from the cache
            </devdoc>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.CachingMechanism.AddParameterSetToCache(System.String,System.Data.IDbCommand,System.Data.IDataParameter[])">
            <devdoc>
            Add a parameter array to the cache for the command.
            </devdoc>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.CachingMechanism.GetCachedParameterSet(System.String,System.Data.IDbCommand)">
            <devdoc>
            Gets a parameter array from the cache for the command. Returns null if no parameters are found.
            </devdoc>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.CachingMechanism.IsParameterSetCached(System.String,System.Data.IDbCommand)">
            <devdoc>
            Gets if a given stored procedure on a specific connection string has a cached parameter set
            </devdoc>        
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1">
            <summary>
            Base class for Data Accessors that execute a <see cref="T:System.Data.Common.DbCommand"/>.
            </summary>
            <typeparam name="TResult">The element type this accessor will return.</typeparam>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.DataAccessor`1">
            <summary>
            An interface representing an object that wraps a database operation.
            An Accessor is executed, at which point it will go out to the database
            and return a <see cref="T:System.Collections.Generic.IEnumerable`1"/> of whatever type <typeparam name="TResult"/>
            is.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataAccessor`1.Execute(System.Object[])">
            <summary>
            Execute the database operation synchronously, returning the
            <see cref="T:System.Collections.Generic.IEnumerable`1"/> sequence containing the
            resulting objects.
            </summary> 
            <param name="parameterValues">Parameters to pass to the database.</param>
            <returns>The sequence of result objects.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataAccessor`1.BeginExecute(System.AsyncCallback,System.Object,System.Object[])">
            <summary>Begin executing the database object asynchronously, returning
            a <see cref="T:System.IAsyncResult"/> object that can be used to retrieve
            the result set after the operation completes.</summary>
            <param name="callback">Callback to execute when the operation's results are available. May
            be null if you don't wish to use a callback.</param>
            <param name="state">Extra information that will be passed to the callback. May be null.</param>
            <param name="parameterValues">Parameters to pass to the database.</param>
            <remarks>This operation will throw if the underlying <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> object does not
            support asynchronous operation.</remarks>
            <exception cref="T:System.InvalidOperationException">The underlying database does not support asynchronous operation.</exception>
            <returns>The <see cref="T:System.IAsyncResult"/> representing this operation.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataAccessor`1.EndExecute(System.IAsyncResult)">
            <summary>Complete an operation started by <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.DataAccessor`1.BeginExecute(System.AsyncCallback,System.Object,System.Object[])"/>.</summary>
            <returns>The result sequence.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1.#ctor(Microsoft.Practices.EnterpriseLibrary.Data.Database,Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper{`0})">
            <summary>
            Initialized the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1"/> with a database instance and a Row Mapper.
            </summary>
            <param name="database">The <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1.Database"/> used to execute the <see cref="T:System.Data.Common.DbCommand"/>.</param>
            <param name="rowMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that will be used to convert the returned data to clr type <typeparamref name="TResult"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1.#ctor(Microsoft.Practices.EnterpriseLibrary.Data.Database,Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper{`0})">
            <summary>
            Initialized the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1"/> with a database instance and a Row Mapper.
            </summary>
            <param name="database">The <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1.Database"/> used to execute the <see cref="T:System.Data.Common.DbCommand"/>.</param>
            <param name="resultSetMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1"/> that will be used to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1.Execute(System.Data.Common.DbCommand)">
            <summary>
            Executes the <paramref name="command"/> and returns an enumerable of <typeparamref name="TResult"/>.
            The enumerable returned by this method uses deferred loading to return the results.
            </summary>
            <param name="command">The command that will be executed.</param>
            <returns>An enumerable of <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1.BeginExecute(System.Data.Common.DbCommand,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            Helper method to kick off execution of an asynchronous database operation.
            This method handles the boilerplate of setting up the parameters and invoking
            the operation on the database with the right options.
            </summary>
            <param name="command">The <see cref="T:System.Data.Common.DbCommand"/> to execute.</param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> to use to set the parameter
            values.</param>
            <param name="callback">Callback to execute when the operation's result is available.</param>
            <param name="state">State to pass to the callback.</param>
            <param name="parameterValues">Input parameter values.</param>
            <returns>An <see cref="T:System.IAsyncResult"/> object representing the outstanding async request.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1.EndExecute(System.IAsyncResult)">
            <summary>Complete an operation started by <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.DataAccessor`1.BeginExecute(System.AsyncCallback,System.Object,System.Object[])"/>.</summary>
            <returns>The result sequence.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1.GuardAsyncAllowed">
            <summary>
            Checks if the current <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1.Database"/> object supports asynchronous operations,
            and throws <see cref="T:System.InvalidOperationException"/> if not.
            </summary>
            <exception cref="T:System.InvalidOperationException">The database does not support asynchronous operations.</exception>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.CommandAccessor`1.Database">
            <summary>
            The database object this accessor is wrapped around.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1">
            <summary>
            Represents the operation of mapping a <see cref="T:System.Data.IDataReader"/> to an enumerable of <typeparamref name="TResult"/>.
            </summary>
            <typeparam name="TResult">The element type this result set mapper will be mapping to.</typeparam>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1.MapSet(System.Data.IDataReader)">
            <summary>
            When implemented by a class, returns an enumerable of <typeparamref name="TResult"/> based on <paramref name="reader"/>.
            </summary>
            <param name="reader">The <see cref="T:System.Data.IDataReader"/> to map.</param>
            <returns>The enurable of <typeparamref name="TResult"/> that is based on <paramref name="reader"/>.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DataAccessDesignTime">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DataAccessDesignTime.ConnectionStringSettingsSectionName">
            <summary>
            Name of the connection strings settings configuration section.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DataAccessDesignTime.MetadataTypes">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DataAccessDesignTime.MetadataTypes.DbProviderMappingMetadata">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DataAccessDesignTime.MetadataTypes.DbProviderMappingMetadata.Name">
            <summary>
            This property supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DataAccessDesignTime.MetadataTypes.ConnectionStringsSectionMetadata">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DataAccessDesignTime.MetadataTypes.ConnectionStringSettingsCollectionMetadata">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DataAccessDesignTime.MetadataTypes.ConnectionStringSettingsMetadata">
            <summary>
            This class supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DataAccessDesignTime.MetadataTypes.ConnectionStringSettingsMetadata.Name">
            <summary>
            This property supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DataAccessDesignTime.MetadataTypes.ConnectionStringSettingsMetadata.ConnectionString">
            <summary>
            This property supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DataAccessDesignTime.MetadataTypes.ConnectionStringSettingsMetadata.ProviderName">
            <summary>
            This property supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.AddDataSettings">
            <summary>
              Looks up a localized string similar to Add Data Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.CategoryName">
            <summary>
              Looks up a localized string similar to (name).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ConnectionStringSettingsCollectionMetadataDescription">
            <summary>
              Looks up a localized string similar to Database Instances.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ConnectionStringSettingsCollectionMetadataDisplayName">
            <summary>
              Looks up a localized string similar to Database Instances.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ConnectionStringSettingsMetadataConnectionStringDescription">
            <summary>
              Looks up a localized string similar to Connection String.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ConnectionStringSettingsMetadataConnectionStringDisplayName">
            <summary>
              Looks up a localized string similar to Connection String.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ConnectionStringSettingsMetadataDescription">
             <summary>
               Looks up a localized string similar to A connection string is used to establish the initial connection to a database instance. 
            The exact contents of the connection string depend on the specific data source for this connection..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ConnectionStringSettingsMetadataDisplayName">
            <summary>
              Looks up a localized string similar to Database Connection String.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ConnectionStringSettingsMetadataNameDescription">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ConnectionStringSettingsMetadataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ConnectionStringSettingsMetadataProviderNameDescription">
            <summary>
              Looks up a localized string similar to Database Provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ConnectionStringSettingsMetadataProviderNameDisplayName">
            <summary>
              Looks up a localized string similar to Database Provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ConnectionStringsSectionMetadataDescription">
            <summary>
              Looks up a localized string similar to Database Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.ConnectionStringsSectionMetadataDisplayName">
            <summary>
              Looks up a localized string similar to Database Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.DatabaseSettingsDefaultDatabaseDescription">
            <summary>
              Looks up a localized string similar to The default Database instance to use when code does not specify a named Database instance..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.DatabaseSettingsDefaultDatabaseDisplayName">
            <summary>
              Looks up a localized string similar to Default Database Instance.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.DatabaseSettingsDescription">
            <summary>
              Looks up a localized string similar to Configuration settings for the Data Access Application Block..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.DatabaseSettingsDisplayName">
            <summary>
              Looks up a localized string similar to Data Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.DatabaseSettingsProviderMappingsDescription">
            <summary>
              Looks up a localized string similar to Providers that allow the Data Access Application Block to use any database for which a suitable Enterprise Library provider is available..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.DatabaseSettingsProviderMappingsDisplayName">
            <summary>
              Looks up a localized string similar to Custom Databases.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.DbProviderMappingDatabaseTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the class that maps the ADO.NET provider for this database type to Enterprise Library..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.DbProviderMappingDatabaseTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.DbProviderMappingDescription">
            <summary>
              Looks up a localized string similar to A custom class that maps the ADO.NET provider for the database type specified as the provider in the connection string of a Database Instance to Enterprise Library..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.DbProviderMappingDisplayName">
            <summary>
              Looks up a localized string similar to Custom Database Provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.DbProviderMappingNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Custom Database Provider..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DesignResources.DbProviderMappingNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseAnotherDatabaseConfiguration">
            <summary>
            Configuration extensions for database types specified via the <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DatabaseProviderExtensions.AnotherDatabaseType(Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProviders,System.String)"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseDefaultConnectionString">
            <summary>
            Defines default connection string settings for fluent-style interface.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseDefaultConnectionString.WithConnectionString(System.String)">
            <summary>
            Connection string to use for this data source.
            </summary>
            <param name="connectionString">The connection string.</param>
            <returns></returns>
            <seealso cref="T:System.Configuration.ConnectionStringSettings"/>
            <seealso cref="T:System.Data.Common.DbConnectionStringBuilder"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProperties">
            <summary>
            Database configuration properties that apply to all databases.
            </summary>
            <remarks>This interface is intended to support a fluent-style configuration interface.</remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProviderEntry">
            <summary>
             This interface support the database configuration fluent interface.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProviderEntry.ThatIs">
            <summary>
             Specify the type of database.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfiguration">
            <summary>
             Supports configuring the data connections via fluent-style interface.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfiguration.ForDatabaseNamed(System.String)">
            <summary>
             Configure a named database.
            </summary>
            <param name="databaseName">Name of database to configure</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProperties.AsDefault">
            <summary>
             Set this database as the default one in the configuration.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseAnotherDatabaseConfiguration.WithConnectionString(System.Data.Common.DbConnectionStringBuilder)">
            <summary />
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.DatabaseConfigurationExtension">
            <summary>
             Base class to help build database-specific configurations extensions for <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DataConfigurationSourceBuilderExtensions"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.DatabaseConfigurationExtension.#ctor(Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProviders)">
            <summary>
             Initializes a new DatabaseConfigurationExtension with a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProviders"/> context.
            </summary>
            <remarks>
             This class supports extending the data configuration section's fluent-style API.  New database providers
             can inherit from this class to gain access to the current <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.DatabaseConfigurationExtension.ConnectionString"/> and underlying <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.DatabaseConfigurationExtension.Builder"/>
             properties.  
             
             This class also implements the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProperties"/> to enable continuation of the data
             fluent interface.
             </remarks>
            <param name="context"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.DatabaseConfigurationExtension.ForDatabaseNamed(System.String)">
            <summary>
             Configure a named database.
            </summary>
            <param name="databaseName">Name of database to configure</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.DatabaseConfigurationExtension.AsDefault">
            <summary>
             Set this database as the default one in the configuration.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.DatabaseConfigurationExtension.WithConnectionString(System.String)">
            <summary>
            Connection string to use for this data source.
            </summary>
            <param name="connectionString">The connection string.</param>
            <returns></returns>
            <seealso cref="T:System.Configuration.ConnectionStringSettings"/>
            <seealso cref="T:System.Data.Common.DbConnectionStringBuilder"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.DatabaseConfigurationExtension.WithConnectionString(System.Data.Common.DbConnectionStringBuilder)">
            <summary>
             Connection string to use for this data source
            </summary>
            <param name="builder"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.DatabaseConfigurationExtension.ThatIs">
            <summary>
             Specify the type of database.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.DatabaseConfigurationExtension.ConnectionString">
            <summary>
             The connection string in progress.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.DatabaseConfigurationExtension.Builder">
            <summary>
             Context of the current builder for the extension.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseData">
            <summary>
            Describes a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> instance, aggregating information from a <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseData.ConnectionStringSettings"/>
            and potentially other sources of configuration.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseData.#ctor(System.Configuration.ConnectionStringSettings,System.Func{System.String,System.Configuration.ConfigurationSection})">
            <summary>
             Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseData"/> class with a connection string and a configuration
             source.
            </summary>
            <param name="connectionStringSettings">The <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseData.ConnectionStringSettings"/> for the represented database.</param>
            <param name="configurationSource">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> from which additional information can 
             be retrieved if necessary.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseData.BuildDatabase">
            <summary>
            Builds the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> represented by this configuration object.
            </summary>
            <returns>A database.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseData.ConnectionStringSettings">
            <summary>
            Gets the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseData.ConnectionStringSettings"/> for the database data.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseData.ConfigurationSource">
            <summary>
             Gets the function to access configuration information.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseData.Name">
            <summary>
            Gets the name for the represented database.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseData.ConnectionString">
            <summary>
            Gets the connection string for the represented database.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings">
            <summary>
            <para>Represents the root configuration for data.</para>
            </summary>
            <remarks>
            <para>The class maps to the <c>databaseSettings</c> element in configuration.</para>
            </remarks>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings.SectionName">
            <summary>
            The name of the data configuration section.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings.#ctor">
            <summary>
            <para>Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings"/> class.</para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings.GetDatabaseSettings(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Retrieves the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings"/> from a configuration source.
            </summary>
            <param name="configurationSource">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> to query for the database settings.</param>
            <returns>The database settings from the configuration source, or <see langword="null"/> (<b>Nothing</b> in Visual Basic) if the 
            configuration source does not contain database settings.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings.DefaultDatabase">
            <summary>
            <para>Gets or sets the default database instance name.</para>
            </summary>
            <value>
            <para>The default database instance name.</para>
            </value>
            <remarks>
            <para>This property maps to the <c>defaultInstance</c> element in configuration.</para>
            </remarks>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings.ProviderMappings">
            <summary>
            Holds the optional mappings from ADO.NET's database providers to Enterprise Library's database types.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSyntheticConfigSettings">
            <summary>
            Provides a configuration-like view over the Data Access Application Block sections
            </summary>
            <remarks>
            As the DataAccessBlock relies on a number of configuration sections (such as connectionStrings), this
            config settings provides an abstraction over all these to simplify creating database objects.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSyntheticConfigSettings.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSyntheticConfigSettings"/> class
            with the given <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/>.
            </summary>
            <remarks>This constructor is primarily for test convenience.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSyntheticConfigSettings.#ctor(System.Func{System.String,System.Configuration.ConfigurationSection})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSyntheticConfigSettings"/> class
            with the given configuraiton access function.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSyntheticConfigSettings.GetConnectionStringSettings(System.String)">
            <summary>
            Returns the <see cref="T:System.Configuration.ConnectionStringSettings"/> object with the given name from the connection strings
            configuration section in the receiver's configuration source.
            </summary>
            <remarks>
            The connection string will be retrieved from the configuration source if it contains the connection strings section,
            otherwise it will be retrieved from the default configuration file.
            </remarks>
            <param name="name">The name for the desired connection string configuration.</param>
            <returns>The connection string configuration.</returns>
            <exception cref="T:System.ArgumentException">if <paramref name="name"/> is <see langword="null"/> (<b>Nothing</b> in Visual Basic) or empty.</exception>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">if the connection string object is not found, or if it does not specify a provider name.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSyntheticConfigSettings.GetDatabase(System.String)">
            <summary>
            Gets the object describing the database instance with name <paramref name="name"/> in the configuration source.
            </summary>
            <param name="name">The name of the database.</param>
            <returns>A configuration object</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSyntheticConfigSettings.GetProviderMapping(System.String)">
            <summary>
            This method is made public for unit testing purposes.
            </summary>
            <param name="dbProviderName"></param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSyntheticConfigSettings.DefaultDatabase">
            <summary>
            Gets the default database instance name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSyntheticConfigSettings.Databases">
            <summary>
            Gets the objects describing the database instances in the configuration source.
            </summary>
            <remarks>
            Databases are derived from the collection of connection strings.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DatabaseProviderExtensions">
            <summary>
             Provides extensions for common database providers.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DatabaseProviderExtensions.ASqlDatabase(Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProviders)">
            <summary>
             A Sql database for use with the System.Data.SqlClient namespace.
            </summary>
             <param name="context">Configuration context</param>
            <returns></returns>
             <seealso cref="N:System.Data.SqlClient"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DatabaseProviderExtensions.ASqlCeDatabase(Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProviders)">
            <summary>
            A Sql CE database for use with the System.Data.SqlServerCe namespace.
            </summary>
            <param name="context">Configuration context</param>
            <returns></returns>   
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DatabaseProviderExtensions.AnOleDbDatabase(Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProviders)">
            <summary>
            An OleDb database for use with the <see cref="N:System.Data.OleDb"/> namespace.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DatabaseProviderExtensions.AnOdbcDatabase(Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProviders)">
            <summary>
            An Odbc database for use with the <see cref="N:System.Data.Odbc"/> namespace.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DatabaseProviderExtensions.AnOracleDatabase(Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProviders)">
            <summary>
             An Oracle database for use with the System.Data.OracleClient namespace.
            </summary>
            <returns></returns>
            <seealso cref="N:System.Data.OracleClient"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DatabaseProviderExtensions.AnotherDatabaseType(Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProviders,System.String)">
            <summary>
             A database with the specified database provider name.
            </summary>
             <param name="context">Extension context for fluent-interface</param>
             <param name="providerName">The provider name to use for this database connection</param>
            <returns></returns>
             <seealso cref="T:System.Data.Common.DbProviderFactories"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DataConfigurationSourceBuilderExtensions">
            <summary>
             Data configuration fluent interface extions to <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceBuilder"/>
            </summary>
             <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ConfigurationSourceBuilder"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DataConfigurationSourceBuilderExtensions.ConfigureData(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceBuilder)">
            <summary>
             Configure database connections for Enterprise Library.
            </summary>
            <param name="configurationSourceBuilderRoot">Source builder root that is extended.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseConfigurationProviders">
            <summary>
             Extension point for database providers to connect to the data configuration fluent-api.
            </summary>
             <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DataConfigurationSourceBuilderExtensions"/>
             <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.DatabaseConfigurationExtension"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseProviderExtensionContext">
            <summary>
            Provides extenson context for database provider extensions.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseProviderExtensionContext.ConnectionString">
            <summary>
             The current connetion string under construction in the fluent interface.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseProviderExtensionContext.Builder">
            <summary>
             Context of the current builder for the extension
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseProviderConfiguration">
            <summary>
            Defines the mapping options for providers.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDataConfiguration">
            <summary>
             Starting point for data configuration.
            </summary>
             <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DataConfigurationSourceBuilderExtensions"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDataConfiguration.WithProviderNamed(System.String)">
            <summary>
            Specify a custom provider name or alias to use.  This must
            map to the name of the invarient name specified by <see cref="T:System.Data.Common.DbProviderFactories"/>
            </summary>
            <remarks>If the provider is not mapped to a specific Enterprise Library <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> class, then the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.GenericDatabase"/> will be used.</remarks>
            <param name="providerName">The name of the database provider's invarient.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseProviderConfiguration.MappedToDatabase(System.Type)">
            <summary>
            The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> to map the provider to.
            </summary>
            <param name="databaseType">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> type.</param>
            <returns></returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.GenericDatabase"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseProviderConfiguration.MappedToDatabase``1">
            <summary>
            The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> to map the provider to.
            </summary>
            <typeparam name="T">Database type to map to</typeparam>
            <returns></returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.GenericDatabase"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DataConfigurationSourceBuilderExtensions.DataConfigurationBuilder.ForDatabaseNamed(System.String)">
            <summary>
             Configure a named database.
            </summary>
            <param name="databaseName">Name of database to configure</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DataConfigurationSourceBuilderExtensions.DataConfigurationBuilder.AsDefault">
            <summary>
             Set this database as the default one in the configuration.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DataConfigurationSourceBuilderExtensions.DataConfigurationBuilder.WithProviderNamed(System.String)">
            <summary />
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DataConfigurationSourceBuilderExtensions.DataConfigurationBuilder.MappedToDatabase(System.Type)">
            <summary>
            Map the provider alias to the specified database type.
            </summary>
            <param name="databaseType">Maps the provider to a type that derives from <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DataConfigurationSourceBuilderExtensions.DataConfigurationBuilder.MappedToDatabase``1">
            <summary />
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.DataConfigurationSourceBuilderExtensions.DataConfigurationBuilder.ThatIs">
            <summary>
             Specify the type of database.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping">
            <summary>
            Represents the mapping from an ADO.NET provider to an Enterprise Library <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/>.
            </summary>
            <remarks>
            <para>
            The Enterprise Library Data Access Application Block leverages the ADO.NET 2.0 provider factories. To determine what type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> matches a given provider factory type, the optional 
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping"/> configuration objects can be defined in the block's configuration section.
            </para>
            <para>
            If a mapping is not present for a given provider type, sensible defaults will be used:
            <list type="bullet">
            <item>For provider name "System.Data.SqlClient", or for a provider of type <see cref="T:System.Data.SqlClient.SqlClientFactory"/>, the 
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase"/> will be used.</item>
            <item>For provider name "System.Data.OracleClient", or for a provider of type <see cref="T:System.Data.OracleClient.OracleClientFactory"/>, the 
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase"/> will be used.</item>
            <item>In any other case, the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.GenericDatabase"/> will be used.</item>
            </list>
            </para>
            </remarks>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSyntheticConfigSettings.GetProviderMapping(System.String)"/>
            <seealso cref="T:System.Data.Common.DbProviderFactory"/>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping.DefaultSqlProviderName">
            <summary>
            Default name for the Sql managed provider.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping.DefaultOracleProviderName">
            <summary>
            Default name for the Oracle managed provider.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping"/> class with name and <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping"/> class with name and fully qualified type name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping.DatabaseType">
            <summary>
            Gets or sets the type of database to use for the mapped ADO.NET provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping.DatabaseTypeName">
            <summary>
            Gets or sets the fully qualified type name of the database to use for the mapped ADO.NET provider.
            </summary>
            <value>
            The fully qualified type name of the database to use for the mapped ADO.NET provider.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping.DbProviderName">
            <summary>
             Gets the logical name of the ADO.NET provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DbProviderMapping.Name">
            <summary/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IOdbcDatabaseConfiguration">
            <summary>   
            Odbc database configuration options. 
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IOdbcDatabaseConfiguration.WithConnectionString(System.Data.Odbc.OdbcConnectionStringBuilder)">
            <summary>
            Define a connection string with the <see cref="T:System.Data.Odbc.OdbcConnectionStringBuilder"/>.
            </summary>
            <param name="builder"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IOleDbDatabaseConfiguration">
            <summary>
            OleDb database configuration options.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IOleDbDatabaseConfiguration.WithConnectionString(System.Data.OleDb.OleDbConnectionStringBuilder)">
            <summary>
            Define an OleDb connection with the <see cref="T:System.Data.OleDb.OleDbConnectionStringBuilder"/>
            </summary>
            <param name="builder"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseOracleConfiguration">
            <summary>
            Oracle configuration options
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseOracleConfiguration.WithConnectionString(System.String)">
            <summary>
            Define an Oracle connection with a connection string.
            </summary>
            <param name="connectionString"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseOracleConfiguration.WithConnectionString(System.Data.OracleClient.OracleConnectionStringBuilder)">
            <summary>
            Define an Oracle connection with the <see cref="T:System.Data.OracleClient.OracleConnectionStringBuilder"/>
            </summary>
            <param name="builder"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseOracleConfiguration.WithPackageNamed(System.String)">
            <summary>
            Define an Oracle package with the specified name.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseOraclePackageConfiguration">
            <summary>
            Oracle package configuration options.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseOraclePackageConfiguration.AndPrefix(System.String)">
            <summary>
            Define the prefix for the Oracle package.
            </summary>
            <param name="prefix"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.OracleConfigurationExtension.Microsoft#Practices#EnterpriseLibrary#Data#Configuration#Fluent#IDatabaseOraclePackageConfiguration#AndPrefix(System.String)">
            <summary />
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.OracleConfigurationExtension.WithPackageNamed(System.String)">
            <summary />
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseSqlCeDatabaseConfiguration">
            <summary>
            SqlCe database configuration options.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseSqlDatabaseConfiguration">
            <summary>
            Sql Server Database configuration options.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.Fluent.IDatabaseSqlDatabaseConfiguration.WithConnectionString(System.Data.SqlClient.SqlConnectionStringBuilder)">
            <summary>
            Define a connection string using the <see cref="T:System.Data.SqlClient.SqlConnectionStringBuilder"/>.
            </summary>
            <param name="builder"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.GenericDatabaseData">
            <summary>
            Describes a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.GenericDatabase"/> instance, aggregating information from a 
            <see cref="T:System.Configuration.ConnectionStringSettings"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.GenericDatabaseData.#ctor(System.Configuration.ConnectionStringSettings,System.Func{System.String,System.Configuration.ConfigurationSection})">
            <summary>
             Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.GenericDatabaseData"/> class with a connection string and a configuration
             source.
            </summary>
            <param name="connectionStringSettings">The <see cref="T:System.Configuration.ConnectionStringSettings"/> for the represented database.</param>
            <param name="configurationSource">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> from which additional information can 
             be retrieved if necessary.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.GenericDatabaseData.BuildDatabase">
            <summary>
            Builds the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> represented by this configuration object.
            </summary>
            <returns>
            A database.
            </returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Configuration.GenericDatabaseData.ProviderName">
            <summary>
            Gets the name of the ADO.NET provider for the represented database.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper">
            <summary>
            This is a small helper class used to manage closing a connection 
            in the presence of transaction pooling. We can't actually
            close the connection until everyone using it is done, thus, we
            need reference counting.
            </summary>
            <remarks>
            User code should not use this class directly - it's used internally
            by the authors of DAAB providers to manage connections when using
            the DAAB methods.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper.#ctor(System.Data.Common.DbConnection)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper"/> that wraps
            the given <paramref name="connection"/>.
            </summary>
            <param name="connection">Database connection to manage the lifetime of.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper.Dispose">
            <summary>
            Decrement the reference count and, if refcount is 0, close the underlying connection.
            </summary>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper.Dispose(System.Boolean)">
            <summary>
            Usual Dispose pattern folderal to shut up FxCop.
            </summary>
            <param name="disposing">true if called via <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper.Dispose"/> method, false
            if called from finalizer. Of course, since we have no finalizer this will never
            be false.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper.AddRef">
            <summary>
            Increment the reference count for the wrapped connection.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper.Connection">
            <summary>
            The underlying <see cref="T:System.Data.Common.DbConnection"/> we're managing.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper.IsDisposed">
            <summary>
            Has this wrapper disposed the underlying connection?
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions">
            <summary>
            Class that contains extension methods that apply on <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.ExecuteSprocAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,System.Object[])">
            <summary>
            Executes a stored procedure and returns the result as an enumerable of <typeparamref name="TResult"/>.
            The conversion from <see cref="T:System.Data.IDataRecord"/> to <typeparamref name="TResult"/> will be done for each property based on matching property name to column name.
            </summary>
            <typeparam name="TResult">The element type that will be returned when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="procedureName">The name of the stored procedure that will be executed.</param>
            <param name="parameterValues">Parameter values passsed to the stored procedure.</param>
            <returns>An enumerable of <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.ExecuteSprocAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper,System.Object[])">
            <summary>
            Executes a stored procedure and returns the result as an enumerable of <typeparamref name="TResult"/>.
            The conversion from <see cref="T:System.Data.IDataRecord"/> to <typeparamref name="TResult"/> will be done for each property based on matching property name to column name.
            </summary>
            <typeparam name="TResult">The element type that will be returned when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="procedureName">The name of the stored procedure that will be executed.</param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <param name="parameterValues">Parameter values passsed to the stored procedure.</param>
            <returns>An enumerable of <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.ExecuteSprocAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper{``0},System.Object[])">
            <summary>
            Executes a stored procedure and returns the result as an enumerable of <typeparamref name="TResult"/>.
            </summary>
            <typeparam name="TResult">The element type that will be returned when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="procedureName">The name of the stored procedure that will be executed.</param>
            <param name="rowMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that will be used to convert the returned data to clr type <typeparamref name="TResult"/>.</param>
            <param name="parameterValues">Parameter values passsed to the stored procedure.</param>
            <returns>An enumerable of <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.ExecuteSprocAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper,Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper{``0},System.Object[])">
            <summary>
            Executes a stored procedure and returns the result as an enumerable of <typeparamref name="TResult"/>.
            </summary>
            <typeparam name="TResult">The element type that will be returned when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="procedureName">The name of the stored procedure that will be executed.</param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <param name="rowMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that will be used to convert the returned data to clr type <typeparamref name="TResult"/>.</param>
            <param name="parameterValues">Parameter values passsed to the stored procedure.</param>
            <returns>An enumerable of <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.ExecuteSprocAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper{``0},System.Object[])">
            <summary>
            Executes a stored procedure and returns the result as an enumerable of <typeparamref name="TResult"/>.
            </summary>
            <typeparam name="TResult">The element type that will be returned when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="procedureName">The name of the stored procedure that will be executed.</param>
            <param name="resultSetMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1"/> that will be used to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.</param>
            <param name="parameterValues">Parameter values passsed to the stored procedure.</param>
            <returns>An enumerable of <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.ExecuteSprocAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper,Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper{``0},System.Object[])">
            <summary>
            Executes a stored procedure and returns the result as an enumerable of <typeparamref name="TResult"/>.
            </summary>
            <typeparam name="TResult">The element type that will be returned when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="procedureName">The name of the stored procedure that will be executed.</param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <param name="resultSetMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1"/> that will be used to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.</param>
            <param name="parameterValues">Parameter values passsed to the stored procedure.</param>
            <returns>An enumerable of <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.CreateSprocAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> for the given stored procedure.
            The conversion from <see cref="T:System.Data.IDataRecord"/> to <typeparamref name="TResult"/> will be done for each property based on matching property name to column name.
            </summary>
            <typeparam name="TResult">The type the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> should return when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="procedureName">The name of the stored procedure that should be executed by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>. </param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.CreateSprocAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper)">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> for the given stored procedure.
            The conversion from <see cref="T:System.Data.IDataRecord"/> to <typeparamref name="TResult"/> will be done for each property based on matching property name to column name.
            </summary>
            <typeparam name="TResult">The type the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> should return when executing.</typeparam>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="procedureName">The name of the stored procedure that should be executed by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>. </param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.CreateSprocAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper{``0})">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> for the given stored procedure.
            </summary>
            <typeparam name="TResult">The type the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> should return when executing.</typeparam>
            <param name="rowMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that will be used to convert the returned data to clr type <typeparamref name="TResult"/>.</param>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="procedureName">The name of the stored procedure that should be executed by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>. </param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.CreateSprocAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper,Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper{``0})">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> for the given stored procedure.
            </summary>
            <typeparam name="TResult">The type the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> should return when executing.</typeparam>
            <param name="rowMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that will be used to convert the returned data to clr type <typeparamref name="TResult"/>.</param>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="procedureName">The name of the stored procedure that should be executed by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>. </param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.CreateSprocAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper{``0})">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> for the given stored procedure.
            </summary>
            <typeparam name="TResult">The type the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> should return when executing.</typeparam>
            <param name="resultSetMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1"/> that will be used to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.</param>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="procedureName">The name of the stored procedure that should be executed by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>. </param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.CreateSprocAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper,Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper{``0})">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> for the given stored procedure.
            </summary>
            <typeparam name="TResult">The type the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> should return when executing.</typeparam>
            <param name="resultSetMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1"/> that will be used to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.</param>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="procedureName">The name of the stored procedure that should be executed by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>. </param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.ExecuteSqlStringAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String)">
            <summary>
            Executes a Transact-SQL query and returns the result as an enumerable of <typeparamref name="TResult"/>.
            The conversion from <see cref="T:System.Data.IDataRecord"/> to <typeparamref name="TResult"/> will be done for each property based on matching property name to column name.
            </summary>
            <typeparam name="TResult">The element type that will be returned when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="sqlString">The Transact-SQL query that will be executed.</param>
            <returns>An enumerable of <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.ExecuteSqlStringAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper{``0})">
            <summary>
            Executes a Transact-SQL query and returns the result as an enumerable of <typeparamref name="TResult"/>.
            The conversion from <see cref="T:System.Data.IDataRecord"/> to <typeparamref name="TResult"/> will be done for each property based on matching property name to column name.
            </summary>
            <typeparam name="TResult">The element type that will be returned when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="resultSetMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1"/> that will be used to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.</param>
            <param name="sqlString">The Transact-SQL query that will be executed.</param>
            <returns>An enumerable of <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.ExecuteSqlStringAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper{``0})">
            <summary>
            Executes a Transact-SQL query and returns the result as an enumerable of <typeparamref name="TResult"/>.
            The conversion from <see cref="T:System.Data.IDataRecord"/> to <typeparamref name="TResult"/> will be done for each property based on matching property name to column name.
            </summary>
            <typeparam name="TResult">The element type that will be returned when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="sqlString">The Transact-SQL query that will be executed.</param>
            <param name="rowMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that will be used to convert the returned data to clr type <typeparamref name="TResult"/>.</param>
            <returns>An enumerable of <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.CreateSqlStringAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/> for the given Transact-SQL query.
            The conversion from <see cref="T:System.Data.IDataRecord"/> to <typeparamref name="TResult"/> will be done for each property based on matching property name to column name.
            </summary>
            <typeparam name="TResult">The type the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> should return when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="sqlString">The Transact-SQL query that will be executed by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/>.</param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.CreateSqlStringAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper)">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/> for the given Transact-SQL query.
            The conversion from <see cref="T:System.Data.IDataRecord"/> to <typeparamref name="TResult"/> will be done for each property based on matching property name to column name.
            </summary>
            <typeparam name="TResult">The type the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> should return when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="sqlString">The Transact-SQL query that will be executed by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/>.</param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.CreateSqlStringAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper{``0})">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/> for the given Transact-SQL query.
            </summary>
            <typeparam name="TResult">The type the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> should return when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="sqlString">The Transact-SQL query that will be executed by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/>.</param>
            <param name="rowMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that will be used to convert the returned data to clr type <typeparamref name="TResult"/>.</param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.CreateSqlStringAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper{``0})">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/> for the given Transact-SQL query.
            </summary>
            <typeparam name="TResult">The type the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> should return when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="sqlString">The Transact-SQL query that will be executed by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/>.</param>
            <param name="resultSetMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1"/> that will be used to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.</param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.CreateSqlStringAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper,Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper{``0})">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/> for the given Transact-SQL query.
            </summary>
            <typeparam name="TResult">The type the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> should return when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="sqlString">The Transact-SQL query that will be executed by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/>.</param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <param name="rowMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that will be used to convert the returned data to clr type <typeparamref name="TResult"/>.</param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseExtensions.CreateSqlStringAccessor``1(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper,Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper{``0})">
            <summary>
            Creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/> for the given Transact-SQL query.
            </summary>
            <typeparam name="TResult">The type the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> should return when executing.</typeparam>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> that contains the stored procedure.</param>
            <param name="sqlString">The Transact-SQL query that will be executed by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/>.</param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <param name="resultSetMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1"/> that will be used to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.</param>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/>.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper">
            <summary>
            Wrapper class that implements a pass through version of <see cref="T:System.Data.IDataReader"/>.
            Useful for various places where we need to wrap data readers for connection
            management.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.#ctor(System.Data.IDataReader)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper"/> that delegates all methods
            to the given <paramref name="innerReader"/>.
            </summary>
            <param name="innerReader"><see cref="T:System.Data.IDataReader"/> to wrap.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.Close">
            <summary>
            Closes the <see cref="T:System.Data.IDataReader"/> Object.
            </summary>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.Dispose(System.Boolean)">
            <summary>
            Close the contained data reader when disposing.
            </summary>
            <param name="disposing">True if called from Dispose method, false if called from finalizer. Since
            this class doesn't have a finalizer, this will always be true.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetName(System.Int32)">
            <summary>
            Gets the name for the field to find.
            </summary>
            <returns>
            The name of the field or the empty string (""), if there is no value to return.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetDataTypeName(System.Int32)">
            <summary>
            Gets the data type information for the specified field.
            </summary>
            <returns>
            The data type information for the specified field.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetFieldType(System.Int32)">
            <summary>
            Gets the <see cref="T:System.Type"/> information corresponding to the type of <see cref="T:System.Object"/> that would be returned from <see cref="M:System.Data.IDataRecord.GetValue(System.Int32)"/>.
            </summary>
            <returns>
            The <see cref="T:System.Type"/> information corresponding to the type of <see cref="T:System.Object"/> that would be returned from <see cref="M:System.Data.IDataRecord.GetValue(System.Int32)"/>.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetValue(System.Int32)">
            <summary>
            Return the value of the specified field.
            </summary>
            <returns>
            The <see cref="T:System.Object"/> which will contain the field value upon return.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetValues(System.Object[])">
            <summary>
            Gets all the attribute fields in the collection for the current record.
            </summary>
            <returns>
            The number of instances of <see cref="T:System.Object"/> in the array.
            </returns>
            <param name="values">An array of <see cref="T:System.Object"/> to copy the attribute fields into. 
                            </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetOrdinal(System.String)">
            <summary>
            Return the index of the named field.
            </summary>
            <returns>
            The index of the named field.
            </returns>
            <param name="name">The name of the field to find. 
                            </param><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetBoolean(System.Int32)">
            <summary>
            Gets the value of the specified column as a Boolean.
            </summary>
            <returns>
            The value of the column.
            </returns>
            <param name="i">The zero-based column ordinal. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetByte(System.Int32)">
            <summary>
            Gets the 8-bit unsigned integer value of the specified column.
            </summary>
            <returns>
            The 8-bit unsigned integer value of the specified column.
            </returns>
            <param name="i">The zero-based column ordinal. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a stream of bytes from the specified column offset into the buffer as an array, starting at the given buffer offset.
            </summary>
            <returns>
            The actual number of bytes read.
            </returns>
            <param name="i">The zero-based column ordinal. 
                            </param><param name="fieldOffset">The index within the field from which to start the read operation. 
                            </param><param name="buffer">The buffer into which to read the stream of bytes. 
                            </param><param name="bufferoffset">The index for <paramref name="buffer"/> to start the read operation. 
                            </param><param name="length">The number of bytes to read. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetChar(System.Int32)">
            <summary>
            Gets the character value of the specified column.
            </summary>
            <returns>
            The character value of the specified column.
            </returns>
            <param name="i">The zero-based column ordinal. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
            <summary>
            Reads a stream of characters from the specified column offset into the buffer as an array, starting at the given buffer offset.
            </summary>
            <returns>
            The actual number of characters read.
            </returns>
            <param name="i">The zero-based column ordinal. 
                            </param><param name="fieldoffset">The index within the row from which to start the read operation. 
                            </param><param name="buffer">The buffer into which to read the stream of bytes. 
                            </param><param name="bufferoffset">The index for <paramref name="buffer"/> to start the read operation. 
                            </param><param name="length">The number of bytes to read. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetGuid(System.Int32)">
            <summary>
            Returns the GUID value of the specified field.
            </summary>
            <returns>
            The GUID value of the specified field.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetInt16(System.Int32)">
            <summary>
            Gets the 16-bit signed integer value of the specified field.
            </summary>
            <returns>
            The 16-bit signed integer value of the specified field.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetInt32(System.Int32)">
            <summary>
            Gets the 32-bit signed integer value of the specified field.
            </summary>
            <returns>
            The 32-bit signed integer value of the specified field.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetInt64(System.Int32)">
            <summary>
            Gets the 64-bit signed integer value of the specified field.
            </summary>
            <returns>
            The 64-bit signed integer value of the specified field.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetFloat(System.Int32)">
            <summary>
            Gets the single-precision floating point number of the specified field.
            </summary>
            <returns>
            The single-precision floating point number of the specified field.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetDouble(System.Int32)">
            <summary>
            Gets the double-precision floating point number of the specified field.
            </summary>
            <returns>
            The double-precision floating point number of the specified field.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetString(System.Int32)">
            <summary>
            Gets the string value of the specified field.
            </summary>
            <returns>
            The string value of the specified field.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetDecimal(System.Int32)">
            <summary>
            Gets the fixed-position numeric value of the specified field.
            </summary>
            <returns>
            The fixed-position numeric value of the specified field.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetDateTime(System.Int32)">
            <summary>
            Gets the date and time data value of the specified field.
            </summary>
            <returns>
            The date and time data value of the specified field.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetData(System.Int32)">
            <summary>
            Returns an <see cref="T:System.Data.IDataReader"/> for the specified column ordinal.
            </summary>
            <returns>
            An <see cref="T:System.Data.IDataReader"/>.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.IsDBNull(System.Int32)">
            <summary>
            Return whether the specified field is set to null.
            </summary>
            <returns>
            true if the specified field is set to null; otherwise, false.
            </returns>
            <param name="i">The index of the field to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.GetSchemaTable">
            <summary>
            Returns a <see cref="T:System.Data.DataTable"/> that describes the column metadata of the <see cref="T:System.Data.IDataReader"/>.
            </summary>
            <returns>
            A <see cref="T:System.Data.DataTable"/> that describes the column metadata.
            </returns>
            <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Data.IDataReader"/> is closed. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.NextResult">
            <summary>
            Advances the data reader to the next result, when reading the results of batch SQL statements.
            </summary>
            <returns>
            true if there are more rows; otherwise, false.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.Read">
            <summary>
            Advances the <see cref="T:System.Data.IDataReader"/> to the next record.
            </summary>
            <returns>
            true if there are more rows; otherwise, false.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.InnerReader">
            <summary>
            The actual raw <see cref="T:System.Data.IDataReader"/> we're wrapping.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.FieldCount">
            <summary>
            Gets the number of columns in the current row.
            </summary>
            <returns>
            When not positioned in a valid recordset, 0; otherwise, the number of columns in the current record. The default is -1.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.Depth">
            <summary>
            Gets a value indicating the depth of nesting for the current row.
            </summary>
            <returns>
            The level of nesting.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.IsClosed">
            <summary>
            Gets a value indicating whether the data reader is closed.
            </summary>
            <returns>
            true if the data reader is closed; otherwise, false.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.RecordsAffected">
            <summary>
            Gets the number of rows changed, inserted, or deleted by execution of the SQL statement.
            </summary>
            <returns>
            The number of rows changed, inserted, or deleted; 0 if no rows were affected or the statement failed; and -1 for SELECT statements.
            </returns>
            <filterpriority>2</filterpriority>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.System#Data#IDataRecord#Item(System.Int32)">
            <summary>
            Gets the column located at the specified index.
            </summary>
            <returns>
            The column located at the specified index as an <see cref="T:System.Object"/>.
            </returns>
            <param name="i">The zero-based index of the column to get. 
                            </param><exception cref="T:System.IndexOutOfRangeException">The index passed was outside the range of 0 through <see cref="P:System.Data.IDataRecord.FieldCount"/>. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DataReaderWrapper.System#Data#IDataRecord#Item(System.String)">
            <summary>
            Gets the column with the specified name.
            </summary>
            <returns>
            The column with the specified name as an <see cref="T:System.Object"/>.
            </returns>
            <param name="name">The name of the column to find. 
                            </param><exception cref="T:System.IndexOutOfRangeException">No column with the specified name was found. 
                            </exception><filterpriority>2</filterpriority>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper">
            <summary>
            Interface used to interpret parameters passed to an <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.DataAccessor`1.Execute(System.Object[])"/> method
            and assign them to the <see cref="T:System.Data.Common.DbCommand"/> that will be executed.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper.AssignParameters(System.Data.Common.DbCommand,System.Object[])">
            <summary>
            
            </summary>
            <param name="command"></param>
            <param name="parameterValues"></param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1">
            <summary>
            Represents the operation of mapping a <see cref="T:System.Data.IDataRecord"/> to <typeparamref name="TResult"/>.
            </summary>
            <typeparam name="TResult">The type this row mapper will be mapping to.</typeparam>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.ReflectionRowMapper`1"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1.MapRow(System.Data.IDataRecord)">
            <summary>
            When implemented by a class, returns a new <typeparamref name="TResult"/> based on <paramref name="row"/>.
            </summary>
            <param name="row">The <see cref="T:System.Data.IDataRecord"/> to map.</param>
            <returns>The instance of <typeparamref name="TResult"/> that is based on <paramref name="row"/>.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.MapBuilder`1">
            <summary>
            Static entry point for the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1"/> interface, which allows to build reflection-based <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/>s.
            </summary>
            <typeparam name="TResult">The type for which a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> should be build.</typeparam>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.MapBuilder`1.BuildAllProperties">
            <summary>
            Returns a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that maps all properties for <typeparamref name="TResult"/> based on name.
            </summary>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.MapBuilder`1.MapAllProperties">
            <summary>
            Returns a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1"/> that can be used to build a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/>.
            The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1"/> has a mapping set up for all properties of <typeparamref name="TResult"/> based on name.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.MapBuilder`1.MapNoProperties">
            <summary>
            Returns a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1"/> that can be used to build a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/>.
            The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1"/> has no mappings to start out with.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/>
            <returns>A new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1"/>.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContextTest`1">
            <summary>
            This type supports the Enterprise Library infrastructure and is not intended to be used directly from your code.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1">
            <summary>
            A fluent interface that can be used to construct a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/>.
            </summary>
            <typeparam name="TResult">The type for which a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> should be build.</typeparam>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1.MapByName(System.Reflection.PropertyInfo)">
            <summary>
            Adds a property mapping to the context for <paramref name="property"/> that specifies this property will be mapped to a column with a matching name.
            </summary>
            <param name="property">The property of <typeparamref name="TResult"/> that should be mapped.</param>
            <returns>The fluent interface that can be used further specify mappings.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1.MapByName``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Adds a property mapping to the context for <paramref name="propertySelector"/> that specifies this property will be mapped to a column with a matching name.
            </summary>
            <param name="propertySelector">A lambda function that returns the property that should be mapped.</param>
            <returns>The fluent interface that can be used further specify mappings.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1.DoNotMap(System.Reflection.PropertyInfo)">
            <summary>
            Adds a property mapping to the context for <paramref name="property"/> that specifies this property will be ignored while mapping.
            </summary>
            <param name="property">The property of <typeparamref name="TResult"/> that should be mapped.</param>
            <returns>The fluent interface that can be used further specify mappings.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1.DoNotMap``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Adds a property mapping to the context for <paramref name="propertySelector"/> that specifies this property will be ignored while mapping.
            </summary>
            <param name="propertySelector">A lambda function that returns the property that should be mapped.</param>
            <returns>The fluent interface that can be used further specify mappings.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1.Map``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Adds a property mapping to the context for <paramref name="propertySelector"/>.
            </summary>
            <param name="propertySelector">A lambda function that returns the property that should be mapped.</param>
            <returns>The fluent interface that can be used to specify how to map this property.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1.Map(System.Reflection.PropertyInfo)">
            <summary>
            Adds a property mapping to the context for <paramref name="property"/>.
            </summary>
            <param name="property">The property of <typeparamref name="TResult"/> that should be mapped.</param>
            <returns>The fluent interface that can be used to specify how to map this property.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1.Build">
            <summary>
            Builds the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that can be used to map data structures to clr types.
            </summary>
            <returns>An instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContextTest`1.GetPropertyMappings">
            <summary>
            Returns the list of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping"/>s that have been accumulated by the context.
            </summary>
            <returns>The list of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping"/>.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContextMap`2">
            <summary>
            A fluent interface that can be used to construct a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/>.
            </summary>
            <typeparam name="TResult">The type for which a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> should be build.</typeparam>
            <typeparam name="TMember">The type of the member for which a mapping needs to specified.</typeparam>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContext`1"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContextMap`2.ToColumn(System.String)">
            <summary>
            Maps the current property to a column with the given name.
            </summary>
            <param name="columnName">The name of the column the current property should be mapped to.</param>
            <returns>The fluent interface that can be used further specify mappings.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.IMapBuilderContextMap`2.WithFunc(System.Func{System.Data.IDataRecord,`1})">
            <summary>
            Maps the current property to a user specified function.
            </summary>
            <param name="f">The user specified function that will map the current property.</param>
            <returns>The fluent interface that can be used further specify mappings.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OracleConnectionDataDescription">
            <summary>
              Looks up a localized string similar to A connection to an Oracle database that includes additional information such as the package to use..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OracleConnectionDataDisplayName">
            <summary>
              Looks up a localized string similar to Oracle Connection.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OracleConnectionDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Oracle Connection..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OracleConnectionDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OracleConnectionDataPackagesDescription">
            <summary>
              Looks up a localized string similar to The Oracle connection packages that define information specific to Oracle databases and this connection..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OracleConnectionDataPackagesDisplayName">
            <summary>
              Looks up a localized string similar to Packages.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OracleConnectionSettingsDescription">
            <summary>
              Looks up a localized string similar to Settings specific to Oracle connections, such as information on Oracle packages..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OracleConnectionSettingsDisplayName">
            <summary>
              Looks up a localized string similar to Oracle Connection Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OracleConnectionSettingsOracleConnectionsDataDescription">
            <summary>
              Looks up a localized string similar to Connections to Oracle databases..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OracleConnectionSettingsOracleConnectionsDataDisplayName">
            <summary>
              Looks up a localized string similar to Oracle Connections.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OraclePackageDataDescription">
            <summary>
              Looks up a localized string similar to The Oracle package to use when calling stored procedures in this Oracle database..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OraclePackageDataDisplayName">
            <summary>
              Looks up a localized string similar to Package.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OraclePackageDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Oracle Package..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OraclePackageDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OraclePackageDataPrefixDescription">
            <summary>
              Looks up a localized string similar to The prefix of the stored procedures in the specified Oracle package..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.DesignResources.OraclePackageDataPrefixDisplayName">
            <summary>
              Looks up a localized string similar to Prefix.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.RefCountingDataReader">
            <summary>
            An implementation of <see cref="T:System.Data.IDataReader"/> which also properly
            cleans up the reference count on the given inner <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper"/>
            when the reader is closed or disposed.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.RefCountingDataReader.#ctor(Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper,System.Data.IDataReader)">
            <summary>
            Create a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.RefCountingDataReader"/> that wraps
            the given <paramref name="innerReader"/> and properly
            cleans the refcount on the given <paramref name="connection"/>
            when done.
            </summary>
            <param name="connection">Connection to close.</param>
            <param name="innerReader">Reader to do the actual work.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.RefCountingDataReader.Close">
            <summary>
            Closes the <see cref="T:System.Data.IDataReader"/> Object.
            </summary>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.RefCountingDataReader.Dispose(System.Boolean)">
            <summary>
            Clean up resources.
            </summary>
            <param name="disposing">True if called from dispose, false if called from finalizer. We have no finalizer,
            so this will never be false.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.ReflectionRowMapper`1">
            <summary>
            An implementation of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that uses reflection to convert data rows to <typeparamref name="TResult"/>.
            Instances of this class can be build using the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.MapBuilder`1"/> API.
            </summary>
            <typeparam name="TResult">The type this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> applies to</typeparam>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.MapBuilder`1"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.ReflectionRowMapper`1.#ctor(System.Collections.Generic.IDictionary{System.Reflection.PropertyInfo,Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.ReflectionRowMapper`1"/>.
            </summary>
            <param name="propertyMappings">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping"/>'s that specify how each property should be mapped.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.ReflectionRowMapper`1.MapRow(System.Data.IDataRecord)">
            <summary>Given a record from a data reader, map the contents to a common language runtime object.</summary>
            <param name="row">The input data from the database.</param>
            <returns>The mapped object.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping">
            <summary>
            Base class for mapping values to properties by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.ReflectionRowMapper`1"/>.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.ColumnNameMapping"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.FuncMapping"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping.#ctor(System.Reflection.PropertyInfo)">
            <summary>
            Initializes a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping.GetPropertyValue(System.Data.IDataRecord)">
            <summary>
            When implemented by a class, extracts the value for the mapped property from <paramref name="row"/>.
            </summary>
            <param name="row">The data record.</param>
            <returns>The properly converted value.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping.Map(System.Object,System.Data.IDataRecord)">
            <summary>
            Performs the actual mapping from column to property.
            </summary>
            <param name="instance">The object that contains the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping.Property"/>.</param>
            <param name="row">The row that contains the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.ColumnNameMapping.ColumnName"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping.SetValue(System.Object,System.Object)">
            <summary>
            Sets the <paramref name="value"/> to <paramref name="instance"/> using <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping.Property"/>.
            </summary>
            <param name="instance">The object <paramref name="value"/> will be assigned to.</param>
            <param name="value">The value that will be assigned to <paramref name="instance"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping.ConvertValue(System.Object,System.Type)">
            <summary>
            Converts the database value <paramref name="value"/> to <paramref name="conversionType"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping.ConvertNullableValue(System.Object,System.Type)">
            <summary>
            Converts the database value <paramref name="value"/> to <paramref name="conversionType"/>,
            where <paramref name="conversionType"/> is a nullable value.
            </summary>
            <param name="value">Value from the database.</param>
            <param name="conversionType">Type to convert to.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping.ConvertNonNullableValue(System.Object,System.Type)">
            <summary>
            Converts the database value <paramref name="value"/> to <paramref name="conversionType"/>.
            Will throw an exception if <paramref name="conversionType"/> is a nullable value.
            </summary>
            <param name="value">Value from the database.</param>
            <param name="conversionType">Type to convert to.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.PropertyMapping.Property">
            <summary>
            Gets the property that will be mapped to.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.ColumnNameMapping">
            <summary>
            Represents the mapping from a database column to a <see cref="T:System.Reflection.PropertyInfo"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.ColumnNameMapping.#ctor(System.Reflection.PropertyInfo,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.ColumnNameMapping"/>
            </summary>
            <param name="columnName">The name of the column that will be used for mapping.</param>
            <param name="property">The property that will be used to map to.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.ColumnNameMapping.GetPropertyValue(System.Data.IDataRecord)">
            <summary>
            Converts the value for the column in the <paramref name="row"/> with a name matching that of the 
            mapped property to the type of the property.
            </summary>
            <param name="row">The data record.</param>
            <returns>The value for the corresponding column converted to the type of the mapped property.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.ColumnNameMapping.ColumnName">
            <summary>
            Gets the name of the column that is used for mapping.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.FuncMapping">
            <summary>
            Represents a property that will be assigned the value of a user specified function when mapping.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.FuncMapping.#ctor(System.Reflection.PropertyInfo,System.Func{System.Data.IDataRecord,System.Object})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.FuncMapping"/>
            </summary>
            <param name="func">The func that will be used to map the property.</param>
            <param name="property">The property that will be used to map to.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.FuncMapping.GetPropertyValue(System.Data.IDataRecord)">
            <summary>
            Gets the value for the mapped property from the <paramref name="row"/>.
            </summary>
            <param name="row">The data record.</param>
            <returns>The value for the corresponding column converted to the type of the mapped property.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.FuncMapping.Func">
            <summary>
            Gets the function that will be used to map the properties value.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1">
            <summary>
            Represents a stored procedure call to the database that will return an enumerable of <typeparamref name="TResult"/>.
            </summary>
            <typeparam name="TResult">The element type that will be used to consume the result set.</typeparam>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1.#ctor(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper{`0})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> that works for a specific <paramref name="database"/>
            and uses <paramref name="rowMapper"/> to convert the returned rows to clr type <typeparamref name="TResult"/>.
            </summary>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> used to execute the Transact-SQL.</param>
            <param name="procedureName">The stored procedure that will be executed.</param>
            <param name="rowMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that will be used to convert the returned data to clr type <typeparamref name="TResult"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1.#ctor(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper{`0})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> that works for a specific <paramref name="database"/>
            and uses <paramref name="resultSetMapper"/> to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.
            </summary>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> used to execute the Transact-SQL.</param>
            <param name="procedureName">The stored procedure that will be executed.</param>
            <param name="resultSetMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1"/> that will be used to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1.#ctor(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper,Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper{`0})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> that works for a specific <paramref name="database"/>
            and uses <paramref name="rowMapper"/> to convert the returned rows to clr type <typeparamref name="TResult"/>.
            The <paramref name="parameterMapper"/> will be used to interpret the parameters passed to the Execute method.
            </summary>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> used to execute the Transact-SQL.</param>
            <param name="procedureName">The stored procedure that will be executed.</param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <param name="rowMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that will be used to convert the returned data to clr type <typeparamref name="TResult"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1.#ctor(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper,Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper{`0})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1"/> that works for a specific <paramref name="database"/>
            and uses <paramref name="resultSetMapper"/> to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.
            The <paramref name="parameterMapper"/> will be used to interpret the parameters passed to the Execute method.
            </summary>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> used to execute the Transact-SQL.</param>
            <param name="procedureName">The stored procedure that will be executed.</param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <param name="resultSetMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1"/> that will be used to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1.Execute(System.Object[])">
            <summary>
            Executes the stored procedure and returns an enumerable of <typeparamref name="TResult"/>.
            The enumerable returned by this method uses deferred loading to return the results.
            </summary>
            <param name="parameterValues">Values that will be interpret by an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> and function as parameters to the stored procedure.</param>
            <returns>An enumerable of <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.SprocAccessor`1.BeginExecute(System.AsyncCallback,System.Object,System.Object[])">
            <summary>Begin executing the database object asynchronously, returning
            a <see cref="T:System.IAsyncResult"/> object that can be used to retrieve
            the result set after the operation completes.</summary>
            <param name="callback">Callback to execute when the operation's results are available. May
            be null if you don't wish to use a callback.</param>
            <param name="state">Extra information that will be passed to the callback. May be null.</param>
            <param name="parameterValues">Parameters to pass to the database.</param>
            <remarks>This operation will throw if the underlying <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> object does not
            support asynchronous operation.</remarks>
            <exception cref="T:System.InvalidOperationException">The underlying database does not support asynchronous operation.</exception>
            <returns>The <see cref="T:System.IAsyncResult"/> representing this operation.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Sql.Configuration.SqlDatabaseData">
            <summary>
            Describes a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase"/> instance, aggregating information from a 
            <see cref="T:System.Configuration.ConnectionStringSettings"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.Configuration.SqlDatabaseData.#ctor(System.Configuration.ConnectionStringSettings,System.Func{System.String,System.Configuration.ConfigurationSection})">
            <summary>
             Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase"/> class with a connection string and a configuration
             source.
            </summary>
            <param name="connectionStringSettings">The <see cref="T:System.Configuration.ConnectionStringSettings"/> for the represented database.</param>
            <param name="configurationSource">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> from which additional information can 
             be retrieved if necessary.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.Configuration.SqlDatabaseData.BuildDatabase">
            <summary>
            Builds the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> represented by this configuration object.
            </summary>
            <returns>
            A database.
            </returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleDatabaseData">
            <summary>
            Describes a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase"/> instance, aggregating information from a 
            <see cref="T:System.Configuration.ConnectionStringSettings"/> and any Oracle-specific database information.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleDatabaseData.#ctor(System.Configuration.ConnectionStringSettings,System.Func{System.String,System.Configuration.ConfigurationSection})">
            <summary>
             Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleDatabaseData"/> class with a connection string and a configuration
             source.
            </summary>
            <param name="connectionStringSettings">The <see cref="T:System.Configuration.ConnectionStringSettings"/> for the represented database.</param>
            <param name="configurationSource">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> from which Oracle-specific information 
             should be retrieved.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleDatabaseData.BuildDatabase">
            <summary>
            Builds the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> represented by this configuration object.
            </summary>
            <returns>
            A database.
            </returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleDatabaseData.PackageMappings">
            <summary>
             Gets the Oracle package mappings for the represented database.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseProviderFactory">
            <summary>
            <para>Represents a factory for creating named instances of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> objects.</para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseProviderFactory.#ctor">
            <summary>
            <para>Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseProviderFactory"/> class 
            with the default configuration source.</para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseProviderFactory.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            <para>Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseProviderFactory"/> class 
            with the given configuration source.</para>
            </summary>
            <param name="configurationSource">The source for configuration information.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseProviderFactory.#ctor(System.Func{System.String,System.Configuration.ConfigurationSection})">
            <summary>
            <para>Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseProviderFactory"/> class 
            with the given configuration accessor.</para>
            </summary>
            <param name="configurationAccessor">The source for configuration information.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseProviderFactory.CreateDefault">
            <summary>
            Returns a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> instance based on the default instance configuration.
            </summary>
            <returns>
            A new Database instance.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseProviderFactory.Create(System.String)">
            <summary>
            Returns a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> instance based on the configuration for <paramref name="name"/>.
            </summary>
            <param name="name">The name of the required instance.</param>
            <returns>
            A new Database instance.
            </returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionData">
            <summary>
            Oracle-specific connection information.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionData"/> class with default values.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionData.Name">
            <summary>
            
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionData.Packages">
            <summary>
            Gets a collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OraclePackageData"/> objects.
            </summary>
            <value>
            A collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OraclePackageData"/> objects.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionSettings">
            <summary>
            Oracle-specific configuration section.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionSettings.SectionName">
            <summary>
            The section name for the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionSettings"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionSettings.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionSettings"/> class with default values.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionSettings.GetSettings(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Retrieves the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionSettings"/> from the configuration source.
            </summary>
            <param name="configurationSource">The configuration source to retrieve the configuration from.</param>
            <returns>The configuration section, or <see langword="null"/> (<b>Nothing</b> in Visual Basic) 
            if not present in the configuration source.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OracleConnectionSettings.OracleConnectionsData">
            <summary>
            Collection of Oracle-specific connection information.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OraclePackageData">
            <summary>
            <para>Represents the package information to use when calling a stored procedure for Oracle.</para>
            </summary>
            <remarks>
            <para>
            A package name can be appended to the stored procedure name of a command if the prefix of the stored procedure
            matchs the prefix defined. This allows the caller of the stored procedure to use stored procedures
            in a more database independent fashion.
            </para>
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.IOraclePackage">
            <summary>
            Represents the description of an Oracle package mapping.
            </summary>
            <remarks>
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.IOraclePackage"/> is used to specify how to transform store procedure names 
            into package qualified Oracle stored procedure names.
            </remarks>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase"/>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.IOraclePackage.Name">
            <summary>
            When implemented by a class, gets the name of the package.
            </summary>
            <value>
            The name of the package.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.IOraclePackage.Prefix">
            <summary>
            When implemented by a class, gets the prefix for the package.
            </summary>
            <value>
            The prefix for the package.
            </value>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OraclePackageData.#ctor">
            <summary>
            <para>Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OraclePackageData"/> class.</para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OraclePackageData.#ctor(System.String,System.String)">
            <summary>
            <para>Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OraclePackageData"/> class, given the prefix to search for and the name of the package.</para>
            </summary>
            <param name="name">
            <para>The name of the package to append to any found procedure that has the <paramref name="prefix"/>.</para>
            </param>
            <param name="prefix">
            <para>The prefix of the stored procedures used in this package.</para>
            </param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OraclePackageData.Prefix">
            <summary>
            <para>Gets or sets the prefix of the stored procedures that are in the package in Oracle.</para>
            </summary>
            <value>
            <para>The prefix of the stored procedures that are in the package in Oracle.</para>
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.Configuration.OraclePackageData.Name">
            <summary>
            Gets or sets the name of the element.
            </summary>
            <value>
            The name of the element.
            </value>
            <remarks>
            Overriden in order to annotate with designtime attribute.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.ConnectionString">
            <summary>
            ConnectionString class constructs a connection string by 
            inserting a username and password into a template.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.ConnectionString.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.ConnectionString"/> with a connection string, the user ID tokens and password tokens.
            </summary>
            <param name="connectionString">The connection string.</param>
            <param name="userIdTokens">The user id tokens that can be parsed out of the connection string.</param>
            <param name="passwordTokens">The password tokens that can be parsed out of the conection string.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.ConnectionString.ToString">
            <devdoc>
            Gets the formatted connection string.
            </devdoc>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.ConnectionString.ToStringNoCredentials">
            <devdoc>
            Gets the formatted connection string without the username and password.
            </devdoc>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.ConnectionString.CreateNewConnectionString(System.String)">
            <summary>
            Formats a new connection string with a user ID and password.
            </summary>  
            <param name="connectionStringToFormat">
            The connection string to format.
            </param>		
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.ConnectionString.UserName">
            <summary>
            Gets or sets the name of the user.
            </summary>
            <value>The name of the user.</value>
            <devdoc>
            Database username for the connection string.
            </devdoc>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.ConnectionString.Password">
            <devdoc>
            User password for the connection string.
            </devdoc>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Database">
            <summary>
            Represents an abstract database that commands can be run against. 
            </summary>
            <remarks>
            The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> class leverages the provider factory model from ADO.NET. A database instance holds 
            a reference to a concrete <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.Database.DbProviderFactory"/> object to which it forwards the creation of ADO.NET objects.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.#ctor(System.String,System.Data.Common.DbProviderFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> class with a connection string and a <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.Database.DbProviderFactory"/>.
            </summary>
            <param name="connectionString">The connection string for the database.</param>
            <param name="dbProviderFactory">A <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.Database.DbProviderFactory"/> object.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.AddInParameter(System.Data.Common.DbCommand,System.String,System.Data.DbType)">
            <summary>
            Adds a new In <see cref="T:System.Data.Common.DbParameter"/> object to the given <paramref name="command"/>.
            </summary>
            <param name="command">The command to add the in parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.DbType"/> values.</para></param>                
            <remarks>
            <para>This version of the method is used when you can have the same parameter object multiple times with different values.</para>
            </remarks>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.AddInParameter(System.Data.Common.DbCommand,System.String,System.Data.DbType,System.Object)">
            <summary>
            Adds a new In <see cref="T:System.Data.Common.DbParameter"/> object to the given <paramref name="command"/>.
            </summary>
            <param name="command">The commmand to add the parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.DbType"/> values.</para></param>                
            <param name="value"><para>The value of the parameter.</para></param>      
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.AddInParameter(System.Data.Common.DbCommand,System.String,System.Data.DbType,System.String,System.Data.DataRowVersion)">
            <summary>
            Adds a new In <see cref="T:System.Data.Common.DbParameter"/> object to the given <paramref name="command"/>.
            </summary>
            <param name="command">The command to add the parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.DbType"/> values.</para></param>                
            <param name="sourceColumn"><para>The name of the source column mapped to the DataSet and used for loading or returning the value.</para></param>
            <param name="sourceVersion"><para>One of the <see cref="T:System.Data.DataRowVersion"/> values.</para></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.AddOutParameter(System.Data.Common.DbCommand,System.String,System.Data.DbType,System.Int32)">
            <summary>
            Adds a new Out <see cref="T:System.Data.Common.DbParameter"/> object to the given <paramref name="command"/>.
            </summary>
            <param name="command">The command to add the out parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.DbType"/> values.</para></param>        
            <param name="size"><para>The maximum size of the data within the column.</para></param>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.AddParameter(System.Data.Common.DbCommand,System.String,System.Data.DbType,System.Int32,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Object)">
            <summary>
            Adds a new In <see cref="T:System.Data.Common.DbParameter"/> object to the given <paramref name="command"/>.
            </summary>
            <param name="command">The command to add the parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.DbType"/> values.</para></param>
            <param name="size"><para>The maximum size of the data within the column.</para></param>
            <param name="direction"><para>One of the <see cref="T:System.Data.ParameterDirection"/> values.</para></param>
            <param name="nullable"><para>Avalue indicating whether the parameter accepts <see langword="null"/> (<b>Nothing</b> in Visual Basic) values.</para></param>
            <param name="precision"><para>The maximum number of digits used to represent the <paramref name="value"/>.</para></param>
            <param name="scale"><para>The number of decimal places to which <paramref name="value"/> is resolved.</para></param>
            <param name="sourceColumn"><para>The name of the source column mapped to the DataSet and used for loading or returning the <paramref name="value"/>.</para></param>
            <param name="sourceVersion"><para>One of the <see cref="T:System.Data.DataRowVersion"/> values.</para></param>
            <param name="value"><para>The value of the parameter.</para></param>       
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.AddParameter(System.Data.Common.DbCommand,System.String,System.Data.DbType,System.Data.ParameterDirection,System.String,System.Data.DataRowVersion,System.Object)">
            <summary>
            <para>Adds a new instance of a <see cref="T:System.Data.Common.DbParameter"/> object to the command.</para>
            </summary>
            <param name="command">The command to add the parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.DbType"/> values.</para></param>        
            <param name="direction"><para>One of the <see cref="T:System.Data.ParameterDirection"/> values.</para></param>                
            <param name="sourceColumn"><para>The name of the source column mapped to the DataSet and used for loading or returning the <paramref name="value"/>.</para></param>
            <param name="sourceVersion"><para>One of the <see cref="T:System.Data.DataRowVersion"/> values.</para></param>
            <param name="value"><para>The value of the parameter.</para></param>    
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BuildParameterName(System.String)">
            <summary>
            Builds a value parameter name for the current database.
            </summary>
            <param name="name">The name of the parameter.</param>
            <returns>A correctly formated parameter name.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ClearParameterCache">
            <summary>
            Clears the parameter cache. Since there is only one parameter cache that is shared by all instances
            of this class, this clears all parameters cached for all databases.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ConfigureParameter(System.Data.Common.DbParameter,System.String,System.Data.DbType,System.Int32,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Object)">
            <summary>
            Configures a given <see cref="T:System.Data.Common.DbParameter"/>.
            </summary>
            <param name="param">The <see cref="T:System.Data.Common.DbParameter"/> to configure.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.DbType"/> values.</para></param>
            <param name="size"><para>The maximum size of the data within the column.</para></param>
            <param name="direction"><para>One of the <see cref="T:System.Data.ParameterDirection"/> values.</para></param>
            <param name="nullable"><para>Avalue indicating whether the parameter accepts <see langword="null"/> (<b>Nothing</b> in Visual Basic) values.</para></param>
            <param name="precision"><para>The maximum number of digits used to represent the <paramref name="value"/>.</para></param>
            <param name="scale"><para>The number of decimal places to which <paramref name="value"/> is resolved.</para></param>
            <param name="sourceColumn"><para>The name of the source column mapped to the DataSet and used for loading or returning the <paramref name="value"/>.</para></param>
            <param name="sourceVersion"><para>One of the <see cref="T:System.Data.DataRowVersion"/> values.</para></param>
            <param name="value"><para>The value of the parameter.</para></param>  
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.CreateConnection">
            <summary>
            <para>Creates a connection for this database.</para>
            </summary>
            <returns>
            <para>The <see cref="T:System.Data.Common.DbConnection"/> for this database.</para>
            </returns>
            <seealso cref="T:System.Data.Common.DbConnection"/>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.CreateParameter(System.String,System.Data.DbType,System.Int32,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Object)">
            <summary>
            <para>Adds a new instance of a <see cref="T:System.Data.Common.DbParameter"/> object.</para>
            </summary>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.DbType"/> values.</para></param>
            <param name="size"><para>The maximum size of the data within the column.</para></param>
            <param name="direction"><para>One of the <see cref="T:System.Data.ParameterDirection"/> values.</para></param>
            <param name="nullable"><para>Avalue indicating whether the parameter accepts <see langword="null"/> (<b>Nothing</b> in Visual Basic) values.</para></param>
            <param name="precision"><para>The maximum number of digits used to represent the <paramref name="value"/>.</para></param>
            <param name="scale"><para>The number of decimal places to which <paramref name="value"/> is resolved.</para></param>
            <param name="sourceColumn"><para>The name of the source column mapped to the DataSet and used for loading or returning the <paramref name="value"/>.</para></param>
            <param name="sourceVersion"><para>One of the <see cref="T:System.Data.DataRowVersion"/> values.</para></param>
            <param name="value"><para>The value of the parameter.</para></param>  
            <returns>A newly created <see cref="T:System.Data.Common.DbParameter"/> fully initialized with given parameters.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.CreateParameter(System.String)">
            <summary>
            <para>Adds a new instance of a <see cref="T:System.Data.Common.DbParameter"/> object.</para>
            </summary>
            <param name="name"><para>The name of the parameter.</para></param>
            <returns><para>An unconfigured parameter.</para></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.DeriveParameters(System.Data.Common.DbCommand)">
            <summary>
            Retrieves parameter information from the stored procedure specified in the <see cref="T:System.Data.Common.DbCommand"/> and populates the Parameters collection of the specified <see cref="T:System.Data.Common.DbCommand"/> object. 
            </summary>
            <param name="discoveryCommand">The <see cref="T:System.Data.Common.DbCommand"/> to do the discovery.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.DiscoverParameters(System.Data.Common.DbCommand)">
            <summary>
            Discovers the parameters for a <see cref="T:System.Data.Common.DbCommand"/>.
            </summary>
            <param name="command">The <see cref="T:System.Data.Common.DbCommand"/> to discover the parameters.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.DoExecuteNonQuery(System.Data.Common.DbCommand)">
            <summary>
            Executes the query for <paramref name="command"/>.
            </summary>
            <param name="command">The <see cref="T:System.Data.Common.DbCommand"/> representing the query to execute.</param>
            <returns>The quantity of rows affected.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteDataSet(System.Data.Common.DbCommand)">
            <summary>
            <para>Executes the <paramref name="command"/> and returns the results in a new <see cref="T:System.Data.DataSet"/>.</para>
            </summary>
            <param name="command"><para>The <see cref="T:System.Data.Common.DbCommand"/> to execute.</para></param>
            <returns>A <see cref="T:System.Data.DataSet"/> with the results of the <paramref name="command"/>.</returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteDataSet(System.Data.Common.DbCommand,System.Data.Common.DbTransaction)">
            <summary>
            <para>Executes the <paramref name="command"/> as part of the <paramref name="transaction"/> and returns the results in a new <see cref="T:System.Data.DataSet"/>.</para>
            </summary>
            <param name="command"><para>The <see cref="T:System.Data.Common.DbCommand"/> to execute.</para></param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <returns>A <see cref="T:System.Data.DataSet"/> with the results of the <paramref name="command"/>.</returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteDataSet(System.String,System.Object[])">
            <summary>
            <para>Executes the <paramref name="storedProcedureName"/> with <paramref name="parameterValues"/> and returns the results in a new <see cref="T:System.Data.DataSet"/>.</para>
            </summary>
            <param name="storedProcedureName">
            <para>The stored procedure to execute.</para>
            </param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>A <see cref="T:System.Data.DataSet"/> with the results of the <paramref name="storedProcedureName"/>.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteDataSet(System.Data.Common.DbTransaction,System.String,System.Object[])">
            <summary>
            <para>Executes the <paramref name="storedProcedureName"/> with <paramref name="parameterValues"/> as part of the <paramref name="transaction"/> and returns the results in a new <see cref="T:System.Data.DataSet"/> within a transaction.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <param name="storedProcedureName">
            <para>The stored procedure to execute.</para>
            </param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>A <see cref="T:System.Data.DataSet"/> with the results of the <paramref name="storedProcedureName"/>.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteDataSet(System.Data.CommandType,System.String)">
            <summary>
            <para>Executes the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> and returns the results in a new <see cref="T:System.Data.DataSet"/>.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <returns>
            <para>A <see cref="T:System.Data.DataSet"/> with the results of the <paramref name="commandText"/>.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteDataSet(System.Data.Common.DbTransaction,System.Data.CommandType,System.String)">
            <summary>
            <para>Executes the <paramref name="commandText"/> as part of the given <paramref name="transaction"/> and returns the results in a new <see cref="T:System.Data.DataSet"/>.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <returns>
            <para>A <see cref="T:System.Data.DataSet"/> with the results of the <paramref name="commandText"/>.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.Common.DbCommand)">
            <summary>
            <para>Executes the <paramref name="command"/> and returns the number of rows affected.</para>
            </summary>
            <param name="command">
            <para>The command that contains the query to execute.</para>
            </param>       
            <seealso cref="M:System.Data.IDbCommand.ExecuteScalar"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.Common.DbCommand,System.Data.Common.DbTransaction)">
            <summary>
            <para>Executes the <paramref name="command"/> within the given <paramref name="transaction"/>, and returns the number of rows affected.</para>
            </summary>
            <param name="command">
            <para>The command that contains the query to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <seealso cref="M:System.Data.IDbCommand.ExecuteScalar"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.String,System.Object[])">
            <summary>
            <para>Executes the <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> and returns the number of rows affected.</para>
            </summary>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>The number of rows affected</para>
            </returns>
            <seealso cref="M:System.Data.IDbCommand.ExecuteScalar"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.Common.DbTransaction,System.String,System.Object[])">
            <summary>
            <para>Executes the <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> within a transaction and returns the number of rows affected.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="parameterValues">
            <para>An array of parameters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>The number of rows affected.</para>
            </returns>
            <seealso cref="M:System.Data.IDbCommand.ExecuteScalar"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.CommandType,System.String)">
            <summary>
            <para>Executes the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> and returns the number of rows affected.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <returns>
            <para>The number of rows affected.</para>
            </returns>
            <seealso cref="M:System.Data.IDbCommand.ExecuteScalar"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.Common.DbTransaction,System.Data.CommandType,System.String)">
            <summary>
            <para>Executes the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> as part of the given <paramref name="transaction"/> and returns the number of rows affected.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <returns>
            <para>The number of rows affected</para>
            </returns>
            <seealso cref="M:System.Data.IDbCommand.ExecuteScalar"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.Common.DbCommand)">
            <summary>
            <para>Executes the <paramref name="command"/> and returns an <see cref="T:System.Data.IDataReader"></see> through which the result can be read.
            It is the responsibility of the caller to close the reader when finished.</para>
            </summary>
            <param name="command">
            <para>The command that contains the query to execute.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.Data.IDataReader"/> object.</para>
            </returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.CreateWrappedReader(Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper,System.Data.IDataReader)">
            <summary>
            All data readers get wrapped in objects so that they properly manage connections.
            Some derived Database classes will need to create a different wrapper, so this
            method is provided so that they can do this.
            </summary>
            <param name="connection">Connection + refcount.</param>
            <param name="innerReader">The reader to wrap.</param>
            <returns>The new reader.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.Common.DbCommand,System.Data.Common.DbTransaction)">
            <summary>
            <para>Executes the <paramref name="command"/> within a transaction and returns an <see cref="T:System.Data.IDataReader"></see> through which the result can be read.
            It is the responsibility of the caller to close the connection and reader when finished.</para>
            </summary>
            <param name="command">
            <para>The command that contains the query to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.Data.IDataReader"/> object.</para>
            </returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.String,System.Object[])">
            <summary>
            <para>Executes the <paramref name="storedProcedureName"/> with the given <paramref name="parameterValues"/> and returns an <see cref="T:System.Data.IDataReader"></see> through which the result can be read.
            It is the responsibility of the caller to close the connection and reader when finished.</para>
            </summary>        
            <param name="storedProcedureName">
            <para>The command that contains the query to execute.</para>
            </param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.Data.IDataReader"/> object.</para>
            </returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.Common.DbTransaction,System.String,System.Object[])">
            <summary>
            <para>Executes the <paramref name="storedProcedureName"/> with the given <paramref name="parameterValues"/> within the given <paramref name="transaction"/> and returns an <see cref="T:System.Data.IDataReader"></see> through which the result can be read.
            It is the responsibility of the caller to close the connection and reader when finished.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <param name="storedProcedureName">
            <para>The command that contains the query to execute.</para>
            </param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.Data.IDataReader"/> object.</para>
            </returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.CommandType,System.String)">
            <summary>
            <para>Executes the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> and returns an <see cref="T:System.Data.IDataReader"></see> through which the result can be read.
            It is the responsibility of the caller to close the connection and reader when finished.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.Data.IDataReader"/> object.</para>
            </returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.Common.DbTransaction,System.Data.CommandType,System.String)">
            <summary>
            <para>Executes the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> within the given 
            <paramref name="transaction"/> and returns an <see cref="T:System.Data.IDataReader"></see> through which the result can be read.
            It is the responsibility of the caller to close the connection and reader when finished.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.Data.IDataReader"/> object.</para>
            </returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbCommand)">
            <summary>
            <para>Executes the <paramref name="command"/> and returns the first column of the first row in the result set returned by the query. Extra columns or rows are ignored.</para>
            </summary>
            <param name="command">
            <para>The command that contains the query to execute.</para>
            </param>
            <returns>
            <para>The first column of the first row in the result set.</para>
            </returns>
            <seealso cref="M:System.Data.IDbCommand.ExecuteScalar"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbCommand,System.Data.Common.DbTransaction)">
            <summary>
            <para>Executes the <paramref name="command"/> within a <paramref name="transaction"/>, and returns the first column of the first row in the result set returned by the query. Extra columns or rows are ignored.</para>
            </summary>
            <param name="command">
            <para>The command that contains the query to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <returns>
            <para>The first column of the first row in the result set.</para>
            </returns>
            <seealso cref="M:System.Data.IDbCommand.ExecuteScalar"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.String,System.Object[])">
            <summary>
            <para>Executes the <paramref name="storedProcedureName"/> with the given <paramref name="parameterValues"/> and returns the first column of the first row in the result set returned by the query. Extra columns or rows are ignored.</para>
            </summary>
            <param name="storedProcedureName">
            <para>The stored procedure to execute.</para>
            </param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>The first column of the first row in the result set.</para>
            </returns>
            <seealso cref="M:System.Data.IDbCommand.ExecuteScalar"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbTransaction,System.String,System.Object[])">
            <summary>
            <para>Executes the <paramref name="storedProcedureName"/> with the given <paramref name="parameterValues"/> within a 
            <paramref name="transaction"/> and returns the first column of the first row in the result set returned by the query. Extra columns or rows are ignored.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <param name="storedProcedureName">
            <para>The stored procedure to execute.</para>
            </param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>The first column of the first row in the result set.</para>
            </returns>
            <seealso cref="M:System.Data.IDbCommand.ExecuteScalar"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.CommandType,System.String)">
            <summary>
            <para>Executes the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/>  and returns the first column of the first row in the result set returned by the query. Extra columns or rows are ignored.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <returns>
            <para>The first column of the first row in the result set.</para>
            </returns>
            <seealso cref="M:System.Data.IDbCommand.ExecuteScalar"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbTransaction,System.Data.CommandType,System.String)">
            <summary>
            <para>Executes the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> 
            within the given <paramref name="transaction"/> and returns the first column of the first row in the result set returned by the query. Extra columns or rows are ignored.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <returns>
            <para>The first column of the first row in the result set.</para>
            </returns>
            <seealso cref="M:System.Data.IDbCommand.ExecuteScalar"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.GetDataAdapter">
            <summary>
            Gets a DbDataAdapter with Standard update behavior.
            </summary>
            <returns>A <see cref="T:System.Data.Common.DbDataAdapter"/>.</returns>
            <seealso cref="T:System.Data.Common.DbDataAdapter"/>
            <devdoc>
            Created this new, public method instead of modifying the protected, abstract one so that there will be no
            breaking changes for any currently derived Database class.
            </devdoc>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.GetDataAdapter(Microsoft.Practices.EnterpriseLibrary.Data.UpdateBehavior)">
            <summary>
            Gets the DbDataAdapter with the given update behavior and connection from the proper derived class.
            </summary>
            <param name="updateBehavior">
            <para>One of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.UpdateBehavior"/> values.</para>
            </param>        
            <returns>A <see cref="T:System.Data.Common.DbDataAdapter"/>.</returns>
            <seealso cref="T:System.Data.Common.DbDataAdapter"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.GetOpenConnection">
            <summary>
            	Gets a "wrapped" connection that will be not be disposed if a transaction is
            	active (created by creating a <see cref="T:System.Transactions.TransactionScope"/> instance). The
            	connection will be disposed when no transaction is active.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.GetWrappedConnection">
            <summary>
            Gets a "wrapped" connection for use outside a transaction.
            </summary>
            <returns>The wrapped connection.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.GetParameterValue(System.Data.Common.DbCommand,System.String)">
            <summary>
            Gets a parameter value.
            </summary>
            <param name="command">The command that contains the parameter.</param>
            <param name="name">The name of the parameter.</param>
            <returns>The value of the parameter.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.GetSqlStringCommand(System.String)">
            <summary>
            <para>Creates a <see cref="T:System.Data.Common.DbCommand"/> for a SQL query.</para>
            </summary>
            <param name="query"><para>The text of the query.</para></param>        
            <returns><para>The <see cref="T:System.Data.Common.DbCommand"/> for the SQL query.</para></returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.GetStoredProcCommand(System.String)">
            <summary>
            <para>Creates a <see cref="T:System.Data.Common.DbCommand"/> for a stored procedure.</para>
            </summary>
            <param name="storedProcedureName"><para>The name of the stored procedure.</para></param>
            <returns><para>The <see cref="T:System.Data.Common.DbCommand"/> for the stored procedure.</para></returns>       
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.GetStoredProcCommand(System.String,System.Object[])">
            <summary>
            <para>Creates a <see cref="T:System.Data.Common.DbCommand"/> for a stored procedure.</para>
            </summary>
            <param name="storedProcedureName"><para>The name of the stored procedure.</para></param>
            <param name="parameterValues"><para>The list of parameters for the procedure.</para></param>
            <returns><para>The <see cref="T:System.Data.Common.DbCommand"/> for the stored procedure.</para></returns>
            <remarks>
            <para>The parameters for the stored procedure will be discovered and the values are assigned in positional order.</para>
            </remarks>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.AssignParameters(System.Data.Common.DbCommand,System.Object[])">
            <summary>
            <para>Discovers parameters on the <paramref name="command"/> and assigns the values from <paramref name="parameterValues"/> to the <paramref name="command"/>s Parameters list.</para>
            </summary>
            <param name="command">The command the parameeter values will be assigned to</param>
            <param name="parameterValues">The parameter values that will be assigned to the command.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.GetStoredProcCommandWithSourceColumns(System.String,System.String[])">
            <summary>
            Wraps around a derived class's implementation of the GetStoredProcCommandWrapper method and adds functionality for
            using this method with UpdateDataSet.  The GetStoredProcCommandWrapper method (above) that takes a params array 
            expects the array to be filled with VALUES for the parameters. This method differs from the GetStoredProcCommandWrapper 
            method in that it allows a user to pass in a string array. It will also dynamically discover the parameters for the 
            stored procedure and set the parameter's SourceColumns to the strings that are passed in. It does this by mapping 
            the parameters to the strings IN ORDER. Thus, order is very important.
            </summary>
            <param name="storedProcedureName"><para>The name of the stored procedure.</para></param>
            <param name="sourceColumns"><para>The list of DataFields for the procedure.</para></param>
            <returns><para>The <see cref="T:System.Data.Common.DbCommand"/> for the stored procedure.</para></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.LoadDataSet(System.Data.Common.DbCommand,System.Data.DataSet,System.String)">
            <summary>
            <para>Executes the <paramref name="command"/> and adds a new <see cref="T:System.Data.DataTable"></see> to the existing <see cref="T:System.Data.DataSet"></see>.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.Common.DbCommand"/> to execute.</para>
            </param>
            <param name="dataSet">
            <para>The <see cref="T:System.Data.DataSet"/> to load.</para>
            </param>
            <param name="tableName">
            <para>The name for the new <see cref="T:System.Data.DataTable"/> to add to the <see cref="T:System.Data.DataSet"/>.</para>
            </param>        
            <exception cref="T:System.ArgumentNullException">Any input parameter was <see langword="null"/> (<b>Nothing</b> in Visual Basic)</exception>
            <exception cref="T:System.ArgumentException">tableName was an empty string</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.LoadDataSet(System.Data.Common.DbCommand,System.Data.DataSet,System.String,System.Data.Common.DbTransaction)">
            <summary>
            <para>Executes the <paramref name="command"/> within the given <paramref name="transaction"/> and adds a new <see cref="T:System.Data.DataTable"></see> to the existing <see cref="T:System.Data.DataSet"></see>.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.Common.DbCommand"/> to execute.</para>
            </param>
            <param name="dataSet">
            <para>The <see cref="T:System.Data.DataSet"/> to load.</para>
            </param>
            <param name="tableName">
            <para>The name for the new <see cref="T:System.Data.DataTable"/> to add to the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>        
            <exception cref="T:System.ArgumentNullException">Any input parameter was <see langword="null"/> (<b>Nothing</b> in Visual Basic).</exception>
            <exception cref="T:System.ArgumentException">tableName was an empty string.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.LoadDataSet(System.Data.Common.DbCommand,System.Data.DataSet,System.String[])">
            <summary>
            <para>Loads a <see cref="T:System.Data.DataSet"/> from a <see cref="T:System.Data.Common.DbCommand"/>.</para>
            </summary>
            <param name="command">
            <para>The command to execute to fill the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
            <param name="dataSet">
            <para>The <see cref="T:System.Data.DataSet"/> to fill.</para>
            </param>
            <param name="tableNames">
            <para>An array of table name mappings for the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.LoadDataSet(System.Data.Common.DbCommand,System.Data.DataSet,System.String[],System.Data.Common.DbTransaction)">
            <summary>
            <para>Loads a <see cref="T:System.Data.DataSet"/> from a <see cref="T:System.Data.Common.DbCommand"/> in  a transaction.</para>
            </summary>
            <param name="command">
            <para>The command to execute to fill the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
            <param name="dataSet">
            <para>The <see cref="T:System.Data.DataSet"/> to fill.</para>
            </param>
            <param name="tableNames">
            <para>An array of table name mappings for the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command in.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.LoadDataSet(System.String,System.Data.DataSet,System.String[],System.Object[])">
            <summary>
            <para>Loads a <see cref="T:System.Data.DataSet"/> with the results returned from a stored procedure.</para>
            </summary>
            <param name="storedProcedureName">
            <para>The stored procedure name to execute.</para>
            </param>
            <param name="dataSet">
            <para>The <see cref="T:System.Data.DataSet"/> to fill.</para>
            </param>
            <param name="tableNames">
            <para>An array of table name mappings for the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.LoadDataSet(System.Data.Common.DbTransaction,System.String,System.Data.DataSet,System.String[],System.Object[])">
            <summary>
            <para>Loads a <see cref="T:System.Data.DataSet"/> with the results returned from a stored procedure executed in a transaction.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the stored procedure in.</para>
            </param>
            <param name="storedProcedureName">
            <para>The stored procedure name to execute.</para>
            </param>
            <param name="dataSet">
            <para>The <see cref="T:System.Data.DataSet"/> to fill.</para>
            </param>
            <param name="tableNames">
            <para>An array of table name mappings for the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.LoadDataSet(System.Data.CommandType,System.String,System.Data.DataSet,System.String[])">
            <summary>
            <para>Loads a <see cref="T:System.Data.DataSet"/> from command text.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="dataSet">
            <para>The <see cref="T:System.Data.DataSet"/> to fill.</para>
            </param>
            <param name="tableNames">
            <para>An array of table name mappings for the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.LoadDataSet(System.Data.Common.DbTransaction,System.Data.CommandType,System.String,System.Data.DataSet,System.String[])">
            <summary>
            <para>Loads a <see cref="T:System.Data.DataSet"/> from command text in a transaction.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command in.</para>
            </param>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="dataSet">
            <para>The <see cref="T:System.Data.DataSet"/> to fill.</para>
            </param>
            <param name="tableNames">
            <para>An array of table name mappings for the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.PrepareCommand(System.Data.Common.DbCommand,System.Data.Common.DbConnection)">
            <summary>
            <para>Assigns a <paramref name="connection"/> to the <paramref name="command"/> and discovers parameters if needed.</para>
            </summary>
            <param name="command"><para>The command that contains the query to prepare.</para></param>
            <param name="connection">The connection to assign to the command.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.PrepareCommand(System.Data.Common.DbCommand,System.Data.Common.DbTransaction)">
            <summary>
            <para>Assigns a <paramref name="transaction"/> to the <paramref name="command"/> and discovers parameters if needed.</para>
            </summary>
            <param name="command"><para>The command that contains the query to prepare.</para></param>
            <param name="transaction">The transaction to assign to the command.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.SameNumberOfParametersAndValues(System.Data.Common.DbCommand,System.Object[])">
            <summary>
            Determines if the number of parameters in the command matches the array of parameter values.
            </summary>
            <param name="command">The <see cref="T:System.Data.Common.DbCommand"/> containing the parameters.</param>
            <param name="values">The array of parameter values.</param>
            <returns><see langword="true"/> if the number of parameters and values match; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.SetParameterValue(System.Data.Common.DbCommand,System.String,System.Object)">
            <summary>
            Sets a parameter value.
            </summary>
            <param name="command">The command with the parameter.</param>
            <param name="parameterName">The parameter name.</param>
            <param name="value">The parameter value.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.SetUpRowUpdatedEvent(System.Data.Common.DbDataAdapter)">
            <summary>
            Sets the RowUpdated event for the data adapter.
            </summary>
            <param name="adapter">The <see cref="T:System.Data.Common.DbDataAdapter"/> to set the event.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.UpdateDataSet(System.Data.DataSet,System.String,System.Data.Common.DbCommand,System.Data.Common.DbCommand,System.Data.Common.DbCommand,Microsoft.Practices.EnterpriseLibrary.Data.UpdateBehavior,System.Nullable{System.Int32})">
            <summary>
            <para>Calls the respective INSERT, UPDATE, or DELETE statements for each inserted, updated, or deleted row in the <see cref="T:System.Data.DataSet"/>.</para>
            </summary>        
            <param name="dataSet"><para>The <see cref="T:System.Data.DataSet"/> used to update the data source.</para></param>
            <param name="tableName"><para>The name of the source table to use for table mapping.</para></param>
            <param name="insertCommand"><para>The <see cref="T:System.Data.Common.DbCommand"/> executed when <see cref="T:System.Data.DataRowState"/> is <seealso cref="F:System.Data.DataRowState.Added"/></para></param>
            <param name="updateCommand"><para>The <see cref="T:System.Data.Common.DbCommand"/> executed when <see cref="T:System.Data.DataRowState"/> is <seealso cref="F:System.Data.DataRowState.Modified"/></para></param>        
            <param name="deleteCommand"><para>The <see cref="T:System.Data.Common.DbCommand"/> executed when <see cref="T:System.Data.DataRowState"/> is <seealso cref="F:System.Data.DataRowState.Deleted"/></para></param>        
            <param name="updateBehavior"><para>One of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.UpdateBehavior"/> values.</para></param>
            <param name="updateBatchSize">The number of database commands to execute in a batch.</param>
            <returns>number of records affected</returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.UpdateDataSet(System.Data.DataSet,System.String,System.Data.Common.DbCommand,System.Data.Common.DbCommand,System.Data.Common.DbCommand,Microsoft.Practices.EnterpriseLibrary.Data.UpdateBehavior)">
            <summary>
            <para>Calls the respective INSERT, UPDATE, or DELETE statements for each inserted, updated, or deleted row in the <see cref="T:System.Data.DataSet"/>.</para>
            </summary>        
            <param name="dataSet"><para>The <see cref="T:System.Data.DataSet"/> used to update the data source.</para></param>
            <param name="tableName"><para>The name of the source table to use for table mapping.</para></param>
            <param name="insertCommand"><para>The <see cref="T:System.Data.Common.DbCommand"/> executed when <see cref="T:System.Data.DataRowState"/> is <seealso cref="F:System.Data.DataRowState.Added"/></para></param>
            <param name="updateCommand"><para>The <see cref="T:System.Data.Common.DbCommand"/> executed when <see cref="T:System.Data.DataRowState"/> is <seealso cref="F:System.Data.DataRowState.Modified"/></para></param>        
            <param name="deleteCommand"><para>The <see cref="T:System.Data.Common.DbCommand"/> executed when <see cref="T:System.Data.DataRowState"/> is <seealso cref="F:System.Data.DataRowState.Deleted"/></para></param>        
            <param name="updateBehavior"><para>One of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.UpdateBehavior"/> values.</para></param>
            <returns>number of records affected</returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.UpdateDataSet(System.Data.DataSet,System.String,System.Data.Common.DbCommand,System.Data.Common.DbCommand,System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.Nullable{System.Int32})">
            <summary>
            <para>Calls the respective INSERT, UPDATE, or DELETE statements for each inserted, updated, or deleted row in the <see cref="T:System.Data.DataSet"/> within a transaction.</para>
            </summary>        
            <param name="dataSet"><para>The <see cref="T:System.Data.DataSet"/> used to update the data source.</para></param>
            <param name="tableName"><para>The name of the source table to use for table mapping.</para></param>
            <param name="insertCommand"><para>The <see cref="T:System.Data.Common.DbCommand"/> executed when <see cref="T:System.Data.DataRowState"/> is <seealso cref="F:System.Data.DataRowState.Added"/>.</para></param>
            <param name="updateCommand"><para>The <see cref="T:System.Data.Common.DbCommand"/> executed when <see cref="T:System.Data.DataRowState"/> is <seealso cref="F:System.Data.DataRowState.Modified"/>.</para></param>        
            <param name="deleteCommand"><para>The <see cref="T:System.Data.Common.DbCommand"/> executed when <see cref="T:System.Data.DataRowState"/> is <seealso cref="F:System.Data.DataRowState.Deleted"/>.</para></param>        
            <param name="transaction"><para>The <see cref="T:System.Data.IDbTransaction"/> to use.</para></param>
            <param name="updateBatchSize">The number of commands that can be executed in a single call to the database. Set to 0 to
            use the largest size the server can handle, 1 to disable batch updates, and anything else to set the number of rows.
            </param>
            <returns>Number of records affected.</returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.UpdateDataSet(System.Data.DataSet,System.String,System.Data.Common.DbCommand,System.Data.Common.DbCommand,System.Data.Common.DbCommand,System.Data.Common.DbTransaction)">
            <summary>
            <para>Calls the respective INSERT, UPDATE, or DELETE statements for each inserted, updated, or deleted row in the <see cref="T:System.Data.DataSet"/> within a transaction.</para>
            </summary>        
            <param name="dataSet"><para>The <see cref="T:System.Data.DataSet"/> used to update the data source.</para></param>
            <param name="tableName"><para>The name of the source table to use for table mapping.</para></param>
            <param name="insertCommand"><para>The <see cref="T:System.Data.Common.DbCommand"/> executed when <see cref="T:System.Data.DataRowState"/> is <seealso cref="F:System.Data.DataRowState.Added"/>.</para></param>
            <param name="updateCommand"><para>The <see cref="T:System.Data.Common.DbCommand"/> executed when <see cref="T:System.Data.DataRowState"/> is <seealso cref="F:System.Data.DataRowState.Modified"/>.</para></param>        
            <param name="deleteCommand"><para>The <see cref="T:System.Data.Common.DbCommand"/> executed when <see cref="T:System.Data.DataRowState"/> is <seealso cref="F:System.Data.DataRowState.Deleted"/>.</para></param>        
            <param name="transaction"><para>The <see cref="T:System.Data.IDbTransaction"/> to use.</para></param>
            <returns>Number of records affected.</returns>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteNonQuery(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <see cref="T:System.Data.Common.DbCommand"/> which will return the number of affected records.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.Common.DbCommand"/> to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)"/>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)"/>, 
            which returns the number of affected records.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteNonQuery(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <see cref="T:System.Data.Common.DbCommand"/> inside a transaction which will return the number of affected records.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.Common.DbCommand"/> to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)"/>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)"/>, 
            which returns the number of affected records.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteNonQuery(System.String,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> which will return the number of rows affected.</para>
            </summary>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)"/>, 
            which returns the number of affected records.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.String,System.Object[])"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteNonQuery(System.Data.Common.DbTransaction,System.String,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> inside a transaction which will return the number of rows affected.</para>
            </summary>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)"/>, 
            which returns the number of affected records.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.String,System.Object[])"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteNonQuery(System.Data.CommandType,System.String,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> which will return the number of rows affected.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)"/>, 
            which returns the number of affected records.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.CommandType,System.String)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteNonQuery(System.Data.Common.DbTransaction,System.Data.CommandType,System.String,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> inside a tranasaction which will return the number of rows affected.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)"/>, 
            which returns the number of affected records.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.CommandType,System.String)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteNonQuery(System.IAsyncResult)">
            <summary>
            Finishes asynchronous execution of a SQL statement, returning the number of affected records.
            </summary>
            <param name="asyncResult">
            <para>The <see cref="T:System.IAsyncResult"/> returned by a call to any overload of <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteNonQuery(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)"/>.</para>
            </param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteNonQuery(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteNonQuery(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)"/>
            <returns>
            <para>The number of affected records.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteReader(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of a <paramref name="command"/> which will return a <see cref="T:System.Data.IDataReader"/>.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.Common.DbCommand"/> to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Data.IDataReader"/>.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteReader(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of a <paramref name="command"/> inside a transaction which will return a <see cref="T:System.Data.IDataReader"/>.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.Common.DbCommand"/> to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Data.IDataReader"/>.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteReader(System.String,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            <para>Initiates the asynchronous execution of <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> which will return a <see cref="T:System.Data.IDataReader"/>.</para>
            </summary>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <param name="parameterValues">
            <para>An array of parameters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Data.IDataReader"/>.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.String,System.Object[])"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteReader(System.Data.Common.DbTransaction,System.String,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            <para>Initiates the asynchronous execution of <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> inside a transaction which will return a <see cref="T:System.Data.IDataReader"/>.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <param name="parameterValues">
            <para>An array of parameters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Data.IDataReader"/>.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.Common.DbTransaction,System.String,System.Object[])"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteReader(System.Data.CommandType,System.String,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="commandText"/> 
            interpreted as specified by the <paramref name="commandType"/> which will return 
            a <see cref="T:System.Data.IDataReader"/>. When the async operation completes, the
            <paramref name="callback"/> will be invoked on another thread to process the
            result.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="callback"><see cref="T:System.AsyncCallback"/> to execute when the async operation
            completes.</param>
            <param name="state">State object passed to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Data.IDataReader"/>.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.CommandType,System.String)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteReader(System.Data.Common.DbTransaction,System.Data.CommandType,System.String,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> inside an transaction which will return a <see cref="T:System.Data.IDataReader"/>.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Data.IDataReader"/>.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.CommandType,System.String)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteReader(System.IAsyncResult)">
            <summary>
            Finishes asynchronous execution of a Transact-SQL statement, returning an <see cref="T:System.Data.IDataReader"/>.
            </summary>
            <param name="asyncResult">
            <para>The <see cref="T:System.IAsyncResult"/> returned by a call to any overload of BeginExecuteReader.</para>
            </param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteReader(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteReader(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)"/>
            <returns>
            <para>An <see cref="T:System.Data.IDataReader"/> object that can be used to consume the queried information.</para>
            </returns>     
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteScalar(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of a <paramref name="command"/> which will return a single value.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.Common.DbCommand"/> to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)"/>, 
            which returns the actual result.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteScalar(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of a <paramref name="command"/> inside a transaction which will return a single value.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.Common.DbCommand"/> to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)"/>, 
            which returns the actual result.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbCommand,System.Data.Common.DbTransaction)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteScalar(System.String,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            <para>Initiates the asynchronous execution of <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> which will return a single value.</para>
            </summary>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <param name="parameterValues">
            <para>An array of parameters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)"/>, 
            which returns the actual result.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.String,System.Object[])"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteScalar(System.Data.Common.DbTransaction,System.String,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            <para>Initiates the asynchronous execution of <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> inside a transaction which will return a single value.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <param name="parameterValues">
            <para>An array of parameters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)"/>, 
            which returns the actual result.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbTransaction,System.String,System.Object[])"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteScalar(System.Data.CommandType,System.String,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> which will return a single value.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)"/>, 
            which returns the actual result.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.CommandType,System.String)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteScalar(System.Data.Common.DbTransaction,System.Data.CommandType,System.String,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> inside an transaction which will return a single value.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)"/>, 
            which returns the actual result.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbTransaction,System.Data.CommandType,System.String)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.EndExecuteScalar(System.IAsyncResult)">
            <summary>
            <para>Finishes asynchronous execution of a Transact-SQL statement, returning the first column of the first row in the result set returned by the query. Extra columns or rows are ignored.</para>
            </summary>
            <param name="asyncResult">
            <para>The <see cref="T:System.IAsyncResult"/> returned by a call to any overload of BeginExecuteScalar.</para>
            </param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteScalar(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.BeginExecuteScalar(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)"/>
            <returns>
            <para>The value of the first column of the first row in the result set returned by the query.
            If the result didn't have any columns or rows <see langword="null"/> (<b>Nothing</b> in Visual Basic).</para>
            </returns>     
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.UserParametersStartIndex">
            <summary>
            Returns the starting index for parameters in a command.
            </summary>
            <returns>The starting index for parameters in a command.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Database.ConnectionString">
            <summary>
            <para>Gets the string used to open a database.</para>
            </summary>
            <value>
            <para>The string used to open a database.</para>
            </value>
            <seealso cref="P:System.Data.Common.DbConnection.ConnectionString"/>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Database.ConnectionStringNoCredentials">
            <summary>
            <para>Gets the connection string without the username and password.</para>
            </summary>
            <value>
            <para>The connection string without the username and password.</para>
            </value>
            <seealso cref="P:Microsoft.Practices.EnterpriseLibrary.Data.Database.ConnectionString"/>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Database.ConnectionStringWithoutCredentials">
            <summary>
            Gets the connection string without credentials.
            </summary>
            <value>
            The connection string without credentials.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Database.DbProviderFactory">
            <summary>
            <para>Gets the DbProviderFactory used by the database instance.</para>
            </summary>
            <seealso cref="P:Microsoft.Practices.EnterpriseLibrary.Data.Database.DbProviderFactory"/>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Database.SupportsParemeterDiscovery">
            <summary>
            Does this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> object support parameter discovery?
            </summary>
            <value>Base class always returns false.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Database.SupportsAsync">
            <summary>
            Does this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> object support asynchronous execution?
            </summary>
            <value>Base class always returns false.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Database.OldConnectionWrapper">
            <summary>
            	This is a helper class that is used to manage the lifetime of a connection for various
            	Execute methods. We needed this class to support implicit transactions created with
            	the <see cref="T:System.Transactions.TransactionScope"/> class. In this case, the various Execute methods
            	need to use a shared connection instead of a new connection for each request in order
            	to prevent a distributed transaction.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.OldConnectionWrapper.#ctor(System.Data.Common.DbConnection,System.Boolean)">
            <summary>
            	Create a new "lifetime" container for a <see cref="T:System.Data.Common.DbConnection"/> instance.
            </summary>
            <param name="connection">The connection</param>
            <param name="disposeConnection">
            	Whether or not to dispose of the connection when this class is disposed.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.OldConnectionWrapper.Dispose">
            <summary>
            	Dispose the wrapped connection, if appropriate.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Database.OldConnectionWrapper.Connection">
            <summary>
            	Gets the actual connection.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseFactory">
            <summary>
            Contains factory methods for creating <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> objects.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseFactory.CreateDatabase">
            <summary>
            Creates the default <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> object
            </summary>
            <example>
            <code>
            Database dbSvc = DatabaseFactory.CreateDatabase();
            </code>
            </example>
            <returns>The default database</returns>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">The configuration information cannot be read.</exception>
            <exception cref="T:System.InvalidOperationException">The database factory has not been intialized or some configuration information is missing.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseFactory.CreateDatabase(System.String)">
            <summary>
            Creates the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> object with the specified name.
            </summary>
            <example>
            <code>
            Database dbSvc = DatabaseFactory.CreateDatabase("SQL_Customers");
            </code>
            </example>
            <param name="name">The configuration key for database service</param>
            <returns>The database with the specified name</returns>
            <exception cref="T:System.Configuration.ConfigurationErrorsException">The configuration information cannot be read.</exception>
            <exception cref="T:System.InvalidOperationException">The database factory has not been intialized or some configuration information is missing.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseFactory.SetDatabaseProviderFactory(Microsoft.Practices.EnterpriseLibrary.Data.DatabaseProviderFactory,System.Boolean)">
            <summary>
            Sets the provider factory for the static <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseFactory"/>.
            </summary>
            <param name="factory">The provider factory.</param>
            <param name="throwIfSet"><see langword="true"/> to thrown an exception if the factory is already set; otherwise, <see langword="false"/>. Defaults to <see langword="true"/>.</param>
            <exception cref="T:System.InvalidOperationException">The factory is already set and <paramref name="throwIfSet"/> is <see langword="true"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseFactory.SetDatabases(System.Func{Microsoft.Practices.EnterpriseLibrary.Data.Database},System.Func{System.String,Microsoft.Practices.EnterpriseLibrary.Data.Database},System.Boolean)">
            <summary>
            Sets the database mappings.
            </summary>
            <param name="createDefaultDatabase">A method that returns the default database.</param>
            <param name="createNamedDatabase">A method that returns a database for the specified name.</param>
            <param name="throwIfSet"><see langword="true"/> to thrown an exception if the factory is already set; otherwise, <see langword="false"/>. Defaults to <see langword="true"/>.</param>
            <exception cref="T:System.InvalidOperationException">The factory is already set and <paramref name="throwIfSet"/> is <see langword="true"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseFactory.ClearDatabaseProviderFactory">
            <summary>
            Clears the provider factory for the static <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.DatabaseFactory"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.GenericDatabase">
            <summary>
            The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.GenericDatabase"/> is used when no specific behavior is required or known for a database.
            </summary>
            <remarks>
            This database exposes the <see cref="T:System.Data.Common.DbProviderFactory"/> used to allow for a provider 
            agnostic programming model.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.GenericDatabase.#ctor(System.String,System.Data.Common.DbProviderFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.GenericDatabase"/> class with a connection string and 
            a provider factory.
            </summary>
            <param name="connectionString">The connection string.</param>
            <param name="dbProviderFactory">The provider factory.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.GenericDatabase.DeriveParameters(System.Data.Common.DbCommand)">
            <summary>
            This operation is not supported in this class.
            </summary>
            <param name="discoveryCommand">The <see cref="T:System.Data.Common.DbCommand"/> to do the discovery.</param>
            <remarks>There is no generic way to do it, the operation is not implemented for <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.GenericDatabase"/>.</remarks>
            <exception cref="T:System.NotSupportedException">Thrown whenever this method is called.</exception>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase">
            <summary>
            <para>Represents an Oracle database.</para>
            </summary>
            <remarks> 
            <para>
            Internally uses Oracle .NET Managed Provider from Microsoft (<see cref="N:System.Data.OracleClient"/>) to connect to a Oracle 9i database.
            </para>  
            <para>
            When retrieving a result set, it will build the package name. The package name should be set based
            on the stored procedure prefix and this should be set via configuration. For 
            example, a package name should be set as prefix of "ENTLIB_" and package name of
            "pkgENTLIB_ARCHITECTURE". For your applications, this is required only if you are defining your stored procedures returning 
            ref cursors.
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase"/> class with a connection string and a list of Oracle packages.
            </summary>
            <param name="connectionString">The connection string for the database.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.#ctor(System.String,System.Collections.Generic.IEnumerable{Microsoft.Practices.EnterpriseLibrary.Data.Oracle.IOraclePackage})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase"/> class with a connection string and a list of Oracle packages.
            </summary>
            <param name="connectionString">The connection string for the database.</param>
            <param name="packages">A list of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.IOraclePackage"/> objects.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.AddParameter(System.Data.Common.DbCommand,System.String,System.Data.DbType,System.Int32,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Object)">
            <summary>
            <para>Adds a new instance of a <see cref="T:System.Data.Common.DbParameter"/> object to the command.</para>
            </summary>
            <param name="command">The command to add the parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.DbType"/> values.</para></param>
            <param name="size"><para>The maximum size of the data within the column.</para></param>
            <param name="direction"><para>One of the <see cref="T:System.Data.ParameterDirection"/> values.</para></param>
            <param name="nullable"><para>A value indicating whether the parameter accepts <see langword="null"/> (<b>Nothing</b> in Visual Basic) values.</para></param>
            <param name="precision"><para>The maximum number of digits used to represent the <paramref name="value"/>.</para></param>
            <param name="scale"><para>The number of decimal places to which <paramref name="value"/> is resolved.</para></param>
            <param name="sourceColumn"><para>The name of the source column mapped to the DataSet and used for loading or returning the <paramref name="value"/>.</para></param>
            <param name="sourceVersion"><para>One of the <see cref="T:System.Data.DataRowVersion"/> values.</para></param>
            <param name="value"><para>The value of the parameter.</para></param>       
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.AddParameter(System.Data.OracleClient.OracleCommand,System.String,System.Data.OracleClient.OracleType,System.Int32,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Object)">
            <summary>
            <para>Adds a new instance of an <see cref="T:System.Data.OracleClient.OracleParameter"/> object to the command.</para>
            </summary>
            <param name="command">The <see cref="T:System.Data.OracleClient.OracleCommand"/> to add the parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="oracleType"><para>One of the <see cref="T:System.Data.OracleClient.OracleType"/> values.</para></param>
            <param name="size"><para>The maximum size of the data within the column.</para></param>
            <param name="direction"><para>One of the <see cref="T:System.Data.ParameterDirection"/> values.</para></param>
            <param name="nullable"><para>A value indicating whether the parameter accepts <see langword="null"/> (<b>Nothing</b> in Visual Basic) values.</para></param>
            <param name="precision"><para>The maximum number of digits used to represent the <paramref name="value"/>.</para></param>
            <param name="scale"><para>The number of decimal places to which <paramref name="value"/> is resolved.</para></param>
            <param name="sourceColumn"><para>The name of the source column mapped to the DataSet and used for loading or returning the <paramref name="value"/>.</para></param>
            <param name="sourceVersion"><para>One of the <see cref="T:System.Data.DataRowVersion"/> values.</para></param>
            <param name="value"><para>The value of the parameter.</para></param>      
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.ExecuteReader(System.Data.Common.DbCommand)">
            <summary>
            Creates an <see cref="T:System.Data.OracleClient.OracleDataReader"/> based on the <paramref name="command"/>.
            </summary>
            <param name="command">The command wrapper to execute.</param>        
            <returns>An <see cref="T:System.Data.OracleClient.OracleDataReader"/> object.</returns>        
            <exception cref="T:System.ArgumentNullException">
            <para><paramref name="command"/> can not be <see langword="null"/> (Nothing in Visual Basic).</para>
            </exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.CreateWrappedReader(Microsoft.Practices.EnterpriseLibrary.Data.DatabaseConnectionWrapper,System.Data.IDataReader)">
            <summary>
            All data readers get wrapped in objects so that they properly manage connections.
            Some derived Database classes will need to create a different wrapper, so this
            method is provided so that they can do this.
            </summary>
            <param name="connection">Connection + refcount.</param>
            <param name="innerReader">The reader to wrap.</param>
            <returns>The new reader.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.ExecuteReader(System.Data.Common.DbCommand,System.Data.Common.DbTransaction)">
            <summary>
            <para>Creates an <see cref="T:System.Data.OracleClient.OracleDataReader"/> based on the <paramref name="command"/>.</para>
            </summary>        
            <param name="command"><para>The command wrapper to execute.</para></param>        
            <param name="transaction"><para>The transaction to participate in when executing this reader.</para></param>        
            <returns><para>An <see cref="T:System.Data.OracleClient.OracleDataReader"/> object.</para></returns>
            <exception cref="T:System.ArgumentNullException">
            <para><paramref name="command"/> can not be <see langword="null"/> (Nothing in Visual Basic).</para>
            <para>- or -</para>
            <para><paramref name="transaction"/> can not be <see langword="null"/> (Nothing in Visual Basic).</para>
            </exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.ExecuteDataSet(System.Data.Common.DbCommand)">
            <summary>
            <para>Executes a command and returns the results in a new <see cref="T:System.Data.DataSet"/>.</para>
            </summary>
            <param name="command"><para>The command to execute to fill the <see cref="T:System.Data.DataSet"/></para></param>
            <returns><para>A <see cref="T:System.Data.DataSet"/> filed with records and, if necessary, schema.</para></returns>
            <exception cref="T:System.ArgumentNullException">
            <para><paramref name="command"/> can not be <see langword="null"/> (Nothing in Visual Basic).</para>
            </exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.ExecuteDataSet(System.Data.Common.DbCommand,System.Data.Common.DbTransaction)">
            <summary>
            <para>Executes a command and returns the result in a new <see cref="T:System.Data.DataSet"/>.</para>
            </summary>
            <param name="command"><para>The command to execute to fill the <see cref="T:System.Data.DataSet"/></para></param>
            <param name="transaction"><para>The transaction to participate in when executing this reader.</para></param>        
            <returns><para>A <see cref="T:System.Data.DataSet"/> filed with records and, if necessary, schema.</para></returns>
            <exception cref="T:System.ArgumentNullException">
            <para><paramref name="command"/> can not be <see langword="null"/> (<b>Nothing</b> in Visual Basic).</para>
            </exception>
            <exception cref="T:System.ArgumentNullException">
            <para><paramref name="command"/> can not be <see langword="null"/> (<b>Nothing</b> in Visual Basic).</para>
            <para>- or -</para>
            <para><paramref name="transaction"/> can not be <see langword="null"/> (<b>Nothing</b> in Visual Basic).</para>
            </exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.LoadDataSet(System.Data.Common.DbCommand,System.Data.DataSet,System.String[])">
            <summary>
            <para>Loads a <see cref="T:System.Data.DataSet"/> from a <see cref="T:System.Data.Common.DbCommand"/>.</para>
            </summary>
            <param name="command">
            <para>The command to execute to fill the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
            <param name="dataSet">
            <para>The <see cref="T:System.Data.DataSet"/> to fill.</para>
            </param>
            <param name="tableNames">
            <para>An array of table name mappings for the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.LoadDataSet(System.Data.Common.DbCommand,System.Data.DataSet,System.String[],System.Data.Common.DbTransaction)">
            <summary>
            <para>Loads a <see cref="T:System.Data.DataSet"/> from a <see cref="T:System.Data.Common.DbCommand"/> in a transaction.</para>
            </summary>
            <param name="command">
            <para>The command to execute to fill the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
            <param name="dataSet">
            <para>The <see cref="T:System.Data.DataSet"/> to fill.</para>
            </param>
            <param name="tableNames">
            <para>An array of table name mappings for the <see cref="T:System.Data.DataSet"/>.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command in.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.GetParameterValue(System.Data.Common.DbCommand,System.String)">
            <summary>
            Gets a parameter value.
            </summary>
            <param name="command">The command that contains the parameter.</param>
            <param name="parameterName">The name of the parameter.</param>
            <returns>The value of the parameter.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.SetParameterValue(System.Data.Common.DbCommand,System.String,System.Object)">
            <summary>
            Sets a parameter value.
            </summary>
            <param name="command">The command with the parameter.</param>
            <param name="parameterName">The parameter name.</param>
            <param name="value">The parameter value.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.PrepareCWRefCursor(System.Data.Common.DbCommand)">
            <devdoc>
            This is a private method that will build the Oracle package name if your stored procedure
            has proper prefix and postfix. 
            This functionality is include for
            the portability of the architecture between SQL and Oracle datbase.
            This method also adds the reference cursor to the command writer if not already added. This
            is required for Oracle .NET managed data provider.
            </devdoc>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.OnOracleRowUpdated(System.Object,System.Data.OracleClient.OracleRowUpdatedEventArgs)">
            <devdoc>
            Listens for the RowUpdate event on a data adapter to support UpdateBehavior.Continue
            </devdoc>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.DeriveParameters(System.Data.Common.DbCommand)">
            <summary>
            Retrieves parameter information from the stored procedure specified in the <see cref="T:System.Data.Common.DbCommand"/> and populates the Parameters collection of the specified <see cref="T:System.Data.Common.DbCommand"/> object. 
            </summary>
            <param name="discoveryCommand">The <see cref="T:System.Data.Common.DbCommand"/> to do the discovery.</param>
            <remarks>
            The <see cref="T:System.Data.Common.DbCommand"/> must be an instance of a <see cref="T:System.Data.OracleClient.OracleCommand"/> object.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.GetStoredProcCommand(System.String,System.Object[])">
            <summary>
            <para>Creates a <see cref="T:System.Data.Common.DbCommand"/> for a stored procedure.</para>
            </summary>
            <param name="storedProcedureName"><para>The name of the stored procedure.</para></param>
            <param name="parameterValues"><para>The list of parameters for the procedure.</para></param>
            <returns><para>The <see cref="T:System.Data.Common.DbCommand"/> for the stored procedure.</para></returns>
            <remarks>
            <para>The parameters for the stored procedure will be discovered and the values are assigned in positional order.</para>
            </remarks>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.AssignParameters(System.Data.Common.DbCommand,System.Object[])">
            <summary>
            <para>Discovers parameters on the <paramref name="command"/> and assigns the values from <paramref name="parameterValues"/> to the <paramref name="command"/>s Parameters list.</para>
            </summary>
            <param name="command">The command the parameeter values will be assigned to</param>
            <param name="parameterValues">The parameter values that will be assigned to the command.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.GetStoredProcCommand(System.String)">
            <summary>
            <para>Creates a <see cref="T:System.Data.Common.DbCommand"/> for a stored procedure.</para>
            </summary>
            <param name="storedProcedureName"><para>The name of the stored procedure.</para></param>		
            <returns><para>The <see cref="T:System.Data.Common.DbCommand"/> for the stored procedure.</para></returns>
            <remarks>
            <para>The parameters for the stored procedure will be discovered and the values are assigned in positional order.</para>
            </remarks>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.TranslatePackageSchema(System.String)">
            <devdoc>
            Looks into configuration and gets the information on how the command wrapper should be updated if calling a package on this
            connection.
            </devdoc>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.SetUpRowUpdatedEvent(System.Data.Common.DbDataAdapter)">
            <summary>
            Sets the RowUpdated event for the data adapter.
            </summary>
            <param name="adapter">The <see cref="T:System.Data.Common.DbDataAdapter"/> to set the event.</param>
            <remarks>The <see cref="T:System.Data.Common.DbDataAdapter"/> must be an <see cref="T:System.Data.OracleClient.OracleDataAdapter"/>.</remarks>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDatabase.SupportsParemeterDiscovery">
            <summary>
            Does this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> object support parameter discovery?
            </summary>
            <value>true.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDataReaderWrapper">
            <summary>
            A wrapper to convert data for Oracle for the reader.
            </summary>
            <remarks>
            The wrapper performs type conversions to enable retrieving values for types not supported natively by the
            <see cref="T:System.Data.OracleClient.OracleDataReader"/>.
            <para/>
            The wrapped data reader can be accessed through the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDataReaderWrapper.InnerReader"/>
            property.
            </remarks>
            <seealso cref="T:System.Data.IDataReader"/>
            <seealso cref="T:System.Data.OracleClient.OracleDataReader"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDataReaderWrapper.GetBoolean(System.Int32)">
            <summary>
            Gets the value of the specified column as a Boolean.
            </summary>
            <param name="index">The zero-based column ordinal.</param>
            <returns>The converted value of the column.</returns>
            <remarks>
            Bit data type is mapped to a number in Oracle database. When reading bit data from Oracle database,
            it will map to 0 as false and everything else as true.  This method uses System.Convert.ToBoolean() method
            for type conversions.
            </remarks>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDataReaderWrapper.GetByte(System.Int32)">
            <summary>
            Gets the value of the specified column converted to an 8-bit unsigned integer. 
            </summary>
            <param name="index">The zero-based column ordinal.</param>
            <returns>The 8-bit unsigned integer value of the specified column.</returns>
            <remarks> This method uses System.Convert.ToByte() method
            for type conversions.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDataReaderWrapper.GetGuid(System.Int32)">
            <summary>
            Gets the value of the specified field converted to a GUID.
            </summary>
            <param name="index">The index of the field to find.</param>
            <returns>The GUID of the specified field.</returns>
            <remarks>
            This method will cast the result data Guid data type. In Oracle you must use that as Raw(16) so
            that this method can convert that to Guid properly.
            </remarks>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDataReaderWrapper.GetInt16(System.Int32)">
            <summary>
            Gets the value of the specified field converted to a 16-bit signed integer.
            </summary>
            <param name="index">The index of the field to find.</param>
            <returns>The 16-bit signed integer value of the specified field.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.OracleDataReaderWrapper.InnerReader">
            <summary>
            Gets the wrapped <see cref="T:System.Data.OracleClient.OracleDataReader"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.RefCountingOracleDataReaderWrapper">
            <summary>
            Another wrapper for <see cref="T:System.Data.OracleClient.OracleDataReader"/> that adds connection
            reference counting.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.RefCountingOracleDataReaderWrapper.Close">
            <summary>
            Closes the <see cref="T:System.Data.IDataReader"/> Object.
            </summary>
            <filterpriority>2</filterpriority>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Oracle.RefCountingOracleDataReaderWrapper.Dispose(System.Boolean)">
            <summary>
            Close the contained data reader when disposing and releases the connection
            if it's not used anymore.
            </summary>
            <param name="disposing">True if called from Dispose method, false if called from finalizer. Since
            this class doesn't have a finalizer, this will always be true.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.ParameterCache">
            <summary>
            <para>
            Provides parameter caching services for dynamic parameter discovery of stored procedures.
            Eliminates the round-trip to the database to derive the parameters and types when a command
            is executed more than once.
            </para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.ParameterCache.SetParameters(System.Data.Common.DbCommand,Microsoft.Practices.EnterpriseLibrary.Data.Database)">
            <summary>
            <para>
            Populates the parameter collection for a command wrapper from the cache 
            or performs a round-trip to the database to query the parameters.
            </para>
            </summary>
            <param name="command">
            <para>The command to add the parameters.</para>
            </param>
            <param name="database">
            <para>The database to use to set the parameters.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.ParameterCache.Clear">
            <summary>
            <para>Empties the parameter cache.</para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.ParameterCache.AddParametersFromCache(System.Data.Common.DbCommand,Microsoft.Practices.EnterpriseLibrary.Data.Database)">
            <summary>
            <para>Adds parameters to a command using the cache.</para>
            </summary>
            <param name="command">
            <para>The command to add the parameters.</para>
            </param>
            <param name="database">The database to use.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.ParameterCache.AlreadyCached(System.Data.IDbCommand,Microsoft.Practices.EnterpriseLibrary.Data.Database)">
            <summary>
            <para>Checks to see if a cache entry exists for a specific command on a specific connection</para>
            </summary>
            <param name="command">
            <para>The command to check.</para>
            </param>
            <param name="database">The database to check.</param>
            <returns>True if the parameters are already cached for the provided command, false otherwise</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult">
            <summary>
            This class represents an asynchronous operation invoked from the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> class methods.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult.#ctor(System.IAsyncResult,System.Data.Common.DbCommand,System.Boolean,System.Boolean,System.DateTime)">
            <summary>
            Construct a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult"/> instance.
            </summary>
            <param name="innerAsyncResult">The <see cref="T:System.IAsyncResult"/> object returned from the underlying
            async operation.</param>
            <param name="command">Command that was executed.</param>
            <param name="disposeCommand">Should the command be disposed at EndInvoke time?</param>
            <param name="closeConnection">Should this connection be closed at EndInvoke time?</param>
            <param name="startTime">Time operation was invoked.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult.AsyncState">
            <summary>
            The state object passed to the callback.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult.AsyncWaitHandle">
            <summary>
            Wait handle to use to wait synchronously for completion.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult.CompletedSynchronously">
            <summary>
            True if begin operation completed synchronously.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult.IsCompleted">
            <summary>
            Has the operation finished?
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult.InnerAsyncResult">
            <summary>
            The underlying <see cref="T:System.IAsyncResult"/> object.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult.DisposeCommand">
            <summary>
            Should the command be disposed by the End method?
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult.Command">
            <summary>
            The command that was executed.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult.CloseConnection">
            <summary>
            Should the connection be closed by the End method?
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult.Connection">
            <summary>
            Connection the operation was invoked on.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.DaabAsyncResult.StartTime">
            <summary>
            Time the operation was started.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase">
            <summary>
            <para>Represents a SQL Server database.</para>
            </summary>
            <remarks> 
            <para>
            Internally uses SQL Server .NET Managed Provider from Microsoft (System.Data.SqlClient) to connect to the database.
            </para>  
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase"/> class with a connection string.
            </summary>
            <param name="connectionString">The connection string.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.ExecuteXmlReader(System.Data.Common.DbCommand)">
            <summary>
            <para>Executes the <see cref="T:System.Data.SqlClient.SqlCommand"/> and returns a new <see cref="T:System.Xml.XmlReader"/>.</para>
            </summary>
            <remarks>
            When the returned reader is closed, the underlying connection will be closed
            (with appropriate handling of connections in the case of an ambient transaction).
            This is a behavior change from Enterprise Library versions prior to v5.
            </remarks>
            <param name="command">
            <para>The <see cref="T:System.Data.SqlClient.SqlCommand"/> to execute.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.Xml.XmlReader"/> object.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.ExecuteXmlReader(System.Data.Common.DbCommand,System.Data.Common.DbTransaction)">
            <summary>
            <para>Executes the <see cref="T:System.Data.SqlClient.SqlCommand"/> in a transaction and returns a new <see cref="T:System.Xml.XmlReader"/>.</para>
            </summary>
            <remarks>
            	Unlike other Execute... methods that take a <see cref="T:System.Data.Common.DbCommand"/> instance, this method
            	does not set the command behavior to close the connection when you close the reader.
            	That means you'll need to close the connection yourself, by calling the
            	command.Connection.Close() method.
            </remarks>
            <param name="command">
            <para>The <see cref="T:System.Data.SqlClient.SqlCommand"/> to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.Xml.XmlReader"/> object.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteXmlReader(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <see cref="T:System.Data.SqlClient.SqlCommand"/> which will result in a <see cref="T:System.Xml.XmlReader"/>.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.SqlClient.SqlCommand"/> to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.ExecuteXmlReader(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteXmlReader(System.IAsyncResult)"/>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteXmlReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Xml.XmlReader"/> object.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteXmlReader(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <see cref="T:System.Data.SqlClient.SqlCommand"/> inside a transaction which will result in a <see cref="T:System.Xml.XmlReader"/>.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.SqlClient.SqlCommand"/> to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.IDbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.ExecuteXmlReader(System.Data.Common.DbCommand,System.Data.Common.DbTransaction)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteXmlReader(System.IAsyncResult)"/>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteXmlReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Xml.XmlReader"/> object.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteXmlReader(System.IAsyncResult)">
            <summary>
            Finishes asynchronous execution of a Transact-SQL statement, returning the requested data as XML.
            </summary>
            <param name="asyncResult">
            <para>The <see cref="T:System.IAsyncResult"/> returned by a call to any overload of <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteXmlReader(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)"/>.</para>
            </param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.ExecuteXmlReader(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteXmlReader(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteXmlReader(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)"/>
            <returns>
            <para>An <see cref="T:System.Xml.XmlReader"/> object that can be used to fetch the resulting XML data.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.DoExecuteXmlReader(System.Data.SqlClient.SqlCommand)">
            <devdoc>
            Execute the actual XML Reader call.
            </devdoc>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.OnSqlRowUpdated(System.Object,System.Data.SqlClient.SqlRowUpdatedEventArgs)">
            <devdoc>
            Listens for the RowUpdate event on a dataadapter to support UpdateBehavior.Continue
            </devdoc>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.DeriveParameters(System.Data.Common.DbCommand)">
            <summary>
            Retrieves parameter information from the stored procedure specified in the <see cref="T:System.Data.Common.DbCommand"/> and populates the Parameters collection of the specified <see cref="T:System.Data.Common.DbCommand"/> object. 
            </summary>
            <param name="discoveryCommand">The <see cref="T:System.Data.Common.DbCommand"/> to do the discovery.</param>
            <remarks>The <see cref="T:System.Data.Common.DbCommand"/> must be a <see cref="T:System.Data.SqlClient.SqlCommand"/> instance.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.UserParametersStartIndex">
            <summary>
            Returns the starting index for parameters in a command.
            </summary>
            <returns>The starting index for parameters in a command.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BuildParameterName(System.String)">
            <summary>
            Builds a value parameter name for the current database.
            </summary>
            <param name="name">The name of the parameter.</param>
            <returns>A correctly formated parameter name.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.SetUpRowUpdatedEvent(System.Data.Common.DbDataAdapter)">
            <summary>
            Sets the RowUpdated event for the data adapter.
            </summary>
            <param name="adapter">The <see cref="T:System.Data.Common.DbDataAdapter"/> to set the event.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.SameNumberOfParametersAndValues(System.Data.Common.DbCommand,System.Object[])">
            <summary>
            Determines if the number of parameters in the command matches the array of parameter values.
            </summary>
            <param name="command">The <see cref="T:System.Data.Common.DbCommand"/> containing the parameters.</param>
            <param name="values">The array of parameter values.</param>
            <returns><see langword="true"/> if the number of parameters and values match; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.AddParameter(System.Data.Common.DbCommand,System.String,System.Data.SqlDbType,System.Int32,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Object)">
            <summary>
            <para>Adds a new instance of a <see cref="T:System.Data.Common.DbParameter"/> object to the command.</para>
            </summary>
            <param name="command">The command to add the parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.DbType"/> values.</para></param>
            <param name="size"><para>The maximum size of the data within the column.</para></param>
            <param name="direction"><para>One of the <see cref="T:System.Data.ParameterDirection"/> values.</para></param>
            <param name="nullable"><para>A value indicating whether the parameter accepts <see langword="null"/> (<b>Nothing</b> in Visual Basic) values.</para></param>
            <param name="precision"><para>The maximum number of digits used to represent the <paramref name="value"/>.</para></param>
            <param name="scale"><para>The number of decimal places to which <paramref name="value"/> is resolved.</para></param>
            <param name="sourceColumn"><para>The name of the source column mapped to the DataSet and used for loading or returning the <paramref name="value"/>.</para></param>
            <param name="sourceVersion"><para>One of the <see cref="T:System.Data.DataRowVersion"/> values.</para></param>
            <param name="value"><para>The value of the parameter.</para></param>       
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.AddParameter(System.Data.Common.DbCommand,System.String,System.Data.SqlDbType,System.Data.ParameterDirection,System.String,System.Data.DataRowVersion,System.Object)">
            <summary>
            <para>Adds a new instance of a <see cref="T:System.Data.Common.DbParameter"/> object to the command.</para>
            </summary>
            <param name="command">The command to add the parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.SqlDbType"/> values.</para></param>        
            <param name="direction"><para>One of the <see cref="T:System.Data.ParameterDirection"/> values.</para></param>                
            <param name="sourceColumn"><para>The name of the source column mapped to the DataSet and used for loading or returning the <paramref name="value"/>.</para></param>
            <param name="sourceVersion"><para>One of the <see cref="T:System.Data.DataRowVersion"/> values.</para></param>
            <param name="value"><para>The value of the parameter.</para></param>    
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.AddOutParameter(System.Data.Common.DbCommand,System.String,System.Data.SqlDbType,System.Int32)">
            <summary>
            Adds a new Out <see cref="T:System.Data.Common.DbParameter"/> object to the given <paramref name="command"/>.
            </summary>
            <param name="command">The command to add the out parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.SqlDbType"/> values.</para></param>        
            <param name="size"><para>The maximum size of the data within the column.</para></param>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.AddInParameter(System.Data.Common.DbCommand,System.String,System.Data.SqlDbType)">
            <summary>
            Adds a new In <see cref="T:System.Data.Common.DbParameter"/> object to the given <paramref name="command"/>.
            </summary>
            <param name="command">The command to add the in parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.SqlDbType"/> values.</para></param>                
            <remarks>
            <para>This version of the method is used when you can have the same parameter object multiple times with different values.</para>
            </remarks>        
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.AddInParameter(System.Data.Common.DbCommand,System.String,System.Data.SqlDbType,System.Object)">
            <summary>
            Adds a new In <see cref="T:System.Data.Common.DbParameter"/> object to the given <paramref name="command"/>.
            </summary>
            <param name="command">The commmand to add the parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.SqlDbType"/> values.</para></param>                
            <param name="value"><para>The value of the parameter.</para></param>      
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.AddInParameter(System.Data.Common.DbCommand,System.String,System.Data.SqlDbType,System.String,System.Data.DataRowVersion)">
            <summary>
            Adds a new In <see cref="T:System.Data.Common.DbParameter"/> object to the given <paramref name="command"/>.
            </summary>
            <param name="command">The command to add the parameter.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.SqlDbType"/> values.</para></param>                
            <param name="sourceColumn"><para>The name of the source column mapped to the DataSet and used for loading or returning the value.</para></param>
            <param name="sourceVersion"><para>One of the <see cref="T:System.Data.DataRowVersion"/> values.</para></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.CreateParameter(System.String,System.Data.SqlDbType,System.Int32,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Object)">
            <summary>
            <para>Adds a new instance of a <see cref="T:System.Data.Common.DbParameter"/> object.</para>
            </summary>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.DbType"/> values.</para></param>
            <param name="size"><para>The maximum size of the data within the column.</para></param>
            <param name="direction"><para>One of the <see cref="T:System.Data.ParameterDirection"/> values.</para></param>
            <param name="nullable"><para>A value indicating whether the parameter accepts <see langword="null"/> (<b>Nothing</b> in Visual Basic) values.</para></param>
            <param name="precision"><para>The maximum number of digits used to represent the <paramref name="value"/>.</para></param>
            <param name="scale"><para>The number of decimal places to which <paramref name="value"/> is resolved.</para></param>
            <param name="sourceColumn"><para>The name of the source column mapped to the DataSet and used for loading or returning the <paramref name="value"/>.</para></param>
            <param name="sourceVersion"><para>One of the <see cref="T:System.Data.DataRowVersion"/> values.</para></param>
            <param name="value"><para>The value of the parameter.</para></param>  
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.ConfigureParameter(System.Data.SqlClient.SqlParameter,System.String,System.Data.SqlDbType,System.Int32,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.String,System.Data.DataRowVersion,System.Object)">
            <summary>
            Configures a given <see cref="T:System.Data.Common.DbParameter"/>.
            </summary>
            <param name="param">The <see cref="T:System.Data.Common.DbParameter"/> to configure.</param>
            <param name="name"><para>The name of the parameter.</para></param>
            <param name="dbType"><para>One of the <see cref="T:System.Data.SqlDbType"/> values.</para></param>
            <param name="size"><para>The maximum size of the data within the column.</para></param>
            <param name="direction"><para>One of the <see cref="T:System.Data.ParameterDirection"/> values.</para></param>
            <param name="nullable"><para>A value indicating whether the parameter accepts <see langword="null"/> (<b>Nothing</b> in Visual Basic) values.</para></param>
            <param name="precision"><para>The maximum number of digits used to represent the <paramref name="value"/>.</para></param>
            <param name="scale"><para>The number of decimal places to which <paramref name="value"/> is resolved.</para></param>
            <param name="sourceColumn"><para>The name of the source column mapped to the DataSet and used for loading or returning the <paramref name="value"/>.</para></param>
            <param name="sourceVersion"><para>One of the <see cref="T:System.Data.DataRowVersion"/> values.</para></param>
            <param name="value"><para>The value of the parameter.</para></param>  
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteNonQuery(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <see cref="T:System.Data.SqlClient.SqlCommand"/> which will return the number of affected records.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.SqlClient.SqlCommand"/> to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)"/>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)"/>, 
            which returns the number of affected records.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteNonQuery(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <see cref="T:System.Data.Common.DbCommand"/> inside a transaction which will return the number of affected records.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.SqlClient.SqlCommand"/> to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)"/>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)"/>, 
            which returns the number of affected records.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteNonQuery(System.String,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> which will return the number of rows affected.</para>
            </summary>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)"/>, 
            which returns the number of affected records.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.String,System.Object[])"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteNonQuery(System.Data.Common.DbTransaction,System.String,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> inside a transaction which will return the number of rows affected.</para>
            </summary>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <param name="parameterValues">
            <para>An array of paramters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)"/>, 
            which returns the number of affected records.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.String,System.Object[])"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteNonQuery(System.Data.CommandType,System.String,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> which will return the number of rows affected.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)"/>, 
            which returns the number of affected records.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.CommandType,System.String)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteNonQuery(System.Data.Common.DbTransaction,System.Data.CommandType,System.String,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> inside a tranasaction which will return the number of rows affected.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)"/>, 
            which returns the number of affected records.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.CommandType,System.String)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteNonQuery(System.IAsyncResult)">
            <summary>
            Finishes asynchronous execution of a Transact-SQL statement, returning the number of affected records.
            </summary>
            <param name="asyncResult">
            <para>The <see cref="T:System.IAsyncResult"/> returned by a call to any overload of <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteNonQuery(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)"/>.</para>
            </param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteNonQuery(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteNonQuery(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteNonQuery(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)"/>
            <returns>
            <para>The number of affected records.</para>
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteReader(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of a <paramref name="command"/> which will return a <see cref="T:System.Data.IDataReader"/>.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.SqlClient.SqlCommand"/> to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Data.IDataReader"/>.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteReader(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of a <paramref name="command"/> inside a transaction which will return a <see cref="T:System.Data.IDataReader"/>.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.SqlClient.SqlCommand"/> to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Data.IDataReader"/>.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteReader(System.String,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            <para>Initiates the asynchronous execution of <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> which will return a <see cref="T:System.Data.IDataReader"/>.</para>
            </summary>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <param name="parameterValues">
            <para>An array of parameters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Data.IDataReader"/>.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.String,System.Object[])"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteReader(System.Data.Common.DbTransaction,System.String,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            <para>Initiates the asynchronous execution of <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> inside a transaction which will return a <see cref="T:System.Data.IDataReader"/>.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <param name="parameterValues">
            <para>An array of parameters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Data.IDataReader"/>.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.Common.DbTransaction,System.String,System.Object[])"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteReader(System.Data.CommandType,System.String,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="commandText"/> 
            interpreted as specified by the <paramref name="commandType"/> which will return 
            a <see cref="T:System.Data.IDataReader"/>. When the async operation completes, the
            <paramref name="callback"/> will be invoked on another thread to process the
            result.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="callback"><see cref="T:System.AsyncCallback"/> to execute when the async operation
            completes.</param>
            <param name="state">State object passed to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Data.IDataReader"/>.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.CommandType,System.String)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteReader(System.Data.Common.DbTransaction,System.Data.CommandType,System.String,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> inside an transaction which will return a <see cref="T:System.Data.IDataReader"/>.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)"/>, 
            which returns the <see cref="T:System.Data.IDataReader"/>.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.CommandType,System.String)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteReader(System.IAsyncResult)">
            <summary>
            Finishes asynchronous execution of a Transact-SQL statement, returning an <see cref="T:System.Data.IDataReader"/>.
            </summary>
            <param name="asyncResult">
            <para>The <see cref="T:System.IAsyncResult"/> returned by a call to any overload of BeginExecuteReader.</para>
            </param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteReader(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteReader(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteReader(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)"/>
            <returns>
            <para>An <see cref="T:System.Data.IDataReader"/> object that can be used to consume the queried information.</para>
            </returns>     
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteScalar(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of a <paramref name="command"/> which will return a single value.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.SqlClient.SqlCommand"/> to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)"/>, 
            which returns the actual result.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteScalar(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of a <paramref name="command"/> inside a transaction which will return a single value.</para>
            </summary>
            <param name="command">
            <para>The <see cref="T:System.Data.SqlClient.SqlCommand"/> to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)"/>, 
            which returns the actual result.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbCommand,System.Data.Common.DbTransaction)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteScalar(System.String,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            <para>Initiates the asynchronous execution of <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> which will return a single value.</para>
            </summary>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <param name="parameterValues">
            <para>An array of parameters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)"/>, 
            which returns the actual result.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.String,System.Object[])"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteScalar(System.Data.Common.DbTransaction,System.String,System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            <para>Initiates the asynchronous execution of <paramref name="storedProcedureName"/> using the given <paramref name="parameterValues"/> inside a transaction which will return a single value.</para>
            </summary>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="storedProcedureName">
            <para>The name of the stored procedure to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <param name="parameterValues">
            <para>An array of parameters to pass to the stored procedure. The parameter values must be in call order as they appear in the stored procedure.</para>
            </param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)"/>, 
            which returns the actual result.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbTransaction,System.String,System.Object[])"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteScalar(System.Data.CommandType,System.String,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> which will return a single value.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)"/>, 
            which returns the actual result.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.CommandType,System.String)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteScalar(System.Data.Common.DbTransaction,System.Data.CommandType,System.String,System.AsyncCallback,System.Object)">
            <summary>
            <para>Initiates the asynchronous execution of the <paramref name="commandText"/> interpreted as specified by the <paramref name="commandType"/> inside an transaction which will return a single value.</para>
            </summary>
            <param name="commandType">
            <para>One of the <see cref="T:System.Data.CommandType"/> values.</para>
            </param>
            <param name="commandText">
            <para>The command text to execute.</para>
            </param>
            <param name="transaction">
            <para>The <see cref="T:System.Data.Common.DbTransaction"/> to execute the command within.</para>
            </param>
            <param name="callback">The async callback to execute when the result of the operation is available. Pass <langword>null</langword>
            if you don't want to use a callback.</param>
            <param name="state">Additional state object to pass to the callback.</param>
            <returns>
            <para>An <see cref="T:System.IAsyncResult"/> that can be used to poll or wait for results, or both; 
            this value is also needed when invoking <see cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)"/>, 
            which returns the actual result.</para>
            </returns>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbTransaction,System.Data.CommandType,System.String)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.EndExecuteScalar(System.IAsyncResult)">
            <summary>
            <para>Finishes asynchronous execution of a Transact-SQL statement, returning the first column of the first row in the result set returned by the query. Extra columns or rows are ignored.</para>
            </summary>
            <param name="asyncResult">
            <para>The <see cref="T:System.IAsyncResult"/> returned by a call to any overload of BeginExecuteScalar.</para>
            </param>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Database.ExecuteScalar(System.Data.Common.DbCommand)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteScalar(System.Data.Common.DbCommand,System.AsyncCallback,System.Object)"/>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.BeginExecuteScalar(System.Data.Common.DbCommand,System.Data.Common.DbTransaction,System.AsyncCallback,System.Object)"/>
            <returns>
            <para>The value of the first column of the first row in the result set returned by the query.
            If the result didn't have any columns or rows <see langword="null"/> (<b>Nothing</b> in Visual Basic).</para>
            </returns>     
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.ParameterToken">
            <summary>
            <para>Gets the parameter token used to delimit parameters for the SQL Server database.</para>
            </summary>
            <value>
            <para>The '@' symbol.</para>
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.SupportsAsync">
            <summary>
            Does this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> object support asynchronous execution?
            </summary>
            <value>true.</value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Sql.SqlDatabase.SupportsParemeterDiscovery">
            <summary>
            Does this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> object support parameter discovery?
            </summary>
            <value>true.</value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1">
            <summary>
            Represents a call to the database using SQL that will return an enumerable of <typeparamref name="TResult"/>.
            </summary>
            <typeparam name="TResult">The element type that will be used to consume the result set.</typeparam>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1.#ctor(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper{`0})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/> that works for a specific <paramref name="database"/>
            and uses <paramref name="rowMapper"/> to convert the returned rows to clr type <typeparamref name="TResult"/>.
            </summary>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> used to execute the SQL.</param>
            <param name="sqlString">The SQL that will be executed.</param>
            <param name="rowMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that will be used to convert the returned data to clr type <typeparamref name="TResult"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1.#ctor(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper{`0})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/> that works for a specific <paramref name="database"/>
            and uses <paramref name="resultSetMapper"/> to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.
            </summary>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> used to execute the SQL.</param>
            <param name="sqlString">The SQL that will be executed.</param>
            <param name="resultSetMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1"/> that will be used to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1.#ctor(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper,Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper{`0})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/> that works for a specific <paramref name="database"/>
            and uses <paramref name="rowMapper"/> to convert the returned rows to clr type <typeparamref name="TResult"/>.
            The <paramref name="parameterMapper"/> will be used to interpret the parameters passed to the Execute method.
            </summary>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> used to execute the SQL.</param>
            <param name="sqlString">The SQL that will be executed.</param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <param name="rowMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IRowMapper`1"/> that will be used to convert the returned data to clr type <typeparamref name="TResult"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1.#ctor(Microsoft.Practices.EnterpriseLibrary.Data.Database,System.String,Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper,Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper{`0})">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1"/> that works for a specific <paramref name="database"/>
            and uses <paramref name="resultSetMapper"/> to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.
            The <paramref name="parameterMapper"/> will be used to interpret the parameters passed to the Execute method.
            </summary>
            <param name="database">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> used to execute the SQL.</param>
            <param name="sqlString">The SQL that will be executed.</param>
            <param name="parameterMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> that will be used to interpret the parameters passed to the Execute method.</param>
            <param name="resultSetMapper">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IResultSetMapper`1"/> that will be used to convert the returned set to an enumerable of clr type <typeparamref name="TResult"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1.Execute(System.Object[])">
            <summary>
            Executes the SQL query and returns an enumerable of <typeparamref name="TResult"/>.
            The enumerable returned by this method uses deferred loading to return the results.
            </summary>
            <param name="parameterValues">Values that will be interpret by an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.IParameterMapper"/> and function as parameters to the Transact SQL query.</param>
            <returns>An enumerable of <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.SqlStringAccessor`1.BeginExecute(System.AsyncCallback,System.Object,System.Object[])">
            <summary>
            Begin executing the SQL query asynchronously. Only supported if the underlying
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Data.Database"/> object supports asynchronous operations.
            </summary>
            <param name="callback">Asynchronous callback to execute when the result of the query is
            available. May be null if no callback is desired.</param>
            <param name="state">Extra arbitrary state information to pass to the callback. May be null.</param>
            <param name="parameterValues">Parameters to pass to the sql query.</param>
            <returns>An <see cref="T:System.IAsyncResult"/> object representing the pending request.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Sql.RefCountingXmlReader">
            <summary>
            Wrapper around <see cref="T:System.Xml.XmlReader"/> that ties into our
            reference counting on connections.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.TransactionScopeConnections">
            <summary>
            	This class manages the connections that will be used when transactions are active
            	as a result of instantiating a <see cref="T:System.Transactions.TransactionScope"/>. When a transaction
            	is active, all database access must be through this single connection unless you want
            	to use a distributed transaction, which is an expensive operation.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.TransactionScopeConnections.GetConnection(Microsoft.Practices.EnterpriseLibrary.Data.Database)">
            <summary>
            	Returns a connection for the current transaction. This will be an existing <see cref="T:System.Data.Common.DbConnection"/>
            	instance or a new one if there is a <see cref="T:System.Transactions.TransactionScope"/> active. Otherwise this method
            	returns null.
            </summary>
            <param name="db"></param>
            <returns>Either a <see cref="T:System.Data.Common.DbConnection"/> instance or null.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Data.TransactionScopeConnections.OnTransactionCompleted(System.Object,System.Transactions.TransactionEventArgs)">
            <summary>
            	This event handler is called whenever a transaction is about to be disposed, which allows
            	us to remove the transaction from our list and dispose the connection instance we created.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.UpdateBehavior">
            <summary>
            Used with the Database.UpdateDataSet method. Provides control over behavior when the Data
            Adapter's update command encounters an error.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Data.UpdateBehavior.Standard">
            <summary>
            No interference with the DataAdapter's Update command. If Update encounters
            an error, the update stops.  Additional rows in the Datatable are uneffected.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Data.UpdateBehavior.Continue">
            <summary>
            If the DataAdapter's Update command encounters an error, the update will
            continue. The Update command will try to update the remaining rows. 
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Data.UpdateBehavior.Transactional">
            <summary>
            If the DataAdapter encounters an error, all updated rows will be rolled back.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.AsyncOperationsNotSupported">
            <summary>
              Looks up a localized string similar to The database type &quot;{0}&quot; does not support asynchronous operations..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.BlockName">
            <summary>
              Looks up a localized string similar to Enterprise Library Data Access Application Block.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ConfigurationFailureCreatingDatabase">
            <summary>
              Looks up a localized string similar to A configuration failure occurred while creating database ‘{0}’..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.DefaultSqlConnctionString">
            <summary>
              Looks up a localized string similar to Database=Database;Server=(local)\SQLEXPRESS;Integrated Security=SSPI.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ErrorConnectionFailedExtraInformation">
            <summary>
              Looks up a localized string similar to The connection string used was &quot;{0}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ErrorConnectionFailedMessage">
            <summary>
              Looks up a localized string similar to The error occurred using the &quot;{0}&quot; database instance while attempting to open the database connection..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionArgumentMustBePropertyExpression">
            <summary>
              Looks up a localized string similar to The expression must contain a MemberAccessExpression to a property ( t =&gt;t.Property)..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionArgumentMustInheritFromDatabase">
            <summary>
              Looks up a localized string similar to Type must inherit from Microsoft.Practices.EnterpriseLibrary.Data.Database.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionCannotCreateRowMapping">
            <summary>
              Looks up a localized string similar to Cannot create a mapping for type {0}..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionColumnNotFoundWhileMapping">
            <summary>
              Looks up a localized string similar to The column {0} was not found on the IDataRecord being evaluated. This might indicate that the accessor was created with the wrong mappings..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionCommandNotSqlCommand">
            <summary>
              Looks up a localized string similar to The command must be a SqlCommand..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionConvertionFailedWhenMappingPropertyToColumn">
            <summary>
              Looks up a localized string similar to The value from column &apos;{0}&apos; cannot be converted to property &apos;{1}&apos; of type &apos;{2}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionDatabaseDataTypeDoesNotHaveRequiredConstructor">
            <summary>
              Looks up a localized string similar to The configuration element type {0} does not have an accessible constructor taking a ConnectionStringSettings and an IConfigurationSource as constructor arguments..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionDatabaseDataTypeDoesNotInheritFromDatabaseData">
            <summary>
              Looks up a localized string similar to The configuration element type {0} does not inherit from the DatabaseData class..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionDatabaseInvalid">
            <summary>
              Looks up a localized string similar to The connection string for the database &apos;{0}&apos; does not exist or does not have a valid provider..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionDatabaseProviderFactoryAlreadySet">
            <summary>
              Looks up a localized string similar to The static DatabaseFactory already has a database provider factory or custom methods set..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionDatabaseProviderFactoryNotSet">
            <summary>
              Looks up a localized string similar to Database provider factory not set for the static DatabaseFactory. Set a provider factory invoking the DatabaseFactory.SetProviderFactory method or by specifying custom mappings by calling the DatabaseFactory.SetDatabases method..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionDatabaseTypeDoesNotHaveAssemblerAttribute">
            <summary>
              Looks up a localized string similar to The database type {0} for the name {1} does not have the required [DatabaseAssembler] attribute required to build the database object..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionDatabaseTypeDoesNotHaveRequiredConfigurationTypeAttribute">
            <summary>
              Looks up a localized string similar to The database type {0} does not have the required ConfigurationElementType attribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionDefaultDatabaseInvalid">
            <summary>
              Looks up a localized string similar to The connection string for the default database &apos;{0}&apos; does not exist or does not have a valid provider..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionMessageParameterMatchFailure">
            <summary>
              Looks up a localized string similar to The number of parameters does not match number of values for stored procedure..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionMessageUpdateDataSetArgumentFailure">
            <summary>
              Looks up a localized string similar to At least one command must be initialized.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionMessageUpdateDataSetRowFailure">
            <summary>
              Looks up a localized string similar to Failed to update row .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionNoConfigurationElementTypeAttribute">
            <summary>
              Looks up a localized string similar to The type {0} does not contain the ConfigurationElementTypeAttribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionNoDatabaseDefined">
            <summary>
              Looks up a localized string similar to The requested database {0} is not defined in configuration..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionNoDefaultDatabaseDefined">
            <summary>
              Looks up a localized string similar to The configuration file does not define a default database..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionNoProviderDefinedForConnectionString">
            <summary>
              Looks up a localized string similar to The requested database {0} does not have provider name set in the connection string..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionNoValidProviderForConnectionString">
            <summary>
              Looks up a localized string similar to The requested database {0} does not have a valid ADO.NET provider name set in the connection string..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionNullOrEmptyString">
            <summary>
              Looks up a localized string similar to The value can not be null or an empty string..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionParameterDiscoveryNotSupported">
            <summary>
              Looks up a localized string similar to The database type &quot;{0}&quot; does not support automatic parameter discovery. Use an IParameterMapper instead..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionParameterDiscoveryNotSupportedOnGenericDatabase">
            <summary>
              Looks up a localized string similar to Parameter discovery is not supported for connections using GenericDatabase. You must specify the parameters explicitly, or configure the connection to use a type deriving from Database that supports parameter discovery..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionSqlStringAccessorCannotDiscoverParameters">
            <summary>
              Looks up a localized string similar to Sql String Accessor cannot be used to discover parameters. Use an IParameterMapper instead..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionTableNameArrayEmpty">
            <summary>
              Looks up a localized string similar to The table name array used to map results to user-specified table names cannot be empty..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.ExceptionTypeNotDatabaseAssembler">
            <summary>
              Looks up a localized string similar to Type {0} is not an implementation of IDatabaseAssembler for DatabaseAssemblerAttribute..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.Password">
            <summary>
              Looks up a localized string similar to password=,pwd=.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Data.Properties.Resources.UserName">
            <summary>
              Looks up a localized string similar to user id=,uid=.
            </summary>
        </member>
    </members>
</doc>
