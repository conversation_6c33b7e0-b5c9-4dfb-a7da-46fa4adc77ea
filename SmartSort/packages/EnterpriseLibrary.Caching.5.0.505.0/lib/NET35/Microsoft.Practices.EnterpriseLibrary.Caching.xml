<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Practices.EnterpriseLibrary.Caching</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackgroundScheduler">
            <summary>
            Represents a cache scavenger that runs on a background thread.
            </summary>
            <remarks>
            The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackgroundScheduler"/> will make its best effort to avoid scheduling a new scavenge request 
            when it is safe to assume that it's not necessary. Since <see cref="P:Microsoft.Practices.EnterpriseLibrary.Caching.ScavengerTask.NumberOfItemsToBeScavenged"/> 
            elements are scavenged each time, there should be at least one scavenge request every 
            <see cref="P:Microsoft.Practices.EnterpriseLibrary.Caching.ScavengerTask.NumberOfItemsToBeScavenged"/> elements the cache over the 
            <see cref="P:Microsoft.Practices.EnterpriseLibrary.Caching.CacheCapacityScavengingPolicy.MaximumItemsAllowedBeforeScavenging"/> threshold.
            <para/>
            Each time a scheduled scavenge task is processed the counter used to avoid superfluous scavenges is reset to 
            zero, so the next scavenge request will result in a new scheduled scavenge task.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheScavenger">
            <summary>
            Represents a cache scavenger.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheScavenger.StartScavenging">
            <summary>
            Starts the scavenging process.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackgroundScheduler.#ctor(Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationTask,Microsoft.Practices.EnterpriseLibrary.Caching.ScavengerTask,Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackgroundScheduler"/> with a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationTask"/> and 
            a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ScavengerTask"/>.
            </summary>
            <param name="expirationTask">The expiration task to use.</param>
            <param name="scavengerTask">The scavenger task to use.</param>
            <param name="instrumentationProvider">The instrumentation provider to use.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackgroundScheduler.ExpirationTimeoutExpired(System.Object)">
            <summary>
            Queues a message that the expiration timeout has expired.
            </summary>
            <param name="notUsed">Ignored.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackgroundScheduler.StartScavenging">
            <summary>
            Starts the scavenging process.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore">
            <summary>
            Base class for backing stores. Contains implementations of common policies
            and utilities usable by all backing stores.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore">
            <summary>
            <P>This interface defines the contract that must be implemented by all backing stores. 
            Implementors of this method are responsible for interacting with their underlying
            persistence mechanisms to store and retrieve CacheItems. All methods below must guarantee 
            Weak Exception Safety. This means that operations must complete entirely, or they must completely
            clean up from the failure and leave the cache in a consistent state. The mandatory
            cleanup process will remove all traces of the item that caused the failure, causing that item
            to be expunged from the cache entirely.
            </P>
            </summary>
            <remarks>
            Due to the way the Caching class is implemented, implementations of this class will always be called in 
            a thread-safe way. There is no need to make derived classes thread-safe.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore.Add(Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem)">
            <summary>
            <p>
            This method is responsible for adding a CacheItem to the BackingStore. This operation must be successful 
            even if an item with the same key already exists. This method must also meet the Weak Exception Safety guarantee
            and remove the item from the backing store if any part of the Add fails.
            </p> 
            </summary>
            <param name="newCacheItem">CacheItem to be added</param>
            <remarks>
            <p>
            Other exceptions can be thrown, depending on what individual Backing Store implementations throw during Add
            </p>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore.Remove(System.String)">
            <summary>
            Removes an item with the given key from the backing store
            </summary>
            <param name="key">Key to remove. Must not be null.</param>
            <remarks>
            <p>
            Other exceptions can be thrown, depending on what individual Backing Store implementations throw during Remove
            </p>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore.UpdateLastAccessedTime(System.String,System.DateTime)">
            <summary>
            Updates the last accessed time for a cache item.
            </summary>
            <param name="key">Key to update</param>
            <param name="timestamp">Time at which item updated</param>
            <remarks>
            <p>
            Other exceptions can be thrown, depending on what individual Backing Store implementations throw during UpdateLastAccessedTime
            </p>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore.Flush">
            <summary>
            Flushes all CacheItems from backing store. This method must meet the Weak Exception Safety guarantee.
            </summary>
            <remarks>
            <p>
            Other exceptions can be thrown, depending on what individual Backing Store implementations throw during Flush
            </p>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore.Load">
            <summary>
            Loads all CacheItems from backing store. 
            </summary>
            <returns>Hashtable filled with all existing CacheItems.</returns>
            <remarks>
            <p>
            Other exceptions can be thrown, depending on what individual Backing Store implementations throw during Load
            </p>
            </remarks>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore.Count">
            <summary>
            Number of objects stored in the backing store
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.#ctor">
            <summary>
            Inherited constructor
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.Finalize">
            <summary>
            Finalizer for BaseBackingStore
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.Dispose">
            <summary>
            Dispose method for all backing stores. This implementation is sufficient for any class that does not need any finalizer behavior
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.Dispose(System.Boolean)">
            <summary>
            Disposing method as used in the Dispose pattern
            </summary>
            <param name="disposing">True if called during Dispose. False if called from finalizer</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.Remove(System.String)">
            <summary>
            Removes an item with the given key from the backing store
            </summary>
            <param name="key">Key to remove. Must not be null.</param>
            <remarks>
            <p>
            Other exceptions can be thrown, depending on what individual Backing Store implementations throw during Remove
            </p>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.Remove(System.Int32)">
            <summary>
            Removes an item with the given storage key from the backing store.
            </summary>
            <param name="storageKey">Unique storage key for the cache item to be removed</param>
            <remarks>
            <p>
            Other exceptions can be thrown, depending on what individual Backing Store implementations throw during Remove
            </p>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.UpdateLastAccessedTime(System.String,System.DateTime)">
            <summary>
            Updates the last accessed time for a cache item.
            </summary>
            <param name="key">Key to update</param>
            <param name="timestamp">Time at which item updated</param>
            <remarks>
            <p>
            Other exceptions can be thrown, depending on what individual Backing Store implementations throw during UpdateLastAccessedTime
            </p>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.UpdateLastAccessedTime(System.Int32,System.DateTime)">
            <summary>
            Updates the last accessed time for a cache item referenced by this unique storage key
            </summary>
            <param name="storageKey">Unique storage key for cache item</param>
            <param name="timestamp">Time at which item updated</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.Flush">
            <summary>
            Flushes all CacheItems from backing store. This method must meet the Strong Exception Safety guarantee.
            </summary>
            <remarks>
            <p>
            Other exceptions can be thrown, depending on what individual Backing Store implementations throw during Flush
            </p>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.Add(Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem)">
            <summary>
            <p>
            This method is responsible for adding a CacheItem to the BackingStore. This operation must be successful 
            even if an item with the same key already exists. This method must also meet the exception safety guarantee
            and make sure that all traces of the new or old item are gone if the add fails in any way.
            </p> 
            </summary>
            <param name="newCacheItem">CacheItem to be added</param>
            <remarks>
            <p>
            Other exceptions can be thrown, depending on what individual Backing Store implementations throw during Add
            </p>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.Load">
            <summary>
            Loads all CacheItems from underlying persistence mechanism.
            </summary>
            <returns>Hashtable containing all existing CacheItems.</returns>
            <remarks>Exceptions thrown depend on the implementation of the underlying database.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.RemoveOldItem(System.Int32)">
            <summary>
            Removed existing item stored in persistence store with same key as new item
            </summary>
            <param name="storageKey">Unique key for cache item</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.AddNewItem(System.Int32,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem)">
            <summary>
            Adds new item to persistence store
            </summary>
            <param name="storageKey">Unique key for cache item</param>
            <param name="newItem">Item to be added to cache. May not be null.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.LoadDataFromStore">
            <summary>
            Responsible for loading items from underlying persistence store. This method should do
            no filtering to remove expired items.
            </summary>
            <returns>Hash table of all items loaded from persistence store</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.BaseBackingStore.Count">
            <summary>
            Number of objects stored in the backing store
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore">
            <summary>
            Implementation of IBackingStore that stores its CacheItems into IsolatedStorage.
            </summary>
            <remarks>
            This class assumes a tree-structured storage schema. Each named instance of an Isolated Storage area creates a 
            separate, top-level directory in Isolated Storage. This is to allow a user to segregate different areas in Isolated Storage
            to allow multiple applications to use their own logically separate areas. Inside each of these areas, each CacheItem is stored
            in its own subdirectory, with separate files in those subdirectories representing the different pieces of a CacheItem. 
            The item was split like this to allow for several optimizations. The first optimization is that now, the essence of a CacheItem
            can be restored independently of the underlying value. It is the deserialization of the value object that could conceivably 
            be very time consuming, so by splitting it off into its own file, that deserialization process could be delayed until the value is 
            actually needed. The second optimization is that we are now able to update the last accessed time for a CacheItem without 
            bringing the entire CacheItem into memory, make the update, and then reserialize it.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore.#ctor(System.String)">
            <summary>
            A <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> that stores objects in Isolated Storage, identified by <paramref name="storageAreaName"/>.
            </summary>
            <param name="storageAreaName">Identifier for this Isolated Storage area. May not be null.</param>
            <permission cref="T:System.Security.Permissions.IsolatedStorageFilePermission">Demanded to ensure caller has permission to access Isolated Storage.</permission>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore.#ctor(System.String,Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider)">
            <summary>
            Initialize Isolated Storage for this CacheItem by creating the directory where it will be stored. This 
            constructor should only be used for testing, and never called from production code.
            </summary>
            <param name="storageAreaName">Identifier for this Isolated Storage area. May not be null.</param>
            <param name="encryptionProvider">
            The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/> to use to encrypt data in storage. This value can be <see langword="null"/>.
            </param>
            <permission cref="T:System.Security.Permissions.IsolatedStorageFilePermission">Demanded to ensure caller has permission to access Isolated Storage.</permission>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore.Flush">
            <summary>
            Removes all items from this Isolated Storage area.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore.Remove(System.Int32)">
            <summary>
            Removes the named item from Isolated Storage.
            </summary>
            <param name="storageKey">Identifier for CacheItem to remove.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore.UpdateLastAccessedTime(System.Int32,System.DateTime)">
            <summary>
            Updates the last accessed time for the specified CacheItem stored in Isolated Storage
            </summary>
            <param name="storageKey">Identifer for CacheItem to remove.</param>
            <param name="timestamp">New timestamp for CacheItem.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore.Dispose(System.Boolean)">
            <summary>
            Releases the unmanaged resources used by the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/> and optionally releases the managed resources.
            </summary>
            <param name="disposing">
            <see langword="true"/> to release both managed and unmanaged resources; <see langword="false"/> to release only unmanaged resources.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore.LoadDataFromStore">
            <summary>
            Loads data from persistence store
            </summary>
            <returns>A Hashtable containing the cache items.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore.RemoveOldItem(System.Int32)">
            <summary>
            Remove existing item stored in persistence store with same key as new item
            </summary>
            <param name="storageKey">Item being removed from cache.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore.AddNewItem(System.Int32,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem)">
            <summary>
            Adds new item to persistence store
            </summary>
            <param name="storageKey">Unique key for storage item</param>
            <param name="newItem">Item to be added to cache. May not be null.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore.Count">
            <summary>
            Returns the number of CacheItems in the Isolated Storage segment identified by the name passed in at construction
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageCacheItem">
            <summary>
            Represents a CacheItem as stored in Isolated Storage. This class is responsible for storing and
            restoring the item from the underlying file system store.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageCacheItem.#ctor(System.IO.IsolatedStorage.IsolatedStorageFile,System.String,Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider)">
            <summary>
            Instance constructor. Ensures that the storage location in Isolated Storage is prepared
            for reading and writing. This class stores each individual field of the CacheItem into its own
            file inside the directory specified by itemDirectoryRoot.
            </summary>
            <param name="storage">Isolated Storage area to use. May not be null.</param>
            <param name="itemDirectoryRoot">Complete path in Isolated Storage where the cache item should be stored. May not be null.</param>
            <param name="encryptionProvider">Encryption provider</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageCacheItem.Store(Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem)">
            <summary>
            Stores specified CacheItem into IsolatedStorage at location specified in constructor
            </summary>
            <param name="itemToStore">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/> to store.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageCacheItem.Load">
            <summary>
            Loads a CacheItem from IsolatedStorage from the location specified in the constructor
            </summary>
            <returns>CacheItem loaded from IsolatedStorage</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageCacheItem.UpdateLastAccessedTime(System.DateTime)">
            <summary>
            Updates the last accessed time for the CacheItem stored at this location in Isolated Storage
            </summary>
            <param name="newTimestamp">New timestamp</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageCacheItemField">
            <summary>
            Defines the mechanism to store and read individual fields from IsolatedStorage. This class maintains no
            state with respect to the data read from IsolatedStorage, so it may be reused to reread or rewrite the same field
            repeatedly.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageCacheItemField.#ctor(System.IO.IsolatedStorage.IsolatedStorageFile,System.String,System.String,Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider)">
            <summary>
            Instance constructor
            </summary>
            <param name="storage">IsolatedStorage area to use. May not be null.</param>
            <param name="fieldName">Name of the file in which the field value is stored. May not be null.</param>
            <param name="fileSystemLocation">Complete path to directory where file specified in fieldName is to be found. May not be null.</param>
            <param name="encryptionProvider">Encryption provider</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageCacheItemField.Write(System.Object,System.Boolean)">
            <summary>
            Writes value to specified location in IsolatedStorage
            </summary>
            <param name="itemToWrite">Object to write into Isolated Storage</param>
            <param name="encrypted">True if item written is to be encrypted</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageCacheItemField.Overwrite(System.Object)">
            <summary>
            Overwrites given field in Isolated Storage. Item will not be encrypted
            </summary>
            <param name="itemToWrite">Object to write into Isolated Storage</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageCacheItemField.Read(System.Boolean)">
            <summary>
            Reads value from specified location in IsolatedStorage
            </summary>
            <param name="encrypted">True if field is stored as encrypted</param>
            <returns>Value read from IsolatedStorage. This value may be null if the value stored is null.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageCacheItemField.WriteField(System.Object,System.IO.IsolatedStorage.IsolatedStorageFileStream,System.Boolean)">
            <summary>
            Responsible for writing value to IsolatedStorage using given IsolatedStorageFileStream reference. Subclasses
            may override this method to provide different implementations of writing to Isolated Storage.
            </summary>
            <param name="itemToWrite">Value to write. May be null.</param>
            <param name="fileStream">Stream to which value should be written. May not be null.</param>
            <param name="encrypted">True if item is to be encrypted</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageCacheItemField.ReadField(System.IO.IsolatedStorage.IsolatedStorageFileStream,System.Boolean)">
            <summary>
            Responsible for reading value from IsolatedStorage using given IsolatedStorageFileStream reference. Subclasses
            may override this method to provide different implementations of reading from IsolatedStorage.
            </summary>
            <param name="fileStream">Stream from which value should be written. May not be null.</param>
            <param name="encrypted">True if item is stored encrypted</param>
            <returns>Value read from Isolated Storage. May be null if value stored is null</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider">
            <summary>
            Not intended for direct use.  Provides symmetric encryption and decryption services 
            to Isolated and Database backing stores.  Allows this block to use 
            Security.Cryptography without having a direct reference to that assembly.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider.Encrypt(System.Byte[])">
            <summary>
            Encrypt backing store data.
            </summary>
            <param name="plaintext">Clear bytes.</param>
            <returns>Encrypted bytes.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider.Decrypt(System.Byte[])">
            <summary>
            Decrypt backing store data.
            </summary>
            <param name="ciphertext">Encrypted bytes.</param>
            <returns>Decrypted bytes.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.NullBackingStore">
            <summary>
            This class is used when no backing store is needed to support the caching storage policy.
            Its job is to provide an implementation of a backing store that does nothing, merely enabling
            the cache to provide a strictly in-memory cache.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.NullBackingStore.Add(Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem)">
            <summary>
            Not used
            </summary>
            <param name="newCacheItem">Not used</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.NullBackingStore.Remove(System.String)">
            <summary>
            Not used
            </summary>
            <param name="key">Not used</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.NullBackingStore.UpdateLastAccessedTime(System.String,System.DateTime)">
            <summary>
            Not used
            </summary>
            <param name="key">Not used</param>
            <param name="timestamp">Not used</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.NullBackingStore.Flush">
            <summary>
            Not used
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.NullBackingStore.Load">
            <summary>
            Always returns an empty hash table.
            </summary>
            <returns>Empty hash table</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.NullBackingStore.Dispose">
            <summary>
            Empty dispose implementation
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.NullBackingStore.Count">
            <summary>
            Always returns 0
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Cache">
            <summary>
            The real worker of the block. The Cache class is the traffic cop that prevents 
            resource contention among the different threads in the system. It also will act
            as the remoting gateway when that feature is added to the cache.
            </summary>	
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheOperations">
            <summary>
            Represents cache operations.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheOperations.RemoveItemFromCache(System.String,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason)">
            <summary>
            Removes a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/>.
            </summary>
            <param name="key">The key of the item to remove.</param>
            <param name="removalReason">One of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason"/> values.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheOperations.CurrentCacheState">
            <summary>
            Gets the current cache state.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheOperations.Count">
            <summary>
            Returns the number of items contained in the cache.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.#ctor(Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore,Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider)">
            <summary>
            Initialzie a new instance of a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Cache"/> class with a backing store, and scavenging policy.
            </summary>
            <param name="backingStore">The cache backing store.</param>
            <param name="instrumentationProvider">The instrumentation provider.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.Contains(System.String)">
            <summary>
            Determines if a particular key is contained in the cache.
            </summary>
            <param name="key">The key to locate.</param>
            <returns>
            <see langword="true"/> if the key is contained in the cache; otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.Add(System.String,System.Object)">
            <summary>
            Add a new keyed object to the cache.
            </summary>
            <param name="key">The key of the object.</param>
            <param name="value">The object to add.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.Add(System.String,System.Object,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemRefreshAction,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration[])">
            <summary>
            Add a new keyed object to the cache.
            </summary>
            <param name="key">The key of the object.</param>
            <param name="value">The object to add.</param>
            <param name="scavengingPriority">One of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority"/> values.</param>
            <param name="refreshAction">An <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemRefreshAction"/> object.</param>
            <param name="expirations">An array of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration"/> objects.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.Remove(System.String)">
            <summary>
            Remove an item from the cache by key.
            </summary>
            <param name="key">The key of the item to remove.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.Remove(System.String,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason)">
            <summary>
            Remove an item from the cache by key.
            </summary>
            <param name="key">The key of the item to remove.</param>
            <param name="removalReason">One of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason"/> values.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.RemoveItemFromCache(System.String,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason)">
            <summary>
            Removes an item from the cache.
            </summary>
            <param name="key">The key to remove.</param>
            <param name="removalReason">One of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason"/> values.</param>
            <remarks>
            This seemingly redundant method is here to be called through the ICacheOperations 
            interface. I put this in place to break any dependency from any other class onto 
            the Cache class
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.GetData(System.String)">
            <summary>
            Get the object from the cache for the key.
            </summary>
            <param name="key">
            The key whose value to get.
            </param>
            <returns>
            The value associated with the specified key. 
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.Flush">
            <summary>
            Flush the cache.
            </summary>
            <remarks>
            There may still be thread safety issues in this class with respect to cacheItemExpirations
            and scavenging, but I really doubt that either of those will be happening while
            a Flush is in progress. It seems that the most likely scenario for a flush
            to be called is at the very start of a program, or when absolutely nothing else
            is going on. Calling flush in the middle of an application would seem to be
            an "interesting" thing to do in normal circumstances.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.Finalize">
            <summary>
            Dispose of the backing store before garbage collection.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.Dispose">
            <summary>
            Dispose of the backing store before garbage collection.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.Dispose(System.Boolean)">
            <summary>
            Dispose of the backing store before garbage collection.
            </summary>
            <param name="disposing">
            <see langword="true"/> if disposing; otherwise, <see langword="false"/>.
            </param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.Count">
            <summary>
            Gets the count of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/> objects.
            </summary>
            <value>
            The count of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/> objects.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Cache.CurrentCacheState">
            <summary>
            Gets the current cache.
            </summary>
            <returns>
            The current cache.
            </returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheCapacityScavengingPolicy">
            <summary>
            Cache scavenging policy based on capacity.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheCapacityScavengingPolicy.#ctor(System.Int32)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheCapacityScavengingPolicy"/> class with the name of the cache manager and the proxy to the configuration data.
            </summary>
            <param name="maximumElementsInCacheBeforeScavenging">The proxy to the latest configuration data.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheCapacityScavengingPolicy.IsScavengingNeeded(System.Int32)">
            <summary>
            Determines if scavenging is needed.
            </summary>
            <param name="currentCacheItemCount">The current number of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/> objects in the cache.</param>
            <returns>
            <see langword="true"/> if scavenging is needed; otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.CacheCapacityScavengingPolicy.MaximumItemsAllowedBeforeScavenging">
            <summary>
            Gets the maximum items to allow before scavenging.
            </summary>
            <value>
            The maximum items to allow before scavenging.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheFactory">
            <summary>
            Static factory class used to get instances of a specified CacheManager
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheFactory.GetCacheManager">
            <summary>
            Returns the default CacheManager instance. The same instance should be returned each time this method
            is called. The name of the instance to treat as the default CacheManager is defined in the configuration file.
            Guaranteed to return an intialized CacheManager if no exception thrown
            </summary>
            <returns>Default cache manager instance.</returns>
            <exception cref="T:System.Configuration.ConfigurationException">Unable to create default CacheManager</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheFactory.GetCacheManager(System.String)">
            <summary>
            Returns the named ICacheManager instance. Guaranteed to return an initialized ICacheManager if no exception thrown.
            </summary>
            <param name="cacheManagerName">Name defined in configuration for the cache manager to instantiate</param>
            <returns>The requested CacheManager instance.</returns>
            <exception cref="T:System.ArgumentNullException">cacheManagerName is null</exception>
            <exception cref="T:System.ArgumentException">cacheManagerName is empty</exception>
            <exception cref="T:System.Configuration.ConfigurationException">Could not find instance specified in cacheManagerName</exception>
            <exception cref="T:System.InvalidOperationException">Error processing configuration information defined in application configuration file.</exception>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem">
            <summary>
            This class contains all data important to define an item stored in the cache. It holds both the key and 
            value specified by the user, as well as housekeeping information used internally by this block. It is public, 
            rather than internal, to allow block extenders access to it inside their own implementations of IBackingStore.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.#ctor(System.String,System.Object,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemRefreshAction,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration[])">
            <summary>
            Constructs a fully formed CacheItem. 
            </summary>
            <param name="key">Key identifying this CacheItem</param>
            <param name="value">Value to be stored. May be null.</param>
            <param name="scavengingPriority">Scavenging priority of CacheItem. See <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority"/> for values.</param>
            <param name="refreshAction">Object supplied by caller that will be invoked upon expiration of the CacheItem. May be null.</param>
            <param name="expirations">Param array of ICacheItemExpiration objects. May provide 0 or more of these.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.#ctor(System.DateTime,System.String,System.Object,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemRefreshAction,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration[])">
            <summary>
            Constructs a fully formed CacheItem. This constructor is to be used when restoring an existing
            CacheItem from the backing store. As such, it does not generate its own Guid for this instance,
            but allows the GUID to be passed in, as read from the backing store.
            </summary>
            <param name="lastAccessedTime">Time this CacheItem last accessed by user.</param>
            <param name="key">Key provided  by the user for this cache item. May not be null.</param>
            <param name="value">Value to be stored. May be null.</param>
            <param name="scavengingPriority">Scavenging priority of CacheItem. See <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority"/> for values.</param>
            <param name="refreshAction">Object supplied by caller that will be invoked upon expiration of the CacheItem. May be null.</param>
            <param name="expirations">Param array of ICacheItemExpiration objects. May provide 0 or more of these.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.Replace(System.Object,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemRefreshAction,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration[])">
            <summary>
            Replaces the internals of the current cache item with the given new values. This is strictly used in the Cache
            class when adding a new item into the cache. By replacing the item's contents, rather than replacing the item
            itself, it allows us to keep a single reference in the cache, simplifying locking.
            </summary>
            <param name="cacheItemData">Value to be stored. May be null.</param>
            <param name="cacheItemPriority">Scavenging priority of CacheItem. See <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority"/> for values.</param>
            <param name="cacheItemRefreshAction">Object supplied by caller that will be invoked upon expiration of the CacheItem. May be null.</param>
            <param name="cacheItemExpirations">Param array of ICacheItemExpiration objects. May provide 0 or more of these.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.GetExpirations">
            <summary>
            Returns array of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration"/> objects for this instance.
            </summary>
            <returns>
            An array of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration"/> objects.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.HasExpired">
            <summary>
            Evaluates all cacheItemExpirations associated with this cache item to determine if it 
            should be considered expired. Evaluation stops as soon as any expiration returns true. 
            </summary>
            <returns>True if item should be considered expired, according to policies
            defined in this item's cacheItemExpirations.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.TouchedByUserAction(System.Boolean)">
            <summary>
            Intended to be used internally only. This method is called whenever a CacheItem is touched through the action of a user. It
            prevents this CacheItem from being expired or scavenged during an in-progress expiration or scavenging process. It has no effect
            on subsequent expiration or scavenging processes.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.TouchedByUserAction(System.Boolean,System.DateTime)">
            <summary>
            Intended to be used internally only. This method is called whenever a CacheItem is touched through the action of a user. It
            prevents this CacheItem from being expired or scavenged during an in-progress expiration or scavenging process. It has no effect
            on subsequent expiration or scavenging processes.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.MakeEligibleForScavenging">
            <summary>
            Makes the cache item eligible for scavenging.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.MakeNotEligibleForScavenging">
            <summary>
            Makes the cache item not eligible for scavenging.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.SetLastAccessedTime(System.DateTime)">
            <summary>
            Sets the last accessed time for the cache item.
            </summary>
            <param name="specificAccessedTime">The last accessed time.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.ScavengingPriority">
            <summary>
            Returns the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority"/> assigned to this CacheItem
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.LastAccessedTime">
            <summary>
            Returns the last accessed time.
            </summary>
            <value>
            Gets the last accessed time.
            </value>
            <remarks>
            The set is present for testing purposes only. Should not be called by application code 
            </remarks>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.WillBeExpired">
            <summary>
            Intended to be used internally only. The value should be true when an item is eligible to be expired.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.EligibleForScavenging">
            <summary>
            Intended to be used internally only. The value should be true when an item is eligible for scavenging.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.Value">
            <summary>
            Returns the cached value of this CacheItem
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.Key">
            <summary>
            Returns the key associated with this CacheItem
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem.RefreshAction">
            <summary>
            Intended to be used internally only. Returns object used to refresh expired CacheItems.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority">
            <summary>
            Specifies the item priority levels.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority.None">
            <summary>
            Should never be seen in nature.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority.Low">
            <summary>
            Low priority for scavenging.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority.Normal">
            <summary>
            Normal priority for scavenging.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority.High">
            <summary>
            High priority for scavenging.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority.NotRemovable">
            <summary>
            Non-removable priority for scavenging.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason">
            <summary>
            The reason that the cache item was removed.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason.Expired">
            <summary>
            The item has expired.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason.Removed">
            <summary>
            The item was manually removed from the cache.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason.Scavenged">
            <summary>
            The item was removed by the scavenger because it had a lower priority that any other item in the cache.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason.Unknown">
            <summary>
            Reserved. Do not use.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager">
            <summary>
            This class represents the interface to caching as shown to the user. All caching operations are performed through this class.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager">
            <summary>
            This interface defines the contract that must be implemented by all cache managers. 
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager.Add(System.String,System.Object)">
            <summary>
            Adds new CacheItem to cache. If another item already exists with the same key, that item is removed before
            the new item is added. If any failure occurs during this process, the cache will not contain the item being added. 
            Items added with this method will be not expire, and will have a Normal <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority"/> priority.
            </summary>
            <param name="key">Identifier for this CacheItem</param>
            <param name="value">Value to be stored in cache. May be null.</param>
            <exception cref="T:System.ArgumentNullException">Provided key is null</exception>
            <exception cref="T:System.ArgumentException">Provided key is an empty string</exception>
            <remarks>The CacheManager can be configured to use different storage mechanisms in which to store the CacheItems.
            Each of these storage mechanisms can throw exceptions particular to their own implementations.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager.Add(System.String,System.Object,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemRefreshAction,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration[])">
            <summary>
            Adds new CacheItem to cache. If another item already exists with the same key, that item is removed before
            the new item is added. If any failure occurs during this process, the cache will not contain the item being added.
            </summary>
            <param name="key">Identifier for this CacheItem</param>
            <param name="value">Value to be stored in cache. May be null.</param>
            <param name="scavengingPriority">Specifies the new item's scavenging priority. 
            See <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority"/> for more information.</param>
            <param name="refreshAction">Object provided to allow the cache to refresh a cache item that has been expired. May be null.</param>
            <param name="expirations">Param array specifying the expiration policies to be applied to this item. May be null or omitted.</param>
            <exception cref="T:System.ArgumentNullException">Provided key is null</exception>
            <exception cref="T:System.ArgumentException">Provided key is an empty string</exception>
            <remarks>The CacheManager can be configured to use different storage mechanisms in which to store the CacheItems.
            Each of these storage mechanisms can throw exceptions particular to their own implementations.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager.Contains(System.String)">
            <summary>
            Returns true if key refers to item current stored in cache
            </summary>
            <param name="key">Key of item to check for</param>
            <returns>True if item referenced by key is in the cache</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager.Flush">
            <summary>
            Removes all items from the cache. If an error occurs during the removal, the cache is left unchanged.
            </summary>
            <remarks>The CacheManager can be configured to use different storage mechanisms in which to store the CacheItems.
            Each of these storage mechanisms can throw exceptions particular to their own implementations.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager.GetData(System.String)">
            <summary>
            Returns the value associated with the given key.
            </summary>
            <param name="key">Key of item to return from cache.</param>
            <returns>Value stored in cache</returns>
            <exception cref="T:System.ArgumentNullException">Provided key is null</exception>
            <exception cref="T:System.ArgumentException">Provided key is an empty string</exception>
            <remarks>The CacheManager can be configured to use different storage mechanisms in which to store the CacheItems.
            Each of these storage mechanisms can throw exceptions particular to their own implementations.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager.Remove(System.String)">
            <summary>
            Removes the given item from the cache. If no item exists with that key, this method does nothing.
            </summary>
            <param name="key">Key of item to remove from cache.</param>
            <exception cref="T:System.ArgumentNullException">Provided key is null</exception>
            <exception cref="T:System.ArgumentException">Provided key is an empty string</exception>
            <remarks>The CacheManager can be configured to use different storage mechanisms in which to store the CacheItems.
            Each of these storage mechanisms can throw exceptions particular to their own implementations.</remarks>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager.Count">
            <summary>
            Returns the number of items currently in the cache.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager.Item(System.String)">
            <summary>
            Returns the item identified by the provided key
            </summary>
            <param name="key">Key to retrieve from cache</param>
            <exception cref="T:System.ArgumentNullException">Provided key is null</exception>
            <exception cref="T:System.ArgumentException">Provided key is an empty string</exception>
            <remarks>The CacheManager can be configured to use different storage mechanisms in which to store the cache items.
            Each of these storage mechanisms can throw exceptions particular to their own implementations.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager.#ctor(Microsoft.Practices.EnterpriseLibrary.Caching.Cache,Microsoft.Practices.EnterpriseLibrary.Caching.BackgroundScheduler,Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationPollTimer)">
            <summary>
            
            </summary>
            <param name="realCache"></param>
            <param name="backgroundScheduler"></param>
            <param name="pollTimer"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager.Contains(System.String)">
            <summary>
            Returns true if key refers to item current stored in cache
            </summary>
            <param name="key">Key of item to check for</param>
            <returns>True if item referenced by key is in the cache</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager.Add(System.String,System.Object)">
            <summary>
            Adds new CacheItem to cache. If another item already exists with the same key, that item is removed before
            the new item is added. If any failure occurs during this process, the cache will not contain the item being added. 
            Items added with this method will be not expire, and will have a Normal <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority"/> priority.
            </summary>
            <param name="key">Identifier for this CacheItem</param>
            <param name="value">Value to be stored in cache. May be null.</param>
            <exception cref="T:System.ArgumentNullException">Provided key is null</exception>
            <exception cref="T:System.ArgumentException">Provided key is an empty string</exception>
            <remarks>The CacheManager can be configured to use different storage mechanisms in which to store the CacheItems.
            Each of these storage mechanisms can throw exceptions particular to their own implementations.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager.Add(System.String,System.Object,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemRefreshAction,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration[])">
            <summary>
            Adds new CacheItem to cache. If another item already exists with the same key, that item is removed before
            the new item is added. If any failure occurs during this process, the cache will not contain the item being added.
            </summary>
            <param name="key">Identifier for this CacheItem</param>
            <param name="value">Value to be stored in cache. May be null.</param>
            <param name="scavengingPriority">Specifies the new item's scavenging priority. 
            See <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemPriority"/> for more information.</param>
            <param name="refreshAction">Object provided to allow the cache to refresh a cache item that has been expired. May be null.</param>
            <param name="expirations">Param array specifying the expiration policies to be applied to this item. May be null or omitted.</param>
            <exception cref="T:System.ArgumentNullException">Provided key is null</exception>
            <exception cref="T:System.ArgumentException">Provided key is an empty string</exception>
            <remarks>The CacheManager can be configured to use different storage mechanisms in which to store the CacheItems.
            Each of these storage mechanisms can throw exceptions particular to their own implementations.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager.Remove(System.String)">
            <summary>
            Removes the given item from the cache. If no item exists with that key, this method does nothing.
            </summary>
            <param name="key">Key of item to remove from cache.</param>
            <exception cref="T:System.ArgumentNullException">Provided key is null</exception>
            <exception cref="T:System.ArgumentException">Provided key is an empty string</exception>
            <remarks>The CacheManager can be configured to use different storage mechanisms in which to store the CacheItems.
            Each of these storage mechanisms can throw exceptions particular to their own implementations.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager.GetData(System.String)">
            <summary>
            Returns the value associated with the given key.
            </summary>
            <param name="key">Key of item to return from cache.</param>
            <returns>Value stored in cache</returns>
            <exception cref="T:System.ArgumentNullException">Provided key is null</exception>
            <exception cref="T:System.ArgumentException">Provided key is an empty string</exception>
            <remarks>The CacheManager can be configured to use different storage mechanisms in which to store the CacheItems.
            Each of these storage mechanisms can throw exceptions particular to their own implementations.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager.Flush">
            <summary>
            Removes all items from the cache. If an error occurs during the removal, the cache is left unchanged.
            </summary>
            <remarks>The CacheManager can be configured to use different storage mechanisms in which to store the CacheItems.
            Each of these storage mechanisms can throw exceptions particular to their own implementations.</remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager.Dispose">
            <summary>
            Not intended for public use. Only public due to requirements of IDisposable. If you call this method, your
            cache will be unusable.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager.Count">
            <summary>
            Returns the number of items currently in the cache.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager.Item(System.String)">
            <summary>
            Returns the item identified by the provided key
            </summary>
            <param name="key">Key to retrieve from cache</param>
            <exception cref="T:System.ArgumentNullException">Provided key is null</exception>
            <exception cref="T:System.ArgumentException">Provided key is an empty string</exception>
            <remarks>The CacheManager can be configured to use different storage mechanisms in which to store the cache items.
            Each of these storage mechanisms can throw exceptions particular to their own implementations.</remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManagerFactory">
            <summary>
            Factory for <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>s. This class is responsible for creating all the internal
            classes needed to implement a CacheManager.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManagerFactory.#ctor">
            <summary>
            <para>Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManagerFactory"/> class 
            with the default configuration source.</para>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManagerFactory.#ctor(Microsoft.Practices.ServiceLocation.IServiceLocator)">
            <summary>
            
            </summary>
            <param name="serviceLocator"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManagerFactory.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            <para>Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManagerFactory"/> class 
            with the given configuration source.</para>
            </summary>
            <param name="configurationSource">The configuration source that contains information on how to build the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> instances</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData">
            <summary>
            Configuration data defining CacheManagerData. Defines the information needed to properly configure
            a CacheManager instance.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerDataBase">
            <summary>
            Base class for configuration data defining CacheManagerDataBase. Defines the information needed to properly configure
            a ICacheManager instance.
            </summary>    	
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerDataBase.#ctor">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerDataBase"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerDataBase.#ctor(System.Type)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerDataBase"/> class.
            </summary>
            <param name="type">The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerDataBase.#ctor(System.String,System.Type)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerDataBase"/> class.
            </summary>
            <param name="name">
            The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerDataBase"/>.
            </param>
            <param name="type">The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerDataBase.GetRegistrations(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Get the set of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ContainerModel.TypeRegistration"/> object needed to
            register the CacheManager represented by this config element.
            </summary>
            <returns>The sequence of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ContainerModel.TypeRegistration"/> objects.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerDataBase.GetCacheManagerCreationExpression">
            <summary>
            Gets the creation expression used to produce a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ContainerModel.TypeRegistration"/> during
            <see cref="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerDataBase.GetRegistrations(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)"/>.
            </summary>
            <remarks>
            This must be overridden by a subclass, but is not marked as abstract due to configuration serialization needs.
            </remarks>
            <returns>A <see cref="T:System.Linq.Expressions.Expression"/> that creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager"/></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData.#ctor">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData.#ctor(System.String,System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/> class.
            </summary>
            <param name="name">
            The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/>.
            </param>
            <param name="expirationPollFrequencyInSeconds">
            Frequency in seconds of expiration polling cycle
            </param>
            <param name="maximumElementsInCacheBeforeScavenging">
            Maximum number of items in cache before an add causes scavenging to take place
            </param>
            <param name="numberToRemoveWhenScavenging">
            Number of items to remove from cache when scavenging
            </param>
            <param name="cacheStorage">
            CacheStorageData object from configuration describing how data is stored 
            in the cache.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData.GetRegistrations(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Get the set of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ContainerModel.TypeRegistration"/> object needed to
            register the CacheManager represented by this config element.
            </summary>
            <returns>The sequence of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ContainerModel.TypeRegistration"/> objects.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData.GetCacheManagerCreationExpression">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData.ExpirationPollFrequencyInSeconds">
            <summary>
            Frequency in seconds of expiration polling cycle
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData.MaximumElementsInCacheBeforeScavenging">
            <summary>
            Maximum number of items in cache before an add causes scavenging to take place
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData.NumberToRemoveWhenScavenging">
            <summary>
            Number of items to remove from cache when scavenging
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData.CacheStorage">
            <summary>
            CacheStorageData object from configuration describing how data is stored 
            in the cache.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings">
            <summary>
            Overall configuration settings for Caching
            </summary>    
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings.SectionName">
            <summary>
            Configuration key for cache manager settings.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings.GetRegistrations(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings.GetUpdatedRegistrations(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Return the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ContainerModel.TypeRegistration"/> objects needed to reconfigure
            the container after a configuration source has changed.
            </summary>
            <remarks>If there are no reregistrations, return an empty sequence.</remarks>
            <param name="configurationSource">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> containing
            the configuration information.</param>
            <returns>The sequence of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ContainerModel.TypeRegistration"/> objects.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings.DefaultCacheManager">
            <summary>
            Defines the default manager instance to use when no other manager is specified
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings.CacheManagers">
            <summary>
            Gets the collection of defined <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> objects.
            </summary>
            <value>
            The collection of defined <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> objects.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings.BackingStores">
            <summary>
            Gets the collection of defined <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> objects.
            </summary>
            <value>
            The collection of defined <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> objects.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings.EncryptionProviders">
            <summary>
            Gets the collection of defined <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/> objects.
            </summary>
            <value>
            The collection of defined <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/> objects.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData">
            <summary>
            Configuration data defining CacheStorageData. This configuration section defines the name and type
            of the IBackingStore used by a CacheManager
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData.#ctor(System.Type)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData"/> class with a name and the type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>.
            </summary>
            <param name="type">The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData.#ctor(System.String,System.Type)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData"/> class with a name and the type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>.
            </summary>
            <param name="name">The name of the configured <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>. </param>
            <param name="type">The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData.#ctor(System.String,System.Type,System.String)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/> class with a name, the type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>, and a reference to a named instance of an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/>.
            </summary>
            <param name="name">The name of the configured <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>. </param>
            <param name="type">The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>.</param>
            <param name="storageEncryption">The name of the referenced <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData.GetRegistrations">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData.StorageEncryption">
            <summary>
            Gets the name of the referenced <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData">
            <summary>
            Configuration object for Custom Providers.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.#ctor">
            <summary>
            Initializes with default values.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.#ctor(System.String,System.Type)">
            <summary>
            Initializes with name and provider type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.#ctor(System.String,System.String)">
            <summary>
            Initializes with name and provider type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.SetAttributeValue(System.String,System.String)">
            <summary>
            Sets the attribute value for a key.
            </summary>
            <param name="key">The attribute name.</param>
            <param name="value">The attribute value.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.Unmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>
            Modifies the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData"/> object to remove all values that should not be saved. 
            </summary>
            <param name="sourceElement">A <see cref="T:System.Configuration.ConfigurationElement"/> object at the current level containing a merged view of the properties.</param>
            <param name="parentElement">A parent <see cref="T:System.Configuration.ConfigurationElement"/> object or <see langword="null"/> if this is the top level.</param>		
            <param name="saveMode">One of the <see cref="T:System.Configuration.ConfigurationSaveMode"/> values.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.Reset(System.Configuration.ConfigurationElement)">
            <summary>
            Resets the internal state of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData"/> object, 
            including the locks and the properties collection.
            </summary>
            <param name="parentElement">The parent element.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.IsModified">
            <summary>
            Indicates whether this configuration element has been modified since it was last 
            saved or loaded when implemented in a derived class.
            </summary>
            <returns><see langword="true"/> if the element has been modified; otherwise, <see langword="false"/>. </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.OnDeserializeUnrecognizedAttribute(System.String,System.String)">
            <summary>
            Called when an unknown attribute is encountered while deserializing the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData"/> object.
            </summary>
            <param name="name">The name of the unrecognized attribute.</param>
            <param name="value">The value of the unrecognized attribute.</param>
            <returns><see langword="true"/> if the processing of the element should continue; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Caching#Configuration#CustomCacheStorageData}#BaseGetPropertyValue(System.Configuration.ConfigurationProperty)">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Caching#Configuration#CustomCacheStorageData}#BaseSetPropertyValue(System.Configuration.ConfigurationProperty,System.Object)">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Caching#Configuration#CustomCacheStorageData}#BaseUnmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Caching#Configuration#CustomCacheStorageData}#BaseReset(System.Configuration.ConfigurationElement)">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Caching#Configuration#CustomCacheStorageData}#BaseIsModified">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.GetRegistrations">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.TypeName">
            <summary>
            Overridden in order to apply <see cref="T:System.ComponentModel.BrowsableAttribute"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.Attributes">
            <summary>
            Gets or sets custom configuration attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.Properties">
            <summary>
            Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> of the properties that are defined for 
            this configuration element when implemented in a derived class. 
            </summary>
            <value>
            A <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> of the properties that are defined for this
            configuration element when implemented in a derived class. 
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Caching#Configuration#CustomCacheStorageData}#Helper">
            <summary>
            Gets the helper.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData">
            <summary>
            Configuration object for Custom Providers.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.#ctor">
            <summary>
            Initializes with default values.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.#ctor(System.String,System.Type)">
            <summary>
            Initializes with name and provider type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.#ctor(System.String,System.String)">
            <summary>
            Initializes with name and provider type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.SetAttributeValue(System.String,System.String)">
            <summary>
            Sets the attribute value for a key.
            </summary>
            <param name="key">The attribute name.</param>
            <param name="value">The attribute value.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.Unmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>
            Modifies the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/> object to remove all values that should not be saved. 
            </summary>
            <param name="sourceElement">A <see cref="T:System.Configuration.ConfigurationElement"/> object at the current level containing a merged view of the properties.</param>
            <param name="parentElement">A parent <see cref="T:System.Configuration.ConfigurationElement"/> object or <see langword="null"/> if this is the top level.</param>		
            <param name="saveMode">One of the <see cref="T:System.Configuration.ConfigurationSaveMode"/> values.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.Reset(System.Configuration.ConfigurationElement)">
            <summary>
            Resets the internal state of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/> object, 
            including the locks and the properties collection.
            </summary>
            <param name="parentElement">The parent element.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.IsModified">
            <summary>
            Indicates whether this configuration element has been modified since it was last 
            saved or loaded when implemented in a derived class.
            </summary>
            <returns><see langword="true"/> if the element has been modified; otherwise, <see langword="false"/>. </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.OnDeserializeUnrecognizedAttribute(System.String,System.String)">
            <summary>
            Called when an unknown attribute is encountered while deserializing the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/> object.
            </summary>
            <param name="name">The name of the unrecognized attribute.</param>
            <param name="value">The value of the unrecognized attribute.</param>
            <returns><see langword="true"/> if the processing of the element should continue; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Caching#Configuration#CustomCacheManagerData}#BaseGetPropertyValue(System.Configuration.ConfigurationProperty)">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Caching#Configuration#CustomCacheManagerData}#BaseSetPropertyValue(System.Configuration.ConfigurationProperty,System.Object)">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Caching#Configuration#CustomCacheManagerData}#BaseUnmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Caching#Configuration#CustomCacheManagerData}#BaseReset(System.Configuration.ConfigurationElement)">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Caching#Configuration#CustomCacheManagerData}#BaseIsModified">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.GetRegistrations(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Get the set of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ContainerModel.TypeRegistration"/> object needed to
            register the CacheManager represented by this config element.
            </summary>
            <returns>The sequence of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ContainerModel.TypeRegistration"/> objects.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.TypeName">
            <summary>
            Overridden in order to apply <see cref="T:System.ComponentModel.BrowsableAttribute"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.Attributes">
            <summary>
            Gets or sets custom configuration attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.Properties">
            <summary>
            Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> of the properties that are defined for 
            this configuration element when implemented in a derived class. 
            </summary>
            <value>
            A <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> of the properties that are defined for this
            configuration element when implemented in a derived class. 
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#Caching#Configuration#CustomCacheManagerData}#Helper">
            <summary>
            Gets the helper.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.AddCachingSettings">
            <summary>
              Looks up a localized string similar to Add Caching Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataBaseDescription">
            <summary>
              Looks up a localized string similar to Persists the cached data in memory, and optionally interacts with a persistent backing store..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataBaseDisplayName">
            <summary>
              Looks up a localized string similar to Cache Manager.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataBaseNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Cache Manager..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataBaseNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataBaseTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Cache Manager..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataBaseTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataCacheStorageDescription">
            <summary>
              Looks up a localized string similar to The name of the Backing Store to use for this Cache Manager..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataCacheStorageDisplayName">
            <summary>
              Looks up a localized string similar to Backing Store.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataDescription">
            <summary>
              Looks up a localized string similar to Persists the cached data in memory, and optionally interacts with a persistent backing store..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataDisplayName">
            <summary>
              Looks up a localized string similar to Cache Manager.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataExpirationPollFrequencyInSecondsDescription">
            <summary>
              Looks up a localized string similar to Time in seconds between each cycle that removes expired cached items..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataExpirationPollFrequencyInSecondsDisplayName">
            <summary>
              Looks up a localized string similar to Expiration Polling Frequency (seconds).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataMaximumElementsInCacheBeforeScavengingDescription">
            <summary>
              Looks up a localized string similar to Maximum number of items in the cache before adding another causes scavenging to occur..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataMaximumElementsInCacheBeforeScavengingDisplayName">
            <summary>
              Looks up a localized string similar to Max. Elements In Cache Before Scavenging.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataNumberToRemoveWhenScavengingDescription">
            <summary>
              Looks up a localized string similar to Number of items to remove from the cache during each scavenging cycle..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerDataNumberToRemoveWhenScavengingDisplayName">
            <summary>
              Looks up a localized string similar to Number to Remove when Scavenging.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerSettingsBackingStoresDescription">
            <summary>
              Looks up a localized string similar to Persists the cached items to a store such as a database or Isolated Storage..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerSettingsBackingStoresDisplayName">
            <summary>
              Looks up a localized string similar to Backing Stores.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerSettingsCacheManagersDescription">
            <summary>
              Looks up a localized string similar to The Cache Managers configured for the Caching Application Block.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerSettingsCacheManagersDisplayName">
            <summary>
              Looks up a localized string similar to Cache Managers.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerSettingsDefaultCacheManagerDescription">
            <summary>
              Looks up a localized string similar to The default Cache Manager to use when code does not specify a named Cache Manager..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerSettingsDefaultCacheManagerDisplayName">
            <summary>
              Looks up a localized string similar to Default Cache Manager.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerSettingsDescription">
            <summary>
              Looks up a localized string similar to Configuration settings for the Caching Application Block.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerSettingsDisplayName">
            <summary>
              Looks up a localized string similar to Caching Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerSettingsEncryptionProvidersDescription">
            <summary>
              Looks up a localized string similar to Provide encryption of the data persisted in a backing store, but not in memory..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheManagerSettingsEncryptionProvidersDisplayName">
            <summary>
              Looks up a localized string similar to Encryption Providers.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheStorageDataDescription">
            <summary>
              Looks up a localized string similar to The name of the referenced Backing Store for this Cache Manager..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheStorageDataDisplayName">
            <summary>
              Looks up a localized string similar to Cache Storage.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheStorageDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the backing store..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheStorageDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheStorageDataStorageEncryptionDescription">
            <summary>
              Looks up a localized string similar to The name of the Encryption Provider to use for this Backing Store..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheStorageDataStorageEncryptionDisplayName">
            <summary>
              Looks up a localized string similar to Encryption Provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheStorageDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Backing Store..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CacheStorageDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheManagerDataAddCommand">
            <summary>
              Looks up a localized string similar to Custom Cache Manager (using type picker).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheManagerDataDescription">
            <summary>
              Looks up a localized string similar to Custom implementation that persists the cached data in memory, and optionally interacts with a persistent backing store..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheManagerDataDisplayName">
            <summary>
              Looks up a localized string similar to Custom Cache Manager.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheManagerDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Custom Cache Manager..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheManagerDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheManagerDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Custom Cache Manager..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheManagerDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheStorageDataAddCommand">
            <summary>
              Looks up a localized string similar to Custom Cache Store (using type picker).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheStorageDataDescription">
            <summary>
              Looks up a localized string similar to A Backing Store implemented as a custom class that persists the cached data, and is added to Enterprise Library..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheStorageDataDisplayName">
            <summary>
              Looks up a localized string similar to Custom Cache Storage.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheStorageDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Custom Backing Store..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheStorageDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheStorageDataStorageEncryptionDescription">
            <summary>
              Looks up a localized string similar to The name of the Encryption Provider for this Backing Store..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheStorageDataStorageEncryptionDisplayName">
            <summary>
              Looks up a localized string similar to Encryption Provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheStorageDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Custom Backing Store..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.CustomCacheStorageDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.IsolatedStorageCacheStorageDataDescription">
            <summary>
              Looks up a localized string similar to Stores the cached data in a persistent Isolated Storage store on the local machine..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.IsolatedStorageCacheStorageDataDisplayName">
            <summary>
              Looks up a localized string similar to Isolated Storage Cache Store.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.IsolatedStorageCacheStorageDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Isolated Storage Cache Store.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.IsolatedStorageCacheStorageDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.IsolatedStorageCacheStorageDataPartitionNameDescription">
             <summary>
               Looks up a localized string similar to The section or partition of the Isolated Storage area for the cached data. 
            This allows different users to cache data on the same machine safely, and prevents different users accessing the same cached data..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.IsolatedStorageCacheStorageDataPartitionNameDisplayName">
            <summary>
              Looks up a localized string similar to Partition Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.IsolatedStorageCacheStorageDataStorageEncryptionDescription">
            <summary>
              Looks up a localized string similar to The name of the Encryption Provider to use for this Isolated Storage Cache Store..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.IsolatedStorageCacheStorageDataStorageEncryptionDisplayName">
            <summary>
              Looks up a localized string similar to Encryption Provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.IsolatedStorageCacheStorageDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Isolated Storage Cache Store..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.IsolatedStorageCacheStorageDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.StorageEncryptionProviderDataDescription">
            <summary>
              Looks up a localized string similar to Encrypts the data in the backing store (but not in memory) using a Cryptography Provider defined in the Cryptography Application Block..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.StorageEncryptionProviderDataDisplayName">
            <summary>
              Looks up a localized string similar to Encryption Provider.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.StorageEncryptionProviderDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Encryption Provider..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.StorageEncryptionProviderDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.StorageEncryptionProviderDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Encryption Provider..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.DesignResources.StorageEncryptionProviderDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type Name.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerExtension">
            <summary>
            Base class for fluent interface builders that extend the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager"/> interface.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheStorageExtension">
            <summary>
            Allows access to the underlying configuration classes that are used for the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> instance being configured.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheStorageExtension.CacheStorage">
            <summary>
            Returns the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData"/> instance that corresponds to the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> being configured.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheStorageExtension.CachingSettings">
            <summary>
            Returns the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings"/> instance that is currently being build up.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerExtension.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager)">
            <summary>
            Creates an instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerExtension"/> passing the current <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerExtension.CacheManager"/>'s fluent interface builder.
            </summary>
            <param name="context">The current <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerExtension.CacheManager"/>'s fluent interface builder.<br/>
            This interface must implement <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManagerExtension"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerExtension.AddBackingStoreToCachingConfigurationAndCurrentCacheManager(Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData)">
            <summary>
            Adds a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData"/> to the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings"/> as well as adds a reference to the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerExtension.CacheManager"/> instance currently being configured.
            </summary>
            <param name="backingStore">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData"/> that should be added to configuration.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerExtension.Microsoft#Practices#EnterpriseLibrary#Common#IFluentInterface#GetType">
            <summary>
            Base class for fluent interface builders that extend the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager"/> interface.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerExtension.CacheManager">
            <summary>
            Returns the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/> instance that corresponds to the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerExtension.CacheManager"/> being configured.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerExtension.CachingSettings">
            <summary>
            Returns the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings"/> instance that is currently being build up.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManagerExtension">
            <summary>
            Allows access to the underlying configuration classes that are used for the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManagerExtension.CacheManager"/> instance being configured.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManagerExtension.CacheManager">
            <summary>
            Returns the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/> instance that corresponds to the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManagerExtension.CacheManager"/> being configured.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManagerExtension.CachingSettings">
            <summary>
            Returns the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings"/> instance that is currently being build up.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerSettingsExtension">
            <summary>
            Base class for fluent interface builders that extend the caching configuration fluent interface.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfiguration">
            <summary>
            Root fluent interface for building up caching configuration.<br/>
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationExtension">
            <summary>
            Allows access to the underlying <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings"/> being configured.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationExtension.CachingSettings">
            <summary>
            Returns the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings"/> instance that is currently being build up.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerSettingsExtension.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfiguration)">
            <summary>
            Creates an instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerSettingsExtension"/> passing the caching configuration's fluent interface builder.
            </summary>
            <param name="context">The current caching configuration's fluent interface builder.<br/>
            This interface must implement <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationExtension"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerSettingsExtension.Microsoft#Practices#EnterpriseLibrary#Common#IFluentInterface#GetType">
            <summary>
            Base class for fluent interface builders that extend the caching configuration fluent interface.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheManagerSettingsExtension.CachingSettings">
            <summary>
            Returns the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings"/> instance that is currently being build up.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CachingConfigurationSourceBuilderExtensions">
            <summary>
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceBuilder"/> extensions to support creation of caching configuration settings.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CachingConfigurationSourceBuilderExtensions.ConfigureCaching(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceBuilder)">
            <summary>
            Main entry point to create a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings"/> section.
            </summary>
            <param name="configurationSourceBuilder">The builder interface to extend.</param>
            <returns>A fluent interface to further configure the caching configuration section.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheStorageExtension">
            <summary>
            Base class for fluent interface builders that extend the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IBackingStoreEncryptItemsUsing"/> interface.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheStorageExtension.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IBackingStoreEncryptItemsUsing)">
            <summary>
            Creates an instance of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheStorageExtension"/> passing the current <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>'s fluent interface builder.
            </summary>
            <param name="context">The current <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>'s fluent interface builder.<br/>
            This interface must implement <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheStorageExtension"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheStorageExtension.AddEncryptionProviderToCachingConfigurationAndBackingStore(Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData)">
            <summary>
            Adds a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData"/> to the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings"/> as well as adds a reference to the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> instance currently being configured.
            </summary>
            <param name="storageEncyption">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData"/> that should be added to configuration.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheStorageExtension.Microsoft#Practices#EnterpriseLibrary#Common#IFluentInterface#GetType">
            <summary>
            Base class for fluent interface builders that extend the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IBackingStoreEncryptItemsUsing"/> interface.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheStorageExtension.CacheStorage">
            <summary>
            Returns the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData"/> instance that corresponds to the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> being configured.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.CacheStorageExtension.CachingSettings">
            <summary>
            Returns the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings"/> instance that is currently being build up.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IBackingStoreEncryptItemsUsing">
            <summary>
            Fluent interface used to add an encryption provider to the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> instance being configured.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IBackingStoreEncryptItemsUsing.SharedEncryptionProviderNamed(System.String)">
            <summary>
            Specifies the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> instance being configured should use the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/> by the name of <paramref name="cacheStorageEncryptionProviderName"/>.
            </summary>
            <param name="cacheStorageEncryptionProviderName">The name od the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/> that should be used.</param>
            <returns>A fluent interface that can be used to further configure caching settings.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager">
            <summary>
            Fluent interface used to configure a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> instance.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager.UseAsDefaultCache">
            <summary>
            Specifies the current <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> as the default cache manager instance.
            </summary>
            <returns>Fluent interface that can be used to further configure this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>.</returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager.StoreInSharedBackingStore(System.String)">
            <summary>
            Specifies cache items should be stored using a previously configured <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> of name <paramref name="backingStoreName"/>.
            </summary>
            <param name="backingStoreName">The name of the backing store that should be used to store cache items.</param>
            <returns>Fluent interface that can be used to further configure caching configuration.</returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager.StoreInMemory">
            <summary>
            Specifies cache items should not be persisted, but kept in memory using a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.NullBackingStore"/>.
            </summary>
            <returns>Fluent interface that can be used to further configure caching configuration.</returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.NullBackingStore"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager.WithOptions">
            <summary>
            Returns a fluent interface to further configure this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> instance.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Fluent.ICachingConfigurationCacheManagerOptions">
            <summary>
            Fluent interface used to further configure a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> instance.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Fluent.ICachingConfigurationCacheManagerOptions.PollWhetherItemsAreExpiredIntervalSeconds(System.Int32)">
            <summary>
            Specifies the time interval, in seconds, that should be waited to see whether cache items should be expired. <br/>
            The default interval is 60 seconds.
            </summary>
            <param name="pollExperitionSeconds">The time interval, in seconds, that should be waited to see whether cache items should be expired.</param>
            <returns>Fluent interface that can be used to further configure this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>.</returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Fluent.ICachingConfigurationCacheManagerOptions.StartScavengingAfterItemCount(System.Int32)">
            <summary>
            Specifies the maximum numer of cache items after which scavenging will be performed. <br/>
            The default maximum number of cache items is 1000.
            </summary>
            <param name="numberOfElementsBeforeScavenging">The maximum numer of cache items after which scavenging will be performed.</param>
            <returns>Fluent interface that can be used to further configure this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>.</returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Fluent.ICachingConfigurationCacheManagerOptions.WhenScavengingRemoveItemCount(System.Int32)">
            <summary>
            Specifies the number of cache items that should be removed when scavenging cache items. <br/>
            The default number of cache items that should be removed is 10.
            </summary>
            <param name="numberOfElementsBeforeScavenging">The number of cache items that should be removed when scavenging cache items.</param>
            <returns>Fluent interface that can be used to further configure this <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>.</returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCustomCacheManager">
            <summary>
            Fluent interface used to configure a custom cache manager.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCustomCacheManager.UseAsDefaultCache">
            <summary>
            Specifies the current custom cache manager as the default cache manager instance.
            </summary>
            <returns>Fluent interface that can be used to further configure this custom cache manager.</returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IStoreInCustomStore">
            <summary>
            Fluent interface used to configure a custom <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> instance.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData"/>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IStoreInCustomStore.EncryptUsing">
            <summary>
            Returns a fluent interface that can be used to set up encryption for the current custom <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> instance.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IStoreInIsolatedStorage">
            <summary>
            Fluent interface used to configure a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/> instance.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IStoreInIsolatedStorage.UsePartition(System.String)">
            <summary>
            Specifies the which partition should be used for the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/> instance being configured.
            </summary>
            <param name="partitionName">The name of the partition that should be used.</param>
            <returns>A fluent interface that can be used to further configure the current <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/> instance.</returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData"/>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IStoreInIsolatedStorage.EncryptUsing">
            <summary>
            Returns a fluent interface that can be used to set up encryption for the current <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/> instance.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.StoreInCustomStoreExtension">
            <summary>
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager"/> extension that allows a custom <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> to be configured.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.StoreInCustomStoreExtension.StoreInCustomStore``1(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager,System.String)">
            <summary>
            Specifies that current <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>'s items should be stored using a custom implementation of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>.
            </summary>
            <typeparam name="TCustomCacheStorageType">The implementation type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> that should be used.</typeparam>
            <param name="context">Fluent interface extension point.</param>
            <param name="backingStoreName">The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> instance.</param>
            <returns>Fluent interface to further configure the custom <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> implementation.</returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.StoreInCustomStoreExtension.StoreInCustomStore(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager,System.String,System.Type)">
            <summary>
            Specifies that current <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>'s items should be stored using a custom implementation of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>.
            </summary>
            <param name="context">Fluent interface extension point.</param>
            <param name="backingStoreName">The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> instance.</param>
            <param name="customCacheStoreType">The implementation type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> that should be used.</param>
            <returns>Fluent interface to further configure the custom <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> implementation.</returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.StoreInCustomStoreExtension.StoreInCustomStore``1(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager,System.String,System.Collections.Specialized.NameValueCollection)">
            <summary>
            Specifies that current <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>'s items should be stored using a custom implementation of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>.
            </summary>
            <typeparam name="TCustomCacheStorageType">The implementation type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> that should be used.</typeparam>
            <param name="context">Fluent interface extension point.</param>
            <param name="backingStoreName">The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> instance.</param>
            <param name="attributes">Attributes that should be passed to <typeparamref name="TCustomCacheStorageType"/> when creating an instance.</param>
            <returns>Fluent interface to further configure the custom <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> implementation.</returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.StoreInCustomStoreExtension.StoreInCustomStore(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager,System.String,System.Type,System.Collections.Specialized.NameValueCollection)">
            <summary>
            Specifies that current <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>'s items should be stored using a custom implementation of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/>.
            </summary>
            <param name="context">Fluent interface extension point.</param>
            <param name="backingStoreName">The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> instance.</param>
            <param name="customCacheStoreType">The implementation type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> that should be used.</param>
            <param name="attributes">Attributes that should be passed to <paramref name="customCacheStoreType"/> when creating an instance.</param>
            <returns>Fluent interface to further configure the custom <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.IBackingStore"/> implementation.</returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.StoreInIsolatedStorageExtension">
            <summary>
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager"/> extension that allows an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/> to be configured.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.StoreInIsolatedStorageExtension.StoreInIsolatedStorage(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfigurationCacheManager,System.String)">
            <summary>
            Specifies that current <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>'s items should be stored using a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/> instance.
            </summary>
            <param name="backingStoreName">The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/> instance</param>
            <param name="context">Fluent interface extension point.</param>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IsolatedStorageBackingStore"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ForCacheManagerNamedExtension">
            <summary>
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfiguration"/> extensions to support configuring <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> intances.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ForCacheManagerNamedExtension.ForCacheManagerNamed(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfiguration,System.String)">
            <summary>
            Adds a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> to the caching configuration.
            </summary>
            <param name="context">Fluent interface extension point.</param>
            <param name="cacheManagerName">The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>.</param>
            <returns>Fluent interface that can be used to further configure the created <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/>. </returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ForCustomCacheManagerNamedExtension">
            <summary>
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfiguration"/> extensions to support configuring custom <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager"/> instances.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ForCustomCacheManagerNamedExtension.ForCustomCacheManagerNamed``1(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfiguration,System.String)">
            <summary>
            Adds a custom cache mananger of type <typeparamref name="TCustomCacheManager"/> to the caching configuration.
            </summary>
            <typeparam name="TCustomCacheManager">The concrete type of the custom cache manager.</typeparam>
            <param name="context">Fluent interface extension point.</param>
            <param name="cacheManagerName">The name of the cache manager that should be added to configuration.</param>
            <returns>Fluent interface that can be used to further configure the created <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/>. </returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ForCustomCacheManagerNamedExtension.ForCustomCacheManagerNamed(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfiguration,System.String,System.Type)">
            <summary>
            Adds a custom cache mananger of type <paramref name="customCacheManagerType"/> to the caching configuration.
            </summary>
            <param name="context">Fluent interface extension point.</param>
            <param name="cacheManagerName">The name of the cache manager that should be added to configuration.</param>
            <returns>Fluent interface that can be used to further configure the created <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/>. </returns>
            <param name="customCacheManagerType">The concrete type of the custom cache manager. This type must implement <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager"/>.</param>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ForCustomCacheManagerNamedExtension.ForCustomCacheManagerNamed``1(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfiguration,System.String,System.Collections.Specialized.NameValueCollection)">
            <summary>
            Adds a custom cache mananger of type <typeparamref name="TCustomCacheManager"/> to the caching configuration.<br/>
            Specifying additional conifguration attributes.
            </summary>
            <typeparam name="TCustomCacheManager">The concrete type of the custom cache manager.</typeparam>
            <param name="context">Fluent interface extension point.</param>
            <param name="cacheManagerName">The name of the cache manager that should be added to configuration.</param>
            <param name="attributes">Attributes that should be passed to <typeparamref name="TCustomCacheManager"/> when creating an instance.</param>
            <returns>Fluent interface that can be used to further configure the created <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/>. </returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ForCustomCacheManagerNamedExtension.ForCustomCacheManagerNamed(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.ICachingConfiguration,System.String,System.Type,System.Collections.Specialized.NameValueCollection)">
            <summary>
            Adds a custom cache mananger of type <paramref name="customCacheManagerType"/> to the caching configuration.<br/>
            Specifying additional conifguration attributes.
            </summary>
            <param name="context">Fluent interface extension point.</param>
            <param name="cacheManagerName">The name of the cache manager that should be added to configuration.</param>
            <param name="customCacheManagerType">The concrete type of the custom cache manager. This type must implement <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager"/>.</param>
            <param name="attributes">Attributes that should be passed to <paramref name="customCacheManagerType"/> when creating an instance.</param>
            <returns>Fluent interface that can be used to further configure the created <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/>. </returns>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData">
            <summary>
            Configuration data defining IsolatedStorageCacheStorageData. This configuration section adds the name
            of the Isolated Storage area to use to store data.
            </summary>    
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData.#ctor(System.String,System.String,System.String)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData"/> class.
            </summary>
            <param name="name">
            The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData"/>.
            </param>
            <param name="storageEncryption">
            Storage Encryption data defined in configuration
            </param>
            <param name="partitionName">
            Name of the Isolated Storage area to use.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData.GetRegistrations">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData.PartitionName">
            <summary>
            Name of the Isolated Storage area to use.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider">
            <summary>
            <para>This type supports the Enterprise Library Manageability Extensions infrastructure and is not intended to 
            be used directly from your code.</para>
            Represents the behavior required to provide Group Policy updates for the Caching Application Block, and it also manages
            the creation of the ADM template categories and policies required to edit Group Policy Objects for the block.
            </summary>
            <remarks>
            This class performs the actual Group Policy update for the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings"/>
            configuration section and the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData"/> instances contained by it. Processing for 
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData"/> and <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData"/> instances is delegated to 
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProvider"/> objects registered to the configuration object data types.
            <para>
            The Group Policy directives for the Caching Application Block differ from other block's directives in that policies are
            only generated for cache managers, and these policies contain the parts used to override the settings for the
            CacheStorageData instance identified by the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerData.CacheStorage">CacheManagerData.CacheStorage</see> 
            property and the StorageEncryptionProviderData instance identified by the <see cref="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData.StorageEncryption">
            CacheStorageData.StorageEncryption</see> property, if any. Manageability providers registered for CacheStorageData and 
            StorageEncryptionProviderData subclasses must not generate policies, and the parts they generate must include the
            corresponding key name, as they will be included in the cache managers' policies. The purpose for this policy structure
            is to make the experience of editing a Group Policy Object's policies for the Caching Application Block similar to that
            of the Enterprise Library Configuration Console.
            </para>
            </remarks>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationSectionManageabilityProvider"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProvider"/>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.BackingStoresKeyName">
            <summary>
            The name of the backing stores property.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.CacheManagerExpirationPollFrequencyInSecondsPropertyName">
            <summary>
            The name of the expiration poll frequency property.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.CacheManagerMaximumElementsInCacheBeforeScavengingPropertyName">
            <summary>
            The name of the maximum number of items in cache before scavenging property.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.CacheManagerNumberToRemoveWhenScavengingPropertyName">
            <summary>
            The name of the number of items to remove when scavenging property.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.CacheManagersKeyName">
            <summary>
            The name of the cache managers property.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.DefaultCacheManagerPropertyName">
            <summary>
            The name of the default cache manager property.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.EncryptionProvidersKeyName">
            <summary>
            The name of the encryption providers property.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.#ctor(System.Collections.Generic.IDictionary{System.Type,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProvider})">
            <summary>
            <para>This method supports the Enterprise Library Manageability Extensions infrastructure and is not intended to 
            be used directly from your code.
            </para>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider"/> class with a 
            given set of manageability providers to use when dealing with the configuration for cache storage and encryption providers.
            </summary>
            <param name="subProviders">The mapping from configuration element type to
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProvider"/>.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.AddAdministrativeTemplateDirectives(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)">
            <summary>
            <para>This method supports the Enterprise Library Manageability Extensions infrastructure and is not intended to 
            be used directly from your code.</para>
            Adds the ADM instructions that describe the policies that can be used to override the configuration
            information for the Caching Application Block.
            </summary>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationSectionManageabilityProvider.AddAdministrativeTemplateDirectives(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,System.Configuration.ConfigurationSection,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.AddAdministrativeTemplateDirectivesForCacheManagers(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)">
            <devdoc>
            ADM templates for caching are different from the other blocks' templates to match the configuration console's 
            user experience. Instead of having separate categories with policies for cache managers, backing stores and 
            encryption providers, the policy for a cache manager includes the parts for its backing store and eventual
            encryption provider.
            </devdoc>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.OverrideWithGroupPoliciesForConfigurationElements(Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings,System.Boolean,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.IRegistryKey,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.IRegistryKey)">
            <summary>
            Overrides the <paramref name="configurationSection"/>'s configuration elements' properties 
            with the Group Policy values from the registry, if any.
            </summary>
            <param name="configurationSection">The configuration section that must be managed.</param>
            <param name="readGroupPolicies"><see langword="true"/> if Group Policy overrides must be applied; otherwise, 
            <see langword="false"/>.</param>
            <param name="machineKey">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.IRegistryKey"/> which holds the Group Policy overrides for the 
            configuration section at the machine level, or <see langword="null"/> 
            if there is no such registry key.</param>
            <param name="userKey">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.IRegistryKey"/> which holds the Group Policy overrides for the 
            configuration section at the user level, or <see langword="null"/> 
            if there is no such registry key.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.OverrideWithGroupPoliciesForConfigurationSection(Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheManagerSettings,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.IRegistryKey)">
            <summary>
            Overrides the <paramref name="configurationSection"/>'s properties with the Group Policy values from 
            the registry.
            </summary>
            <param name="configurationSection">The configuration section that must be managed.</param>
            <param name="policyKey">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.IRegistryKey"/> which holds the Group Policy overrides.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.SectionCategoryName">
            <summary>
            Gets the name of the category that represents the whole configuration section.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheManagerSettingsManageabilityProvider.SectionName">
            <summary>
            Gets the name of the managed configuration section.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheStorageDataManageabilityProvider">
            <summary>
            Provides a default implementation for <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData"/> that
            processes policy overrides, performing appropriate logging of 
            policy processing errors.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheStorageDataManageabilityProvider.#ctor">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheStorageDataManageabilityProvider"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheStorageDataManageabilityProvider.AddAdministrativeTemplateDirectives(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)">
            <summary>
            Adds the ADM instructions that describe the policies that can be used to override the properties of
            a specific instance of the configuration element type managed by the receiver.
            </summary>
            <param name="contentBuilder">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder"/> to which the Adm instructions are to be appended.</param>
            <param name="configurationObject">The configuration object instance.</param>
            <param name="configurationSource">The configuration source from where to get additional configuration
            information, if necessary.</param>
            <param name="elementPolicyKeyName">The key for the element's policies.</param>
            <remarks>
            The default implementation for this method creates a policy, using 
            <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProviderBase`1.ElementPolicyNameTemplate"/> to create the policy name and invoking
            <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProviderBase`1.AddElementAdministrativeTemplateParts(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,`0,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)"/>
            to add the policy parts.
            Subclasses managing objects that must not create a policy must override this method to just add the parts.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheStorageDataManageabilityProvider.AddElementAdministrativeTemplateParts(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)">
            <summary>
            Adds the ADM parts that represent the properties of
            a specific instance of the configuration element type managed by the receiver.
            </summary>
            <param name="contentBuilder">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder"/> to which the Adm instructions are to be appended.</param>
            <param name="configurationObject">The configuration object instance.</param>
            <param name="configurationSource">The configuration source from where to get additional configuration
            information, if necessary.</param>
            <param name="elementPolicyKeyName">The key for the element's policies.</param>
            <remarks>
            Subclasses managing objects that must not create a policy will likely need to include the elements' keys when creating the parts.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheStorageDataManageabilityProvider.OverrideWithGroupPolicies(Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CacheStorageData,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.IRegistryKey)">
            <summary>
            Overrides the <paramref name="configurationObject"/>'s properties with the Group Policy values from the 
            registry.
            </summary>
            <param name="configurationObject">The configuration object for instances that must be managed.</param>
            <param name="policyKey">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.IRegistryKey"/> which holds the Group Policy overrides for the 
            configuration element.</param>
            <remarks>Subclasses implementing this method must retrieve all the override values from the registry
            before making modifications to the <paramref name="configurationObject"/> so any error retrieving
            the override values will cancel policy processing.</remarks>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CacheStorageDataManageabilityProvider.ElementPolicyNameTemplate">
            <summary>
            Gets the template for the name of the policy associated to the object.
            </summary>
            <remarks>
            Elements that override 
            <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProviderBase`1.AddAdministrativeTemplateDirectives(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,`0,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)"/>
            to avoid creating a policy must still override this property.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CustomCacheManagerDataManageabilityProvider">
            <summary>
            Provides a default implementation for <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheManagerData"/> that
            processes policy overrides, performing appropriate logging of 
            policy processing errors.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CustomCacheManagerDataManageabilityProvider.#ctor">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CustomCacheManagerDataManageabilityProvider"/> class.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CustomCacheStorageDataManageabilityProvider">
            <summary>
            Provides a default implementation for <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData"/> that
            processes policy overrides, performing appropriate logging of 
            policy processing errors.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CustomCacheStorageDataManageabilityProvider.AttributesPropertyName">
            <summary>
            The name of the attributes property.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CustomCacheStorageDataManageabilityProvider.ProviderTypePropertyName">
            <summary>
            The name of the provider type property.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CustomCacheStorageDataManageabilityProvider.#ctor">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CustomCacheStorageDataManageabilityProvider"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CustomCacheStorageDataManageabilityProvider.AddAdministrativeTemplateDirectives(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)">
            <summary>
            Adds the ADM instructions that describe the policies that can be used to override the properties of
            a specific instance of the configuration element type managed by the receiver.
            </summary>
            <param name="contentBuilder">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder"/> to which the Adm instructions are to be appended.</param>
            <param name="configurationObject">The configuration object instance.</param>
            <param name="configurationSource">The configuration source from where to get additional configuration
            information, if necessary.</param>
            <param name="elementPolicyKeyName">The key for the element's policies.</param>
            <remarks>
            The default implementation for this method creates a policy, using 
            <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProviderBase`1.ElementPolicyNameTemplate"/> to create the policy name and invoking
            <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProviderBase`1.AddElementAdministrativeTemplateParts(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,`0,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)"/>
            to add the policy parts.
            Subclasses managing objects that must not create a policy must override this method to just add the parts.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.CustomCacheStorageDataManageabilityProvider.AddElementAdministrativeTemplateParts(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.CustomCacheStorageData,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)">
            <summary>
            Adds the ADM parts that represent the properties of
            a specific instance of the configuration element type managed by the receiver.
            </summary>
            <param name="contentBuilder">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder"/> to which the Adm instructions are to be appended.</param>
            <param name="configurationObject">The configuration object instance.</param>
            <param name="configurationSource">The configuration source from where to get additional configuration
            information, if necessary.</param>
            <param name="elementPolicyKeyName">The key for the element's policies.</param>
            <remarks>
            Subclasses that manage custom provider's configuration objects with additional properties may
            override this method to add the corresponding parts.
            </remarks>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProviderBase`1.AddAdministrativeTemplateDirectives(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,`0,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.IsolatedStorageCacheStorageDataManageabilityProvider">
            <summary>
            Provides a default implementation for <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData"/> that
            processes policy overrides, performing appropriate logging of 
            policy processing errors.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.IsolatedStorageCacheStorageDataManageabilityProvider.PartitionNamePropertyName">
            <summary>
            The name of the partition name property.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.IsolatedStorageCacheStorageDataManageabilityProvider.#ctor">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.IsolatedStorageCacheStorageDataManageabilityProvider"/> clas.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.IsolatedStorageCacheStorageDataManageabilityProvider.AddAdministrativeTemplateDirectives(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)">
            <summary>
            Adds the ADM instructions that describe the policies that can be used to override the properties of
            a specific instance of the configuration element type managed by the receiver.
            </summary>
            <param name="contentBuilder">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder"/> to which the Adm instructions are to be appended.</param>
            <param name="configurationObject">The configuration object instance.</param>
            <param name="configurationSource">The configuration source from where to get additional configuration
            information, if necessary.</param>
            <param name="elementPolicyKeyName">The key for the element's policies.</param>
            <remarks>
            The default implementation for this method creates a policy, using 
            <see cref="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProviderBase`1.ElementPolicyNameTemplate"/> to create the policy name and invoking
            <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProviderBase`1.AddElementAdministrativeTemplateParts(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,`0,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)"/>
            to add the policy parts.
            Subclasses managing objects that must not create a policy must override this method to just add the parts.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.IsolatedStorageCacheStorageDataManageabilityProvider.AddElementAdministrativeTemplateParts(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)">
            <summary>
            Adds the ADM parts that represent the properties of
            a specific instance of the configuration element type managed by the receiver.
            </summary>
            <param name="contentBuilder">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder"/> to which the Adm instructions are to be appended.</param>
            <param name="configurationObject">The configuration object instance.</param>
            <param name="configurationSource">The configuration source from where to get additional configuration
            information, if necessary.</param>
            <param name="elementPolicyKeyName">The key for the element's policies.</param>
            <remarks>
            Subclasses managing objects that must not create a policy will likely need to include the elements' keys when creating the parts.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.IsolatedStorageCacheStorageDataManageabilityProvider.OverrideWithGroupPolicies(Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.IsolatedStorageCacheStorageData,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.IRegistryKey)">
            <summary>
            Overrides the <paramref name="configurationObject"/>'s properties with the Group Policy values from the 
            registry.
            </summary>
            <param name="configurationObject">The configuration object for instances that must be managed.</param>
            <param name="policyKey">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.IRegistryKey"/> which holds the Group Policy overrides for the 
            configuration element.</param>
            <remarks>Subclasses implementing this method must retrieve all the override values from the registry
            before making modifications to the <paramref name="configurationObject"/> so any error retrieving
            the override values will cancel policy processing.</remarks>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.IsolatedStorageCacheStorageDataManageabilityProvider.ElementPolicyNameTemplate">
            <summary>
            Gets the template for the name of the policy associated to the object.
            </summary>
            <remarks>
            Elements that override 
            <see cref="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.ConfigurationElementManageabilityProviderBase`1.AddAdministrativeTemplateDirectives(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Manageability.Adm.AdmContentBuilder,`0,Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource,System.String)"/>
            to avoid creating a policy must still override this property.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.BackingStoreSettingsPartName">
            <summary>
              Looks up a localized string similar to Backing store settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.CacheManagerExpirationPollFrequencyInSecondsPartName">
            <summary>
              Looks up a localized string similar to Expiration poll frequency (secs.).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.CacheManagerMaximumElementsInCacheBeforeScavengingPartName">
            <summary>
              Looks up a localized string similar to Maximum elements in cache before scavenging.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.CacheManagerNumberToRemoveWhenScavengingPartName">
            <summary>
              Looks up a localized string similar to Number to remove when scavenging.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.CacheManagerPolicyNameTemplate">
            <summary>
              Looks up a localized string similar to Specify settings for cache manager &apos;{0}&apos;.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.CacheManagersCategoryName">
            <summary>
              Looks up a localized string similar to Cache managers.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.CacheManagerSettingsDefaultCacheManagerPartName">
            <summary>
              Looks up a localized string similar to Default cache manager.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.CacheManagerSettingsPolicyName">
            <summary>
              Looks up a localized string similar to Specify settings for Caching Application Block.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.CachingSectionCategoryName">
            <summary>
              Looks up a localized string similar to Caching.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.CustomProviderAttributesPartName">
            <summary>
              Looks up a localized string similar to Attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.CustomProviderTypePartName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.IsolatedStorageCacheStorageDataPartitionNamePartName">
            <summary>
              Looks up a localized string similar to Partition name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.NullBackingStoreNoSettingsPartName">
            <summary>
              Looks up a localized string similar to In memory store - no settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Manageability.Properties.Resources.StorageEncryptionProviderSettingsPartName">
            <summary>
              Looks up a localized string similar to Encryption provider settings.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData">
            <summary>
            Base class for configuration data defined for all types of StorageEncryptionProviders
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData.#ctor">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData.#ctor(System.Type)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData"/> class with a name and the type of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/>.
            </summary>
            <param name="type">
            The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/>.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData.#ctor(System.String,System.Type)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData"/> class with a name and the type of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/>.
            </summary>
            <param name="name">
            The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/>.
            </param>
            <param name="type">
            The type of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/>.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData.GetRegistrations">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData.GetCreationExpression">
            <summary>
            Gets the creation expression used to produce a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ContainerModel.TypeRegistration"/> during
            <see cref="M:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.StorageEncryptionProviderData.GetRegistrations"/>.
            </summary>
            <remarks>
            This must be overridden by a subclass, but is not marked as abstract due to configuration serialization needs.
            </remarks>
            <returns>A Expression that creates a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.BackingStoreImplementations.IStorageEncryptionProvider"/></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Configuration.Unity.CachingBlockExtension">
            <summary>
            A <see cref="T:Microsoft.Practices.Unity.UnityContainerExtension"/> that registers the policies necessary
            to create <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheManager"/> instances described in the standard
            configuration file.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationPollTimer">
            <summary>
            Represents an expiration poll timer.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationPollTimer.#ctor(System.Int32)">
            <summary>
            
            </summary>
            <param name="expirationPollFrequencyInMilliSeconds"></param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationPollTimer.StartPolling(System.Threading.TimerCallback)">
            <summary>
            Start the polling process.
            </summary>
            <param name="callbackMethod">The method to callback when a cycle has completed.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationPollTimer.StopPolling">
            <summary>
            Stop the polling process.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.AbsoluteTime">
            <summary>
            This class tests if a data item was expired using a absolute time 
            schema.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration">
            <summary>
            Allows end users to implement their own cache item expiration schema.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration.HasExpired">
            <summary>
            Specifies if item has expired or not.
            </summary>
            <returns>Returns true if the item has expired, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration.Notify">
            <summary>
            Called to tell the expiration that the CacheItem to which this expiration belongs has been touched by the user
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemExpiration.Initialize(Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem)">
            <summary>
            Called to give the instance the opportunity to initialize itself from information contained in the CacheItem.
            </summary>
            <param name="owningCacheItem">CacheItem that owns this expiration object</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.AbsoluteTime.#ctor(System.DateTime)">
            <summary>
            Create an instance of the class with a time value as input and 
            convert it to UTC.
            </summary>
            <param name="absoluteTime">
            The time to be checked for expiration
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.AbsoluteTime.#ctor(System.TimeSpan)">
            <summary>
            Creates an instance based on a time interval starting from now.
            </summary>
            <param name="timeFromNow">Time interval</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.AbsoluteTime.HasExpired">
            <summary>
            Specifies if item has expired or not.
            </summary>
            <remarks>
            bool isExpired = ICacheItemExpiration.HasExpired();
            </remarks>
            <returns>
            "True", if the data item has expired or "false", if the data item 
            has not expired
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.AbsoluteTime.Notify">
            <summary>
            Called to notify this object that the CacheItem owning this expiration was just touched by a user action
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.AbsoluteTime.Initialize(Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem)">
            <summary>
            Called to give this object an opportunity to initialize itself from data inside a CacheItem
            </summary>
            <param name="owningCacheItem">CacheItem provided to read initialization information from. Will never be null.</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.AbsoluteTime.AbsoluteExpirationTime">
            <summary>
            Gets the absolute expiration time.
            </summary>
            <value>
            The absolute expiration time.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat">
             <summary>
             Represents the extended format for the cache.
             </summary>    
             <remarks>
             Extended format syntax : <br/><br/>
             
             Minute       - 0-59 <br/>
             Hour         - 0-23 <br/>
             Day of month - 1-31 <br/>
             Month        - 1-12 <br/>
             Day of week  - 0-6 (Sunday is 0) <br/>
             Wildcards    - * means run every <br/>
             Examples: <br/>
             * * * * *    - expires every minute<br/>
             5 * * * *    - expire 5th minute of every hour <br/>
             * 21 * * *   - expire every minute of the 21st hour of every day <br/>
             31 15 * * *  - expire 3:31 PM every day <br/>
             7 4 * * 6    - expire Saturday 4:07 AM <br/>
             15 21 4 7 *  - expire 9:15 PM on 4 July <br/>
            	Therefore 6 6 6 6 1 means:
            	•	have we crossed/entered the 6th minute AND
            	•	have we crossed/entered the 6th hour AND 
            	•	have we crossed/entered the 6th day AND
            	•	have we crossed/entered the 6th month AND
            	•	have we crossed/entered A MONDAY?
            
            	Therefore these cases should exhibit these behaviors:
            
            	getTime = DateTime.Parse( "02/20/2003 04:06:55 AM" );
            	nowTime = DateTime.Parse( "06/07/2003 07:07:00 AM" );
            	isExpired = ExtendedFormatHelper.IsExtendedExpired( "6 6 6 6 1", getTime, nowTime );
            	TRUE, ALL CROSSED/ENTERED
            			
            	getTime = DateTime.Parse( "02/20/2003 04:06:55 AM" );
            	nowTime = DateTime.Parse( "06/07/2003 07:07:00 AM" );
            	isExpired = ExtendedFormatHelper.IsExtendedExpired( "6 6 6 6 5", getTime, nowTime );
            	TRUE
            			
            	getTime = DateTime.Parse( "02/20/2003 04:06:55 AM" );
            	nowTime = DateTime.Parse( "06/06/2003 06:06:00 AM" );
            	isExpired = ExtendedFormatHelper.IsExtendedExpired( "6 6 6 6 *", getTime, nowTime );
            	TRUE
            	
            			
            	getTime = DateTime.Parse( "06/05/2003 04:06:55 AM" );
            	nowTime = DateTime.Parse( "06/06/2003 06:06:00 AM" );
            	isExpired = ExtendedFormatHelper.IsExtendedExpired( "6 6 6 6 5", getTime, nowTime );
            	TRUE
            						
            	getTime = DateTime.Parse( "06/05/2003 04:06:55 AM" );
            	nowTime = DateTime.Parse( "06/06/2005 05:06:00 AM" );
            	isExpired = ExtendedFormatHelper.IsExtendedExpired( "6 6 6 6 1", getTime, nowTime );
            	TRUE
            						
            	getTime = DateTime.Parse( "06/05/2003 04:06:55 AM" );
            	nowTime = DateTime.Parse( "06/06/2003 05:06:00 AM" );
            	isExpired = ExtendedFormatHelper.IsExtendedExpired( "6 6 6 6 1", getTime, nowTime );
            	FALSE:  we did not cross 6th hour, nor did we cross Monday
            						
            	getTime = DateTime.Parse( "06/05/2003 04:06:55 AM" );
            	nowTime = DateTime.Parse( "06/06/2003 06:06:00 AM" );
            	isExpired = ExtendedFormatHelper.IsExtendedExpired( "6 6 6 6 5", getTime, nowTime );
            	TRUE, we cross/enter Friday
            
            
            	getTime = DateTime.Parse( "06/05/2003 04:06:55 AM" );
            	nowTime = DateTime.Parse( "06/06/2003 06:06:00 AM" );
            	isExpired = ExtendedFormatHelper.IsExtendedExpired( "6 6 6 6 1", getTime, nowTime );
            	FALSE:  we don’t cross Monday but all other conditions satisfied
             </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.Validate(System.String)">
            <summary>
            Validates the format.
            </summary>
            <param name="timeFormat">
            The format to validate.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.#ctor(System.String)">
            <summary>
            Initializes a new instnace of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat"/> class with a format.
            </summary>
            <param name="format">The extended format time.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.GetMinutes">
            <summary>
            Gets the minutes to expire.
            </summary>
            <value>
            The minutes to expire.
            </value>
            <remarks>
            This returns a copy of the integer array of minutes to expire.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.GetHours">
            <summary>
            Gets the hours to expire.
            </summary>
            <value>
            The hours to expire.
            </value>
            <remarks>
            This returns a copy of the integer array of hours to expire.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.GetDays">
            <summary>
            Gets the days to expire.
            </summary>
            <value>
            The days to expire.
            </value>
            <remarks>
            This returns a copy of the integer array of days to expire.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.GetMonths">
            <summary>
            Gets the months of the year to expire.
            </summary>
            <value>
            The months of the year to expire.
            </value>
            <remarks>
            This returns a copy of the integer array of months to expire.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.GetDaysOfWeek">
            <summary>
            Gets the days of the week to expire.
            </summary>
            <value>
            The days of the week to expire.
            </value>
            <remarks>
            This returns a copy of the integer array of the days of the week to expire.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.IsExpired(System.DateTime,System.DateTime)">
            <summary>
            Determines if the time has expired.
            </summary>
            <param name="getTime">The time to compare.</param>
            <param name="nowTime">The current time.</param>
            <returns>
            <see langword="true"/> if the time is expired; otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.Format">
            <summary>
            Gets the exteneded format.
            </summary>
            <value>
            The extended format.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.ExpireEveryMinute">
            <summary>
            Determines if should expire every minute.
            </summary>
            <value>
            <see langword="true"/> if should expire every minute; otherwise, <see langword="false"/>.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.ExpireEveryDay">
            <summary>
            Determines if item should expire every day.
            </summary>
            <value>
            <see langword="true"/> if should expire every day; otherwise, <see langword="false"/>.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.ExpireEveryHour">
            <summary>
            Determines if should expire every hour.
            </summary>
            <value>
            <see langword="true"/> if should expire every hour; otherwise, <see langword="false"/>.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.ExpireEveryMonth">
            <summary>
            Determines if should expire every month.
            </summary>
            <value>
            <see langword="true"/> if should expire every month; otherwise, <see langword="false"/>.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormat.ExpireEveryDayOfWeek">
            <summary>
            Determines if should expire every day of the week.
            </summary>
            <value>
            <see langword="true"/> if should expire every day of the week; otherwise, <see langword="false"/>.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormatTime">
            <summary>
            This provider tests if a item was expired using a extended format.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormatTime.#ctor(System.String)">
            <summary>
            Convert the input format to the extented time format.
            </summary>
            <param name="timeFormat">
            This contains the expiration information
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormatTime.HasExpired">
            <summary>
            Specifies if item has expired or not.
            </summary>
            <returns>
            Returns true if the data is expired otherwise false
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormatTime.Notify">
            <summary>
            Notifies that the item was recently used.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormatTime.Initialize(Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem)">
            <summary>
            Not used
            </summary>
            <param name="owningCacheItem">Not used</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.ExtendedFormatTime.TimeFormat">
            <summary>
            Gets the extended time format.
            </summary>
            <value>
            The extended time format.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.FileDependency">
            <summary>
            This class tracks a file cache dependency.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.FileDependency.#ctor(System.String)">
            <summary>
            Constructor with one argument.
            </summary>
            <param name="fullFileName">
            Indicates the name of the file
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.FileDependency.HasExpired">
            <summary>
            Specifies if the item has expired or not.
            </summary>
            <returns>Returns true if the item has expired, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.FileDependency.Notify">
            <summary>
            Notifies that the item was recently used.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.FileDependency.Initialize(Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem)">
            <summary>
            Not used
            </summary>
            <param name="owningCacheItem">Not used</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.FileDependency.FileName">
            <summary>
            Gets the name of the dependent file.
            </summary>
            <value>
            The name of the dependent file.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.FileDependency.LastModifiedTime">
            <summary>
            Gets the last modifed time of the file.
            </summary>
            <value>
            The last modifed time of the file
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.NeverExpired">
            <summary>
            This class reflects an expiration policy of never being expired.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.NeverExpired.HasExpired">
            <summary>
            Always returns false
            </summary>
            <returns>False always</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.NeverExpired.Notify">
            <summary>
            Not used
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.NeverExpired.Initialize(Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem)">
            <summary>
            Not used
            </summary>
            <param name="owningCacheItem">Not used</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.SlidingTime">
            <summary>
            This provider tests if a item was expired using a time slice schema.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.SlidingTime.#ctor(System.TimeSpan)">
            <summary>
            Create an instance of this class with the timespan for expiration.
            </summary>
            <param name="slidingExpiration">
            Expiration time span
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.SlidingTime.#ctor(System.TimeSpan,System.DateTime)">
            <summary>
            For internal use only.
            </summary>
            <param name="slidingExpiration"/>
            <param name="originalTimeStamp"/>
            <remarks>
            This constructor is for testing purposes only. Never, ever call it in a real program
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.SlidingTime.HasExpired">
            <summary>
            Specifies if item has expired or not.
            </summary>
            <returns>Returns true if the item has expired otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.SlidingTime.Notify">
            <summary>
            Notifies that the item was recently used.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.SlidingTime.Initialize(Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem)">
            <summary>
            Used to set the initial value of TimeLastUsed. This method is invoked during the reinstantiation of
            an instance from a persistent store. 
            </summary>
            <param name="owningCacheItem">CacheItem to which this expiration belongs.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.SlidingTime.CheckSlidingExpiration(System.DateTime,System.DateTime,System.TimeSpan)">
            <summary>
            Check whether the sliding time has expired.
            </summary>
            <param name="nowDateTime">Current time </param>
            <param name="lastUsed">The last time when the item has been used</param>
            <param name="slidingExpiration">The span of sliding expiration</param>
            <returns>True if the item was expired, otherwise false</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.SlidingTime.ItemSlidingExpiration">
            <summary>
            Returns sliding time window that must be exceeded for expiration to occur
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Expirations.SlidingTime.TimeLastUsed">
            <summary>
            Returns time that this object was last touched
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationTask">
            <summary>
            Represents a task to perform expiration on cached items.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationTask.#ctor(Microsoft.Practices.EnterpriseLibrary.Caching.ICacheOperations,Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider)">
            <summary>
            Initialize an instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationTask"/> class with an <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheOperations"/> object.
            </summary>
            <param name="cacheOperations">An <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheOperations"/> object.</param>
            <param name="instrumentationProvider">An instrumentation provider.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationTask.DoExpirations">
            <summary>
            Perform the cacheItemExpirations.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationTask.MarkAsExpired(System.Collections.Hashtable)">
            <summary>
            Mark each <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/> as expired. 
            </summary>
            <param name="liveCacheRepresentation">The set of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/> objects to expire.</param>
            <returns>
            The number of items marked.
            </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationTask.SweepExpiredItemsFromCache(System.Collections.Hashtable)">
            <summary>
            Sweep and remove the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/>s.
            </summary>
            <param name="liveCacheRepresentation">
            The set of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/> objects to remove.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ExpirationTask.PrepareForSweep">
            <summary>
            Prepare to sweep the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/>s.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemRefreshAction">
            <summary>
            This interface defines the contract that must be implemented to create an object that can be used to refresh 
            an expired item from the cache. The implementing class must be serializable. Care must be taken when implementing 
            this interface not to create an object that maintains too much state about its environment, as all portions of its
            environment will be serialized as well, creating possibly a huge object graph.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheItemRefreshAction.Refresh(System.String,System.Object,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason)">
            <summary>
            Called when an item expires from the cache. This method can be used to notify an application that
            the expiration occured, cause the item to be refetched and refreshed from its original location, or 
            perform any other application-specific action. 
            </summary>
            <param name="removedKey">Key of item removed from cache. Will never be null.</param>
            <param name="expiredValue">Value from cache item that was just expired</param>
            <param name="removalReason">Reason the item was removed from the cache. See <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason"/></param>
            <remarks>This method should catch and handle any exceptions thrown during its operation. No exceptions should leak
            out of it.</remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationInstaller">
            <summary>
            Let the system know that the InstallUtil.exe tool will be run against this assembly
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationInstaller.#ctor">
            <summary>
            Represents the installer for the instrumentation events. Not intended for direct use.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationInstaller.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationInstaller.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationInstaller.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.DefaultCachingEventLogger">
            <summary>
            The instrumentation gateway when no instances of the objects from the block are involved.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.DefaultCachingEventLogger.EventLogSourceName">
            <summary>
            The event log source name.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.DefaultCachingEventLogger.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.DefaultCachingEventLogger"/> class, specifying whether 
            logging to the event log is allowed.
            </summary>
            <param name="eventLoggingEnabled"><b>true</b> if writing to the event log is allowed, <b>false</b> otherwise.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.DefaultCachingEventLogger.LogConfigurationError(System.String,System.Exception)">
            <summary>
            Logs the occurrence of a configuration error for the Enterprise Library Caching Application Block through the 
            available instrumentation mechanisms.
            </summary>
            <param name="instanceName">Name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> instance in which the configuration error was detected.</param>
            <param name="exception">The exception raised for the configuration error.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider">
            <summary>
            This interface defines the instrumentation events that can be raised from a
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider.FireCacheUpdated(System.Int64,System.Int64)">
            <summary>
            Fires the CacheUpdated event - reported when items added or
            removed from the cache.
            </summary>
            <param name="updatedEntriesCount">The number of entries updated.</param>
            <param name="totalEntriesCount">The total number of entries in cache.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider.FireCacheAccessed(System.String,System.Boolean)">
            <summary>
            Fires the CacheAccessed event - reported when an item is retrieved from the
            cache, or if an item was requested but not found.
            </summary>
            <param name="key">The key which was used to access the cache.</param>
            <param name="hit"><code>true</code> if accessing the cache was successful</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider.FireCacheExpired(System.Int64)">
            <summary>
            Fires the CacheExpired event - reported when items are expired from the cache.
            </summary>
            <param name="itemsExpired">The number of items that are expired.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider.FireCacheScavenged(System.Int64)">
            <summary>
            Fires the CacheScavenged event - reported when the cache is scavenged.
            </summary>
            <param name="itemsScavenged">The number of items scavenged from cache.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider.FireCacheCallbackFailed(System.String,System.Exception)">
            <summary>
            Fires the CacheCallbackFailed event - reported when an exception occurs during
            a cache callback.
            </summary>
            <param name="key">The key that was used accessing the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> when this failure occurred.</param>
            <param name="exception">The exception causing the failure.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider.FireCacheFailed(System.String,System.Exception)">
            <summary>
            Fires the CacheFailed event - reported when an exception is thrown during a cache operation.
            </summary>
            <param name="errorMessage">The message that describes the failure.</param>
            <param name="exception">The message that represents the exception causing the failure.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider">
            <summary>
            Implementation of <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider"/> that generates
            performance counter updates in response to instrumentation activities.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.CounterCategoryName">
            <summary>
            The name of the caching counters.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.EventLogSourceName">
            <summary>
            The name of the event log source.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.TotalCacheExpiriesCounterName">
            <summary>
            The total cache expires counter name.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.TotalCacheHitsCounterName">
            <summary>
            The total cache hits counter name.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.TotalCacheMissesCounterName">
            <summary>
            The total cache misses counter name.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.TotalCacheScavengedItemsCounterName">
            <summary>
            The total cache scavenged items counter name.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.TotalUpdatedEntriesItemsCounterName">
            <summary>
            The total updated entries counter name.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.#ctor(System.String,System.Boolean,System.Boolean,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider"/> class.
            </summary>
            <param name="instanceName">The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> instance this instrumentation listener is created for.</param>
            <param name="performanceCountersEnabled"><b>true</b> if performance counters should be updated.</param>
            <param name="eventLoggingEnabled"><b>true</b> if event log entries should be written.</param>
            <param name="applicationInstanceName">The application instance name.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.#ctor(System.String,System.Boolean,System.Boolean,Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.IPerformanceCounterNameFormatter)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider"/> class.
            </summary>
            <param name="instanceName">The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> instance this instrumentation listener is created for.</param>
            <param name="performanceCountersEnabled"><b>true</b> if performance counters should be updated.</param>
            <param name="eventLoggingEnabled"><b>true</b> if event log entries should be written.</param>
            <param name="nameFormatter">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Instrumentation.IPerformanceCounterNameFormatter"/> that is used to creates unique name for each <see cref="T:System.Diagnostics.PerformanceCounter"/> instance.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.FireCacheUpdated(System.Int64,System.Int64)">
            <summary>
            Fires the CacheUpdated event - reported when items added or
            removed from the cache.
            </summary>
            <param name="updatedEntriesCount">The number of entries updated.</param>
            <param name="totalEntriesCount">The total number of entries in cache.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.FireCacheAccessed(System.String,System.Boolean)">
            <summary>
            Fires the CacheAccessed event - reported when an item is retrieved from the
            cache, or if an item was requested but not found.
            </summary>
            <param name="key">The key which was used to access the cache.</param>
            <param name="hit"><code>true</code> if accessing the cache was successful</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.FireCacheExpired(System.Int64)">
            <summary>
            Fires the CacheExpired event - reported when items are expired from the cache.
            </summary>
            <param name="itemsExpired">The number of items that are expired.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.FireCacheScavenged(System.Int64)">
            <summary>
            Fires the CacheScavenged event - reported when the cache is scavenged.
            </summary>
            <param name="itemsScavenged">The number of items scavenged from cache.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.FireCacheCallbackFailed(System.String,System.Exception)">
            <summary>
            Fires the CacheCallbackFailed event - reported when an exception occurs during
            a cache callback.
            </summary>
            <param name="key">The key that was used accessing the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> when this failure occurred.</param>
            <param name="exception">The exception causing the failure.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.FireCacheFailed(System.String,System.Exception)">
            <summary>
            Fires the CacheFailed event - reported when an exception is thrown during a cache operation.
            </summary>
            <param name="errorMessage">The message that describes the failure.</param>
            <param name="exception">The message that represents the exception causing the failure.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.CachingInstrumentationProvider.CreatePerformanceCounters(System.String[])">
            <summary>
            Creates the performance counters to instrument the caching events for the specified instance names.
            </summary>
            <param name="instanceNames">The instance names for the performance counters.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.PriorityDateComparer">
            <summary>
            Sorts the cache items in data for scavenging
            </summary>>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.PriorityDateComparer.#ctor(System.Collections.Hashtable)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.PriorityDateComparer"/> class with a list of unsorted cache items.
            </summary>
            <param name="unsortedItems">
            A set of unsorted cache items.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.PriorityDateComparer.Compare(System.Object,System.Object)">
            <summary>
            Compares two <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/> objects and returns a value indicating whether one is less than, equal to or greater than the other in priority by date.
            </summary>
            <param name="x">
            First <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/> to compare.
            </param>
            <param name="y">
            Second <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem"/> to compare.
            </param>
            <returns>
            <list type="table">
            <listheader>
            <term>Value</term>
            <description>Condition</description>
            </listheader>
            <item>
            <term>Less than zero</term>
            <description><paramref name="x"/> is less than <paramref name="y"/></description>
            </item>
            <item>
            <term>Zero</term>
            <description><paramref name="x"/> equals <paramref name="y"/></description>
            </item>
            <item>
            <term>Greater than zero</term>
            <description><paramref name="x"/> is greater than <paramref name="y"/></description>
            </item>
            </list>
            </returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.RefreshActionInvoker">
            <summary>
            Purpose of this class is to encapsulate the behavior of how ICacheItemRefreshActions
            are invoked in the background.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.RefreshActionInvoker.InvokeRefreshAction(Microsoft.Practices.EnterpriseLibrary.Caching.CacheItem,Microsoft.Practices.EnterpriseLibrary.Caching.CacheItemRemovedReason,Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider)">
            <summary>
            Invokes the refresh action on a thread pool thread
            </summary>
            <param name="removedCacheItem">Cache item being removed. Must never be null.</param>
            <param name="removalReason">The reason the item was removed.</param>	
            <param name="instrumentationProvider">The instrumentation provider.</param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.ScavengerTask">
            <summary>
            Represents the task to start scavenging items in a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ScavengerTask.#ctor(System.Int32,System.Int32,Microsoft.Practices.EnterpriseLibrary.Caching.ICacheOperations,Microsoft.Practices.EnterpriseLibrary.Caching.Instrumentation.ICachingInstrumentationProvider)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ScavengerTask"/> with a <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheManager"/> name, the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.CacheCapacityScavengingPolicy"/> and the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheOperations"/>.
            </summary>
            <param name="numberToRemoveWhenScavenging">The number of items that should be removed from the cache when scavenging.</param>
            <param name="maximumElementsInCacheBeforeScavenging"></param>
            <param name="cacheOperations">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Caching.ICacheOperations"/> to perform.</param>
            <param name="instrumentationProvider">An instrumentation provider.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.ScavengerTask.DoScavenging">
            <summary>
            Performs the scavenging.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.SerializationUtility">
            <summary>
            Utility class for serializing and deserializing objects to and from byte streams
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.SerializationUtility.ToBytes(System.Object)">
            <summary>
            Converts an object into an array of bytes. Object must be serializable.
            </summary>
            <param name="value">Object to serialize. May be null.</param>
            <returns>Serialized object, or null if input was null.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Caching.SerializationUtility.ToObject(System.Byte[])">
            <summary>
            Converts a byte array into an object. 
            </summary>
            <param name="serializedObject">Object to deserialize. May be null.</param>
            <returns>Deserialized object, or null if input was null.</returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.BackgroundSchedulerProducerConsumerQueueFailure">
            <summary>
              Looks up a localized string similar to Internal ProducerConsumerQueue thread failed..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.BlockName">
            <summary>
              Looks up a localized string similar to Enterprise Library Caching Application Block.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheAccessAttemptsCounterHelpResource">
            <summary>
              Looks up a localized string similar to Total # of Cache Access Attempts is the number of reads from the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheAccessedExpiredItemsRatioBaseCounterHelpResource">
            <summary>
              Looks up a localized string similar to Cache Accessed Expired Items Ratio Base is the total number of items expired from the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheAccessedExpiredItemsRatioCounterHelpResource">
            <summary>
              Looks up a localized string similar to Cache Accessed Expired Items Ratio is the ratio between expired items accessed by the user and total items expired from the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheAccessedScavengedItemsRatioBaseCounterHelpResource">
            <summary>
              Looks up a localized string similar to Cache Accessed Scavenged Items Ratio Base is the total number of items scavenged from the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheAccessedScavengedItemsRatioCounterHelpResource">
            <summary>
              Looks up a localized string similar to Cache Accessed Scavenged Items Ratio is the ratio between scavenged items accessed by the user and total items scavenged from the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheExpiriesPerSecCounterHelpResource">
            <summary>
              Looks up a localized string similar to Cache Expiries/sec is the rate at which items were expired from the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheHitRatioCounterHelpResource">
            <summary>
              Looks up a localized string similar to Cache Hit Ratio is the ratio between hits and reads from the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheHitsPerSecCounterHelpResource">
            <summary>
              Looks up a localized string similar to Cache Hits/sec is the rate at which requests for existing items were received by the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheMissesPerSecCounterHelpResource">
            <summary>
              Looks up a localized string similar to Cache Misses/sec is the rate at which requests for non existing items were received by the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheNotInitializedException">
            <summary>
              Looks up a localized string similar to Add called without setting BackgroundScheduler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheScavengedItemsPerSecCounterHelpResource">
            <summary>
              Looks up a localized string similar to Cache Scavenged Items/sec is the rate at which items were scavenged from the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheTotalEntriesCounterHelpResource">
            <summary>
              Looks up a localized string similar to Total Cache Entries is the total number of entries in the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.CacheUpdatedEntriesPerSecHelpResource">
            <summary>
              Looks up a localized string similar to Updated Entries/sec is the rate at which items were updated in the cache. An update can be either an &quot;add&quot; or a &quot;remove&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.EmptyParameterName">
            <summary>
              Looks up a localized string similar to Parameter name cannot be null or an empty string..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ErrorCacheCallbackFailedMessage">
            <summary>
              Looks up a localized string similar to The error occurred executing the removal callback for key &quot;{1}&quot; in the the &quot;{0}&quot; instance..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ErrorCacheConfigurationFailedMessage">
            <summary>
              Looks up a localized string similar to The error occurred retrieving the configuration for instance &quot;{0}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ErrorCacheOperationFailedMessage">
            <summary>
              Looks up a localized string similar to The error occurred using the &quot;{0}&quot; instance..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionBackingStoresWithNonDefaultConstructorsShouldOverrideGetRegistrations">
            <summary>
              Looks up a localized string similar to Backing Stores with non-default constructors should override GetRegistrations()..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionInvalidExtendedFormatArguments">
            <summary>
              Looks up a localized string similar to The format length is invalid..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionInvalidFileName">
            <summary>
              Looks up a localized string similar to The file does not exist..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionMethodMustBeImplementedBySubclasses">
            <summary>
              Looks up a localized string similar to Must be implemented by subclasses..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionNullFileName">
            <summary>
              Looks up a localized string similar to The file name cannot be null..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionNullTimeFormat">
            <summary>
              Looks up a localized string similar to Time format cannot be null..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionParameterMustImplement">
            <summary>
              Looks up a localized string similar to Parameter must implement type &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionRangeAbsoluteTime">
            <summary>
              Looks up a localized string similar to Absolute time cannot be less than current time..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionRangeDay">
            <summary>
              Looks up a localized string similar to Day of Week in Extended Format out of range..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionRangeHour">
            <summary>
              Looks up a localized string similar to Hour in Extended Format out of range..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionRangeMinute">
            <summary>
              Looks up a localized string similar to Minutes in Extended Format out of range..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionRangeMonth">
            <summary>
              Looks up a localized string similar to Month of Year in Extended Format out of range..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionRangeSlidingExpiration">
            <summary>
              Looks up a localized string similar to Sliding time should be greater than or equal to 1s..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionStorageAreaNullOrEmpty">
            <summary>
              Looks up a localized string similar to The storage are name for the Isolated Storage Backing Store can not be null or an empty string..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionTypeForCustomBackingStoreMustDeriveFrom">
            <summary>
              Looks up a localized string similar to The Custom Backing Store &apos;{0}&apos; with Type &apos;{1}&apos; must derive from IBackingStore..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.ExceptionTypeForCustomCacheManagerMustDeriveFrom">
            <summary>
              Looks up a localized string similar to The Custom Cache Manager &apos;{0}&apos; with Type &apos;{1}&apos; must derive from ICacheManager..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.FailureToRemoveCacheItemInBackground">
            <summary>
              Looks up a localized string similar to Failure while removing item from cache in background..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.FailureToSpawnUserSpecifiedRefreshAction">
            <summary>
              Looks up a localized string similar to Failure while starting application-specified refresh action..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.InvalidExpirationPollFrequencyInMilliSeconds">
            <summary>
              Looks up a localized string similar to Expiration poll frequency time must be at least 1 millisecond..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.InvalidPollingStopOperation">
            <summary>
              Looks up a localized string similar to Cannot stop polling before it is started..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.TotalCacheExpiriesCounterHelpResource">
            <summary>
              Looks up a localized string similar to Total Cache Expiries is the total number of items expired from the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.TotalCacheHitsCounterHelpResource">
            <summary>
              Looks up a localized string similar to Total Cache Hits is the total number of requests for existing items received by the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.TotalCacheMissesCounterHelpResource">
            <summary>
              Looks up a localized string similar to Total Cache Misses is the total number for non existing items received by the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.TotalCacheScavengedItemsCounterHelpResource">
            <summary>
              Looks up a localized string similar to Total Cache Scavenged Items is the total number of items scavenged from the cache..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Caching.Properties.Resources.TotalCacheUpdatedEntriesHelpResource">
            <summary>
              Looks up a localized string similar to Total Updated Entries is the total number of items updated in the cache. An update can be either an &quot;add&quot; or a &quot;remove&quot;..
            </summary>
        </member>
    </members>
</doc>
