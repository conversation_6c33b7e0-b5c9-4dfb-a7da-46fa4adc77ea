<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Mo<PERSON>.<PERSON></name>
    </assembly>
    <members>
        <member name="T:Moq.Mock`1">
            <summary>
			Provides a mock implementation of <typeparamref name="T"/>.
		</summary><remarks>
			Any interface type can be used for mocking, but for classes, only abstract and virtual members can be mocked.
			<para>
				The behavior of the mock with regards to the setups and the actual calls is determined
				by the optional <see cref="T:Moq.MockBehavior"/> that can be passed to the <see cref="M:Moq.Mock`1.#ctor(Moq.MockBehavior)"/>
				constructor.
			</para>
		</remarks><typeparam name="T">Type to mock, which can be an interface or a class.</typeparam><example group="overview" order="0">
			The following example shows establishing setups with specific values
			for method invocations:
			<code>
				// Arrange
				var order = new Order(TALISKER, 50);
				var mock = new Mock&lt;IWarehouse&gt;();

				mock.Setup(x =&gt; x.HasInventory(TALISKER, 50)).Returns(true);

				// Act
				order.Fill(mock.Object);

				// Assert
				Assert.True(order.IsFilled);
			</code>
			The following example shows how to use the <see cref="T:Moq.It"/> class
			to specify conditions for arguments instead of specific values:
			<code>
				// Arrange
				var order = new Order(TALISKER, 50);
				var mock = new Mock&lt;IWarehouse&gt;();

				// shows how to expect a value within a range
				mock.Setup(x =&gt; x.HasInventory(
							It.IsAny&lt;string&gt;(),
							It.IsInRange(0, 100, Range.Inclusive)))
					 .Returns(false);

				// shows how to throw for unexpected calls.
				mock.Setup(x =&gt; x.Remove(
							It.IsAny&lt;string&gt;(),
							It.IsAny&lt;int&gt;()))
					 .Throws(new InvalidOperationException());

				// Act
				order.Fill(mock.Object);

				// Assert
				Assert.False(order.IsFilled);
			</code>
		</example>
        </member>
        <member name="T:Moq.Mock">
            <summary>
			Base class for mocks and static helper class with methods that
			apply to mocked objects, such as <see cref="M:Moq.Mock.Get``1(``0)"/> to
			retrieve a <see cref="T:Moq.Mock`1"/> from an object instance.
		</summary>
        </member>
        <member name="T:Moq.IHideObjectMembers">
            <summary>
            Helper interface used to hide the base <see cref="T:System.Object"/> 
            members from the fluent API to make it much cleaner 
            in Visual Studio intellisense.
            </summary>
        </member>
        <member name="M:Moq.IHideObjectMembers.GetType">
            <summary/>
        </member>
        <member name="M:Moq.IHideObjectMembers.GetHashCode">
            <summary/>
        </member>
        <member name="M:Moq.IHideObjectMembers.ToString">
            <summary/>
        </member>
        <member name="M:Moq.IHideObjectMembers.Equals(System.Object)">
            <summary/>
        </member>
        <member name="M:Moq.Mock.Of``1">
            <summary>
            Creates an mock object of the indicated type.
            </summary>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.Mock.Of``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Creates an mock object of the indicated type.
            </summary>
            <param name="predicate">The predicate with the specification of how the mocked object should behave.</param>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.Mock.#ctor">
            <summary>
			Initializes a new instance of the <see cref="T:Moq.Mock"/> class.
		</summary>
        </member>
        <member name="M:Moq.Mock.Get``1(``0)">
            <summary>
			Retrieves the mock object for the given object instance.
		</summary><typeparam name="T">
			Type of the mock to retrieve. Can be omitted as it's inferred
			from the object instance passed in as the <paramref name="mocked"/> instance.
		</typeparam><param name="mocked">The instance of the mocked object.</param><returns>The mock associated with the mocked object.</returns><exception cref="T:System.ArgumentException">
			The received <paramref name="mocked"/> instance
			was not created by Moq.
		</exception><example group="advanced">
			The following example shows how to add a new setup to an object
			instance which is not the original <see cref="T:Moq.Mock`1"/> but rather
			the object associated with it:
			<code>
				// Typed instance, not the mock, is retrieved from some test API.
				HttpContextBase context = GetMockContext();

				// context.Request is the typed object from the "real" API
				// so in order to add a setup to it, we need to get
				// the mock that "owns" it
				Mock&lt;HttpRequestBase&gt; request = Mock.Get(context.Request);
				mock.Setup(req =&gt; req.AppRelativeCurrentExecutionFilePath)
					 .Returns(tempUrl);
			</code>
		</example>
        </member>
        <member name="M:Moq.Mock.OnGetObject">
            <summary>
			Returns the mocked object value.
		</summary>
        </member>
        <member name="M:Moq.Mock.Verify">
            <summary>
			Verifies that all verifiable expectations have been met.
		</summary><example group="verification">
			This example sets up an expectation and marks it as verifiable. After
			the mock is used, a <c>Verify()</c> call is issued on the mock
			to ensure the method in the setup was invoked:
			<code>
				var mock = new Mock&lt;IWarehouse&gt;();
				this.Setup(x =&gt; x.HasInventory(TALISKER, 50)).Verifiable().Returns(true);
				...
				// other test code
				...
				// Will throw if the test code has didn't call HasInventory.
				this.Verify();
			</code>
		</example><exception cref="T:Moq.MockException">Not all verifiable expectations were met.</exception>
        </member>
        <member name="M:Moq.Mock.VerifyAll">
            <summary>
			Verifies all expectations regardless of whether they have
			been flagged as verifiable.
		</summary><example group="verification">
			This example sets up an expectation without marking it as verifiable. After
			the mock is used, a <see cref="M:Moq.Mock.VerifyAll"/> call is issued on the mock
			to ensure that all expectations are met:
			<code>
				var mock = new Mock&lt;IWarehouse&gt;();
				this.Setup(x =&gt; x.HasInventory(TALISKER, 50)).Returns(true);
				...
				// other test code
				...
				// Will throw if the test code has didn't call HasInventory, even
				// that expectation was not marked as verifiable.
				this.VerifyAll();
			</code>
		</example><exception cref="T:Moq.MockException">At least one expectation was not met.</exception>		
        </member>
        <member name="M:Moq.Mock.GetInterceptor(System.Linq.Expressions.Expression,Moq.Mock)">
            <summary>
            Gets the interceptor target for the given expression and root mock, 
            building the intermediate hierarchy of mock objects if necessary.
            </summary>
        </member>
        <member name="M:Moq.Mock.DoRaise(System.Reflection.EventInfo,System.EventArgs)">
            <summary>
            Raises the associated event with the given 
            event argument data.
            </summary>
        </member>
        <member name="M:Moq.Mock.DoRaise(System.Reflection.EventInfo,System.Object[])">
            <summary>
            Raises the associated event with the given 
            event argument data.
            </summary>
        </member>
        <member name="M:Moq.Mock.As``1">
            <summary>
			Adds an interface implementation to the mock,
			allowing setups to be specified for it.
		</summary><remarks>
			This method can only be called before the first use
			of the mock <see cref="P:Moq.Mock.Object"/> property, at which
			point the runtime type has already been generated
			and no more interfaces can be added to it.
			<para>
				Also, <typeparamref name="TInterface"/> must be an
				interface and not a class, which must be specified
				when creating the mock instead.
			</para>
		</remarks><exception cref="T:System.InvalidOperationException">
			The mock type
			has already been generated by accessing the <see cref="P:Moq.Mock.Object"/> property.
		</exception><exception cref="T:System.ArgumentException">
			The <typeparamref name="TInterface"/> specified
			is not an interface.
		</exception><example>
			The following example creates a mock for the main interface
			and later adds <see cref="T:System.IDisposable"/> to it to verify
			it's called by the consumer code:
			<code>
				var mock = new Mock&lt;IProcessor&gt;();
				mock.Setup(x =&gt; x.Execute("ping"));

				// add IDisposable interface
				var disposable = mock.As&lt;IDisposable&gt;();
				disposable.Setup(d =&gt; d.Dispose()).Verifiable();
			</code>
		</example><typeparam name="TInterface">Type of interface to cast the mock to.</typeparam>
        </member>
        <member name="M:Moq.Mock.SetReturnsDefault``1(``0)">
            <!-- No matching elements were found for the following include tag --><include file="Mock.Generic.xdoc" path="docs/doc[@for=&quot;Mock.SetReturnDefault{TReturn}&quot;]/*"/>
        </member>
        <member name="P:Moq.Mock.Behavior">
            <summary>
			Behavior of the mock, according to the value set in the constructor.
		</summary>
        </member>
        <member name="P:Moq.Mock.CallBase">
            <summary>
			Whether the base member virtual implementation will be called
			for mocked classes if no setup is matched. Defaults to <see langword="false"/>.
		</summary>
        </member>
        <member name="P:Moq.Mock.DefaultValue">
            <summary>
			Specifies the behavior to use when returning default values for
			unexpected invocations on loose mocks.
		</summary>
        </member>
        <member name="P:Moq.Mock.Object">
            <summary>
			Gets the mocked object instance.
		</summary>
        </member>
        <member name="P:Moq.Mock.MockedType">
            <summary>
            Retrieves the type of the mocked object, its generic type argument.
            This is used in the auto-mocking of hierarchy access.
            </summary>
        </member>
        <member name="P:Moq.Mock.DelegateInterfaceMethod">
            <summary>
            If this is a mock of a delegate, this property contains the method
            on the autogenerated interface so that we can convert setup + verify
            expressions on the delegate into expressions on the interface proxy.
            </summary>
        </member>
        <member name="P:Moq.Mock.IsDelegateMock">
            <summary>
            Allows to check whether expression conversion to the <see cref="P:Moq.Mock.DelegateInterfaceMethod"/> 
            must be performed on the mock, without causing unnecessarily early initialization of 
            the mock instance, which breaks As{T}.
            </summary>
        </member>
        <member name="P:Moq.Mock.DefaultValueProvider">
            <summary>
            Specifies the class that will determine the default 
            value to return when invocations are made that 
            have no setups and need to return a default 
            value (for loose mocks).
            </summary>
        </member>
        <member name="P:Moq.Mock.ImplementedInterfaces">
            <summary>
            Exposes the list of extra interfaces implemented by the mock.
            </summary>
        </member>
        <member name="T:Moq.IMock`1">
            <summary>
            Covarient interface for Mock&lt;T&gt; such that casts between IMock&lt;Employee&gt; to IMock&lt;Person&gt;
            are possible. Only covers the covariant members of Mock&lt;T&gt;.
            </summary>
        </member>
        <member name="P:Moq.IMock`1.Object">
            <summary>
			Exposes the mocked object instance.
		</summary>
        </member>
        <member name="P:Moq.IMock`1.Behavior">
            <summary>
			Behavior of the mock, according to the value set in the constructor.
		</summary>
        </member>
        <member name="P:Moq.IMock`1.CallBase">
            <summary>
			Whether the base member virtual implementation will be called
			for mocked classes if no setup is matched. Defaults to <see langword="false"/>.
		</summary>
        </member>
        <member name="P:Moq.IMock`1.DefaultValue">
            <summary>
			Specifies the behavior to use when returning default values for
			unexpected invocations on loose mocks.
		</summary>
        </member>
        <member name="M:Moq.Mock`1.#ctor(System.Boolean)">
            <summary>
            Ctor invoked by AsTInterface exclusively.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.#ctor">
            <summary>
			Initializes an instance of the mock with <see cref="F:Moq.MockBehavior.Default">default behavior</see>.
		</summary><example>
			<code>var mock = new Mock&lt;IFormatProvider&gt;();</code>
		</example>
        </member>
        <member name="M:Moq.Mock`1.#ctor(System.Object[])">
            <summary>
			Initializes an instance of the mock with <see cref="F:Moq.MockBehavior.Default">default behavior</see> and with
			the given constructor arguments for the class. (Only valid when <typeparamref name="T"/> is a class)
		</summary><remarks>
			The mock will try to find the best match constructor given the constructor arguments, and invoke that
			to initialize the instance. This applies only for classes, not interfaces.
		</remarks><example>
			<code>var mock = new Mock&lt;MyProvider&gt;(someArgument, 25);</code>
		</example><param name="args">Optional constructor arguments if the mocked type is a class.</param>
        </member>
        <member name="M:Moq.Mock`1.#ctor(Moq.MockBehavior)">
            <summary>
			Initializes an instance of the mock with the specified <see cref="T:Moq.MockBehavior">behavior</see>.
		</summary><example>
			<code>var mock = new Mock&lt;IFormatProvider&gt;(MockBehavior.Relaxed);</code>
		</example><param name="behavior">Behavior of the mock.</param>
        </member>
        <member name="M:Moq.Mock`1.#ctor(Moq.MockBehavior,System.Object[])">
            <summary>
			Initializes an instance of the mock with a specific <see cref="T:Moq.MockBehavior">behavior</see> with
			the given constructor arguments for the class.
		</summary><remarks>
			The mock will try to find the best match constructor given the constructor arguments, and invoke that
			to initialize the instance. This applies only to classes, not interfaces.
		</remarks><example>
			<code>var mock = new Mock&lt;MyProvider&gt;(someArgument, 25);</code>
		</example><param name="behavior">Behavior of the mock.</param><param name="args">Optional constructor arguments if the mocked type is a class.</param>
        </member>
        <member name="M:Moq.Mock`1.ToString">
            <summary>
			Returns the name of the mock
		</summary>
        </member>
        <member name="M:Moq.Mock`1.OnGetObject">
            <summary>
            Returns the mocked object value.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.Setup(System.Linq.Expressions.Expression{System.Action{`0}})">
            <summary>
			Specifies a setup on the mocked type for a call to
			to a void method.
		</summary><remarks>
			If more than one setup is specified for the same method or property,
			the latest one wins and is the one that will be executed.
		</remarks><param name="expression">Lambda expression that specifies the expected method invocation.</param><example group="setups">
			<code>
				var mock = new Mock&lt;IProcessor&gt;();
				mock.Setup(x =&gt; x.Execute("ping"));
			</code>
		</example>
        </member>
        <member name="M:Moq.Mock`1.Setup``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
			Specifies a setup on the mocked type for a call to
			to a value returning method.
		</summary><typeparam name="TResult">Type of the return value. Typically omitted as it can be inferred from the expression.</typeparam><remarks>
			If more than one setup is specified for the same method or property,
			the latest one wins and is the one that will be executed.
		</remarks><param name="expression">Lambda expression that specifies the method invocation.</param><example group="setups">
			<code>
				mock.Setup(x =&gt; x.HasInventory("Talisker", 50)).Returns(true);
			</code>
		</example>
        </member>
        <member name="M:Moq.Mock`1.SetupGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
			Specifies a setup on the mocked type for a call to
			to a property getter.
		</summary><remarks>
			If more than one setup is set for the same property getter,
			the latest one wins and is the one that will be executed.
		</remarks><typeparam name="TProperty">Type of the property. Typically omitted as it can be inferred from the expression.</typeparam><param name="expression">Lambda expression that specifies the property getter.</param><example group="setups">
			<code>
				mock.SetupGet(x =&gt; x.Suspended)
					 .Returns(true);
			</code>
		</example>
        </member>
        <member name="M:Moq.Mock`1.SetupSet``1(System.Action{`0})">
            <summary>
			Specifies a setup on the mocked type for a call to
			to a property setter.
		</summary><remarks>
			If more than one setup is set for the same property setter,
			the latest one wins and is the one that will be executed.
			<para>
				This overloads allows the use of a callback already
				typed for the property type.
			</para>
		</remarks><typeparam name="TProperty">Type of the property. Typically omitted as it can be inferred from the expression.</typeparam><param name="setterExpression">The Lambda expression that sets a property to a value.</param><example group="setups">
			<code>
				mock.SetupSet(x =&gt; x.Suspended = true);
			</code>
		</example>
        </member>
        <member name="M:Moq.Mock`1.SetupSet(System.Action{`0})">
            <summary>
			Specifies a setup on the mocked type for a call to
			to a property setter.
		</summary><remarks>
			If more than one setup is set for the same property setter,
			the latest one wins and is the one that will be executed.
		</remarks><param name="setterExpression">Lambda expression that sets a property to a value.</param><example group="setups">
			<code>
				mock.SetupSet(x =&gt; x.Suspended = true);
			</code>
		</example>
        </member>
        <member name="M:Moq.Mock`1.SetupProperty``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
			Specifies that the given property should have "property behavior",
			meaning that setting its value will cause it to be saved and
			later returned when the property is requested. (this is also
			known as "stubbing").
		</summary><typeparam name="TProperty">
			Type of the property, inferred from the property
			expression (does not need to be specified).
		</typeparam><param name="property">Property expression to stub.</param><example>
			If you have an interface with an int property <c>Value</c>, you might
			stub it using the following straightforward call:
			<code>
				var mock = new Mock&lt;IHaveValue&gt;();
				mock.Stub(v =&gt; v.Value);
			</code>
			After the <c>Stub</c> call has been issued, setting and
			retrieving the object value will behave as expected:
			<code>
				IHaveValue v = mock.Object;

				v.Value = 5;
				Assert.Equal(5, v.Value);
			</code>
		</example>
        </member>
        <member name="M:Moq.Mock`1.SetupProperty``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},``0)">
            <summary>
			Specifies that the given property should have "property behavior",
			meaning that setting its value will cause it to be saved and
			later returned when the property is requested. This overload
			allows setting the initial value for the property. (this is also
			known as "stubbing").
		</summary><typeparam name="TProperty">
			Type of the property, inferred from the property
			expression (does not need to be specified).
		</typeparam><param name="property">Property expression to stub.</param><param name="initialValue">Initial value for the property.</param><example>
			If you have an interface with an int property <c>Value</c>, you might
			stub it using the following straightforward call:
			<code>
				var mock = new Mock&lt;IHaveValue&gt;();
				mock.SetupProperty(v =&gt; v.Value, 5);
			</code>
			After the <c>SetupProperty</c> call has been issued, setting and
			retrieving the object value will behave as expected:
			<code>
				IHaveValue v = mock.Object;
				// Initial value was stored
				Assert.Equal(5, v.Value);

				// New value set which changes the initial value
				v.Value = 6;
				Assert.Equal(6, v.Value);
			</code>
		</example>
        </member>
        <member name="M:Moq.Mock`1.SetupAllProperties">
            <summary>
			Specifies that the all properties on the mock should have "property behavior",
			meaning that setting its value will cause it to be saved and
			later returned when the property is requested. (this is also
			known as "stubbing"). The default value for each property will be the
			one generated as specified by the <see cref="P:Moq.Mock.DefaultValue"/> property for the mock.
		</summary><remarks>
			If the mock <see cref="P:Moq.Mock.DefaultValue"/> is set to <see cref="F:Moq.DefaultValue.Mock"/>,
			the mocked default values will also get all properties setup recursively.
		</remarks>
        </member>
        <member name="M:Moq.Mock`1.When(System.Func{System.Boolean})">
            <!-- No matching elements were found for the following include tag --><include file="Mock.Generic.xdoc" path="docs/doc[@for=&quot;Mock{T}.When&quot;]/*"/>
        </member>
        <member name="M:Moq.Mock`1.Verify(System.Linq.Expressions.Expression{System.Action{`0}})">
            <summary>
			Verifies that a specific invocation matching the given expression was performed on the mock. Use
			in conjuntion with the default <see cref="F:Moq.MockBehavior.Loose"/>.
		</summary><example group="verification">
			This example assumes that the mock has been used, and later we want to verify that a given
			invocation with specific parameters was performed:
			<code>
				var mock = new Mock&lt;IProcessor&gt;();
				// exercise mock
				//...
				// Will throw if the test code didn't call Execute with a "ping" string argument.
				mock.Verify(proc =&gt; proc.Execute("ping"));
			</code>
		</example><exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception><param name="expression">Expression to verify.</param>
        </member>
        <member name="M:Moq.Mock`1.Verify(System.Linq.Expressions.Expression{System.Action{`0}},Moq.Times)">
            <summary>
			Verifies that a specific invocation matching the given expression was performed on the mock. Use
			in conjuntion with the default <see cref="F:Moq.MockBehavior.Loose"/>.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="expression">Expression to verify.</param><param name="times">The number of times a method is allowed to be called.</param>
        </member>
        <member name="M:Moq.Mock`1.Verify(System.Linq.Expressions.Expression{System.Action{`0}},System.Func{Moq.Times})">
            <summary>
			Verifies that a specific invocation matching the given expression was performed on the mock. Use
			in conjuntion with the default <see cref="F:Moq.MockBehavior.Loose"/>.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="expression">Expression to verify.</param><param name="times">The number of times a method is allowed to be called.</param>
        </member>
        <member name="M:Moq.Mock`1.Verify(System.Linq.Expressions.Expression{System.Action{`0}},System.String)">
            <summary>
			Verifies that a specific invocation matching the given expression was performed on the mock,
			specifying a failure error message. Use in conjuntion with the default
			<see cref="F:Moq.MockBehavior.Loose"/>.
		</summary><example group="verification">
			This example assumes that the mock has been used, and later we want to verify that a given
			invocation with specific parameters was performed:
			<code>
				var mock = new Mock&lt;IProcessor&gt;();
				// exercise mock
				//...
				// Will throw if the test code didn't call Execute with a "ping" string argument.
				mock.Verify(proc =&gt; proc.Execute("ping"));
			</code>
		</example><exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception><param name="expression">Expression to verify.</param><param name="failMessage">Message to show if verification fails.</param>
        </member>
        <member name="M:Moq.Mock`1.Verify(System.Linq.Expressions.Expression{System.Action{`0}},Moq.Times,System.String)">
            <summary>
			Verifies that a specific invocation matching the given expression was performed on the mock,
			specifying a failure error message. Use in conjuntion with the default
			<see cref="F:Moq.MockBehavior.Loose"/>.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="expression">Expression to verify.</param><param name="times">The number of times a method is allowed to be called.</param><param name="failMessage">Message to show if verification fails.</param>
        </member>
        <member name="M:Moq.Mock`1.Verify(System.Linq.Expressions.Expression{System.Action{`0}},System.Func{Moq.Times},System.String)">
            <summary>
			Verifies that a specific invocation matching the given expression was performed on the mock,
			specifying a failure error message. Use in conjuntion with the default
			<see cref="F:Moq.MockBehavior.Loose"/>.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="expression">Expression to verify.</param><param name="times">The number of times a method is allowed to be called.</param><param name="failMessage">Message to show if verification fails.</param>
        </member>
        <member name="M:Moq.Mock`1.Verify``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
			Verifies that a specific invocation matching the given expression was performed on the mock. Use
			in conjuntion with the default <see cref="F:Moq.MockBehavior.Loose"/>.
		</summary><example group="verification">
			This example assumes that the mock has been used, and later we want to verify that a given
			invocation with specific parameters was performed:
			<code>
				var mock = new Mock&lt;IWarehouse&gt;();
				// exercise mock
				//...
				// Will throw if the test code didn't call HasInventory.
				mock.Verify(warehouse =&gt; warehouse.HasInventory(TALISKER, 50));
			</code>
		</example><exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception><param name="expression">Expression to verify.</param><typeparam name="TResult">Type of return value from the expression.</typeparam>
        </member>
        <member name="M:Moq.Mock`1.Verify``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},Moq.Times)">
            <summary>
			Verifies that a specific invocation matching the given
			expression was performed on the mock. Use in conjuntion
			with the default <see cref="F:Moq.MockBehavior.Loose"/>.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="expression">Expression to verify.</param><param name="times">The number of times a method is allowed to be called.</param><typeparam name="TResult">Type of return value from the expression.</typeparam>
        </member>
        <member name="M:Moq.Mock`1.Verify``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{Moq.Times})">
            <summary>
			Verifies that a specific invocation matching the given
			expression was performed on the mock. Use in conjuntion
			with the default <see cref="F:Moq.MockBehavior.Loose"/>.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="expression">Expression to verify.</param><param name="times">The number of times a method is allowed to be called.</param><typeparam name="TResult">Type of return value from the expression.</typeparam>
        </member>
        <member name="M:Moq.Mock`1.Verify``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.String)">
            <summary>
			Verifies that a specific invocation matching the given
			expression was performed on the mock, specifying a failure
			error message.
		</summary><example group="verification">
			This example assumes that the mock has been used,
			and later we want to verify that a given invocation
			with specific parameters was performed:
			<code>
				var mock = new Mock&lt;IWarehouse&gt;();
				// exercise mock
				//...
				// Will throw if the test code didn't call HasInventory.
				mock.Verify(warehouse =&gt; warehouse.HasInventory(TALISKER, 50), "When filling orders, inventory has to be checked");
			</code>
		</example><exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception><param name="expression">Expression to verify.</param><param name="failMessage">Message to show if verification fails.</param><typeparam name="TResult">Type of return value from the expression.</typeparam>
        </member>
        <member name="M:Moq.Mock`1.Verify``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},Moq.Times,System.String)">
            <summary>
			Verifies that a specific invocation matching the given
			expression was performed on the mock, specifying a failure
			error message.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="expression">Expression to verify.</param><param name="times">The number of times a method is allowed to be called.</param><param name="failMessage">Message to show if verification fails.</param><typeparam name="TResult">Type of return value from the expression.</typeparam>
        </member>
        <member name="M:Moq.Mock`1.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
			Verifies that a property was read on the mock.
		</summary><example group="verification">
			This example assumes that the mock has been used,
			and later we want to verify that a given property
			was retrieved from it:
			<code>
				var mock = new Mock&lt;IWarehouse&gt;();
				// exercise mock
				//...
				// Will throw if the test code didn't retrieve the IsClosed property.
				mock.VerifyGet(warehouse =&gt; warehouse.IsClosed);
			</code>
		</example><exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception><param name="expression">Expression to verify.</param><typeparam name="TProperty">
			Type of the property to verify. Typically omitted as it can
			be inferred from the expression's return type.
		</typeparam>
        </member>
        <member name="M:Moq.Mock`1.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},Moq.Times)">
            <summary>
			Verifies that a property was read on the mock.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="times">The number of times a method is allowed to be called.</param><param name="expression">Expression to verify.</param><typeparam name="TProperty">
			Type of the property to verify. Typically omitted as it can
			be inferred from the expression's return type.
		</typeparam>
        </member>
        <member name="M:Moq.Mock`1.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{Moq.Times})">
            <summary>
			Verifies that a property was read on the mock.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="times">The number of times a method is allowed to be called.</param><param name="expression">Expression to verify.</param><typeparam name="TProperty">
			Type of the property to verify. Typically omitted as it can
			be inferred from the expression's return type.
		</typeparam>
        </member>
        <member name="M:Moq.Mock`1.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.String)">
            <summary>
			Verifies that a property was read on the mock, specifying a failure
			error message.
		</summary><example group="verification">
			This example assumes that the mock has been used,
			and later we want to verify that a given property
			was retrieved from it:
			<code>
				var mock = new Mock&lt;IWarehouse&gt;();
				// exercise mock
				//...
				// Will throw if the test code didn't retrieve the IsClosed property.
				mock.VerifyGet(warehouse =&gt; warehouse.IsClosed);
			</code>
		</example><exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception><param name="expression">Expression to verify.</param><param name="failMessage">Message to show if verification fails.</param><typeparam name="TProperty">
			Type of the property to verify. Typically omitted as it can
			be inferred from the expression's return type.
		</typeparam>
        </member>
        <member name="M:Moq.Mock`1.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},Moq.Times,System.String)">
            <summary>
			Verifies that a property was read on the mock, specifying a failure
			error message.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="times">The number of times a method is allowed to be called.</param><param name="expression">Expression to verify.</param><param name="failMessage">Message to show if verification fails.</param><typeparam name="TProperty">
			Type of the property to verify. Typically omitted as it can
			be inferred from the expression's return type.
		</typeparam>
        </member>
        <member name="M:Moq.Mock`1.VerifyGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{Moq.Times},System.String)">
            <summary>
			Verifies that a property was read on the mock, specifying a failure
			error message.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="times">The number of times a method is allowed to be called.</param><param name="expression">Expression to verify.</param><param name="failMessage">Message to show if verification fails.</param><typeparam name="TProperty">
			Type of the property to verify. Typically omitted as it can
			be inferred from the expression's return type.
		</typeparam>
        </member>
        <member name="M:Moq.Mock`1.VerifySet(System.Action{`0})">
            <summary>
			Verifies that a property was set on the mock.
		</summary><example group="verification">
			This example assumes that the mock has been used,
			and later we want to verify that a given property
			was set on it:
			<code>
				var mock = new Mock&lt;IWarehouse&gt;();
				// exercise mock
				//...
				// Will throw if the test code didn't set the IsClosed property.
				mock.VerifySet(warehouse =&gt; warehouse.IsClosed = true);
			</code>
		</example><exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception><param name="setterExpression">Expression to verify.</param>
        </member>
        <member name="M:Moq.Mock`1.VerifySet(System.Action{`0},Moq.Times)">
            <summary>
			Verifies that a property was set on the mock.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="times">The number of times a method is allowed to be called.</param><param name="setterExpression">Expression to verify.</param>
        </member>
        <member name="M:Moq.Mock`1.VerifySet(System.Action{`0},System.Func{Moq.Times})">
            <summary>
			Verifies that a property was set on the mock.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="times">The number of times a method is allowed to be called.</param><param name="setterExpression">Expression to verify.</param>
        </member>
        <member name="M:Moq.Mock`1.VerifySet(System.Action{`0},System.String)">
            <summary>
			Verifies that a property was set on the mock, specifying
			a failure message.
		</summary><example group="verification">
			This example assumes that the mock has been used,
			and later we want to verify that a given property
			was set on it:
			<code>
				var mock = new Mock&lt;IWarehouse&gt;();
				// exercise mock
				//...
				// Will throw if the test code didn't set the IsClosed property.
				mock.VerifySet(warehouse =&gt; warehouse.IsClosed = true, "Warehouse should always be closed after the action");
			</code>
		</example><exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception><param name="setterExpression">Expression to verify.</param><param name="failMessage">Message to show if verification fails.</param>
        </member>
        <member name="M:Moq.Mock`1.VerifySet(System.Action{`0},Moq.Times,System.String)">
            <summary>
			Verifies that a property was set on the mock, specifying
			a failure message.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="times">The number of times a method is allowed to be called.</param><param name="setterExpression">Expression to verify.</param><param name="failMessage">Message to show if verification fails.</param>
        </member>
        <member name="M:Moq.Mock`1.VerifySet(System.Action{`0},System.Func{Moq.Times},System.String)">
            <summary>
			Verifies that a property was set on the mock, specifying
			a failure message.
		</summary><exception cref="T:Moq.MockException">
			The invocation was not call the times specified by
			<paramref name="times"/>.
		</exception><param name="times">The number of times a method is allowed to be called.</param><param name="setterExpression">Expression to verify.</param><param name="failMessage">Message to show if verification fails.</param>
        </member>
        <member name="M:Moq.Mock`1.Raise(System.Action{`0},System.EventArgs)">
            <summary>
			Raises the event referenced in <paramref name="eventExpression"/> using
			the given <paramref name="args"/> argument.
		</summary><exception cref="T:System.ArgumentException">
			The <paramref name="args"/> argument is
			invalid for the target event invocation, or the <paramref name="eventExpression"/> is
			not an event attach or detach expression.
		</exception><example>
			The following example shows how to raise a <see cref="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged"/> event:
			<code>
				var mock = new Mock&lt;IViewModel&gt;();

				mock.Raise(x =&gt; x.PropertyChanged -= null, new PropertyChangedEventArgs("Name"));
			</code>
		</example><example>
			This example shows how to invoke an event with a custom event arguments
			class in a view that will cause its corresponding presenter to
			react by changing its state:
			<code>
				var mockView = new Mock&lt;IOrdersView&gt;();
				var presenter = new OrdersPresenter(mockView.Object);

				// Check that the presenter has no selection by default
				Assert.Null(presenter.SelectedOrder);

				// Raise the event with a specific arguments data
				mockView.Raise(v =&gt; v.SelectionChanged += null, new OrderEventArgs { Order = new Order("moq", 500) });

				// Now the presenter reacted to the event, and we have a selected order
				Assert.NotNull(presenter.SelectedOrder);
				Assert.Equal("moq", presenter.SelectedOrder.ProductName);
			</code>
		</example>
        </member>
        <member name="M:Moq.Mock`1.Raise(System.Action{`0},System.Object[])">
            <summary>
			Raises the event referenced in <paramref name="eventExpression"/> using
			the given <paramref name="args"/> argument for a non-EventHandler typed event.
		</summary><exception cref="T:System.ArgumentException">
			The <paramref name="args"/> arguments are
			invalid for the target event invocation, or the <paramref name="eventExpression"/> is
			not an event attach or detach expression.
		</exception><example>
			The following example shows how to raise a custom event that does not adhere to 
			the standard <c>EventHandler</c>:
			<code>
				var mock = new Mock&lt;IViewModel&gt;();

				mock.Raise(x =&gt; x.MyEvent -= null, "Name", bool, 25);
			</code>
		</example>
        </member>
        <member name="M:Moq.Mock`1.Expect(System.Linq.Expressions.Expression{System.Action{`0}})">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.Expect``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.ExpectGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.ExpectSet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="M:Moq.Mock`1.ExpectSet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},``0)">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="P:Moq.Mock`1.Object">
            <summary>
			Exposes the mocked object instance.
		</summary>
        </member>
        <member name="P:Moq.Mock`1.Name">
            <summary>
			Allows naming of your mocks, so they can be easily identified in error messages (e.g. from failed assertions).
		</summary>
        </member>
        <member name="P:Moq.Mock`1.DelegateInterfaceMethod">
            <inheritdoc />
        </member>
        <member name="T:Moq.Language.ISetupConditionResult`1">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="M:Moq.Language.ISetupConditionResult`1.Setup(System.Linq.Expressions.Expression{System.Action{`0}})">
            <summary>
            The expectation will be considered only in the former condition.
            </summary>
            <param name="expression"></param>
            <returns></returns>
        </member>
        <member name="M:Moq.Language.ISetupConditionResult`1.Setup``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            The expectation will be considered only in the former condition.
            </summary>
            <typeparam name="TResult"></typeparam>
            <param name="expression"></param>
            <returns></returns>
        </member>
        <member name="M:Moq.Language.ISetupConditionResult`1.SetupGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Setups the get.
            </summary>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="expression">The expression.</param>
            <returns></returns>
        </member>
        <member name="M:Moq.Language.ISetupConditionResult`1.SetupSet``1(System.Action{`0})">
            <summary>
            Setups the set.
            </summary>
            <typeparam name="TProperty">The type of the property.</typeparam>
            <param name="setterExpression">The setter expression.</param>
            <returns></returns>
        </member>
        <member name="M:Moq.Language.ISetupConditionResult`1.SetupSet(System.Action{`0})">
            <summary>
            Setups the set.
            </summary>
            <param name="setterExpression">The setter expression.</param>
            <returns></returns>
        </member>
        <member name="T:Moq.DefaultValue">
            <summary>
            Determines the way default values are generated 
            calculated for loose mocks.
            </summary>
        </member>
        <member name="F:Moq.DefaultValue.Empty">
            <summary>
            Default behavior, which generates empty values for 
            value types (i.e. default(int)), empty array and 
            enumerables, and nulls for all other reference types.
            </summary>
        </member>
        <member name="F:Moq.DefaultValue.Mock">
            <summary>
            Whenever the default value generated by <see cref="F:Moq.DefaultValue.Empty"/> 
            is null, replaces this value with a mock (if the type 
            can be mocked). 
            </summary>
            <remarks>
            For sealed classes, a null value will be generated.
            </remarks>
        </member>
        <member name="T:Moq.EmptyDefaultValueProvider">
            <summary>
            A <see cref="T:Moq.IDefaultValueProvider"/> that returns an empty default value 
            for invocations that do not have setups or return values, with loose mocks.
            This is the default behavior for a mock.
            </summary>
        </member>
        <member name="T:Moq.IDefaultValueProvider">
            <summary>
			Interface to be implemented by classes that determine the
			default value of non-expected invocations.
		</summary>
        </member>
        <member name="M:Moq.IDefaultValueProvider.DefineDefault``1(``0)">
            <summary>
			Defines the default value to return in all the methods returning <typeparamref name="T"/>.
		</summary><typeparam name="T">The type of the return value.</typeparam><param name="value">The value to set as default.</param>
        </member>
        <member name="M:Moq.IDefaultValueProvider.ProvideDefault(System.Reflection.MethodInfo)">
            <summary>
			Provides a value for the given member and arguments.
		</summary><param name="member">
			The member to provide a default	value for.
		</param>
        </member>
        <member name="T:Moq.Evaluator">
            <summary>
            Provides partial evaluation of subtrees, whenever they can be evaluated locally.
            </summary>
            <author>Matt Warren: http://blogs.msdn.com/mattwar</author>
            <contributor>Documented by InSTEDD: http://www.instedd.org</contributor>
        </member>
        <member name="M:Moq.Evaluator.PartialEval(System.Linq.Expressions.Expression,System.Func{System.Linq.Expressions.Expression,System.Boolean})">
            <summary>
            Performs evaluation and replacement of independent sub-trees
            </summary>
            <param name="expression">The root of the expression tree.</param>
            <param name="fnCanBeEvaluated">A function that decides whether a given expression
            node can be part of the local function.</param>
            <returns>A new tree with sub-trees evaluated and replaced.</returns>
        </member>
        <member name="M:Moq.Evaluator.PartialEval(System.Linq.Expressions.Expression)">
            <summary>
            Performs evaluation and replacement of independent sub-trees
            </summary>
            <param name="expression">The root of the expression tree.</param>
            <returns>A new tree with sub-trees evaluated and replaced.</returns>
        </member>
        <member name="T:Moq.Evaluator.SubtreeEvaluator">
            <summary>
            Evaluates and replaces sub-trees when first candidate is reached (top-down)
            </summary>
        </member>
        <member name="T:Moq.Evaluator.Nominator">
            <summary>
            Performs bottom-up analysis to determine which nodes can possibly
            be part of an evaluated sub-tree.
            </summary>
        </member>
        <member name="M:Moq.ExpressionExtensions.ToLambda(System.Linq.Expressions.Expression)">
            <summary>
            Casts the expression to a lambda expression, removing 
            a cast if there's any.
            </summary>
        </member>
        <member name="M:Moq.ExpressionExtensions.ToMethodCall(System.Linq.Expressions.LambdaExpression)">
            <summary>
            Casts the body of the lambda expression to a <see cref="T:System.Linq.Expressions.MethodCallExpression"/>.
            </summary>
            <exception cref="T:System.ArgumentException">If the body is not a method call.</exception>
        </member>
        <member name="M:Moq.ExpressionExtensions.ToPropertyInfo(System.Linq.Expressions.LambdaExpression)">
            <summary>
            Converts the body of the lambda expression into the <see cref="T:System.Reflection.PropertyInfo"/> referenced by it.
            </summary>
        </member>
        <member name="M:Moq.ExpressionExtensions.IsProperty(System.Linq.Expressions.LambdaExpression)">
            <summary>
            Checks whether the body of the lambda expression is a property access.
            </summary>
        </member>
        <member name="M:Moq.ExpressionExtensions.IsProperty(System.Linq.Expressions.Expression)">
            <summary>
            Checks whether the expression is a property access.
            </summary>
        </member>
        <member name="M:Moq.ExpressionExtensions.IsPropertyIndexer(System.Linq.Expressions.LambdaExpression)">
            <summary>
            Checks whether the body of the lambda expression is a property indexer, which is true 
            when the expression is an <see cref="T:System.Linq.Expressions.MethodCallExpression"/> whose 
            <see cref="P:System.Linq.Expressions.MethodCallExpression.Method"/> has <see cref="P:System.Reflection.MethodBase.IsSpecialName"/> 
            equal to <see langword="true"/>.
            </summary>
        </member>
        <member name="M:Moq.ExpressionExtensions.IsPropertyIndexer(System.Linq.Expressions.Expression)">
            <summary>
            Checks whether the expression is a property indexer, which is true 
            when the expression is an <see cref="T:System.Linq.Expressions.MethodCallExpression"/> whose 
            <see cref="P:System.Linq.Expressions.MethodCallExpression.Method"/> has <see cref="P:System.Reflection.MethodBase.IsSpecialName"/> 
            equal to <see langword="true"/>.
            </summary>
        </member>
        <member name="M:Moq.ExpressionExtensions.CastTo``1(System.Linq.Expressions.Expression)">
            <summary>
            Creates an expression that casts the given expression to the <typeparamref name="T"/> 
            type.
            </summary>
        </member>
        <member name="M:Moq.ExpressionExtensions.ToStringFixed(System.Linq.Expressions.Expression)">
            <devdoc>
            TODO: remove this code when https://connect.microsoft.com/VisualStudio/feedback/ViewFeedback.aspx?FeedbackID=331583 
            is fixed.
            </devdoc>
        </member>
        <member name="M:Moq.ExpressionExtensions.GetCallInfo(System.Linq.Expressions.LambdaExpression,Moq.Mock)">
            <summary>
            Extracts, into a common form, information from a <see cref="T:System.Linq.Expressions.LambdaExpression"/>
            around either a <see cref="T:System.Linq.Expressions.MethodCallExpression"/> (for a normal method call)
            or a <see cref="T:System.Linq.Expressions.InvocationExpression"/> (for a delegate invocation).
            </summary>
        </member>
        <member name="T:Moq.ExpressionStringBuilder">
            <summary>
            The intention of <see cref="T:Moq.ExpressionStringBuilder"/> is to create a more readable 
            string representation for the failure message.
            </summary>
        </member>
        <member name="M:Moq.Extensions.IsDelegate(System.Type)">
            <summary>
            Tests if a type is a delegate type (subclasses <see cref="T:System.Delegate"/>).
            </summary>
        </member>
        <member name="T:Moq.FluentMockContext">
            <summary>
            Tracks the current mock and interception context.
            </summary>
        </member>
        <member name="P:Moq.FluentMockContext.IsActive">
            <summary>
            Having an active fluent mock context means that the invocation 
            is being performed in "trial" mode, just to gather the 
            target method and arguments that need to be matched later 
            when the actual invocation is made.
            </summary>
        </member>
        <member name="M:Moq.Guard.NotNull``1(System.Linq.Expressions.Expression{System.Func{``0}},``0)">
            <summary>
            Ensures the given <paramref name="value"/> is not null.
            Throws <see cref="T:System.ArgumentNullException"/> otherwise.
            </summary>
        </member>
        <member name="M:Moq.Guard.NotNullOrEmpty(System.Linq.Expressions.Expression{System.Func{System.String}},System.String)">
            <summary>
            Ensures the given string <paramref name="value"/> is not null or empty.
            Throws <see cref="T:System.ArgumentNullException"/> in the first case, or 
            <see cref="T:System.ArgumentException"/> in the latter.
            </summary>
        </member>
        <member name="M:Moq.Guard.NotOutOfRangeInclusive``1(System.Linq.Expressions.Expression{System.Func{``0}},``0,``0,``0)">
            <summary>
            Checks an argument to ensure it is in the specified range including the edges.
            </summary>
            <typeparam name="T">Type of the argument to check, it must be an <see cref="T:System.IComparable"/> type.
            </typeparam>
            <param name="reference">The expression containing the name of the argument.</param>
            <param name="value">The argument value to check.</param>
            <param name="from">The minimun allowed value for the argument.</param>
            <param name="to">The maximun allowed value for the argument.</param>
        </member>
        <member name="M:Moq.Guard.NotOutOfRangeExclusive``1(System.Linq.Expressions.Expression{System.Func{``0}},``0,``0,``0)">
            <summary>
            Checks an argument to ensure it is in the specified range excluding the edges.
            </summary>
            <typeparam name="T">Type of the argument to check, it must be an <see cref="T:System.IComparable"/> type.
            </typeparam>
            <param name="reference">The expression containing the name of the argument.</param>
            <param name="value">The argument value to check.</param>
            <param name="from">The minimun allowed value for the argument.</param>
            <param name="to">The maximun allowed value for the argument.</param>
        </member>
        <member name="M:Moq.IInterceptStrategy.HandleIntercept(Moq.Proxy.ICallContext,Moq.InterceptorContext,Moq.CurrentInterceptContext)">
            <summary>
            Handle interception
            </summary>
            <param name="invocation">the current invocation context</param>
            <param name="ctx">shared data for the interceptor as a whole</param>
            <param name="localCtx">shared data among the strategies during a single interception</param>
            <returns>InterceptionAction.Continue if further interception has to be processed, otherwise InterceptionAction.Stop</returns>
        </member>
        <member name="T:Moq.IMocked`1">
            <summary>
            Implemented by all generated mock object instances.
            </summary>
        </member>
        <member name="T:Moq.IMocked">
            <summary>
            Implemented by all generated mock object instances.
            </summary>
        </member>
        <member name="P:Moq.IMocked.Mock">
            <summary>
            Reference the Mock that contains this as the <c>mock.Object</c> value.
            </summary>
        </member>
        <member name="P:Moq.IMocked`1.Mock">
            <summary>
            Reference the Mock that contains this as the <c>mock.Object</c> value.
            </summary>
        </member>
        <member name="T:Moq.Interceptor">
            <summary>
            Implements the actual interception and method invocation for 
            all mocks.
            </summary>
        </member>
        <member name="M:Moq.AddActualInvocation.GetEventFromName(System.String)">
            <summary>
            Get an eventInfo for a given event name.  Search type ancestors depth first if necessary.
            </summary>
            <param name="eventName">Name of the event, with the set_ or get_ prefix already removed</param>
        </member>
        <member name="M:Moq.AddActualInvocation.GetNonPublicEventFromName(System.String)">
            <summary>
            Get an eventInfo for a given event name.  Search type ancestors depth first if necessary.
            Searches also in non public events.
            </summary>
            <param name="eventName">Name of the event, with the set_ or get_ prefix already removed</param>
        </member>
        <member name="M:Moq.AddActualInvocation.GetAncestorTypes(System.Type)">
            <summary>
            Given a type return all of its ancestors, both types and interfaces.
            </summary>
            <param name="initialType">The type to find immediate ancestors of</param>
        </member>
        <member name="T:Moq.It">
            <summary>
			Allows the specification of a matching condition for an
			argument in a method invocation, rather than a specific
			argument value. "It" refers to the argument being matched.
		</summary><remarks>
			This class allows the setup to match a method invocation
			with an arbitrary value, with a value in a specified range, or
			even one that matches a given predicate.
		</remarks>
        </member>
        <member name="M:Moq.It.IsAny``1">
            <summary>
			Matches any value of the given <typeparamref name="TValue"/> type.
		</summary><remarks>
			Typically used when the actual argument value for a method
			call is not relevant.
		</remarks><example>
			<code>
				// Throws an exception for a call to Remove with any string value.
				mock.Setup(x =&gt; x.Remove(It.IsAny&lt;string&gt;())).Throws(new InvalidOperationException());
			</code>
		</example><typeparam name="TValue">Type of the value.</typeparam>
        </member>
        <member name="M:Moq.It.IsNotNull``1">
            <summary>
         Matches any value of the given <typeparamref name="TValue"/> type, except null.
      </summary><typeparam name="TValue">Type of the value.</typeparam>
        </member>
        <member name="M:Moq.It.Is``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
			Matches any value that satisfies the given predicate.
		</summary><typeparam name="TValue">Type of the argument to check.</typeparam><param name="match">The predicate used to match the method argument.</param><remarks>
			Allows the specification of a predicate to perform matching
			of method call arguments.
		</remarks><example>
			This example shows how to return the value <c>1</c> whenever the argument to the
			<c>Do</c> method is an even number.
			<code>
				mock.Setup(x =&gt; x.Do(It.Is&lt;int&gt;(i =&gt; i % 2 == 0)))
				.Returns(1);
			</code>
			This example shows how to throw an exception if the argument to the
			method is a negative number:
			<code>
				mock.Setup(x =&gt; x.GetUser(It.Is&lt;int&gt;(i =&gt; i &lt; 0)))
				.Throws(new ArgumentException());
			</code>
		</example>
        </member>
        <member name="M:Moq.It.IsInRange``1(``0,``0,Moq.Range)">
            <summary>
			Matches any value that is in the range specified.
		</summary><typeparam name="TValue">Type of the argument to check.</typeparam><param name="from">The lower bound of the range.</param><param name="to">The upper bound of the range.</param><param name="rangeKind">
			The kind of range. See <see cref="T:Moq.Range"/>.
		</param><example>
			The following example shows how to expect a method call
			with an integer argument within the 0..100 range.
			<code>
				mock.Setup(x =&gt; x.HasInventory(
				It.IsAny&lt;string&gt;(),
				It.IsInRange(0, 100, Range.Inclusive)))
				.Returns(false);
			</code>
		</example>
        </member>
        <member name="M:Moq.It.IsIn``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
			Matches any value that is present in the sequence specified.
		</summary><typeparam name="TValue">Type of the argument to check.</typeparam><param name="items">The sequence of possible values.</param><example>
			The following example shows how to expect a method call
			with an integer argument with value from a list.
			<code>
				var values = new List&lt;int&gt; { 1, 2, 3 };
			
				mock.Setup(x =&gt; x.HasInventory(
				It.IsAny&lt;string&gt;(),
				It.IsIn(values)))
				.Returns(false);
			</code>
		</example>
        </member>
        <member name="M:Moq.It.IsIn``1(``0[])">
            <summary>
			Matches any value that is present in the sequence specified.
		</summary><typeparam name="TValue">Type of the argument to check.</typeparam><param name="items">The sequence of possible values.</param><example>
			The following example shows how to expect a method call
			with an integer argument with a value of 1, 2, or 3.
			<code>
				mock.Setup(x =&gt; x.HasInventory(
				It.IsAny&lt;string&gt;(),
				It.IsIn(1, 2, 3)))
				.Returns(false);
			</code>
		</example>
        </member>
        <member name="M:Moq.It.IsNotIn``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
			Matches any value that is not found in the sequence specified.
		</summary><typeparam name="TValue">Type of the argument to check.</typeparam><param name="items">The sequence of disallowed values.</param><example>
			The following example shows how to expect a method call
			with an integer argument with value not found from a list.
			<code>
				var values = new List&lt;int&gt; { 1, 2, 3 };
				
				mock.Setup(x =&gt; x.HasInventory(
				It.IsAny&lt;string&gt;(),
				It.IsNotIn(values)))
				.Returns(false);
			</code>
		</example>
        </member>
        <member name="M:Moq.It.IsNotIn``1(``0[])">
            <summary>
			Matches any value that is not found in the sequence specified.
		</summary><typeparam name="TValue">Type of the argument to check.</typeparam><param name="items">The sequence of disallowed values.</param><example>
			The following example shows how to expect a method call
			with an integer argument of any value except 1, 2, or 3.
			<code>
				mock.Setup(x =&gt; x.HasInventory(
				It.IsAny&lt;string&gt;(),
				It.IsNotIn(1, 2, 3)))
				.Returns(false);
			</code>
		</example>
        </member>
        <member name="M:Moq.It.IsRegex(System.String)">
            <summary>
			Matches a string argument if it matches the given regular expression pattern.
		</summary><param name="regex">The pattern to use to match the string argument value.</param><example>
			The following example shows how to expect a call to a method where the
			string argument matches the given regular expression:
			<code>
				mock.Setup(x =&gt; x.Check(It.IsRegex("[a-z]+"))).Returns(1);
			</code>
		</example>
        </member>
        <member name="M:Moq.It.IsRegex(System.String,System.Text.RegularExpressions.RegexOptions)">
            <summary>
			Matches a string argument if it matches the given regular expression pattern.
		</summary><param name="regex">The pattern to use to match the string argument value.</param><param name="options">The options used to interpret the pattern.</param><example>
			The following example shows how to expect a call to a method where the
			string argument matches the given regular expression, in a case insensitive way:
			<code>
				mock.Setup(x =&gt; x.Check(It.IsRegex("[a-z]+", RegexOptions.IgnoreCase))).Returns(1);
			</code>
		</example>
        </member>
        <member name="T:Moq.Language.Flow.IReturnsResult`1">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.ICallback">
            <summary>
            Defines the <c>Callback</c> verb and overloads.
            </summary>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``2(System.Action{``0,``1})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2) =&gt; Console.WriteLine(arg1 + arg2));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``3(System.Action{``0,``1,``2})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3) =&gt; Console.WriteLine(arg1 + arg2 + arg3));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``4(System.Action{``0,``1,``2,``3})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``5(System.Action{``0,``1,``2,``3,``4})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``6(System.Action{``0,``1,``2,``3,``4,``5})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``7(System.Action{``0,``1,``2,``3,``4,``5,``6})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``8(System.Action{``0,``1,``2,``3,``4,``5,``6,``7})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``9(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``10(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``11(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``12(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``13(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``14(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13, string arg14) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``15(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13, string arg14, string arg15) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14 + arg15));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``16(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument of the invoked method.</typeparam>
            <typeparam name="T16">The type of the sixteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.ICallbackResult"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13, string arg14, string arg15, string arg16) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14 + arg15 + arg16));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback(System.Action)">
            <summary>
            Specifies a callback to invoke when the method is called.
            </summary>
            <param name="action">The callback method to invoke.</param>
            <example>
            The following example specifies a callback to set a boolean 
            value that can be used later:
            <code>
            var called = false;
            mock.Setup(x => x.Execute())
                .Callback(() => called = true);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback.Callback``1(System.Action{``0})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T">The argument type of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <example>
            Invokes the given callback with the concrete invocation argument value. 
            <para>
            Notice how the specific string argument is retrieved by simply declaring 
            it as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(It.IsAny&lt;string&gt;()))
                .Callback((string command) => Console.WriteLine(command));
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.IOccurrence">
            <summary>
            Defines occurrence members to constraint setups.
            </summary>
        </member>
        <member name="M:Moq.Language.IOccurrence.AtMostOnce">
            <summary>
            The expected invocation can happen at most once.
            </summary>
            <example>
            <code>
            var mock = new Mock&lt;ICommand&gt;();
            mock.Setup(foo => foo.Execute("ping"))
                .AtMostOnce();
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IOccurrence.AtMost(System.Int32)">
            <summary>
            The expected invocation can happen at most specified number of times.
            </summary>
            <param name="callCount">The number of times to accept calls.</param>
            <example>
            <code>
            var mock = new Mock&lt;ICommand&gt;();
            mock.Setup(foo => foo.Execute("ping"))
                .AtMost( 5 );
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.IRaise`1">
            <summary>
            Defines the <c>Raises</c> verb.
            </summary>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)">
            <summary>
            Specifies the event that will be raised 
            when the setup is met.
            </summary>
            <param name="eventExpression">An expression that represents an event attach or detach action.</param>
            <param name="args">The event arguments to pass for the raised event.</param>
            <example>
            The following example shows how to raise an event when 
            the setup is met:
            <code>
            var mock = new Mock&lt;IContainer&gt;();
            
            mock.Setup(add => add.Add(It.IsAny&lt;string&gt;(), It.IsAny&lt;object&gt;()))
                .Raises(add => add.Added += null, EventArgs.Empty);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.Func{System.EventArgs})">
            <summary>
            Specifies the event that will be raised 
            when the setup is matched.
            </summary>
            <param name="eventExpression">An expression that represents an event attach or detach action.</param>
            <param name="func">A function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.Object[])">
            <summary>
            Specifies the custom event that will be raised 
            when the setup is matched.
            </summary>
            <param name="eventExpression">An expression that represents an event attach or detach action.</param>
            <param name="args">The arguments to pass to the custom delegate (non EventHandler-compatible).</param>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``1(System.Action{`0},System.Func{``0,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``2(System.Action{`0},System.Func{``0,``1,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``3(System.Action{`0},System.Func{``0,``1,``2,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``4(System.Action{`0},System.Func{``0,``1,``2,``3,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``5(System.Action{`0},System.Func{``0,``1,``2,``3,``4,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``6(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``7(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``8(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``9(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the nineth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``10(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the nineth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``11(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the nineth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <typeparam name="T11">The type of the eleventh argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``12(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the nineth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <typeparam name="T11">The type of the eleventh argument received by the expected invocation.</typeparam>
            <typeparam name="T12">The type of the twelfth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``13(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the nineth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <typeparam name="T11">The type of the eleventh argument received by the expected invocation.</typeparam>
            <typeparam name="T12">The type of the twelfth argument received by the expected invocation.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``14(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the nineth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <typeparam name="T11">The type of the eleventh argument received by the expected invocation.</typeparam>
            <typeparam name="T12">The type of the twelfth argument received by the expected invocation.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument received by the expected invocation.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``15(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the nineth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <typeparam name="T11">The type of the eleventh argument received by the expected invocation.</typeparam>
            <typeparam name="T12">The type of the twelfth argument received by the expected invocation.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument received by the expected invocation.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument received by the expected invocation.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="M:Moq.Language.IRaise`1.Raises``16(System.Action{`0},System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15,System.EventArgs})">
            <summary>
            Specifies the event that will be raised when the setup is matched.
            </summary>
            <param name="eventExpression">The expression that represents an event attach or detach action.</param>
            <param name="func">The function that will build the <see cref="T:System.EventArgs"/> 
            to pass when raising the event.</param>
            <typeparam name="T1">The type of the first argument received by the expected invocation.</typeparam>
            <typeparam name="T2">The type of the second argument received by the expected invocation.</typeparam>
            <typeparam name="T3">The type of the third argument received by the expected invocation.</typeparam>
            <typeparam name="T4">The type of the fourth argument received by the expected invocation.</typeparam>
            <typeparam name="T5">The type of the fifth argument received by the expected invocation.</typeparam>
            <typeparam name="T6">The type of the sixth argument received by the expected invocation.</typeparam>
            <typeparam name="T7">The type of the seventh argument received by the expected invocation.</typeparam>
            <typeparam name="T8">The type of the eighth argument received by the expected invocation.</typeparam>
            <typeparam name="T9">The type of the nineth argument received by the expected invocation.</typeparam>
            <typeparam name="T10">The type of the tenth argument received by the expected invocation.</typeparam>
            <typeparam name="T11">The type of the eleventh argument received by the expected invocation.</typeparam>
            <typeparam name="T12">The type of the twelfth argument received by the expected invocation.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument received by the expected invocation.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument received by the expected invocation.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument received by the expected invocation.</typeparam>
            <typeparam name="T16">The type of the sixteenth argument received by the expected invocation.</typeparam>
            <seealso cref="M:Moq.Language.IRaise`1.Raises(System.Action{`0},System.EventArgs)"/>
        </member>
        <member name="T:Moq.Language.IVerifies">
            <summary>
            Defines the <c>Verifiable</c> verb.
            </summary>
        </member>
        <member name="M:Moq.Language.IVerifies.Verifiable">
            <summary>
            Marks the expectation as verifiable, meaning that a call 
            to <see cref="M:Moq.Mock.Verify"/> will check if this particular 
            expectation was met.
            </summary>
            <example>
            The following example marks the expectation as verifiable:
            <code>
            mock.Expect(x =&gt; x.Execute("ping"))
                .Returns(true)
                .Verifiable();
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IVerifies.Verifiable(System.String)">
            <summary>
            Marks the expectation as verifiable, meaning that a call 
            to <see cref="M:Moq.Mock.Verify"/> will check if this particular 
            expectation was met, and specifies a message for failures.
            </summary>
            <example>
            The following example marks the expectation as verifiable:
            <code>
            mock.Expect(x =&gt; x.Execute("ping"))
                .Returns(true)
                .Verifiable("Ping should be executed always!");
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.Flow.ISetup`1">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.Flow.ICallbackResult">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.IThrows">
            <summary>
            Defines the <c>Throws</c> verb.
            </summary>
        </member>
        <member name="M:Moq.Language.IThrows.Throws(System.Exception)">
            <summary>
            Specifies the exception to throw when the method is invoked.
            </summary>
            <param name="exception">Exception instance to throw.</param>
            <example>
            This example shows how to throw an exception when the method is 
            invoked with an empty string argument:
            <code>
            mock.Setup(x =&gt; x.Execute(""))
                .Throws(new ArgumentException());
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IThrows.Throws``1">
            <summary>
            Specifies the type of exception to throw when the method is invoked.
            </summary>
            <typeparam name="TException">Type of exception to instantiate and throw when the setup is matched.</typeparam>
            <example>
            This example shows how to throw an exception when the method is 
            invoked with an empty string argument:
            <code>
            mock.Setup(x =&gt; x.Execute(""))
                .Throws&lt;ArgumentException&gt;();
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.Flow.IThrowsResult">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.Flow.ISetup`2">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.ICallback`2">
            <summary>
            Defines the <c>Callback</c> verb and overloads for callbacks on
            setups that return a value.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TResult">Type of the return value of the setup.</typeparam>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``2(System.Action{``0,``1})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2) =&gt; Console.WriteLine(arg1 + arg2));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``3(System.Action{``0,``1,``2})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3) =&gt; Console.WriteLine(arg1 + arg2 + arg3));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``4(System.Action{``0,``1,``2,``3})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``5(System.Action{``0,``1,``2,``3,``4})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``6(System.Action{``0,``1,``2,``3,``4,``5})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``7(System.Action{``0,``1,``2,``3,``4,``5,``6})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``8(System.Action{``0,``1,``2,``3,``4,``5,``6,``7})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``9(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``10(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``11(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``12(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``13(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12, arg13) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``14(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12, arg13, arg14) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``15(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12, arg13, arg14, arg15) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14 + arg15));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``16(System.Action{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original
            arguments.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument of the invoked method.</typeparam>
            <typeparam name="T16">The type of the sixteenth argument of the invoked method.</typeparam>
            <param name="action">The callback method to invoke.</param>
            <returns>A reference to <see cref="T:Moq.Language.Flow.IReturnsThrows`2"/> interface.</returns>
            <example>
            Invokes the given callback with the concrete invocation arguments values. 
            <para>
            Notice how the specific arguments are retrieved by simply declaring 
            them as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x =&gt; x.Execute(
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;(),
                                 It.IsAny&lt;string&gt;()))
                .Callback((arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12, arg13, arg14, arg15, arg16) =&gt; Console.WriteLine(arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14 + arg15 + arg16));
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback(System.Action)">
            <summary>
            Specifies a callback to invoke when the method is called.
            </summary>
            <param name="action">The callback method to invoke.</param>
            <example>
            The following example specifies a callback to set a boolean value that can be used later:
            <code>
            var called = false;
            mock.Setup(x => x.Execute())
                .Callback(() => called = true)
                .Returns(true);
            </code>
            Note that in the case of value-returning methods, after the <c>Callback</c>
            call you can still specify the return value.
            </example>
        </member>
        <member name="M:Moq.Language.ICallback`2.Callback``1(System.Action{``0})">
            <summary>
            Specifies a callback to invoke when the method is called that receives the original arguments.
            </summary>
            <typeparam name="T">The type of the argument of the invoked method.</typeparam>
            <param name="action">Callback method to invoke.</param>
            <example>
            Invokes the given callback with the concrete invocation argument value.
            <para>
            Notice how the specific string argument is retrieved by simply declaring
            it as part of the lambda expression for the callback:
            </para>
            <code>
            mock.Setup(x => x.Execute(It.IsAny&lt;string&gt;()))
                .Callback(command => Console.WriteLine(command))
                .Returns(true);
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.Flow.IReturnsThrows`2">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.IReturns`2">
            <summary>
            Defines the <c>Returns</c> verb.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TResult">Type of the return value from the expression.</typeparam>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``2(System.Func{``0,``1,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2) => arg1 + arg2);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``3(System.Func{``0,``1,``2,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3) => arg1 + arg2 + arg3);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``4(System.Func{``0,``1,``2,``3,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4) => arg1 + arg2 + arg3 + arg4);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``5(System.Func{``0,``1,``2,``3,``4,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5) => arg1 + arg2 + arg3 + arg4 + arg5);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``6(System.Func{``0,``1,``2,``3,``4,``5,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``7(System.Func{``0,``1,``2,``3,``4,``5,``6,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``8(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``9(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``10(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``11(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``12(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``13(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``14(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13, string arg14) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``15(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13, string arg14, string arg15) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14 + arg15);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``16(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,``14,``15,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T1">The type of the first argument of the invoked method.</typeparam>
            <typeparam name="T2">The type of the second argument of the invoked method.</typeparam>
            <typeparam name="T3">The type of the third argument of the invoked method.</typeparam>
            <typeparam name="T4">The type of the fourth argument of the invoked method.</typeparam>
            <typeparam name="T5">The type of the fifth argument of the invoked method.</typeparam>
            <typeparam name="T6">The type of the sixth argument of the invoked method.</typeparam>
            <typeparam name="T7">The type of the seventh argument of the invoked method.</typeparam>
            <typeparam name="T8">The type of the eighth argument of the invoked method.</typeparam>
            <typeparam name="T9">The type of the nineth argument of the invoked method.</typeparam>
            <typeparam name="T10">The type of the tenth argument of the invoked method.</typeparam>
            <typeparam name="T11">The type of the eleventh argument of the invoked method.</typeparam>
            <typeparam name="T12">The type of the twelfth argument of the invoked method.</typeparam>
            <typeparam name="T13">The type of the thirteenth argument of the invoked method.</typeparam>
            <typeparam name="T14">The type of the fourteenth argument of the invoked method.</typeparam>
            <typeparam name="T15">The type of the fifteenth argument of the invoked method.</typeparam>
            <typeparam name="T16">The type of the sixteenth argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <return>Returns a calculated value which is evaluated lazily at the time of the invocation.</return>
            <example>
            <para>
            The return value is calculated from the value of the actual method invocation arguments. 
            Notice how the arguments are retrieved by simply declaring them as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;(), 
                                 It.IsAny&lt;int&gt;()))
                .Returns((string arg1, string arg2, string arg3, string arg4, string arg5, string arg6, string arg7, string arg8, string arg9, string arg10, string arg11, string arg12, string arg13, string arg14, string arg15, string arg16) => arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8 + arg9 + arg10 + arg11 + arg12 + arg13 + arg14 + arg15 + arg16);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns(`1)">
            <summary>
            Specifies the value to return.
            </summary>
            <param name="value">The value to return, or <see langword="null"/>.</param>
            <example>
            Return a <c>true</c> value from the method call:
            <code>
            mock.Setup(x => x.Execute("ping"))
                .Returns(true);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns(System.Func{`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method.
            </summary>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <example group="returns">
            Return a calculated value when the method is called:
            <code>
            mock.Setup(x => x.Execute("ping"))
                .Returns(() => returnValues[0]);
            </code>
            The lambda expression to retrieve the return value is lazy-executed, 
            meaning that its value may change depending on the moment the method 
            is executed and the value the <c>returnValues</c> array has at 
            that moment.
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.Returns``1(System.Func{``0,`1})">
            <summary>
            Specifies a function that will calculate the value to return from the method, 
            retrieving the arguments for the invocation.
            </summary>
            <typeparam name="T">The type of the argument of the invoked method.</typeparam>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <example group="returns">
            Return a calculated value which is evaluated lazily at the time of the invocation.
            <para>
            The lookup list can change between invocations and the setup 
            will return different values accordingly. Also, notice how the specific 
            string argument is retrieved by simply declaring it as part of the lambda 
            expression:
            </para>
            <code>
            mock.Setup(x => x.Execute(It.IsAny&lt;string&gt;()))
                .Returns((string command) => returnValues[command]);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturns`2.CallBase">
            <summary>
            Calls the real method of the object and returns its return value.
            </summary>
            <returns>The value calculated by the real method of the object.</returns>
        </member>
        <member name="T:Moq.Language.Flow.ISetupGetter`2">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.ICallbackGetter`2">
            <summary>
            Defines the <c>Callback</c> verb for property getter setups.
            </summary>
            <seealso cref="M:Moq.Mock`1.SetupGet``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})"/>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TProperty">Type of the property.</typeparam>
        </member>
        <member name="M:Moq.Language.ICallbackGetter`2.Callback(System.Action)">
            <summary>
            Specifies a callback to invoke when the property is retrieved.
            </summary>
            <param name="action">Callback method to invoke.</param>
            <example>
            Invokes the given callback with the property value being set. 
            <code>
            mock.SetupGet(x => x.Suspended)
                .Callback(() => called = true)
                .Returns(true);
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.Flow.IReturnsThrowsGetter`2">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.IReturnsGetter`2">
            <summary>
            Defines the <c>Returns</c> verb for property get setups.
            </summary>
            <typeparam name="TMock">Mocked type.</typeparam>
            <typeparam name="TProperty">Type of the property.</typeparam>
        </member>
        <member name="M:Moq.Language.IReturnsGetter`2.Returns(`1)">
            <summary>
            Specifies the value to return.
            </summary>
            <param name="value">The value to return, or <see langword="null"/>.</param>
            <example>
            Return a <c>true</c> value from the property getter call:
            <code>
            mock.SetupGet(x => x.Suspended)
                .Returns(true);
            </code>
            </example>
        </member>
        <member name="M:Moq.Language.IReturnsGetter`2.Returns(System.Func{`1})">
            <summary>
            Specifies a function that will calculate the value to return for the property.
            </summary>
            <param name="valueFunction">The function that will calculate the return value.</param>
            <example>
            Return a calculated value when the property is retrieved:
            <code>
            mock.SetupGet(x => x.Suspended)
                .Returns(() => returnValues[0]);
            </code>
            The lambda expression to retrieve the return value is lazy-executed, 
            meaning that its value may change depending on the moment the property  
            is retrieved and the value the <c>returnValues</c> array has at 
            that moment.
            </example>
        </member>
        <member name="M:Moq.Language.IReturnsGetter`2.CallBase">
            <summary>
            Calls the real property of the object and returns its return value.
            </summary>
            <returns>The value calculated by the real property of the object.</returns>
        </member>
        <member name="T:Moq.Language.Flow.ISetupSetter`2">
            <summary>
            Implements the fluent API.
            </summary>
        </member>
        <member name="T:Moq.Language.ICallbackSetter`1">
            <summary>
            Defines the <c>Callback</c> verb for property setter setups.
            </summary>
            <typeparam name="TProperty">Type of the property.</typeparam>
        </member>
        <member name="M:Moq.Language.ICallbackSetter`1.Callback(System.Action{`0})">
            <summary>
            Specifies a callback to invoke when the property is set that receives the 
            property value being set.
            </summary>
            <param name="action">Callback method to invoke.</param>
            <example>
            Invokes the given callback with the property value being set. 
            <code>
            mock.SetupSet(x => x.Suspended)
                .Callback((bool state) => Console.WriteLine(state));
            </code>
            </example>
        </member>
        <member name="T:Moq.Language.ISetupSequentialResult`1">
            <summary>
            Language for ReturnSequence
            </summary>
        </member>
        <member name="M:Moq.Language.ISetupSequentialResult`1.Returns(`0)">
            <summary>
            Returns value
            </summary>
        </member>
        <member name="M:Moq.Language.ISetupSequentialResult`1.Throws(System.Exception)">
            <summary>
            Throws an exception
            </summary>
        </member>
        <member name="M:Moq.Language.ISetupSequentialResult`1.Throws``1">
            <summary>
            Throws an exception
            </summary>
        </member>
        <member name="M:Moq.Language.ISetupSequentialResult`1.CallBase">
            <summary>
            Calls original method
            </summary>
        </member>
        <member name="F:Moq.Linq.FluentMockVisitor.isFirst">
            <summary>
            The first method call or member access will be the 
            last segment of the expression (depth-first traversal), 
            which is the one we have to Setup rather than FluentMock.
            And the last one is the one we have to Mock.Get rather 
            than FluentMock.
            </summary>
        </member>
        <member name="T:Moq.Linq.MockQueryable`1">
            <summary>
            A default implementation of IQueryable for use with QueryProvider
            </summary>
        </member>
        <member name="M:Moq.Linq.MockQueryable`1.#ctor(System.Linq.Expressions.MethodCallExpression)">
            <summary>
            The <paramref name="underlyingCreateMocks"/> is a 
            static method that returns an IQueryable of Mocks of T which is used to 
            apply the linq specification to.
            </summary>
        </member>
        <member name="T:Moq.MockRepository">
            <summary>
            Utility repository class to use to construct multiple 
            mocks when consistent verification is 
            desired for all of them.
            </summary>
            <remarks>
            If multiple mocks will be created during a test, passing 
            the desired <see cref="T:Moq.MockBehavior"/> (if different than the 
            <see cref="F:Moq.MockBehavior.Default"/> or the one 
            passed to the repository constructor) and later verifying each
            mock can become repetitive and tedious.
            <para>
            This repository class helps in that scenario by providing a 
            simplified creation of multiple mocks with a default 
            <see cref="T:Moq.MockBehavior"/> (unless overriden by calling 
            <see cref="M:Moq.MockFactory.Create``1(Moq.MockBehavior)"/>) and posterior verification.
            </para>
            </remarks>
            <example group="repository">
            The following is a straightforward example on how to 
            create and automatically verify strict mocks using a <see cref="T:Moq.MockRepository"/>:
            <code>
            var repository = new MockRepository(MockBehavior.Strict);
            
            var foo = repository.Create&lt;IFoo&gt;();
            var bar = repository.Create&lt;IBar&gt;();
            
            // no need to call Verifiable() on the setup 
            // as we'll be validating all of them anyway.
            foo.Setup(f =&gt; f.Do());
            bar.Setup(b =&gt; b.Redo());
            
            // exercise the mocks here
            
            repository.VerifyAll(); 
            // At this point all setups are already checked 
            // and an optional MockException might be thrown. 
            // Note also that because the mocks are strict, any invocation 
            // that doesn't have a matching setup will also throw a MockException.
            </code>
            The following examples shows how to setup the repository 
            to create loose mocks and later verify only verifiable setups:
            <code>
            var repository = new MockRepository(MockBehavior.Loose);
            
            var foo = repository.Create&lt;IFoo&gt;();
            var bar = repository.Create&lt;IBar&gt;();
            
            // this setup will be verified when we verify the repository
            foo.Setup(f =&gt; f.Do()).Verifiable();
            	
            // this setup will NOT be verified 
            foo.Setup(f =&gt; f.Calculate());
            	
            // this setup will be verified when we verify the repository
            bar.Setup(b =&gt; b.Redo()).Verifiable();
            
            // exercise the mocks here
            // note that because the mocks are Loose, members 
            // called in the interfaces for which no matching
            // setups exist will NOT throw exceptions, 
            // and will rather return default values.
            
            repository.Verify();
            // At this point verifiable setups are already checked 
            // and an optional MockException might be thrown.
            </code>
            The following examples shows how to setup the repository with a 
            default strict behavior, overriding that default for a 
            specific mock:
            <code>
            var repository = new MockRepository(MockBehavior.Strict);
            
            // this particular one we want loose
            var foo = repository.Create&lt;IFoo&gt;(MockBehavior.Loose);
            var bar = repository.Create&lt;IBar&gt;();
            
            // specify setups
            
            // exercise the mocks here
            
            repository.Verify();
            </code>
            </example>
            <seealso cref="T:Moq.MockBehavior"/>
        </member>
        <member name="T:Moq.MockFactory">
            <summary>
            Utility factory class to use to construct multiple 
            mocks when consistent verification is 
            desired for all of them.
            </summary>
            <remarks>
            If multiple mocks will be created during a test, passing 
            the desired <see cref="T:Moq.MockBehavior"/> (if different than the 
            <see cref="F:Moq.MockBehavior.Default"/> or the one 
            passed to the factory constructor) and later verifying each
            mock can become repetitive and tedious.
            <para>
            This factory class helps in that scenario by providing a 
            simplified creation of multiple mocks with a default 
            <see cref="T:Moq.MockBehavior"/> (unless overriden by calling 
            <see cref="M:Moq.MockFactory.Create``1(Moq.MockBehavior)"/>) and posterior verification.
            </para>
            </remarks>
            <example group="factory">
            The following is a straightforward example on how to 
            create and automatically verify strict mocks using a <see cref="T:Moq.MockFactory"/>:
            <code>
            var factory = new MockFactory(MockBehavior.Strict);
            
            var foo = factory.Create&lt;IFoo&gt;();
            var bar = factory.Create&lt;IBar&gt;();
            
            // no need to call Verifiable() on the setup 
            // as we'll be validating all of them anyway.
            foo.Setup(f =&gt; f.Do());
            bar.Setup(b =&gt; b.Redo());
            
            // exercise the mocks here
            
            factory.VerifyAll(); 
            // At this point all setups are already checked 
            // and an optional MockException might be thrown. 
            // Note also that because the mocks are strict, any invocation 
            // that doesn't have a matching setup will also throw a MockException.
            </code>
            The following examples shows how to setup the factory 
            to create loose mocks and later verify only verifiable setups:
            <code>
            var factory = new MockFactory(MockBehavior.Loose);
            
            var foo = factory.Create&lt;IFoo&gt;();
            var bar = factory.Create&lt;IBar&gt;();
            
            // this setup will be verified when we verify the factory
            foo.Setup(f =&gt; f.Do()).Verifiable();
            	
            // this setup will NOT be verified 
            foo.Setup(f =&gt; f.Calculate());
            	
            // this setup will be verified when we verify the factory
            bar.Setup(b =&gt; b.Redo()).Verifiable();
            
            // exercise the mocks here
            // note that because the mocks are Loose, members 
            // called in the interfaces for which no matching
            // setups exist will NOT throw exceptions, 
            // and will rather return default values.
            
            factory.Verify();
            // At this point verifiable setups are already checked 
            // and an optional MockException might be thrown.
            </code>
            The following examples shows how to setup the factory with a 
            default strict behavior, overriding that default for a 
            specific mock:
            <code>
            var factory = new MockFactory(MockBehavior.Strict);
            
            // this particular one we want loose
            var foo = factory.Create&lt;IFoo&gt;(MockBehavior.Loose);
            var bar = factory.Create&lt;IBar&gt;();
            
            // specify setups
            
            // exercise the mocks here
            
            factory.Verify();
            </code>
            </example>
            <seealso cref="T:Moq.MockBehavior"/>
        </member>
        <member name="M:Moq.MockFactory.#ctor(Moq.MockBehavior)">
            <summary>
            Initializes the factory with the given <paramref name="defaultBehavior"/> 
            for newly created mocks from the factory.
            </summary>
            <param name="defaultBehavior">The behavior to use for mocks created 
            using the <see cref="M:Moq.MockFactory.Create``1"/> factory method if not overriden 
            by using the <see cref="M:Moq.MockFactory.Create``1(Moq.MockBehavior)"/> overload.</param>
        </member>
        <member name="M:Moq.MockFactory.Create``1">
            <summary>
            Creates a new mock with the default <see cref="T:Moq.MockBehavior"/> 
            specified at factory construction time.
            </summary>
            <typeparam name="T">Type to mock.</typeparam>
            <returns>A new <see cref="T:Moq.Mock`1"/>.</returns>
            <example ignore="true">
            <code>
            var factory = new MockFactory(MockBehavior.Strict);
            
            var foo = factory.Create&lt;IFoo&gt;();
            // use mock on tests
            
            factory.VerifyAll();
            </code>
            </example>
        </member>
        <member name="M:Moq.MockFactory.Create``1(System.Object[])">
            <summary>
            Creates a new mock with the default <see cref="T:Moq.MockBehavior"/> 
            specified at factory construction time and with the 
            the given constructor arguments for the class.
            </summary>
            <remarks>
            The mock will try to find the best match constructor given the 
            constructor arguments, and invoke that to initialize the instance. 
            This applies only to classes, not interfaces.
            </remarks>
            <typeparam name="T">Type to mock.</typeparam>
            <param name="args">Constructor arguments for mocked classes.</param>
            <returns>A new <see cref="T:Moq.Mock`1"/>.</returns>
            <example ignore="true">
            <code>
            var factory = new MockFactory(MockBehavior.Default);
            
            var mock = factory.Create&lt;MyBase&gt;("Foo", 25, true);
            // use mock on tests
            
            factory.Verify();
            </code>
            </example>
        </member>
        <member name="M:Moq.MockFactory.Create``1(Moq.MockBehavior)">
            <summary>
            Creates a new mock with the given <paramref name="behavior"/>.
            </summary>
            <typeparam name="T">Type to mock.</typeparam>
            <param name="behavior">Behavior to use for the mock, which overrides 
            the default behavior specified at factory construction time.</param>
            <returns>A new <see cref="T:Moq.Mock`1"/>.</returns>
            <example group="factory">
            The following example shows how to create a mock with a different 
            behavior to that specified as the default for the factory:
            <code>
            var factory = new MockFactory(MockBehavior.Strict);
            
            var foo = factory.Create&lt;IFoo&gt;(MockBehavior.Loose);
            </code>
            </example>
        </member>
        <member name="M:Moq.MockFactory.Create``1(Moq.MockBehavior,System.Object[])">
            <summary>
            Creates a new mock with the given <paramref name="behavior"/> 
            and with the the given constructor arguments for the class.
            </summary>
            <remarks>
            The mock will try to find the best match constructor given the 
            constructor arguments, and invoke that to initialize the instance. 
            This applies only to classes, not interfaces.
            </remarks>
            <typeparam name="T">Type to mock.</typeparam>
            <param name="behavior">Behavior to use for the mock, which overrides 
            the default behavior specified at factory construction time.</param>
            <param name="args">Constructor arguments for mocked classes.</param>
            <returns>A new <see cref="T:Moq.Mock`1"/>.</returns>
            <example group="factory">
            The following example shows how to create a mock with a different 
            behavior to that specified as the default for the factory, passing 
            constructor arguments:
            <code>
            var factory = new MockFactory(MockBehavior.Default);
            
            var mock = factory.Create&lt;MyBase&gt;(MockBehavior.Strict, "Foo", 25, true);
            </code>
            </example>
        </member>
        <member name="M:Moq.MockFactory.CreateMock``1(Moq.MockBehavior,System.Object[])">
            <summary>
            Implements creation of a new mock within the factory.
            </summary>
            <typeparam name="T">Type to mock.</typeparam>
            <param name="behavior">The behavior for the new mock.</param>
            <param name="args">Optional arguments for the construction of the mock.</param>
        </member>
        <member name="M:Moq.MockFactory.Verify">
            <summary>
            Verifies all verifiable expectations on all mocks created 
            by this factory.
            </summary>
            <seealso cref="M:Moq.Mock.Verify"/>
            <exception cref="T:Moq.MockException">One or more mocks had expectations that were not satisfied.</exception>
        </member>
        <member name="M:Moq.MockFactory.VerifyAll">
            <summary>
            Verifies all verifiable expectations on all mocks created 
            by this factory.
            </summary>
            <seealso cref="M:Moq.Mock.Verify"/>
            <exception cref="T:Moq.MockException">One or more mocks had expectations that were not satisfied.</exception>
        </member>
        <member name="M:Moq.MockFactory.VerifyMocks(System.Action{Moq.Mock})">
            <summary>
            Invokes <paramref name="verifyAction"/> for each mock 
            in <see cref="P:Moq.MockFactory.Mocks"/>, and accumulates the resulting 
            <see cref="T:Moq.MockVerificationException"/> that might be 
            thrown from the action.
            </summary>
            <param name="verifyAction">The action to execute against 
            each mock.</param>
        </member>
        <member name="P:Moq.MockFactory.CallBase">
            <summary>
            Whether the base member virtual implementation will be called 
            for mocked classes if no setup is matched. Defaults to <see langword="false"/>.
            </summary>
        </member>
        <member name="P:Moq.MockFactory.DefaultValue">
            <summary>
            Specifies the behavior to use when returning default values for 
            unexpected invocations on loose mocks.
            </summary>
        </member>
        <member name="P:Moq.MockFactory.Mocks">
            <summary>
            Gets the mocks that have been created by this factory and 
            that will get verified together.
            </summary>
        </member>
        <member name="M:Moq.MockRepository.Of``1">
            <summary>
            Access the universe of mocks of the given type, to retrieve those 
            that behave according to the LINQ query specification.
            </summary>
            <typeparam name="T">The type of the mocked object to query.</typeparam>
        </member>
        <member name="M:Moq.MockRepository.Of``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Access the universe of mocks of the given type, to retrieve those 
            that behave according to the LINQ query specification.
            </summary>
            <param name="specification">The predicate with the setup expressions.</param>
            <typeparam name="T">The type of the mocked object to query.</typeparam>
        </member>
        <member name="M:Moq.MockRepository.OneOf``1">
            <summary>
            Creates an mock object of the indicated type.
            </summary>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.MockRepository.OneOf``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Creates an mock object of the indicated type.
            </summary>
            <param name="specification">The predicate with the setup expressions.</param>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.MockRepository.CreateMockQuery``1">
            <summary>
            Creates the mock query with the underlying queriable implementation.
            </summary>
        </member>
        <member name="M:Moq.MockRepository.CreateQueryable``1">
            <summary>
            Wraps the enumerator inside a queryable.
            </summary>
        </member>
        <member name="M:Moq.MockRepository.CreateMocks``1">
            <summary>
            Method that is turned into the actual call from .Query{T}, to 
            transform the queryable query into a normal enumerable query.
            This method is never used directly by consumers.
            </summary>
        </member>
        <member name="M:Moq.MockRepository.#ctor(Moq.MockBehavior)">
            <summary>
            Initializes the repository with the given <paramref name="defaultBehavior"/> 
            for newly created mocks from the repository.
            </summary>
            <param name="defaultBehavior">The behavior to use for mocks created 
            using the <see cref="M:Moq.MockFactory.Create``1"/> repository method if not overriden 
            by using the <see cref="M:Moq.MockFactory.Create``1(Moq.MockBehavior)"/> overload.</param>
        </member>
        <member name="T:Moq.Mocks">
            <summary>
            Allows querying the universe of mocks for those that behave 
            according to the LINQ query specification.
            </summary>
            <devdoc>
            This entry-point into Linq to Mocks is the only one in the root Moq 
            namespace to ease discovery. But to get all the mocking extension 
            methods on Object, a using of Moq.Linq must be done, so that the 
            polluting of the intellisense for all objects is an explicit opt-in.
            </devdoc>
        </member>
        <member name="M:Moq.Mocks.Of``1">
            <summary>
            Access the universe of mocks of the given type, to retrieve those 
            that behave according to the LINQ query specification.
            </summary>
            <typeparam name="T">The type of the mocked object to query.</typeparam>
        </member>
        <member name="M:Moq.Mocks.Of``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Access the universe of mocks of the given type, to retrieve those 
            that behave according to the LINQ query specification.
            </summary>
            <param name="specification">The predicate with the setup expressions.</param>
            <typeparam name="T">The type of the mocked object to query.</typeparam>
        </member>
        <member name="M:Moq.Mocks.OneOf``1">
            <summary>
            Creates an mock object of the indicated type.
            </summary>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.Mocks.OneOf``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Creates an mock object of the indicated type.
            </summary>
            <param name="specification">The predicate with the setup expressions.</param>
            <typeparam name="T">The type of the mocked object.</typeparam>
            <returns>The mocked object created.</returns>
        </member>
        <member name="M:Moq.Mocks.CreateMockQuery``1">
            <summary>
            Creates the mock query with the underlying queriable implementation.
            </summary>
        </member>
        <member name="M:Moq.Mocks.CreateQueryable``1">
            <summary>
            Wraps the enumerator inside a queryable.
            </summary>
        </member>
        <member name="M:Moq.Mocks.CreateMocks``1">
            <summary>
            Method that is turned into the actual call from .Query{T}, to 
            transform the queryable query into a normal enumerable query.
            This method is never used directly by consumers.
            </summary>
        </member>
        <member name="M:Moq.Mocks.SetPropery``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},``1)">
            <summary>
            Extension method used to support Linq-like setup properties that are not virtual but do have 
            a getter and a setter, thereby allowing the use of Linq to Mocks to quickly initialize Dtos too :)
            </summary>
        </member>
        <member name="T:Moq.QueryableMockExtensions">
            <summary>
            Helper extensions that are used by the query translator.
            </summary>
        </member>
        <member name="M:Moq.QueryableMockExtensions.FluentMock``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Retrieves a fluent mock from the given setup expression.
            </summary>
        </member>
        <member name="T:Moq.Match">
            <summary>
			Allows creation custom value matchers that can be used on setups and verification,
			completely replacing the built-in <see cref="T:Moq.It"/> class with your own argument
			matching rules.
		</summary><remarks>
			 See also <see cref="T:Moq.Match`1"/>.
		</remarks>
        </member>
        <member name="M:Moq.Match.Matcher``1">
            <devdoc>
            Provided for the sole purpose of rendering the delegate passed to the 
            matcher constructor if no friendly render lambda is provided.
            </devdoc>
        </member>
        <member name="M:Moq.Match.Create``1(System.Predicate{``0})">
            <summary>
			Initializes the match with the condition that
			will be checked in order to match invocation
			values.
		</summary><param name="condition">The condition to match against actual values.</param><remarks>
			 <seealso cref="T:Moq.Match`1"/>
		</remarks>
        </member>
        <member name="M:Moq.Match.Create``1(System.Predicate{``0},System.Linq.Expressions.Expression{System.Func{``0}})">
            <!-- No matching elements were found for the following include tag --><include file="Match.xdoc" path="docs/doc[@for=&quot;Match.Create{T}(condition,renderExpression&quot;]/*"/>
        </member>
        <member name="M:Moq.Match.SetLastMatch``1(Moq.Match{``0})">
            <devdoc>
            This method is used to set an expression as the last matcher invoked, 
            which is used in the SetupSet to allow matchers in the prop = value 
            delegate expression. This delegate is executed in "fluent" mode in 
            order to capture the value being set, and construct the corresponding 
            methodcall.
            This is also used in the MatcherFactory for each argument expression.
            This method ensures that when we execute the delegate, we 
            also track the matcher that was invoked, so that when we create the 
            methodcall we build the expression using it, rather than the null/default 
            value returned from the actual invocation.
            </devdoc>
        </member>
        <member name="T:Moq.Match`1">
            <summary>
			Allows creation custom value matchers that can be used on setups and verification,
			completely replacing the built-in <see cref="T:Moq.It"/> class with your own argument
			matching rules.
		</summary><typeparam name="T">Type of the value to match.</typeparam><remarks>
			The argument matching is used to determine whether a concrete
			invocation in the mock matches a given setup. This
			matching mechanism is fully extensible.
		</remarks><example>
			Creating a custom matcher is straightforward. You just need to create a method
			that returns a value from a call to <see cref="M:Moq.Match.Create``1(System.Predicate{``0})"/> with 
			your matching condition and optional friendly render expression:
			<code>
				[Matcher]
				public Order IsBigOrder()
				{
					return Match&lt;Order&gt;.Create(
						o =&gt; o.GrandTotal &gt;= 5000, 
						/* a friendly expression to render on failures */
						() =&gt; IsBigOrder());
				}
			</code>
			This method can be used in any mock setup invocation:
			<code>
				mock.Setup(m =&gt; m.Submit(IsBigOrder()).Throws&lt;UnauthorizedAccessException&gt;();
			</code>
			At runtime, Moq knows that the return value was a matcher (note that the method MUST be 
			annotated with the [Matcher] attribute in order to determine this) and
			evaluates your predicate with the actual value passed into your predicate.
			<para>
				Another example might be a case where you want to match a lists of orders
				that contains a particular one. You might create matcher like the following:
			</para>
			<code>
				public static class Orders
				{
					[Matcher]
					public static IEnumerable&lt;Order&gt; Contains(Order order)
					{
						return Match&lt;IEnumerable&lt;Order&gt;&gt;.Create(orders =&gt; orders.Contains(order));
					}
				}
			</code>
			Now we can invoke this static method instead of an argument in an
			invocation:
			<code>
				var order = new Order { ... };
				var mock = new Mock&lt;IRepository&lt;Order&gt;&gt;();

				mock.Setup(x =&gt; x.Save(Orders.Contains(order)))
					 .Throws&lt;ArgumentException&gt;();
			</code>
		</example>
        </member>
        <member name="T:Moq.MatcherAttribute">
            <summary>
            Marks a method as a matcher, which allows complete replacement 
            of the built-in <see cref="T:Moq.It"/> class with your own argument 
            matching rules.
            </summary>
            <remarks>
            <b>This feature has been deprecated in favor of the new 
            and simpler <see cref="T:Moq.Match`1"/>.
            </b>
            <para>
            The argument matching is used to determine whether a concrete 
            invocation in the mock matches a given setup. This 
            matching mechanism is fully extensible. 
            </para>
            <para>
            There are two parts of a matcher: the compiler matcher 
            and the runtime matcher.
            <list type="bullet">
            <item>
            <term>Compiler matcher</term>
            <description>Used to satisfy the compiler requirements for the 
            argument. Needs to be a method optionally receiving any arguments 
            you might need for the matching, but with a return type that 
            matches that of the argument. 
            <para>
            Let's say I want to match a lists of orders that contains 
            a particular one. I might create a compiler matcher like the following:
            </para>
            <code>
            public static class Orders
            {
              [Matcher]
              public static IEnumerable&lt;Order&gt; Contains(Order order)
              {
                return null;
              }
            }
            </code>
            Now we can invoke this static method instead of an argument in an 
            invocation:
            <code>
            var order = new Order { ... };
            var mock = new Mock&lt;IRepository&lt;Order&gt;&gt;();
            
            mock.Setup(x =&gt; x.Save(Orders.Contains(order)))
                .Throws&lt;ArgumentException&gt;();
            </code>
            Note that the return value from the compiler matcher is irrelevant. 
            This method will never be called, and is just used to satisfy the 
            compiler and to signal Moq that this is not a method that we want 
            to be invoked at runtime.
            </description>
            </item>
            <item>
            <term>Runtime matcher</term>
            <description>
            The runtime matcher is the one that will actually perform evaluation 
            when the test is run, and is defined by convention to have the 
            same signature as the compiler matcher, but where the return 
            value is the first argument to the call, which contains the 
            object received by the actual invocation at runtime:
            <code>
              public static bool Contains(IEnumerable&lt;Order&gt; orders, Order order)
              {
                return orders.Contains(order);
              }
            </code>
            At runtime, the mocked method will be invoked with a specific 
            list of orders. This value will be passed to this runtime 
            matcher as the first argument, while the second argument is the 
            one specified in the setup (<c>x.Save(Orders.Contains(order))</c>).
            <para>
            The boolean returned determines whether the given argument has been 
            matched. If all arguments to the expected method are matched, then 
            the setup matches and is evaluated.
            </para>
            </description>
            </item>
            </list>
            </para>
            Using this extensible infrastructure, you can easily replace the entire 
            <see cref="T:Moq.It"/> set of matchers with your own. You can also avoid the 
            typical (and annoying) lengthy expressions that result when you have 
            multiple arguments that use generics.
            </remarks>
            <example>
            The following is the complete example explained above:
            <code>
            public static class Orders
            {
              [Matcher]
              public static IEnumerable&lt;Order&gt; Contains(Order order)
              {
                return null;
              }
              
              public static bool Contains(IEnumerable&lt;Order&gt; orders, Order order)
              {
                return orders.Contains(order);
              }
            }
            </code>
            And the concrete test using this matcher:
            <code>
            var order = new Order { ... };
            var mock = new Mock&lt;IRepository&lt;Order&gt;&gt;();
            
            mock.Setup(x =&gt; x.Save(Orders.Contains(order)))
                .Throws&lt;ArgumentException&gt;();
                
            // use mock, invoke Save, and have the matcher filter.
            </code>
            </example>
        </member>
        <member name="T:Moq.Matchers.MatcherAttributeMatcher">
            <summary>
            Matcher to treat static functions as matchers.
            
            mock.Setup(x => x.StringMethod(A.MagicString()));
            
            public static class A 
            {
                [Matcher]
                public static string MagicString() { return null; }
                public static bool MagicString(string arg)
                {
                    return arg == "magic";
                }
            }
            
            Will succeed if: mock.Object.StringMethod("magic");
            and fail with any other call.
            </summary>
        </member>
        <member name="T:Moq.MethodCallReturn">
            <devdoc>
            We need this non-generics base class so that 
            we can use <see cref="P:Moq.MethodCallReturn.HasReturnValue"/> from 
            generic code.
            </devdoc>
        </member>
        <member name="T:Moq.MockBehavior">
            <summary>
            Options to customize the behavior of the mock. 
            </summary>
        </member>
        <member name="F:Moq.MockBehavior.Strict">
            <summary>
            Causes the mock to always throw 
            an exception for invocations that don't have a 
            corresponding setup.
            </summary>
        </member>
        <member name="F:Moq.MockBehavior.Loose">
            <summary>
            Will never throw exceptions, returning default  
            values when necessary (null for reference types, 
            zero for value types or empty enumerables and arrays).
            </summary>
        </member>
        <member name="F:Moq.MockBehavior.Default">
            <summary>
            Default mock behavior, which equals <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
        </member>
        <member name="T:Moq.MockDefaultValueProvider">
            <summary>
            A <see cref="T:Moq.IDefaultValueProvider"/> that returns an empty default value 
            for non-mockeable types, and mocks for all other types (interfaces and 
            non-sealed classes) that can be mocked.
            </summary>
        </member>
        <member name="T:Moq.MockException">
            <summary>
            Exception thrown by mocks when setups are not matched, 
            the mock is not properly setup, etc.
            </summary>
            <remarks>
            A distinct exception type is provided so that exceptions 
            thrown by the mock can be differentiated in tests that 
            expect other exceptions to be thrown (i.e. ArgumentException).
            <para>
            Richer exception hierarchy/types are not provided as 
            tests typically should <b>not</b> catch or expect exceptions 
            from the mocks. These are typically the result of changes 
            in the tested class or its collaborators implementation, and 
            result in fixes in the mock setup so that they dissapear and 
            allow the test to pass.
            </para>
            </remarks>
        </member>
        <member name="P:Moq.MockException.IsVerificationError">
            <summary>
            Indicates whether this exception is a verification fault raised by Verify()
            </summary>
        </member>
        <member name="T:Moq.MockException.ExceptionReason">
            <summary>
            Made internal as it's of no use for 
            consumers, but it's important for 
            our own tests.
            </summary>
        </member>
        <member name="T:Moq.MockVerificationException">
            <devdoc>
            Used by the mock factory to accumulate verification 
            failures.
            </devdoc>
        </member>
        <member name="T:Moq.MockSequence">
            <summary>
            Helper class to setup a full trace between many mocks
            </summary>
        </member>
        <member name="M:Moq.MockSequence.#ctor">
            <summary>
            Initialize a trace setup
            </summary>
        </member>
        <member name="P:Moq.MockSequence.Cyclic">
            <summary>
            Allow sequence to be repeated
            </summary>
        </member>
        <member name="T:Moq.MockSequenceHelper">
            <summary>
            define nice api
            </summary>
        </member>
        <member name="M:Moq.MockSequenceHelper.InSequence``1(Moq.Mock{``0},Moq.MockSequence)">
            <summary>
            Perform an expectation in the trace.
            </summary>
        </member>
        <member name="T:Moq.MockLegacyExtensions">
            <summary>
            Provides legacy API members as extensions so that 
            existing code continues to compile, but new code 
            doesn't see then.
            </summary>
        </member>
        <member name="M:Moq.MockLegacyExtensions.SetupSet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},``1)">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="M:Moq.MockLegacyExtensions.VerifySet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},``1)">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="M:Moq.MockLegacyExtensions.VerifySet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},``1,System.String)">
            <summary>
            Obsolete.
            </summary>
        </member>
        <member name="T:Moq.ObsoleteMockExtensions">
            <summary>
            Provides additional methods on mocks.
            </summary>
            <devdoc>
            Provided as extension methods as they confuse the compiler 
            with the overloads taking Action.
            </devdoc>
        </member>
        <member name="M:Moq.ObsoleteMockExtensions.SetupSet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Specifies a setup on the mocked type for a call to 
            to a property setter, regardless of its value.
            </summary>
            <remarks>
            If more than one setup is set for the same property setter, 
            the latest one wins and is the one that will be executed.
            </remarks>
            <typeparam name="TProperty">Type of the property. Typically omitted as it can be inferred from the expression.</typeparam>
            <typeparam name="T">Type of the mock.</typeparam>
            <param name="mock">The target mock for the setup.</param>
            <param name="expression">Lambda expression that specifies the property setter.</param>
            <example group="setups">
            <code>
            mock.SetupSet(x =&gt; x.Suspended);
            </code>
            </example>
            <devdoc>
            This method is not legacy, but must be on an extension method to avoid 
            confusing the compiler with the new Action syntax.
            </devdoc>
        </member>
        <member name="M:Moq.ObsoleteMockExtensions.VerifySet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Verifies that a property has been set on the mock, regarless of its value.
            </summary>
            <example group="verification">
            This example assumes that the mock has been used, 
            and later we want to verify that a given invocation 
            with specific parameters was performed:
            <code>
            var mock = new Mock&lt;IWarehouse&gt;();
            // exercise mock
            //...
            // Will throw if the test code didn't set the IsClosed property.
            mock.VerifySet(warehouse =&gt; warehouse.IsClosed);
            </code>
            </example>
            <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
            <param name="expression">Expression to verify.</param>
            <param name="mock">The mock instance.</param>
            <typeparam name="T">Mocked type.</typeparam>
            <typeparam name="TProperty">Type of the property to verify. Typically omitted as it can 
            be inferred from the expression's return type.</typeparam>
        </member>
        <member name="M:Moq.ObsoleteMockExtensions.VerifySet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.String)">
            <summary>
            Verifies that a property has been set on the mock, specifying a failure  
            error message. 
            </summary>
            <example group="verification">
            This example assumes that the mock has been used, 
            and later we want to verify that a given invocation 
            with specific parameters was performed:
            <code>
            var mock = new Mock&lt;IWarehouse&gt;();
            // exercise mock
            //...
            // Will throw if the test code didn't set the IsClosed property.
            mock.VerifySet(warehouse =&gt; warehouse.IsClosed);
            </code>
            </example>
            <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
            <param name="expression">Expression to verify.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <param name="mock">The mock instance.</param>
            <typeparam name="T">Mocked type.</typeparam>
            <typeparam name="TProperty">Type of the property to verify. Typically omitted as it can 
            be inferred from the expression's return type.</typeparam>
        </member>
        <member name="M:Moq.ObsoleteMockExtensions.VerifySet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},Moq.Times)">
            <summary>
            Verifies that a property has been set on the mock, regardless 
            of the value but only the specified number of times.
            </summary>
            <example group="verification">
            This example assumes that the mock has been used, 
            and later we want to verify that a given invocation 
            with specific parameters was performed:
            <code>
            var mock = new Mock&lt;IWarehouse&gt;();
            // exercise mock
            //...
            // Will throw if the test code didn't set the IsClosed property.
            mock.VerifySet(warehouse =&gt; warehouse.IsClosed);
            </code>
            </example>
            <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by
            <paramref name="times"/>.</exception>
            <param name="mock">The mock instance.</param>
            <typeparam name="T">Mocked type.</typeparam>
            <param name="times">The number of times a method is allowed to be called.</param>
            <param name="expression">Expression to verify.</param>
            <typeparam name="TProperty">Type of the property to verify. Typically omitted as it can 
            be inferred from the expression's return type.</typeparam>
        </member>
        <member name="M:Moq.ObsoleteMockExtensions.VerifySet``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},Moq.Times,System.String)">
            <summary>
            Verifies that a property has been set on the mock, regardless 
            of the value but only the specified number of times, and specifying a failure  
            error message. 
            </summary>
            <example group="verification">
            This example assumes that the mock has been used, 
            and later we want to verify that a given invocation 
            with specific parameters was performed:
            <code>
            var mock = new Mock&lt;IWarehouse&gt;();
            // exercise mock
            //...
            // Will throw if the test code didn't set the IsClosed property.
            mock.VerifySet(warehouse =&gt; warehouse.IsClosed);
            </code>
            </example>
            <exception cref="T:Moq.MockException">The invocation was not performed on the mock.</exception>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by
            <paramref name="times"/>.</exception>
            <param name="mock">The mock instance.</param>
            <typeparam name="T">Mocked type.</typeparam>
            <param name="times">The number of times a method is allowed to be called.</param>
            <param name="failMessage">Message to show if verification fails.</param>
            <param name="expression">Expression to verify.</param>
            <typeparam name="TProperty">Type of the property to verify. Typically omitted as it can 
            be inferred from the expression's return type.</typeparam>
        </member>
        <member name="T:Moq.Protected.IProtectedMock`1">
            <summary>
            Allows setups to be specified for protected members by using their 
            name as a string, rather than strong-typing them which is not possible 
            due to their visibility.
            </summary>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Setup(System.String,System.Object[])">
            <summary>
            Specifies a setup for a void method invocation with the given 
            <paramref name="voidMethodName"/>, optionally specifying arguments for the method call.
            </summary>
            <param name="voidMethodName">The name of the void method to be invoked.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Setup``1(System.String,System.Object[])">
            <summary>
            Specifies a setup for an invocation on a property or a non void method with the given 
            <paramref name="methodOrPropertyName"/>, optionally specifying arguments for the method call.
            </summary>
            <param name="methodOrPropertyName">The name of the method or property to be invoked.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <typeparam name="TResult">The return type of the method or property.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.SetupGet``1(System.String)">
            <summary>
            Specifies a setup for an invocation on a property getter with the given 
            <paramref name="propertyName"/>.
            </summary>
            <param name="propertyName">The name of the property.</param>
            <typeparam name="TProperty">The type of the property.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.SetupSet``1(System.String,System.Object)">
            <summary>
            Specifies a setup for an invocation on a property setter with the given 
            <paramref name="propertyName"/>.
            </summary>
            <param name="propertyName">The name of the property.</param>
            <param name="value">The property value. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <typeparam name="TProperty">The type of the property.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Verify(System.String,Moq.Times,System.Object[])">
            <summary>
            Specifies a verify for a void method with the given <paramref name="methodName"/>,
            optionally specifying arguments for the method call. Use in conjuntion with the default
            <see cref="F:Moq.MockBehavior.Loose"/>.
            </summary>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by
            <paramref name="times"/>.</exception>
            <param name="methodName">The name of the void method to be verified.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.Verify``1(System.String,Moq.Times,System.Object[])">
            <summary>
            Specifies a verify for an invocation on a property or a non void method with the given 
            <paramref name="methodName"/>, optionally specifying arguments for the method call.
            </summary>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by 
            <paramref name="times"/>.</exception>
            <param name="methodName">The name of the method or property to be invoked.</param>
            <param name="args">The optional arguments for the invocation. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <typeparam name="TResult">The type of return value from the expression.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.VerifyGet``1(System.String,Moq.Times)">
            <summary>
            Specifies a verify for an invocation on a property getter with the given 
            <paramref name="propertyName"/>.
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by 
            <paramref name="times"/>.</exception>
            </summary>
            <param name="propertyName">The name of the property.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <typeparam name="TProperty">The type of the property.</typeparam>
        </member>
        <member name="M:Moq.Protected.IProtectedMock`1.VerifySet``1(System.String,Moq.Times,System.Object)">
            <summary>
            Specifies a setup for an invocation on a property setter with the given 
            <paramref name="propertyName"/>.
            </summary>
            <exception cref="T:Moq.MockException">The invocation was not call the times specified by 
            <paramref name="times"/>.</exception>
            <param name="propertyName">The name of the property.</param>
            <param name="times">The number of times a method is allowed to be called.</param>
            <param name="value">The property value.</param>
            <typeparam name="TProperty">The type of the property. If argument matchers are used, 
            remember to use <see cref="T:Moq.Protected.ItExpr"/> rather than <see cref="T:Moq.It"/>.</typeparam>
        </member>
        <member name="T:Moq.Protected.ItExpr">
            <summary>
            Allows the specification of a matching condition for an 
            argument in a protected member setup, rather than a specific 
            argument value. "ItExpr" refers to the argument being matched.
            </summary>
            <remarks>
            <para>Use this variant of argument matching instead of 
            <see cref="T:Moq.It"/> for protected setups.</para>
            This class allows the setup to match a method invocation 
            with an arbitrary value, with a value in a specified range, or 
            even one that matches a given predicate, or null.
            </remarks>
        </member>
        <member name="M:Moq.Protected.ItExpr.IsNull``1">
            <summary>
            Matches a null value of the given <typeparamref name="TValue"/> type.
            </summary>
            <remarks>
            Required for protected mocks as the null value cannot be used 
            directly as it prevents proper method overload selection.
            </remarks>
            <example>
            <code>
            // Throws an exception for a call to Remove with a null string value.
            mock.Protected()
                .Setup("Remove", ItExpr.IsNull&lt;string&gt;())
                .Throws(new InvalidOperationException());
            </code>
            </example>
            <typeparam name="TValue">Type of the value.</typeparam>
        </member>
        <member name="M:Moq.Protected.ItExpr.IsAny``1">
            <summary>
            Matches any value of the given <typeparamref name="TValue"/> type.
            </summary>
            <remarks>
            Typically used when the actual argument value for a method 
            call is not relevant.
            </remarks>
            <example>
            <code>
            // Throws an exception for a call to Remove with any string value.
            mock.Protected()
                .Setup("Remove", ItExpr.IsAny&lt;string&gt;())
                .Throws(new InvalidOperationException());
            </code>
            </example>
            <typeparam name="TValue">Type of the value.</typeparam>
        </member>
        <member name="M:Moq.Protected.ItExpr.Is``1(System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
            <summary>
            Matches any value that satisfies the given predicate.
            </summary>
            <typeparam name="TValue">Type of the argument to check.</typeparam>
            <param name="match">The predicate used to match the method argument.</param>
            <remarks>
            Allows the specification of a predicate to perform matching 
            of method call arguments.
            </remarks>
            <example>
            This example shows how to return the value <c>1</c> whenever the argument to the 
            <c>Do</c> method is an even number.
            <code>
            mock.Protected()
                .Setup("Do", ItExpr.Is&lt;int&gt;(i =&gt; i % 2 == 0))
                .Returns(1);
            </code>
            This example shows how to throw an exception if the argument to the 
            method is a negative number:
            <code>
            mock.Protected()
                .Setup("GetUser", ItExpr.Is&lt;int&gt;(i =&gt; i &lt; 0))
                .Throws(new ArgumentException());
            </code>
            </example>
        </member>
        <member name="M:Moq.Protected.ItExpr.IsInRange``1(``0,``0,Moq.Range)">
            <summary>
            Matches any value that is in the range specified.
            </summary>
            <typeparam name="TValue">Type of the argument to check.</typeparam>
            <param name="from">The lower bound of the range.</param>
            <param name="to">The upper bound of the range.</param>
            <param name="rangeKind">The kind of range. See <see cref="T:Moq.Range"/>.</param>
            <example>
            The following example shows how to expect a method call 
            with an integer argument within the 0..100 range.
            <code>
            mock.Protected()
                .Setup("HasInventory",
                        ItExpr.IsAny&lt;string&gt;(),
                        ItExpr.IsInRange(0, 100, Range.Inclusive))
                .Returns(false);
            </code>
            </example>
        </member>
        <member name="M:Moq.Protected.ItExpr.IsRegex(System.String)">
            <summary>
            Matches a string argument if it matches the given regular expression pattern.
            </summary>
            <param name="regex">The pattern to use to match the string argument value.</param>
            <example>
            The following example shows how to expect a call to a method where the 
            string argument matches the given regular expression:
            <code>
            mock.Protected()
                .Setup("Check", ItExpr.IsRegex("[a-z]+"))
                .Returns(1);
            </code>
            </example>
        </member>
        <member name="M:Moq.Protected.ItExpr.IsRegex(System.String,System.Text.RegularExpressions.RegexOptions)">
            <summary>
            Matches a string argument if it matches the given regular expression pattern.
            </summary>
            <param name="regex">The pattern to use to match the string argument value.</param>
            <param name="options">The options used to interpret the pattern.</param>
            <example>
            The following example shows how to expect a call to a method where the 
            string argument matches the given regular expression, in a case insensitive way:
            <code>
            mock.Protected()
                .Setup("Check", ItExpr.IsRegex("[a-z]+", RegexOptions.IgnoreCase))
                .Returns(1);
            </code>
            </example>
        </member>
        <member name="T:Moq.Protected.ProtectedExtension">
            <summary>
            Enables the <c>Protected()</c> method on <see cref="T:Moq.Mock`1"/>, 
            allowing setups to be set for protected members by using their 
            name as a string, rather than strong-typing them which is not possible 
            due to their visibility.
            </summary>
        </member>
        <member name="M:Moq.Protected.ProtectedExtension.Protected``1(Moq.Mock{``0})">
            <summary>
            Enable protected setups for the mock.
            </summary>
            <typeparam name="T">Mocked object type. Typically omitted as it can be inferred from the mock instance.</typeparam>
            <param name="mock">The mock to set the protected setups on.</param>
        </member>
        <member name="T:ThisAssembly">
            <group name="overview" title="Overview" order="0" />
            <group name="setups" title="Specifying setups" order="1" />
            <group name="returns" title="Returning values from members" order="2" />
            <group name="verification" title="Verifying setups" order="3" />
            <group name="advanced" title="Advanced scenarios" order="99" />
            <group name="factory" title="Using MockFactory for consistency across mocks" order="4" />
        </member>
        <member name="T:Moq.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.AlreadyInitialized">
            <summary>
              Looks up a localized string similar to Mock type has already been initialized by accessing its Object property. Adding interfaces must be done before that..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ArgumentCannotBeEmpty">
            <summary>
              Looks up a localized string similar to Value cannot be an empty string..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.AsMustBeInterface">
            <summary>
              Looks up a localized string similar to Can only add interfaces to the mock..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.CantSetReturnValueForVoid">
            <summary>
              Looks up a localized string similar to Can&apos;t set return value for void method {0}..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ConstructorArgsForDelegate">
            <summary>
              Looks up a localized string similar to Constructor arguments cannot be passed for delegate mocks..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ConstructorArgsForInterface">
            <summary>
              Looks up a localized string similar to Constructor arguments cannot be passed for interface mocks..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ConstructorNotFound">
            <summary>
              Looks up a localized string similar to A matching constructor for the given arguments was not found on the mocked type..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.EventNofFound">
            <summary>
              Looks up a localized string similar to Could not locate event for attach or detach method {0}..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.FieldsNotSupported">
            <summary>
              Looks up a localized string similar to Expression {0} involves a field access, which is not supported. Use properties instead..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.InvalidMockClass">
            <summary>
              Looks up a localized string similar to Type to mock must be an interface or an abstract or non-sealed class. .
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.InvalidMockGetType">
             <summary>
               Looks up a localized string similar to Cannot retrieve a mock with the given object type {0} as it&apos;s not the main type of the mock or any of its additional interfaces.
            Please cast the argument to one of the supported types: {1}.
            Remember that there&apos;s no generics covariance in the CLR, so your object must be one of these types in order for the call to succeed..
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.LinqBinaryOperatorNotSupported">
            <summary>
              Looks up a localized string similar to The equals (&quot;==&quot; or &quot;=&quot; in VB) and the conditional &apos;and&apos; (&quot;&amp;&amp;&quot; or &quot;AndAlso&quot; in VB) operators are the only ones supported in the query specification expression. Unsupported expression: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.LinqMethodNotSupported">
            <summary>
              Looks up a localized string similar to LINQ method &apos;{0}&apos; not supported..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.LinqMethodNotVirtual">
            <summary>
              Looks up a localized string similar to Expression contains a call to a method which is not virtual (overridable in VB) or abstract. Unsupported expression: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.MemberMissing">
            <summary>
              Looks up a localized string similar to Member {0}.{1} does not exist..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.MethodIsPublic">
             <summary>
               Looks up a localized string similar to Method {0}.{1} is public. Use strong-typed Expect overload instead:
            mock.Setup(x =&gt; x.{1}());
            .
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.MockExceptionMessage">
             <summary>
               Looks up a localized string similar to {0} invocation failed with mock behavior {1}.
            {2}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.MoreThanNCalls">
            <summary>
              Looks up a localized string similar to Expected only {0} calls to {1}..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.MoreThanOneCall">
            <summary>
              Looks up a localized string similar to Expected only one call to {0}..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsAtLeast">
             <summary>
               Looks up a localized string similar to {0}
            Expected invocation on the mock at least {2} times, but was {4} times: {1}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsAtLeastOnce">
             <summary>
               Looks up a localized string similar to {0}
            Expected invocation on the mock at least once, but was never performed: {1}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsAtMost">
             <summary>
               Looks up a localized string similar to {0}
            Expected invocation on the mock at most {3} times, but was {4} times: {1}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsAtMostOnce">
             <summary>
               Looks up a localized string similar to {0}
            Expected invocation on the mock at most once, but was {4} times: {1}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsBetweenExclusive">
             <summary>
               Looks up a localized string similar to {0}
            Expected invocation on the mock between {2} and {3} times (Exclusive), but was {4} times: {1}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsBetweenInclusive">
             <summary>
               Looks up a localized string similar to {0}
            Expected invocation on the mock between {2} and {3} times (Inclusive), but was {4} times: {1}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsExactly">
             <summary>
               Looks up a localized string similar to {0}
            Expected invocation on the mock exactly {2} times, but was {4} times: {1}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsNever">
             <summary>
               Looks up a localized string similar to {0}
            Expected invocation on the mock should never have been performed, but was {4} times: {1}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoMatchingCallsOnce">
             <summary>
               Looks up a localized string similar to {0}
            Expected invocation on the mock once, but was {4} times: {1}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.NoSetup">
            <summary>
              Looks up a localized string similar to All invocations on the mock must have a corresponding setup..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ObjectInstanceNotMock">
            <summary>
              Looks up a localized string similar to Object instance was not created by Moq..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.OutExpressionMustBeConstantValue">
            <summary>
              Looks up a localized string similar to Out expression must evaluate to a constant value..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.PropertyGetNotFound">
            <summary>
              Looks up a localized string similar to Property {0}.{1} does not have a getter..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.PropertyMissing">
            <summary>
              Looks up a localized string similar to Property {0}.{1} does not exist..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.PropertyNotReadable">
            <summary>
              Looks up a localized string similar to Property {0}.{1} is write-only..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.PropertyNotWritable">
            <summary>
              Looks up a localized string similar to Property {0}.{1} is read-only..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.PropertySetNotFound">
            <summary>
              Looks up a localized string similar to Property {0}.{1} does not have a setter..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.RaisedUnassociatedEvent">
            <summary>
              Looks up a localized string similar to Cannot raise a mocked event unless it has been associated (attached) to a concrete event in a mocked object..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.RefExpressionMustBeConstantValue">
            <summary>
              Looks up a localized string similar to Ref expression must evaluate to a constant value..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.ReturnValueRequired">
            <summary>
              Looks up a localized string similar to Invocation needs to return a value and therefore must have a corresponding setup that provides it..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.SetupLambda">
            <summary>
              Looks up a localized string similar to A lambda expression is expected as the argument to It.Is&lt;T&gt;..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.SetupNever">
            <summary>
              Looks up a localized string similar to Invocation {0} should not have been made..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.SetupNotMethod">
            <summary>
              Looks up a localized string similar to Expression is not a method invocation: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.SetupNotProperty">
            <summary>
              Looks up a localized string similar to Expression is not a property access: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.SetupNotSetter">
            <summary>
              Looks up a localized string similar to Expression is not a property setter invocation..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.SetupOnNonMemberMethod">
            <summary>
              Looks up a localized string similar to Expression references a method that does not belong to the mocked object: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.SetupOnNonOverridableMember">
            <summary>
              Looks up a localized string similar to Invalid setup on a non-virtual (overridable in VB) member: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.TypeNotImplementInterface">
            <summary>
              Looks up a localized string similar to Type {0} does not implement required interface {1}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.TypeNotInheritFromType">
            <summary>
              Looks up a localized string similar to Type {0} does not from required type {1}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnexpectedPublicProperty">
             <summary>
               Looks up a localized string similar to To specify a setup for public property {0}.{1}, use the typed overloads, such as:
            mock.Setup(x =&gt; x.{1}).Returns(value);
            mock.SetupGet(x =&gt; x.{1}).Returns(value); //equivalent to previous one
            mock.SetupSet(x =&gt; x.{1}).Callback(callbackDelegate);
            .
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnsupportedExpression">
            <summary>
              Looks up a localized string similar to Unsupported expression: {0}.
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnsupportedIntermediateExpression">
            <summary>
              Looks up a localized string similar to Only property accesses are supported in intermediate invocations on a setup. Unsupported expression {0}..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnsupportedIntermediateType">
            <summary>
              Looks up a localized string similar to Expression contains intermediate property access {0}.{1} which is of type {2} and cannot be mocked. Unsupported expression {3}..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnsupportedMatcherParamsForSetter">
            <summary>
              Looks up a localized string similar to Setter expression cannot use argument matchers that receive parameters..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnsupportedMember">
            <summary>
              Looks up a localized string similar to Member {0} is not supported for protected mocking..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.UnsupportedNonStaticMatcherForSetter">
            <summary>
              Looks up a localized string similar to Setter expression can only use static custom matchers..
            </summary>
        </member>
        <member name="P:Moq.Properties.Resources.VerficationFailed">
             <summary>
               Looks up a localized string similar to The following setups were not matched:
            {0}.
             </summary>
        </member>
        <member name="P:Moq.Properties.Resources.VerifyOnNonVirtualMember">
            <summary>
              Looks up a localized string similar to Invalid verify on a non-virtual (overridable in VB) member: {0}.
            </summary>
        </member>
        <member name="M:Moq.Proxy.IProxyFactory.GetDelegateProxyInterface(System.Type,System.Reflection.MethodInfo@)">
            <summary>
            Gets an autogenerated interface with a method on it that matches the signature of the specified
            <paramref name="delegateType"/>.
            </summary>
            <remarks>
            Such an interface can then be mocked, and a delegate pointed at the method on the mocked instance.
            This is how we support delegate mocking.  The factory caches such interfaces and reuses them
            for repeated requests for the same delegate type.
            </remarks>
            <param name="delegateType">The delegate type for which an interface is required.</param>
            <param name="delegateInterfaceMethod">The method on the autogenerated interface.</param>
        </member>
        <member name="M:Moq.Proxy.CastleProxyFactory.CreateProxy(System.Type,Moq.Proxy.ICallInterceptor,System.Type[],System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Moq.Proxy.CastleProxyFactory.GetDelegateProxyInterface(System.Type,System.Reflection.MethodInfo@)">
            <inheritdoc />
        </member>
        <member name="T:Moq.Proxy.ProxyMethodHook">
            <summary>
            Hook used to tells Castle which methods to proxy in mocked classes.
            
            Here we proxy the default methods Castle suggests (everything Object's methods)
            plus Object.ToString(), so we can give mocks useful default names.
            
            This is required to allow Moq to mock ToString on proxy *class* implementations.
            </summary>
        </member>
        <member name="M:Moq.Proxy.ProxyMethodHook.ShouldInterceptMethod(System.Type,System.Reflection.MethodInfo)">
            <summary>
            Extends AllMethodsHook.ShouldInterceptMethod to also intercept Object.ToString().
            </summary>
        </member>
        <member name="T:Moq.Proxy.InterfaceProxy">
            <summary>
            <para>The base class used for all our interface-inheriting proxies, which overrides the default
            Object.ToString() behavior, to route it via the mock by default, unless overriden by a
            real implementation.</para>
            
            <para>This is required to allow Moq to mock ToString on proxy *interface* implementations.</para>
            </summary>
            <remarks>
            <para><strong>This is internal to Moq and should not be generally used.</strong></para>
            
            <para>Unfortunately it must be public, due to cross-assembly visibility issues with reflection, 
            see github.com/Moq/moq4/issues/98 for details.</para>
            </remarks>
        </member>
        <member name="M:Moq.Proxy.InterfaceProxy.ToString">
            <summary>
            Overrides the default ToString implementation to instead find the mock for this mock.Object,
            and return MockName + '.Object' as the mocked object's ToString, to make it easy to relate
            mocks and mock object instances in error messages.
            </summary>
        </member>
        <member name="T:Moq.Range">
            <summary>
            Kind of range to use in a filter specified through 
            <see cref="M:Moq.It.IsInRange``1(``0,``0,Moq.Range)"/>.
            </summary>
        </member>
        <member name="F:Moq.Range.Inclusive">
            <summary>
            The range includes the <c>to</c> and 
            <c>from</c> values.
            </summary>
        </member>
        <member name="F:Moq.Range.Exclusive">
            <summary>
            The range does not include the <c>to</c> and 
            <c>from</c> values.
            </summary>
        </member>
        <member name="T:Moq.SequenceExtensions">
            <summary>
            Helper for sequencing return values in the same method.
            </summary>
        </member>
        <member name="M:Moq.SequenceExtensions.SetupSequence``2(Moq.Mock{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Return a sequence of values, once per call.
            </summary>
        </member>
        <member name="T:Moq.Times">
            <summary>
			Defines the number of invocations allowed by a mocked method.
		</summary>
        </member>
        <member name="M:Moq.Times.AtLeast(System.Int32)">
            <summary>
			Specifies that a mocked method should be invoked <paramref name="callCount"/> times as minimum.
		</summary><param name="callCount">The minimun number of times.</param><returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.AtLeastOnce">
            <summary>
			Specifies that a mocked method should be invoked one time as minimum.
		</summary><returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.AtMost(System.Int32)">
            <summary>
			Specifies that a mocked method should be invoked <paramref name="callCount"/> time as maximun.
		</summary><param name="callCount">The maximun number of times.</param><returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.AtMostOnce">
            <summary>
			Specifies that a mocked method should be invoked one time as maximun.
		</summary><returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.Between(System.Int32,System.Int32,Moq.Range)">
            <summary>
			Specifies that a mocked method should be invoked between <paramref name="callCountFrom"/> and
			<paramref name="callCountTo"/> times.
		</summary><param name="callCountFrom">The minimun number of times.</param><param name="callCountTo">The maximun number of times.</param><param name="rangeKind">
			The kind of range. See <see cref="T:Moq.Range"/>.
		</param><returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.Exactly(System.Int32)">
            <summary>
			Specifies that a mocked method should be invoked exactly <paramref name="callCount"/> times.
		</summary><param name="callCount">The times that a method or property can be called.</param><returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.Never">
            <summary>
			Specifies that a mocked method should not be invoked.
		</summary><returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.Once">
            <summary>
			Specifies that a mocked method should be invoked exactly one time.
		</summary><returns>An object defining the allowed number of invocations.</returns>
        </member>
        <member name="M:Moq.Times.Equals(System.Object)">
            <summary>
			Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
		</summary><param name="obj">
			The <see cref="T:System.Object"/> to compare with this instance.
		</param><returns>
			<c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
		</returns>
        </member>
        <member name="M:Moq.Times.GetHashCode">
            <summary>
			Returns a hash code for this instance.
		</summary><returns>
			A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table.
		</returns>
        </member>
        <member name="M:Moq.Times.op_Equality(Moq.Times,Moq.Times)">
            <summary>
			Determines whether two specified <see cref="T:Moq.Times"/> objects have the same value.
		</summary><param name="left">
			The first <see cref="T:Moq.Times"/>.
		</param><param name="right">
			The second <see cref="T:Moq.Times"/>.
		</param><returns>
			<c>true</c> if the value of left is the same as the value of right; otherwise, <c>false</c>.
		</returns>
        </member>
        <member name="M:Moq.Times.op_Inequality(Moq.Times,Moq.Times)">
            <summary>
			Determines whether two specified <see cref="T:Moq.Times"/> objects have different values.
		</summary><param name="left">
			The first <see cref="T:Moq.Times"/>.
		</param><param name="right">
			The second <see cref="T:Moq.Times"/>.
		</param><returns>
			<c>true</c> if the value of left is different from the value of right; otherwise, <c>false</c>.
		</returns>
        </member>
    </members>
</doc>
