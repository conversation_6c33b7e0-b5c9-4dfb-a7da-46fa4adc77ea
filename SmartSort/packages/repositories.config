﻿<?xml version="1.0" encoding="utf-8"?>
<repositories>
  <repository path="..\..\DB3FileConversion\FileConversion\packages.config" />
  <repository path="..\DevTrends.WCFDataAnnotations.UnitTests\packages.config" />
  <repository path="..\Purolator.SmartSort.Business.Common\packages.config" />
  <repository path="..\Purolator.SmartSort.Common\packages.config" />
  <repository path="..\Purolator.SmartSort.Data.Access\packages.config" />
  <repository path="..\Purolator.SmartSort.Service.Contracts\packages.config" />
  <repository path="..\Purolator.SmartSort.Service.DataContracts\packages.config" />
  <repository path="..\Purolator.SmartSort.Service.Implementation.ShipmentEvent\packages.config" />
  <repository path="..\Purolator.Smartsort.Service.MessageContracts\packages.config" />
  <repository path="..\Purolator.SmartSort.Windows.Service.RoutePrinter\packages.config" />
  <repository path="..\Purolator.SmartSort.Windows.Services.AddressValidationService\packages.config" />
  <repository path="..\Purolator.SmartSort.Windows.Services.EventSender\packages.config" />
  <repository path="..\Purolator.SmartSort.Windows.Services.OFDSender\packages.config" />
  <repository path="..\TestLoadDeviation\packages.config" />
  <repository path="..\TestSmartSortScanService\packages.config" />
  <repository path="..\WebServices\packages.config" />
</repositories>