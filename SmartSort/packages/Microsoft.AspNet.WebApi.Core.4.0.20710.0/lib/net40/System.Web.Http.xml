﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Http</name>
  </assembly>
  <members>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.Exception)">
      <summary>Creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> that represents an exception.</summary>
      <returns>The request must be associated with an <see cref="T:System.Web.Http.HttpConfiguration" /> instance.An <see cref="T:System.Net.Http.HttpResponseMessage" /> whose content is a serialized representation of an  <see cref="T:System.Web.Http.HttpError" /> instance.</returns>
      <param name="request">The HTTP request.</param>
      <param name="statusCode">The status code of the response.</param>
      <param name="exception">The exception.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.String)">
      <summary>Creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> that represents an error message.</summary>
      <returns>The request must be associated with an <see cref="T:System.Web.Http.HttpConfiguration" /> instance.An <see cref="T:System.Net.Http.HttpResponseMessage" /> whose content is a serialized representation of an <see cref="T:System.Web.Http.HttpError" /> instance.</returns>
      <param name="request">The HTTP request.</param>
      <param name="statusCode">The status code of the response.</param>
      <param name="message">The error message.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.String,System.Exception)">
      <summary>Creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> that represents an exception with an error message.</summary>
      <returns>The request must be associated with an <see cref="T:System.Web.Http.HttpConfiguration" /> instance.An <see cref="T:System.Net.Http.HttpResponseMessage" /> whose content is a serialized representation of an <see cref="T:System.Web.Http.HttpError" /> instance.</returns>
      <param name="request">The HTTP request.</param>
      <param name="statusCode">The status code of the response.</param>
      <param name="message">The error message.</param>
      <param name="exception">The exception.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.Web.Http.HttpError)">
      <summary>Creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> that represents an error.</summary>
      <returns>The request must be associated with an <see cref="T:System.Web.Http.HttpConfiguration" /> instance.An <see cref="T:System.Net.Http.HttpResponseMessage" /> whose content is a serialized representation of an <see cref="T:System.Web.Http.HttpError" /> instance.</returns>
      <param name="request">The HTTP request.</param>
      <param name="statusCode">The status code of the response.</param>
      <param name="error">The HTTP error.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateErrorResponse(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,System.Web.Http.ModelBinding.ModelStateDictionary)">
      <summary>Creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> that represents an error in the model state.</summary>
      <returns>The request must be associated with an <see cref="T:System.Web.Http.HttpConfiguration" /> instance.An <see cref="T:System.Net.Http.HttpResponseMessage" /> whose content is a serialized representation of an <see cref="T:System.Web.Http.HttpError" /> instance.</returns>
      <param name="request">The HTTP request.</param>
      <param name="statusCode">The status code of the response.</param>
      <param name="modelState">The model state.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0)">
      <summary>Creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>An initialized <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</returns>
      <param name="request">The HTTP request message which led to this response message.</param>
      <param name="statusCode">The HTTP response status code.</param>
      <param name="value">The content of the HTTP response message.</param>
      <typeparam name="T">The type of the HTTP response message.</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter)">
      <summary>Creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>An initialized <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</returns>
      <param name="request">The HTTP request message which led to this response message.</param>
      <param name="statusCode">The HTTP response status code.</param>
      <param name="value">The content of the HTTP response message.</param>
      <param name="formatter">The media type formatter.</param>
      <typeparam name="T">The type of the HTTP response message.</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>Creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>An initialized <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</returns>
      <param name="request">The HTTP request message which led to this response message.</param>
      <param name="statusCode">The HTTP response status code.</param>
      <param name="value">The content of the HTTP response message.</param>
      <param name="formatter">The media type formatter.</param>
      <param name="mediaType">The media type header value.</param>
      <typeparam name="T">The type of the HTTP response message.</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Net.Http.Formatting.MediaTypeFormatter,System.String)">
      <summary>Creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>An initialized <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</returns>
      <param name="request">The HTTP request message which led to this response message.</param>
      <param name="statusCode">The HTTP response status code.</param>
      <param name="value">The content of the HTTP response message.</param>
      <param name="formatter">The media type formatter.</param>
      <param name="mediaType">The media type.</param>
      <typeparam name="T">The type of the HTTP response message.</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary>Creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>An initialized <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</returns>
      <param name="request">The HTTP request message which led to this response message.</param>
      <param name="statusCode">The HTTP response status code.</param>
      <param name="value">The content of the HTTP response message.</param>
      <param name="mediaType">The media type header value.</param>
      <typeparam name="T">The type of the HTTP response message.</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.String)">
      <summary>Creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>An initialized <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</returns>
      <param name="request">The HTTP request message which led to this response message.</param>
      <param name="statusCode">The HTTP response status code.</param>
      <param name="value">The content of the HTTP response message.</param>
      <param name="mediaType">The media type.</param>
      <typeparam name="T">The type of the HTTP response message.</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.CreateResponse``1(System.Net.Http.HttpRequestMessage,System.Net.HttpStatusCode,``0,System.Web.Http.HttpConfiguration)">
      <summary>Creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>An initialized <see cref="T:System.Net.Http.HttpResponseMessage" /> wired up to the associated <see cref="T:System.Net.Http.HttpRequestMessage" />.</returns>
      <param name="request">The HTTP request message which led to this response message.</param>
      <param name="statusCode">The HTTP response status code.</param>
      <param name="value">The content of the HTTP response message.</param>
      <param name="configuration">The HTTP configuration which contains the dependency resolver used to resolve services.</param>
      <typeparam name="T">The type of the HTTP response message.</typeparam>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.DisposeRequestResources(System.Net.Http.HttpRequestMessage)">
      <summary>Disposes of all tracked resources associated with the <paramref name="request" /> which were added via the <see cref="M:System.Net.Http.HttpRequestMessageExtensions.RegisterForDispose(System.Net.Http.HttpRequestMessage,System.IDisposable)" /> method.</summary>
      <param name="request">The HTTP request.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetClientCertificate(System.Net.Http.HttpRequestMessage)">
      <summary>Gets the current X.509 certificate from the given HTTP request.</summary>
      <returns>The current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />, or null if a certificate is not available.</returns>
      <param name="request">The HTTP request.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetConfiguration(System.Net.Http.HttpRequestMessage)">
      <summary>Retrieves the <see cref="T:System.Web.Http.HttpConfiguration" /> for the given request.</summary>
      <returns>The <see cref="T:System.Web.Http.HttpConfiguration" /> for the given request.</returns>
      <param name="request">The HTTP request.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetCorrelationId(System.Net.Http.HttpRequestMessage)">
      <summary>Retrieves the <see cref="T:System.Guid" /> which has been assigned as the correlation ID associated with the given <paramref name="request" />. The value will be created and set the first time this method is called.</summary>
      <returns>The <see cref="T:System.Guid" /> object that represents the correlation ID associated with the request.</returns>
      <param name="request">The HTTP request.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetDependencyScope(System.Net.Http.HttpRequestMessage)">
      <summary>Retrieves the <see cref="T:System.Web.Http.Dependencies.IDependencyScope" /> for the given request or null if not available.</summary>
      <returns>The <see cref="T:System.Web.Http.Dependencies.IDependencyScope" /> for the given request or null if not available.</returns>
      <param name="request">The HTTP request.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetQueryNameValuePairs(System.Net.Http.HttpRequestMessage)">
      <summary>Gets the parsed query string as a collection of key-value pairs.</summary>
      <returns>The query string as a collection of key-value pairs.</returns>
      <param name="request">The HTTP request.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetRouteData(System.Net.Http.HttpRequestMessage)">
      <summary>Retrieves the <see cref="T:System.Web.Http.Routing.IHttpRouteData" /> for the given request or null if not available.</summary>
      <returns>The <see cref="T:System.Web.Http.Routing.IHttpRouteData" /> for the given request or null if not available.</returns>
      <param name="request">The HTTP request.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetSynchronizationContext(System.Net.Http.HttpRequestMessage)">
      <summary>Retrieves the <see cref="T:System.Threading.SynchronizationContext" /> for the given request or null if not available.</summary>
      <returns>The <see cref="T:System.Threading.SynchronizationContext" /> for the given request or null if not available.</returns>
      <param name="request">The HTTP request.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.GetUrlHelper(System.Net.Http.HttpRequestMessage)">
      <summary>Gets a <see cref="T:System.Web.Http.Routing.UrlHelper" /> instance for an HTTP request.</summary>
      <returns>A <see cref="T:System.Web.Http.Routing.UrlHelper" /> instance that is initialized for the specified HTTP request.</returns>
      <param name="request">The HTTP request.</param>
    </member>
    <member name="M:System.Net.Http.HttpRequestMessageExtensions.RegisterForDispose(System.Net.Http.HttpRequestMessage,System.IDisposable)">
      <summary>Adds the given <paramref name="resource" /> to a list of resources that will be disposed by a host once the <paramref name="request" /> is disposed.</summary>
      <param name="request">The HTTP request controlling the lifecycle of <paramref name="resource" />.</param>
      <param name="resource">The resource to dispose when <paramref name="request" /> is being disposed.</param>
    </member>
    <member name="T:System.Net.Http.HttpResponseMessageExtensions">
      <summary>Represents the message extensions for the HTTP response from an ASP.NET operation.</summary>
    </member>
    <member name="M:System.Net.Http.HttpResponseMessageExtensions.TryGetContentValue``1(System.Net.Http.HttpResponseMessage,``0@)">
      <summary>Attempts to retrieve the value of the content for the <see cref="T:System.Net.Http.HttpResponseMessageExtensions" />.</summary>
      <returns>The result of the retrieval of value of the content.</returns>
      <param name="response">The response of the operation.</param>
      <param name="value">The value of the content.</param>
      <typeparam name="T">The type of the value to retrieve.</typeparam>
    </member>
    <member name="T:System.Net.Http.Formatting.MediaTypeFormatterExtensions">
      <summary>Represents extensions for adding <see cref="T:System.Net.Http.Formatting.MediaTypeMapping" /> items to a <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" />. </summary>
    </member>
    <member name="M:System.Net.Http.Formatting.MediaTypeFormatterExtensions.AddUriPathExtensionMapping(System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary> Updates the given formatter's set of <see cref="T:System.Net.Http.Formatting.MediaTypeMapping" /> elements so that it associates the mediaType with <see cref="T:System.Uri" />s ending with the given uriPathExtension. </summary>
      <param name="formatter">The <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> to receive the new <see cref="T:System.Net.Http.Formatting.UriPathExtensionMapping" /> item.</param>
      <param name="uriPathExtension">The string of the <see cref="T:System.Uri" /> path extension.</param>
      <param name="mediaType">The <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> to associate with <see cref="T:System.Uri" />s ending with uriPathExtension.</param>
    </member>
    <member name="M:System.Net.Http.Formatting.MediaTypeFormatterExtensions.AddUriPathExtensionMapping(System.Net.Http.Formatting.MediaTypeFormatter,System.String,System.String)">
      <summary> Updates the given formatter's set of <see cref="T:System.Net.Http.Formatting.MediaTypeMapping" /> elements so that it associates the mediaType with <see cref="T:System.Uri" />s ending with the given uriPathExtension. </summary>
      <param name="formatter">The <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> to receive the new <see cref="T:System.Net.Http.Formatting.UriPathExtensionMapping" /> item.</param>
      <param name="uriPathExtension">The string of the <see cref="T:System.Uri" /> path extension.</param>
      <param name="mediaType">The string media type to associate with <see cref="T:System.Uri" />s ending with uriPathExtension.</param>
    </member>
    <member name="T:System.Net.Http.Formatting.UriPathExtensionMapping">
      <summary>Provides <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" />s from path extensions appearing in a <see cref="T:System.Uri" />. </summary>
    </member>
    <member name="M:System.Net.Http.Formatting.UriPathExtensionMapping.#ctor(System.String,System.Net.Http.Headers.MediaTypeHeaderValue)">
      <summary> Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.UriPathExtensionMapping" /> class. </summary>
      <param name="uriPathExtension">The extension corresponding to mediaType. This value should not include a dot or wildcards.</param>
      <param name="mediaType">The <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> that will be returned if uriPathExtension is matched.</param>
    </member>
    <member name="M:System.Net.Http.Formatting.UriPathExtensionMapping.#ctor(System.String,System.String)">
      <summary> Initializes a new instance of the <see cref="T:System.Net.Http.Formatting.UriPathExtensionMapping" /> class. </summary>
      <param name="uriPathExtension">The extension corresponding to mediaType. This value should not include a dot or wildcards.</param>
      <param name="mediaType">The media type that will be returned if uriPathExtension is matched.</param>
    </member>
    <member name="M:System.Net.Http.Formatting.UriPathExtensionMapping.TryMatchMediaType(System.Net.Http.HttpRequestMessage)">
      <summary> Returns a value indicating whether this <see cref="T:System.Net.Http.Formatting.UriPathExtensionMapping" /> instance can provide a <see cref="T:System.Net.Http.Headers.MediaTypeHeaderValue" /> for the <see cref="T:System.Uri" />  of request. </summary>
      <returns>If this instance can match a file extension in request it returns 1.0 otherwise 0.0.</returns>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> to check.</param>
    </member>
    <member name="P:System.Net.Http.Formatting.UriPathExtensionMapping.UriPathExtension">
      <summary> Gets the <see cref="T:System.Uri" /> path extension. </summary>
      <returns>The <see cref="T:System.Uri" /> path extension.</returns>
    </member>
    <member name="F:System.Net.Http.Formatting.UriPathExtensionMapping.UriPathExtensionKey">
      <summary>The <see cref="T:System.Uri" /> path extension key.</summary>
    </member>
    <member name="T:System.Web.Http.AcceptVerbsAttribute">
      <summary>Represents an attribute that specifies which HTTP methods an action method will respond to.</summary>
    </member>
    <member name="M:System.Web.Http.AcceptVerbsAttribute.#ctor(System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.AcceptVerbsAttribute" /> class by using a list of HTTP methods that the action method will respond to.</summary>
      <param name="methods">The HTTP methods that the action method will respond to.</param>
    </member>
    <member name="P:System.Web.Http.AcceptVerbsAttribute.HttpMethods">
      <summary>Gets or sets the list of HTTP methods that the action method will respond to.</summary>
      <returns>Gets or sets the list of HTTP methods that the action method will respond to.</returns>
    </member>
    <member name="T:System.Web.Http.ActionNameAttribute">
      <summary>Represents an attribute that is used for the name of an action.</summary>
    </member>
    <member name="M:System.Web.Http.ActionNameAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ActionNameAttribute" /> class.</summary>
      <param name="name">The name of the action.</param>
    </member>
    <member name="P:System.Web.Http.ActionNameAttribute.Name">
      <summary>Gets or sets the name of the action.</summary>
      <returns>The name of the action.</returns>
    </member>
    <member name="T:System.Web.Http.AllowAnonymousAttribute">
      <summary>Specifies that actions and controllers are skipped by <see cref="T:System.Web.Http.AuthorizeAttribute" /> during authorization.</summary>
    </member>
    <member name="M:System.Web.Http.AllowAnonymousAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.AllowAnonymousAttribute" /> class.</summary>
    </member>
    <member name="T:System.Web.Http.ApiController">
      <summary>Defines properties and methods for API controller.</summary>
    </member>
    <member name="M:System.Web.Http.ApiController.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ApiController" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.ApiController.Configuration">
      <summary>Gets or sets the <see cref="T:System.Web.Http.HttpConfiguration" /> of the current <see cref="T:System.Web.Http.ApiController" />.</summary>
      <returns>The <see cref="T:System.Web.Http.HttpConfiguration" /> of the current <see cref="T:System.Web.Http.ApiController" />.</returns>
    </member>
    <member name="P:System.Web.Http.ApiController.ControllerContext">
      <summary>Gets the <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> of the current <see cref="T:System.Web.Http.ApiController" />.</summary>
      <returns>The <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> of the current <see cref="T:System.Web.Http.ApiController" />.</returns>
    </member>
    <member name="M:System.Web.Http.ApiController.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="M:System.Web.Http.ApiController.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are used by the object and, optionally, releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Web.Http.ApiController.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Threading.CancellationToken)">
      <summary>Executes asynchronously a single HTTP operation.</summary>
      <returns>The newly started task.</returns>
      <param name="controllerContext">The controller context for a single HTTP operation.</param>
      <param name="cancellationToken">The cancellation token assigned for the HTTP operation.</param>
    </member>
    <member name="M:System.Web.Http.ApiController.Initialize(System.Web.Http.Controllers.HttpControllerContext)">
      <summary>Initializes the <see cref="T:System.Web.Http.ApiController" /> instance with the specified <paramref name="controllerContext" />.</summary>
      <param name="controllerContext">The <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> object that is used for the initialization.</param>
    </member>
    <member name="P:System.Web.Http.ApiController.ModelState">
      <summary>Gets the model state after the model binding process.</summary>
      <returns>The model state after the model binding process.</returns>
    </member>
    <member name="P:System.Web.Http.ApiController.Request">
      <summary>Gets or sets the <see cref="T:System.Net.Http.HttpRequestMessage" /> of the current <see cref="T:System.Web.Http.ApiController" />.</summary>
      <returns>The <see cref="T:System.Net.Http.HttpRequestMessage" /> of the current <see cref="T:System.Web.Http.ApiController" />.</returns>
    </member>
    <member name="P:System.Web.Http.ApiController.Url">
      <summary>Returns an instance of a <see cref="T:System.Web.Http.Routing.UrlHelper" />, which is used to generate URLs to other APIs.</summary>
      <returns>A <see cref="T:System.Web.Http.Routing.UrlHelper" /> object which is used to generate URLs to other APIs.</returns>
    </member>
    <member name="P:System.Web.Http.ApiController.User">
      <summary> Returns the current principal associated with this request. </summary>
      <returns>The current principal associated with this request.</returns>
    </member>
    <member name="T:System.Web.Http.AuthorizeAttribute">
      <summary>Specifies the authorization filter that verifies the request's <see cref="T:System.Security.Principal.IPrincipal" />.</summary>
    </member>
    <member name="M:System.Web.Http.AuthorizeAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.AuthorizeAttribute" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.AuthorizeAttribute.HandleUnauthorizedRequest(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Processes requests that fail authorization.</summary>
      <param name="actionContext">The context.</param>
    </member>
    <member name="M:System.Web.Http.AuthorizeAttribute.IsAuthorized(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Indicates whether the specified control is authorized.</summary>
      <returns>true if the control is authorized; otherwise, false.</returns>
      <param name="actionContext">The context.</param>
    </member>
    <member name="M:System.Web.Http.AuthorizeAttribute.OnAuthorization(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Calls when an action is being authorized.</summary>
      <param name="actionContext">The context.</param>
      <exception cref="T:System.ArgumentNullException">The context parameter is null.</exception>
    </member>
    <member name="P:System.Web.Http.AuthorizeAttribute.Roles">
      <summary>Gets or sets the authorized roles. </summary>
      <returns>The roles string. </returns>
    </member>
    <member name="P:System.Web.Http.AuthorizeAttribute.TypeId">
      <summary>Gets a unique identifier for this attribute.</summary>
      <returns>A unique identifier for this attribute.</returns>
    </member>
    <member name="P:System.Web.Http.AuthorizeAttribute.Users">
      <summary>Gets or sets the authorized users. </summary>
      <returns>The users string. </returns>
    </member>
    <member name="T:System.Web.Http.FromBodyAttribute">
      <summary> An attribute that specifies that an action parameter comes only from the entity body of the incoming <see cref="T:System.Net.Http.HttpRequestMessage" />. </summary>
    </member>
    <member name="M:System.Web.Http.FromBodyAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.FromBodyAttribute" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.FromBodyAttribute.GetBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>Gets a parameter binding.</summary>
      <returns>The parameter binding.</returns>
      <param name="parameter">The parameter description.</param>
    </member>
    <member name="T:System.Web.Http.FromUriAttribute">
      <summary>An attribute that specifies that an action parameter comes from the URI of the incoming <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
    </member>
    <member name="M:System.Web.Http.FromUriAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.FromUriAttribute" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.FromUriAttribute.GetValueProviderFactories(System.Web.Http.HttpConfiguration)">
      <summary>Gets the value provider factories for the model binder.</summary>
      <returns>A collection of <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> objects.</returns>
      <param name="configuration">The configuration.</param>
    </member>
    <member name="T:System.Web.Http.HttpBindNeverAttribute">
      <summary>Represents attributes that specifies that HTTP binding should exclude a property.</summary>
    </member>
    <member name="M:System.Web.Http.HttpBindNeverAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpBindNeverAttribute" /> class.</summary>
    </member>
    <member name="T:System.Web.Http.HttpBindRequiredAttribute">
      <summary>Represents the required attribute for http binding.</summary>
    </member>
    <member name="M:System.Web.Http.HttpBindRequiredAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpBindRequiredAttribute" /> class.</summary>
    </member>
    <member name="T:System.Web.Http.HttpConfiguration">
      <summary> Configuration of <see cref="T:System.Web.Http.HttpServer" /> instances. </summary>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpConfiguration" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.#ctor(System.Web.Http.HttpRouteCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpConfiguration" /> class with an HTTP route collection.</summary>
      <param name="routes">The HTTP route collection to associate with this instance.</param>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.DependencyResolver">
      <summary>Gets or sets the dependency resolver associated with thisinstance.</summary>
      <returns>The dependency resolver.</returns>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="M:System.Web.Http.HttpConfiguration.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are used by the object and, optionally, releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Filters">
      <summary>Gets the list of filters that apply to all requests served using this <see cref="T:System.Web.Http.HttpConfiguration" /> instance.</summary>
      <returns>The list of filters.</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Formatters">
      <summary>Gets the media-type formatters for this instance.</summary>
      <returns>A collection of <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> objects.</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.IncludeErrorDetailPolicy">
      <summary>Gets or sets a value indicating whether error details should be included in error messages.</summary>
      <returns>The <see cref="T:System.Web.Http.IncludeErrorDetailPolicy" /> value that indicates that error detail policy.</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Initializer">
      <summary> Gets or sets the action that will perform final initialization of the <see cref="T:System.Web.Http.HttpConfiguration" /> instance before it is used to process requests. </summary>
      <returns>The action that will perform final initialization of the <see cref="T:System.Web.Http.HttpConfiguration" /> instance.</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.MessageHandlers">
      <summary>Gets an ordered list of <see cref="T:System.Net.Http.DelegatingHandler" /> instances to be invoked as an <see cref="T:System.Net.Http.HttpRequestMessage" /> travels up the stack and an <see cref="T:System.Net.Http.HttpResponseMessage" /> travels down in stack in return. </summary>
      <returns>The message handler collection.</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.ParameterBindingRules">
      <summary>The collection of rules for how parameters should be bound.</summary>
      <returns>A collection of functions that can produce a parameter binding for a given parameter.</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Properties">
      <summary>Gets the properties associated with this instance.</summary>
      <returns>The <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2" />that contains the properties.</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Routes">
      <summary>Gets the <see cref="T:System.Web.Http.HttpRouteCollection" /> associated with this <see cref="T:System.Web.Http.HttpConfiguration" /> instance.</summary>
      <returns>The <see cref="T:System.Web.Http.HttpRouteCollection" />.</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.Services">
      <summary>Gets the container of default services associated with this instance.</summary>
      <returns>The <see cref="T:System.Web.Http.Controllers.ServicesContainer" /> that contains the default services for this instance.</returns>
    </member>
    <member name="P:System.Web.Http.HttpConfiguration.VirtualPathRoot">
      <summary>Gets the root virtual path.</summary>
      <returns>The root virtual path.</returns>
    </member>
    <member name="T:System.Web.Http.HttpConfigurationExtensions">
      <summary>Contains extension methods for the <see cref="T:System.Web.Http.HttpConfiguration" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.HttpConfigurationExtensions.BindParameter(System.Web.Http.HttpConfiguration,System.Type,System.Web.Http.ModelBinding.IModelBinder)">
      <summary> Register that the given parameter type on an Action is to be bound using the model binder. </summary>
      <param name="configuration">configuration to be updated.</param>
      <param name="type">parameter type that binder is applied to</param>
      <param name="binder">a model binder</param>
    </member>
    <member name="T:System.Web.Http.HttpDeleteAttribute">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.HttpDeleteAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpDeleteAttribute" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.HttpDeleteAttribute.HttpMethods">
      <summary>Gets a collection of HTTP methods.</summary>
      <returns>A collection of HTTP methods.</returns>
    </member>
    <member name="T:System.Web.Http.HttpError">
      <summary> Defines a serializable container for arbitrary error information. </summary>
    </member>
    <member name="M:System.Web.Http.HttpError.#ctor">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.HttpError" /> class. </summary>
    </member>
    <member name="M:System.Web.Http.HttpError.#ctor(System.Exception,System.Boolean)">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.HttpError" /> class for exception. </summary>
      <param name="exception">The exception to use for error information.</param>
      <param name="includeErrorDetail">  true to include the exception information in the error; false otherwise</param>
    </member>
    <member name="M:System.Web.Http.HttpError.#ctor(System.String)">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.HttpError" /> class containing error message message. </summary>
      <param name="message">The error message to associate with this instance.</param>
    </member>
    <member name="M:System.Web.Http.HttpError.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary,System.Boolean)">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.HttpError" /> class for modelState. </summary>
      <param name="modelState">The invalid model state to use for error information.</param>
      <param name="includeErrorDetail">  true to include exception messages in the error; false otherwise</param>
    </member>
    <member name="P:System.Web.Http.HttpError.Message">
      <summary> The error message associated with this instance. </summary>
    </member>
    <member name="M:System.Web.Http.HttpError.System#Xml#Serialization#IXmlSerializable#GetSchema">
      <summary>This method is reserved and should not be used.</summary>
      <returns>Always returns null.</returns>
    </member>
    <member name="M:System.Web.Http.HttpError.System#Xml#Serialization#IXmlSerializable#ReadXml(System.Xml.XmlReader)">
      <summary>Generates an <see cref="T:System.Web.Http.HttpError" /> instance from its XML representation.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> stream from which the object is deserialized.</param>
    </member>
    <member name="M:System.Web.Http.HttpError.System#Xml#Serialization#IXmlSerializable#WriteXml(System.Xml.XmlWriter)">
      <summary>Converts an <see cref="T:System.Web.Http.HttpError" /> instance into its XML representation.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> stream to which the object is serialized.</param>
    </member>
    <member name="T:System.Web.Http.HttpGetAttribute">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.HttpGetAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpGetAttribute" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.HttpGetAttribute.HttpMethods">
      <summary>Gets the collection of HTTP methods.</summary>
      <returns>A collection of HTTP methods.</returns>
    </member>
    <member name="T:System.Web.Http.HttpHeadAttribute">
      <summary>Represents an HTTP head attribute.</summary>
    </member>
    <member name="M:System.Web.Http.HttpHeadAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpHeadAttribute" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.HttpHeadAttribute.HttpMethods">
      <summary>Gets the collection of HTTP methods.</summary>
      <returns>A collection of HTTP methods.</returns>
    </member>
    <member name="T:System.Web.Http.HttpOptionsAttribute">
      <summary>Represents an attribute that is used to restrict an HTTP method so that the method handles only HTTP OPTIONS requests.</summary>
    </member>
    <member name="M:System.Web.Http.HttpOptionsAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpOptionsAttribute" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.HttpOptionsAttribute.HttpMethods">
      <summary>Gets the collection of methods supported by HTTP OPTIONS requests.</summary>
      <returns>The collection of methods supported by HTTP OPTIONS requests.</returns>
    </member>
    <member name="T:System.Web.Http.HttpPatchAttribute">
      <summary>Represents a HTTP patch attribute.</summary>
    </member>
    <member name="M:System.Web.Http.HttpPatchAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpPatchAttribute" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.HttpPatchAttribute.HttpMethods">
      <summary>Gets a collection of HTTP methods.</summary>
      <returns>A collection of HTTP methods.</returns>
    </member>
    <member name="T:System.Web.Http.HttpPostAttribute">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.HttpPostAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpPostAttribute" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.HttpPostAttribute.HttpMethods">
      <summary>Gets a collection of HTTP methods.</summary>
      <returns>A collection of HTTP methods.</returns>
    </member>
    <member name="T:System.Web.Http.HttpPutAttribute">
      <summary>Represents an attribute that is used to restrict an HTTP method so that the method handles only HTTP PUT requests.</summary>
    </member>
    <member name="M:System.Web.Http.HttpPutAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpPutAttribute" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.HttpPutAttribute.HttpMethods">
      <summary>Gets the read-only collection of HTTP PUT methods.</summary>
      <returns>The read-only collection of HTTP PUT methods.</returns>
    </member>
    <member name="T:System.Web.Http.HttpResponseException">
      <summary> An exception that allows for a given <see cref="T:System.Net.Http.HttpResponseMessage" /> to be returned to the client. </summary>
    </member>
    <member name="M:System.Web.Http.HttpResponseException.#ctor(System.Net.Http.HttpResponseMessage)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpResponseException" /> class.</summary>
      <param name="response">The HTTP response to return to the client.</param>
    </member>
    <member name="M:System.Web.Http.HttpResponseException.#ctor(System.Net.HttpStatusCode)">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.HttpResponseException" /> class. </summary>
      <param name="statusCode">The status code of the response.</param>
    </member>
    <member name="P:System.Web.Http.HttpResponseException.Response">
      <summary>Gets the HTTP response to return to the client.</summary>
      <returns>The <see cref="T:System.Net.Http.HttpResponseMessage" /> that represents the HTTP response.</returns>
    </member>
    <member name="T:System.Web.Http.HttpRouteCollection">
      <summary>A collection of <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instances.</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpRouteCollection" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpRouteCollection" /> class.</summary>
      <param name="virtualPathRoot">The virtual path root.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Add(System.String,System.Web.Http.Routing.IHttpRoute)">
      <summary>Adds an <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instance to the collection.</summary>
      <param name="name">The name of the route.</param>
      <param name="route">The <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instance to add to the collection.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Clear">
      <summary>Removes all items from  the collection.</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Contains(System.Web.Http.Routing.IHttpRoute)">
      <summary>Determines whether the collection contains a specific <see cref="T:System.Web.Http.Routing.IHttpRoute" />.</summary>
      <returns>true if the <see cref="T:System.Web.Http.Routing.IHttpRoute" /> is found in the collection; otherwise, false.</returns>
      <param name="item">The object to locate in the collection.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.ContainsKey(System.String)">
      <summary>Determines whether the collection contains an element with the specified key.</summary>
      <returns>true if the collection contains an element with the key; otherwise, false.</returns>
      <param name="name">The key to locate in the collection.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.Routing.IHttpRoute}[],System.Int32)">
      <summary>Copies the <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instances of the collection to an array, starting at a particular array index.</summary>
      <param name="array">The array that is the destination of the elements copied from the collection.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CopyTo(System.Web.Http.Routing.IHttpRoute[],System.Int32)">
      <summary>Copies the route names and <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instances of the collection to an array, starting at a particular array index.</summary>
      <param name="array">The array that is the destination of the elements copied from the collection.</param>
      <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.Count">
      <summary>Gets the number of items in the collection.</summary>
      <returns>The number of items in the collection.</returns>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CreateRoute(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Creates an <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instance.</summary>
      <returns>The new <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instance.</returns>
      <param name="routeTemplate">The route template.</param>
      <param name="defaults">An object that contains the default route parameters.</param>
      <param name="constraints">An object that contains the route constraints.</param>
      <param name="dataTokens">The route data tokens.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CreateRoute(System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Collections.Generic.IDictionary{System.String,System.Object},System.Net.Http.HttpMessageHandler)">
      <summary>Creates an <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instance.</summary>
      <returns>The new <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instance.</returns>
      <param name="routeTemplate">The route template.</param>
      <param name="defaults">An object that contains the default route parameters.</param>
      <param name="constraints">An object that contains the route constraints.</param>
      <param name="dataTokens">The route data tokens.</param>
      <param name="handler">The message handler for the route.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.CreateRoute(System.String,System.Object,System.Object)">
      <summary>Creates an <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instance.</summary>
      <returns>The new <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instance.</returns>
      <param name="routeTemplate">The route template.</param>
      <param name="defaults">An object that contains the default route parameters.</param>
      <param name="constraints">An object that contains the route constraints.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are used by the object and, optionally, releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.GetRouteData(System.Net.Http.HttpRequestMessage)">
      <summary>Gets the route data for a specified HTTP request.</summary>
      <returns>An<see cref="T:System.Web.Http.Routing.IHttpRouteData" /> instance that represents the route data.</returns>
      <param name="request">The HTTP request.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.GetVirtualPath(System.Net.Http.HttpRequestMessage,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Gets a virtual path.</summary>
      <returns>An <see cref="T:System.Web.Http.Routing.IHttpVirtualPathData" /> instance that represents the virtual path.</returns>
      <param name="request">The HTTP request.</param>
      <param name="name">The route name.</param>
      <param name="values">The route values.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Insert(System.Int32,System.String,System.Web.Http.Routing.IHttpRoute)">
      <summary>Inserts an <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instance into the collection.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> should be inserted.</param>
      <param name="name">The route name.</param>
      <param name="value">The <see cref="T:System.Web.Http.Routing.IHttpRoute" /> to insert. The value cannot be null.</param>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.IsReadOnly">
      <summary>Gets a value indicating whether the collection is read-only.</summary>
      <returns>true if the collection is read-only; otherwise, false.</returns>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <returns>The  <see cref="T:System.Web.Http.Routing.IHttpRoute" /> at the specified index.</returns>
      <param name="index">The zero-based index of the element to get or set.</param>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.Item(System.String)">
      <summary>Gets or sets the element with the specified route name.</summary>
      <returns>The  <see cref="T:System.Web.Http.Routing.IHttpRoute" /> at the specified index.</returns>
      <param name="name">The route name.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.OnGetEnumerator">
      <summary>Called internally to get the enumerator for the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.Remove(System.String)">
      <summary>Removes an <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instance from the collection.</summary>
      <returns>true if the element is successfully removed; otherwise, false. This method also returns false if <paramref name="name" /> was not found in the collection.</returns>
      <param name="name">The name of the route to remove.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.System#Collections#Generic#ICollection{T}#Add(System.Web.Http.Routing.IHttpRoute)">
      <summary>Adds an item to the collection.</summary>
      <param name="route">The object to add to the collection.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.System#Collections#Generic#ICollection{T}#Remove(System.Web.Http.Routing.IHttpRoute)">
      <summary>Removes the first occurrence of a specific object from the collection.</summary>
      <returns>true if <paramref name="route" /> was successfully removed from the collection; otherwise, false. This method also returns false if <paramref name="route" /> is not found in the original collection.</returns>
      <param name="route">The object to remove from the collection.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollection.TryGetValue(System.String,System.Web.Http.Routing.IHttpRoute@)">
      <summary>Gets the <see cref="T:System.Web.Http.Routing.IHttpRoute" /> with the specified route name.</summary>
      <returns>true if the collection contains an element with the specified name; otherwise, false.</returns>
      <param name="name">The route name.</param>
      <param name="route">When this method returns, contains the <see cref="T:System.Web.Http.Routing.IHttpRoute" /> instance, if the route name is found; otherwise, null. This parameter is passed uninitialized.</param>
    </member>
    <member name="P:System.Web.Http.HttpRouteCollection.VirtualPathRoot">
      <summary>Gets the virtual path root.</summary>
      <returns>The virtual path root.</returns>
    </member>
    <member name="T:System.Web.Http.HttpRouteCollectionExtensions">
      <summary> Extension methods for <see cref="T:System.Web.Http.HttpRouteCollection" /></summary>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpRoute(System.Web.Http.HttpRouteCollection,System.String,System.String)">
      <summary>Maps the specified route template.</summary>
      <returns>A reference to the mapped route.</returns>
      <param name="routes">A collection of routes for the application.</param>
      <param name="name">The name of the route to map.</param>
      <param name="routeTemplate">The route template for the route.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Object)">
      <summary>Maps the specified route template and sets default route values.</summary>
      <returns>A reference to the mapped route.</returns>
      <param name="routes">A collection of routes for the application.</param>
      <param name="name">The name of the route to map.</param>
      <param name="routeTemplate">The route template for the route.</param>
      <param name="defaults">An object that contains default route values.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Object,System.Object)">
      <summary>Maps the specified route template and sets default route values and constraints.</summary>
      <returns>A reference to the mapped route.</returns>
      <param name="routes">A collection of routes for the application.</param>
      <param name="name">The name of the route to map.</param>
      <param name="routeTemplate">The route template for the route.</param>
      <param name="defaults">An object that contains default route values.</param>
      <param name="constraints">A set of expressions that constrain the values for routeTemplate.</param>
    </member>
    <member name="M:System.Web.Http.HttpRouteCollectionExtensions.MapHttpRoute(System.Web.Http.HttpRouteCollection,System.String,System.String,System.Object,System.Object,System.Net.Http.HttpMessageHandler)">
      <summary> Maps the specified route template and sets default route values, constraints, and end-point message handler. </summary>
      <returns>A reference to the mapped route.</returns>
      <param name="routes">A collection of routes for the application.</param>
      <param name="name">The name of the route to map.</param>
      <param name="routeTemplate">The route template for the route.</param>
      <param name="defaults">An object that contains default route values.</param>
      <param name="constraints">A set of expressions that constrain the values for routeTemplate.</param>
      <param name="handler">The handler to which the request will be dispatched.</param>
    </member>
    <member name="T:System.Web.Http.HttpServer">
      <summary> Defines an implementation of an <see cref="T:System.Net.Http.HttpMessageHandler" /> which dispatches an  incoming <see cref="T:System.Net.Http.HttpRequestMessage" /> and creates an <see cref="T:System.Net.Http.HttpResponseMessage" /> as a result. </summary>
    </member>
    <member name="M:System.Web.Http.HttpServer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpServer" /> class, using the default configuration and dispatcher.</summary>
    </member>
    <member name="M:System.Web.Http.HttpServer.#ctor(System.Net.Http.HttpMessageHandler)">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.HttpServer" /> class with a specified dispatcher. </summary>
      <param name="dispatcher">The HTTP dispatcher that will handle incoming requests.</param>
    </member>
    <member name="M:System.Web.Http.HttpServer.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpServer" /> class with a specified configuration.</summary>
      <param name="configuration">The <see cref="T:System.Web.Http.HttpConfiguration" /> used to configure this instance.</param>
    </member>
    <member name="M:System.Web.Http.HttpServer.#ctor(System.Web.Http.HttpConfiguration,System.Net.Http.HttpMessageHandler)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HttpServer" /> class with a specified configuration and dispatcher.</summary>
      <param name="configuration">The <see cref="T:System.Web.Http.HttpConfiguration" /> used to configure this instance.</param>
      <param name="dispatcher">The HTTP dispatcher that will handle incoming requests.</param>
    </member>
    <member name="P:System.Web.Http.HttpServer.Configuration">
      <summary>Gets the <see cref="T:System.Web.Http.HttpConfiguration" /> used to configure this instance.</summary>
      <returns>The <see cref="T:System.Web.Http.HttpConfiguration" /> used to configure this instance.</returns>
    </member>
    <member name="P:System.Web.Http.HttpServer.Dispatcher">
      <summary>Gets the HTTP dispatcher that handles incoming requests.</summary>
      <returns>The HTTP dispatcher that handles incoming requests.</returns>
    </member>
    <member name="M:System.Web.Http.HttpServer.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources that are used by the object and, optionally, releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Web.Http.HttpServer.Initialize">
      <summary>Prepares the server for operation.</summary>
    </member>
    <member name="M:System.Web.Http.HttpServer.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Dispatches an incoming <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns> A task representing the asynchronous operation.</returns>
      <param name="request">The request to dispatch.</param>
      <param name="cancellationToken">The token to monitor for cancellation requests.</param>
    </member>
    <member name="T:System.Web.Http.IncludeErrorDetailPolicy">
      <summary>Specifies whether error details, such as exception messages and stack traces, should be included in error messages.</summary>
    </member>
    <member name="F:System.Web.Http.IncludeErrorDetailPolicy.Default">
      <summary>Use the default behavior for the host environment. For ASP.NET hosting, use the value from the customErrors element in the Web.config file. For self-hosting, use the value <see cref="F:System.Web.Http.IncludeErrorDetailPolicy.LocalOnly" />.</summary>
    </member>
    <member name="F:System.Web.Http.IncludeErrorDetailPolicy.LocalOnly">
      <summary>Only include error details when responding to a local request. </summary>
    </member>
    <member name="F:System.Web.Http.IncludeErrorDetailPolicy.Always">
      <summary>Always include error details.</summary>
    </member>
    <member name="F:System.Web.Http.IncludeErrorDetailPolicy.Never">
      <summary>Never include error details. </summary>
    </member>
    <member name="T:System.Web.Http.NonActionAttribute">
      <summary>Represents an attribute that is used to indicate that a controller method is not an action method.</summary>
    </member>
    <member name="M:System.Web.Http.NonActionAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.NonActionAttribute" /> class.</summary>
    </member>
    <member name="T:System.Web.Http.ParameterBindingAttribute">
      <summary> Attribute on a parameter or type that produces a <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" />.  If the attribute is on a type-declaration, then it's as if that attribute is present on all action parameters  of that type. </summary>
    </member>
    <member name="M:System.Web.Http.ParameterBindingAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ParameterBindingAttribute" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ParameterBindingAttribute.GetBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>Gets the parameter binding.</summary>
      <returns>The parameter binding.</returns>
      <param name="parameter">The parameter description.</param>
    </member>
    <member name="T:System.Web.Http.QueryableAttribute">
      <summary>Enables a controller action to support OData query parameters.</summary>
    </member>
    <member name="M:System.Web.Http.QueryableAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.QueryableAttribute" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.QueryableAttribute.ApplyResultLimit(System.Web.Http.Filters.HttpActionExecutedContext,System.Linq.IQueryable)">
      <summary>Applies the result limit to the query results.</summary>
      <returns>The query results after the result limit is applied.</returns>
      <param name="actionExecutedContext">The context for the action.</param>
      <param name="query">The original query results.</param>
    </member>
    <member name="M:System.Web.Http.QueryableAttribute.OnActionExecuted(System.Web.Http.Filters.HttpActionExecutedContext)">
      <summary>Called by the Web API framework after the action method executes.</summary>
      <param name="actionExecutedContext">The filter context.</param>
    </member>
    <member name="M:System.Web.Http.QueryableAttribute.OnActionExecuting(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Called by the Web API framework before the action method executes.</summary>
      <param name="actionContext">The filter context.</param>
    </member>
    <member name="P:System.Web.Http.QueryableAttribute.ResultLimit">
      <summary>The maximum number of results that should be returned from this query regardless of query-specified limits.</summary>
      <returns>The maximum number of results that should be returned. A value of zero indicates no limit.</returns>
    </member>
    <member name="P:System.Web.Http.QueryableAttribute.StructuredQueryBuilder">
      <summary> The <see cref="T:System.Web.Http.Query.IStructuredQueryBuilder" /> to use. Derived classes can use this to have a per-attribute query builder  instead of the one on <see cref="T:System.Web.Http.HttpConfiguration" /></summary>
    </member>
    <member name="T:System.Web.Http.RouteParameter">
      <summary>The <see cref="T:System.Web.Http.RouteParameter" /> class can be used to indicate properties about a route parameter (the literals and placeholders  located within segments of a <see cref="M:IHttpRoute.RouteTemplate" />).  It can for example be used to indicate that a route parameter is optional. </summary>
    </member>
    <member name="F:System.Web.Http.RouteParameter.Optional">
      <summary>An optional parameter.</summary>
    </member>
    <member name="M:System.Web.Http.RouteParameter.ToString">
      <summary>Returns a <see cref="T:System.String" /> that represents this instance.</summary>
      <returns>A <see cref="T:System.String" /> that represents this instance.</returns>
    </member>
    <member name="T:System.Web.Http.ServicesExtensions">
      <summary>Provides type-safe accessors for services obtained from a <see cref="T:System.Web.Http.Controllers.ServicesContainer" />object.</summary>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetActionInvoker(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Controllers.IHttpActionInvoker" /> service.</summary>
      <returns>Returns an <see cref="T:System.Web.Http.Controllers.IHttpActionInvoker" /> instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetActionSelector(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Controllers.IHttpActionSelector" /> service.</summary>
      <returns>Returns an<see cref="T:System.Web.Http.Controllers.IHttpActionSelector" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetActionValueBinder(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Controllers.IActionValueBinder" /> service.</summary>
      <returns>Returns an<see cref="T:System.Web.Http.Controllers.IActionValueBinder" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetApiExplorer(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Description.IApiExplorer" /> service.</summary>
      <returns>Returns an <see cref="T:System.Web.Http.Description.IApiExplorer" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetAssembliesResolver(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Dispatcher.IAssembliesResolver" /> service.</summary>
      <returns>Returns an<see cref="T:System.Web.Http.Dispatcher.IAssembliesResolver" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetBodyModelValidator(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Validation.IBodyModelValidator" /> service.</summary>
      <returns>Returns an<see cref="T:System.Web.Http.Validation.IBodyModelValidator" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetContentNegotiator(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Net.Http.Formatting.IContentNegotiator" /> service.</summary>
      <returns>Returns an<see cref="T:System.Net.Http.Formatting.IContentNegotiator" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetDocumentationProvider(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Description.IDocumentationProvider" /> service.</summary>
      <returns>Returns an<see cref="T:System.Web.Http.Description.IDocumentationProvider" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetFilterProviders(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Filters.IFilterProvider" /> collection.</summary>
      <returns>Returns a collection of<see cref="T:System.Web.Http.Filters.IFilterProvider" /> objects.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetHostBufferPolicySelector(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /> service.</summary>
      <returns>Returns an <see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetHttpControllerActivator(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" /> service.</summary>
      <returns>Returns an <see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" />instance, or null if no instance was registered.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetHttpControllerSelector(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Dispatcher.IHttpControllerSelector" /> service.</summary>
      <returns>Returns an<see cref="T:System.Web.Http.Dispatcher.IHttpControllerSelector" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetHttpControllerTypeResolver(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver" /> service.</summary>
      <returns>Returns an <see cref="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetModelBinderProviders(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> collection.</summary>
      <returns>Returns a collection of <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> objects.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetModelMetadataProvider(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /> service.</summary>
      <returns>Returns an  <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetModelValidatorProviders(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> collection.</summary>
      <returns>Returns a collection of<see cref="T:System.Web.Http.Validation.ModelValidatorProvider" />objects.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetStructuredQueryBuilder(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Query.IStructuredQueryBuilder" /> service.</summary>
      <returns>Returns an<see cref="T:System.Web.Http.Query.IStructuredQueryBuilder" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetTraceManager(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Tracing.ITraceManager" /> service.</summary>
      <returns>Returns an<see cref="T:System.Web.Http.Tracing.ITraceManager" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetTraceWriter(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.Tracing.ITraceWriter" />service. </summary>
      <returns>Returns an<see cref="T:System.Web.Http.Tracing.ITraceWriter" />instance.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="M:System.Web.Http.ServicesExtensions.GetValueProviderFactories(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Gets the <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> collection.</summary>
      <returns>Returns  a colleciton of<see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" />objects.</returns>
      <param name="services">The services container.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ApiControllerActionInvoker">
      <summary>Invokes the action methods of a controller.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionInvoker.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.ApiControllerActionInvoker" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionInvoker.InvokeActionAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>Asynchronously invokes the specified action by using the specified controller context.</summary>
      <returns>The invoked action.</returns>
      <param name="actionContext">The controller context.</param>
      <param name="cancellationToken">The cancellation token.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ApiControllerActionSelector">
      <summary>Represents a reflection based action selector.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionSelector.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.ApiControllerActionSelector" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionSelector.GetActionMapping(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>Gets the action mappings for the <see cref="T:System.Web.Http.Controllers.ApiControllerActionSelector" />.</summary>
      <returns>The action mappings.</returns>
      <param name="controllerDescriptor">The information that describes a controller.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ApiControllerActionSelector.SelectAction(System.Web.Http.Controllers.HttpControllerContext)">
      <summary>Selects an action for the <see cref="T:System.Web.Http.Controllers.ApiControllerActionSelector" />.</summary>
      <returns>The selected action.</returns>
      <param name="controllerContext">The controller context.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ControllerServices">
      <summary> Represents a container for services that can be specific to a controller.  This shadows the services from its parent <see cref="T:System.Web.Http.Controllers.ServicesContainer" />. A controller can either set a service here, or fall through  to the more global set of services.  </summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.#ctor(System.Web.Http.Controllers.ServicesContainer)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.ControllerServices" /> class.</summary>
      <param name="parent">The parent services container.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.ClearSingle(System.Type)">
      <summary>Removes a single-instance service from the default services.</summary>
      <param name="serviceType">The type of service.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.GetService(System.Type)">
      <summary>Gets a service of the specified type.</summary>
      <returns>The first instance of the service, or null if the service is not found.</returns>
      <param name="serviceType">The type of service.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.GetServiceInstances(System.Type)">
      <summary>Gets the list of service objects for a given service type, and validates the service type.</summary>
      <returns>The list of service objects of the specified type.</returns>
      <param name="serviceType">The service type.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.GetServices(System.Type)">
      <summary>Gets the list of service objects for a given service type.</summary>
      <returns>The list of service objects of the specified type, or an empty list if the service is not found.</returns>
      <param name="serviceType">The type of service.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.IsSingleService(System.Type)">
      <summary>Queries whether a service type is single-instance.</summary>
      <returns>true if the service type has at most one instance, or false if the service type supports multiple instances.</returns>
      <param name="serviceType">The service type.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ControllerServices.ReplaceSingle(System.Type,System.Object)">
      <summary>Replaces a single-instance service object.</summary>
      <param name="serviceType">The service type.</param>
      <param name="service">The service object that replaces the previous instance.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpActionBinding">
      <summary>Describes *how* the binding will happen and does not actually bind.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionBinding.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpActionBinding" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionBinding.#ctor(System.Web.Http.Controllers.HttpActionDescriptor,System.Web.Http.Controllers.HttpParameterBinding[])">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpActionBinding" /> class.</summary>
      <param name="actionDescriptor">The back pointer to the action this binding is for.  </param>
      <param name="bindings">The synchronous bindings for each parameter.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionBinding.ActionDescriptor">
      <summary>Gets or sets the back pointer to the action this binding is for.  </summary>
      <returns>The back pointer to the action this binding is for.  </returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionBinding.ExecuteBindingAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>Executes asynchronously the binding for the given request context.</summary>
      <returns>Task that is signaled when the binding is complete. </returns>
      <param name="actionContext">The action context for the binding. This contains the parameter dictionary that will get populated.</param>
      <param name="cancellationToken">The cancellation token for cancelling the binding operation. Or a binder can also bind a parameter to this.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionBinding.ParameterBindings">
      <summary>Gets or sets the synchronous bindings for each parameter. </summary>
      <returns>The synchronous bindings for each parameter.</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpActionContext">
      <summary>Contains information for the executing action.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContext.#ctor">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> class. </summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContext.#ctor(System.Web.Http.Controllers.HttpControllerContext,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> class.</summary>
      <param name="controllerContext">The controller context.</param>
      <param name="actionDescriptor">The action descriptor.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.ActionArguments">
      <summary>Gets a list of action arguments.</summary>
      <returns>A list of action arguments.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.ActionDescriptor">
      <summary>Gets or sets the action descriptor for the action context.</summary>
      <returns>The action descriptor.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.ControllerContext">
      <summary>Gets or sets the controller context.</summary>
      <returns>The controller context.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.ModelState">
      <summary>Gets the model state dictionary for the context.</summary>
      <returns>The model state dictionary.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.Request">
      <summary>Gets the request message for the action context.</summary>
      <returns>The request message for the action context.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionContext.Response">
      <summary>Gets or sets the response message for the action context.</summary>
      <returns>The response message for the action context.</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpActionContextExtensions">
      <summary>Contains extension methods for <see cref="T:System.Web.Http.Controllers.HttpActionContext" />.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.Bind(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Binds the model to a value by using the specified controller context and binding context.</summary>
      <returns>true if the bind succeeded; otherwise, false.</returns>
      <param name="actionContext">The execution context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.Bind(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Collections.Generic.IEnumerable{System.Web.Http.ModelBinding.IModelBinder})">
      <summary>Binds the model to a value by using the specified controller context, binding context, and model binders.</summary>
      <returns>true if the bind succeeded; otherwise, false.</returns>
      <param name="actionContext">The execution context.</param>
      <param name="bindingContext">The binding context.</param>
      <param name="binders">The collection of model binders.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.GetMetadataProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Retrieves the <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /> instance for a given <see cref="T:System.Web.Http.Controllers.HttpActionContext" />. </summary>
      <returns>An <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /> instance.</returns>
      <param name="actionContext">The context.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.GetValidatorProviders(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Retrieves the collection of registered <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> instances.</summary>
      <returns>A collection of <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> instances.</returns>
      <param name="actionContext">The context.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.GetValidators(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.Metadata.ModelMetadata)">
      <summary>Retrieves the collection of registered <see cref="T:System.Web.Http.Validation.ModelValidator" /> instances. </summary>
      <returns>A collection of registered <see cref="T:System.Web.Http.Validation.ModelValidator" /> instances.</returns>
      <param name="actionContext">The context.</param>
      <param name="metadata">The metadata.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionContextExtensions.TryBindStrongModel``1(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.String,System.Web.Http.Metadata.ModelMetadataProvider,``0@)">
      <summary>Binds the model to the property by using the specified execution context and binding context.</summary>
      <returns>true if the bind succeeded; otherwise, false.</returns>
      <param name="actionContext">The execution context.</param>
      <param name="parentBindingContext">The parent binding context.</param>
      <param name="propertyName">The name of the property to bind with the model.</param>
      <param name="metadataProvider">The metadata provider for the model.</param>
      <param name="model">When this method returns, contains the bound model.</param>
      <typeparam name="TModel">The type of the model.</typeparam>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpActionDescriptor">
      <summary>Provides information about the action methods.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.#ctor(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> class with specified information that describes the controller of the action.</summary>
      <param name="controllerDescriptor">The information that describes the controller of the action.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ActionBinding">
      <summary>Gets or sets the binding that describes the action.</summary>
      <returns>The binding that describes the action.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ActionName">
      <summary>Gets the name of the action.</summary>
      <returns>The name of the action.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.Configuration">
      <summary>Gets or sets the action configuration.</summary>
      <returns>The action configuration.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ControllerDescriptor">
      <summary>Gets the information that describes the controller of the action.</summary>
      <returns>The information that describes the controller of the action.</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.CancellationToken)">
      <summary>Executes the described action and returns a <see cref="T:System.Threading.Tasks.Task`1" /> that once completed will contain the return value of the action.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that once completed will contain the return value of the action.</returns>
      <param name="controllerContext">The controller context.</param>
      <param name="arguments">A list of arguments.</param>
      <param name="cancellationToken">The cancellation token.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetCustomAttributes``1">
      <summary>Returns the custom attributes associated with the action descriptor.</summary>
      <returns>The custom attributes associated with the action descriptor.</returns>
      <typeparam name="T">The action descriptor.</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetFilterPipeline">
      <summary>Retrieves the filters for the given configuration and action.</summary>
      <returns>The filters for the given configuration and action.</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetFilters">
      <summary>Retrieves the filters for the action descriptor.</summary>
      <returns>The filters for the action descriptor.</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpActionDescriptor.GetParameters">
      <summary>Retrieves the parameters for the action descriptor.</summary>
      <returns>The parameters for the action descriptor.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.Properties">
      <summary>Gets the properties associated with this instance.</summary>
      <returns>The properties associated with this instance.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ResultConverter">
      <summary>Gets the converter for correctly transforming the result of calling <see cref="M:System.Web.Http.Controllers.HttpActionDescriptor.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Collections.Generic.IDictionary{System.String,System.Object})" />" into an instance of <see cref="T:System.Net.Http.HttpResponseMessage" />.</summary>
      <returns>The action result converter.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.ReturnType">
      <summary>Gets the return type of the descriptor.</summary>
      <returns>The return type of the descriptor.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpActionDescriptor.SupportedHttpMethods">
      <summary>Gets the collection of supported HTTP methods for the descriptor.</summary>
      <returns>The collection of supported HTTP methods for the descriptor.</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpControllerContext">
      <summary>Contains information for a single HTTP operation.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerContext.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerContext.#ctor(System.Web.Http.HttpConfiguration,System.Web.Http.Routing.IHttpRouteData,System.Net.Http.HttpRequestMessage)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpControllerContext" /> class.</summary>
      <param name="configuration">The configuration.</param>
      <param name="routeData">The route data.</param>
      <param name="request">The request.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.Configuration">
      <summary>Gets or sets the configuration.</summary>
      <returns>The configuration.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.Controller">
      <summary>Gets or sets the HTTP controller.</summary>
      <returns>The HTTP controller.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.ControllerDescriptor">
      <summary>Gets or sets the controller descriptor.</summary>
      <returns>The controller descriptor.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.Request">
      <summary>Gets or sets the request.</summary>
      <returns>The request.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerContext.RouteData">
      <summary>Gets or sets the route data.</summary>
      <returns>The route data.</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpControllerDescriptor">
      <summary>Represents information that describes the HTTP controller.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.#ctor(System.Web.Http.HttpConfiguration,System.String,System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> class.</summary>
      <param name="configuration">The configuration.</param>
      <param name="controllerName">The controller name.</param>
      <param name="controllerType">The controller type.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerDescriptor.Configuration">
      <summary>Gets or sets the configurations associated with the controller.</summary>
      <returns>The configurations associated with the controller.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerDescriptor.ControllerName">
      <summary>Gets or sets the name of the controller.</summary>
      <returns>The name of the controller.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerDescriptor.ControllerType">
      <summary>Gets or sets the type of the controller.</summary>
      <returns>The type of the controller.</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.CreateController(System.Net.Http.HttpRequestMessage)">
      <summary>Creates a controller instance for the given <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>The created controller instance.</returns>
      <param name="request">The request message</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.GetCustomAttributes``1">
      <summary>Retrieves a collection of custom attributes of the controller.</summary>
      <returns>A collection of custom attributes</returns>
      <typeparam name="T">The type of the object.</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerDescriptor.GetFilters">
      <summary>Returns a collection of filters associated with the controller.</summary>
      <returns>A collection of filters associated with the controller.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerDescriptor.Properties">
      <summary>Gets the properties associated with this instance.</summary>
      <returns>The properties associated with this instance.</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpControllerSettings">
      <summary>Contains settings for an HTTP controller.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpControllerSettings.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpControllerSettings" /> class.</summary>
      <param name="configuration">A configuration object that is used to initialize the instance.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerSettings.Formatters">
      <summary>Gets the collection of <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> instances for the controller.</summary>
      <returns>The collection of <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> instances.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerSettings.ParameterBindingRules">
      <summary>Gets the collection of parameter bindingfunctions for for the controller.</summary>
      <returns>The collection of parameter binding functions.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpControllerSettings.Services">
      <summary>Gets the collection of service instances for the controller.</summary>
      <returns>The collection of service instances.</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpParameterBinding">
      <summary> Describes how a parameter is bound. The binding should be static (based purely on the descriptor) and  can be shared across requests.  </summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> class.</summary>
      <param name="descriptor">An <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> that describes the parameters.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterBinding.Descriptor">
      <summary>Gets the <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> that was used to initialize this instance.</summary>
      <returns>The <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> instance.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterBinding.ErrorMessage">
      <summary>If the binding is invalid, gets an error message that describes the binding error.</summary>
      <returns>An error message. If the binding was successful, the value is null.</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>Asynchronously executes the binding for the given request.</summary>
      <returns>A task object representing the asynchronous operation.</returns>
      <param name="metadataProvider">Metadata provider to use for validation.</param>
      <param name="actionContext">The action context for the binding. The action context contains the parameter dictionary that will get populated with the parameter.</param>
      <param name="cancellationToken">Cancellation token for cancelling the binding operation.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterBinding.GetValue(System.Web.Http.Controllers.HttpActionContext)">
      <summary> Gets the parameter value from argument dictionary of the action context.</summary>
      <returns>The value for this parameter in the given action context, or null if the parameter has not yet been set.</returns>
      <param name="actionContext">The action context.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterBinding.IsValid">
      <summary>Gets a value that indicates whether the binding was successful.</summary>
      <returns>true if the binding was successful; otherwise, false.</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterBinding.SetValue(System.Web.Http.Controllers.HttpActionContext,System.Object)">
      <summary>Sets the result of this parameter binding in the argument dictionary of the action context.  </summary>
      <param name="actionContext">The action context.</param>
      <param name="value">The parameter value.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterBinding.WillReadBody">
      <summary>Returns a value indicating whether this <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> instance will read the entity body of the HTTP message.</summary>
      <returns>true if this <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> will read the entity body; otherwise, false.</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.HttpParameterDescriptor">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterDescriptor.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterDescriptor.#ctor(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" /> class.</summary>
      <param name="actionDescriptor">The action descriptor.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.ActionDescriptor">
      <summary>Gets or sets the action descriptor.</summary>
      <returns>The action descriptor.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.Configuration">
      <summary>Gets or sets the <see cref="T:System.Web.Http.HttpConfiguration" /> for the <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" />.</summary>
      <returns>The <see cref="T:System.Web.Http.HttpConfiguration" /> for the <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" />.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.DefaultValue">
      <summary>Gets the default value of the parameter.</summary>
      <returns>The default value of the parameter.</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.HttpParameterDescriptor.GetCustomAttributes``1">
      <summary>Retrieves a collection of the custom attributes from the parameter.</summary>
      <returns>A collection of the custom attributes from the parameter.</returns>
      <typeparam name="T">The type of the custom attributes.</typeparam>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.IsOptional">
      <summary>Gets a value that indicates whether the parameter is optional.</summary>
      <returns>true if the parameter is optional; otherwise, false..</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.ParameterBinderAttribute">
      <summary>Gets or sets the parameter binding attribute.</summary>
      <returns>The parameter binding attribute.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.ParameterName">
      <summary>Gets the name of the parameter.</summary>
      <returns>The name of the parameter.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.ParameterType">
      <summary>Gets the type of the parameter.</summary>
      <returns>The type of the parameter.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.Prefix">
      <summary>Gets the prefix of this parameter.</summary>
      <returns>The prefix of this parameter.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.HttpParameterDescriptor.Properties">
      <summary>Gets the properties of this parameter.</summary>
      <returns>The properties of this parameter.</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.IActionResultConverter">
      <summary> A contract for a conversion routine that can take the result of an action returned from &lt;see cref="M:System.Web.Http.Controllers.HttpActionDescriptor.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Collections.Generic.IDictionary{System.String,System.Object})" /&gt; and convert it to an instance of <see cref="T:System.Net.Http.HttpResponseMessage" />. </summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IActionResultConverter.Convert(System.Web.Http.Controllers.HttpControllerContext,System.Object)">
      <summary>Converts the specified <see cref="T:System.Web.Http.Controllers.IActionResultConverter" /> object to another object.</summary>
      <returns>The converted object.</returns>
      <param name="controllerContext">The controller context.</param>
      <param name="actionResult">The action result.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IActionValueBinder">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IActionValueBinder.GetBinding(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>Gets the <see cref="T:System.Web.Http.Controllers.HttpActionBinding" /></summary>
      <returns>A <see cref="T:System.Web.Http.Controllers.HttpActionBinding" /> object.</returns>
      <param name="actionDescriptor">The action descriptor.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IControllerConfiguration">
      <summary> If a controller is decorated with an attribute with this interface, then it gets invoked to initialize the controller settings.  </summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IControllerConfiguration.Initialize(System.Web.Http.Controllers.HttpControllerSettings,System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary> Callback invoked to set per-controller overrides for this controllerDescriptor. </summary>
      <param name="controllerSettings">The controller settings to initialize.</param>
      <param name="controllerDescriptor">The controller descriptor. Note that the <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> can be associated with the derived controller type given that <see cref="T:System.Web.Http.Controllers.IControllerConfiguration" /> is inherited.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IHttpActionInvoker">
      <summary>Contains method that is used to invoke HTTP operation.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IHttpActionInvoker.InvokeActionAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>Executes asynchronously the HTTP operation.</summary>
      <returns>The newly started task.</returns>
      <param name="actionContext">The execution context.</param>
      <param name="cancellationToken">The cancellation token assigned for the HTTP operation.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IHttpActionSelector">
      <summary>Contains the logic for selecting an action method.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IHttpActionSelector.GetActionMapping(System.Web.Http.Controllers.HttpControllerDescriptor)">
      <summary>Returns a map, keyed by action string, of all <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> that the selector can select.  This is primarily called by <see cref="T:System.Web.Http.Description.IApiExplorer" /> to discover all the possible actions in the controller.</summary>
      <returns>A map of <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" /> that the selector can select, or null if the selector does not have a well-defined mapping of <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" />.</returns>
      <param name="controllerDescriptor">The controller descriptor.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.IHttpActionSelector.SelectAction(System.Web.Http.Controllers.HttpControllerContext)">
      <summary>Selects the action for the controller.</summary>
      <returns>The action for the controller.</returns>
      <param name="controllerContext">The context of the controller.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.IHttpController">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.IHttpController.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Threading.CancellationToken)">
      <summary>Executes the controller for synchronization.</summary>
      <returns>The controller.</returns>
      <param name="controllerContext">The current context for a test controller.</param>
      <param name="cancellationToken">The notification that cancels the operation.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ParameterBindingExtensions">
      <summary>Defines extension methods for <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" />.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindAsError(System.Web.Http.Controllers.HttpParameterDescriptor,System.String)">
      <summary>Binds parameter that results as an error. </summary>
      <returns>The HTTP parameter binding object.</returns>
      <param name="parameter">The parameter descriptor that describes the parameter to bind.</param>
      <param name="message">The error message that describes the reason for fail bind.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithAttribute(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ParameterBindingAttribute)">
      <summary>Bind the parameter as if it had the given attribute on the declaration.</summary>
      <returns>The HTTP parameter binding object.</returns>
      <param name="parameter">The parameter to provide binding for.</param>
      <param name="attribute">The attribute that describes the binding.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithFormatter(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>Binds parameter by parsing the HTTP body content.</summary>
      <returns>The HTTP parameter binding object.</returns>
      <param name="parameter">The parameter descriptor that describes the parameter to bind.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithFormatter(System.Web.Http.Controllers.HttpParameterDescriptor,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter})">
      <summary>Binds parameter by parsing the HTTP body content.</summary>
      <returns>The HTTP parameter binding object.</returns>
      <param name="parameter">The parameter descriptor that describes the parameter to bind.</param>
      <param name="formatters">The list of formatters which provides selection of an appropriate formatter for serializing the parameter into object.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithFormatter(System.Web.Http.Controllers.HttpParameterDescriptor,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Web.Http.Validation.IBodyModelValidator)">
      <summary>Binds parameter by parsing the HTTP body content.</summary>
      <returns>The HTTP parameter binding object.</returns>
      <param name="parameter">The parameter descriptor that describes the parameter to bind.</param>
      <param name="formatters">The list of formatters which provides selection of an appropriate formatter for serializing the parameter into object.</param>
      <param name="bodyModelValidator">The body model validator used to validate the parameter.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithFormatter(System.Web.Http.Controllers.HttpParameterDescriptor,System.Net.Http.Formatting.MediaTypeFormatter[])">
      <summary>Binds parameter by parsing the HTTP body content.</summary>
      <returns>The HTTP parameter binding object.</returns>
      <param name="parameter">The parameter descriptor that describes the parameter to bind.</param>
      <param name="formatters">The list of formatters which provides selection of an appropriate formatter for serializing the parameter into object.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>Binds parameter by parsing the query string.</summary>
      <returns>The HTTP parameter binding object.</returns>
      <param name="parameter">The parameter descriptor that describes the parameter to bind.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor,System.Collections.Generic.IEnumerable{System.Web.Http.ValueProviders.ValueProviderFactory})">
      <summary>Binds parameter by parsing the query string.</summary>
      <returns>The HTTP parameter binding object.</returns>
      <param name="parameter">The parameter descriptor that describes the parameter to bind.</param>
      <param name="valueProviderFactories">The value provider factories which provide query string parameter data.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ModelBinding.IModelBinder)">
      <summary>Binds parameter by parsing the query string.</summary>
      <returns>The HTTP parameter binding object.</returns>
      <param name="parameter">The parameter descriptor that describes the parameter to bind.</param>
      <param name="binder">The model binder used to assemble the parameter into an object.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ModelBinding.IModelBinder,System.Collections.Generic.IEnumerable{System.Web.Http.ValueProviders.ValueProviderFactory})">
      <summary>Binds parameter by parsing the query string.</summary>
      <returns>The HTTP parameter binding object.</returns>
      <param name="parameter">The parameter descriptor that describes the parameter to bind.</param>
      <param name="binder">The model binder used to assemble the parameter into an object.</param>
      <param name="valueProviderFactories">The value provider factories which provide query string parameter data.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ParameterBindingExtensions.BindWithModelBinding(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ValueProviders.ValueProviderFactory[])">
      <summary>Binds parameter by parsing the query string.</summary>
      <returns>The HTTP parameter binding object.</returns>
      <param name="parameter">The parameter descriptor that describes the parameter to bind.</param>
      <param name="valueProviderFactories">The value provider factories which provide query string parameter data.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ReflectedHttpActionDescriptor">
      <summary>Represents a reflected synchronous or asynchronous action method. </summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.ReflectedHttpActionDescriptor" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.#ctor(System.Web.Http.Controllers.HttpControllerDescriptor,System.Reflection.MethodInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.ReflectedHttpActionDescriptor" /> class with the specified descriptor and method details.</summary>
      <param name="controllerDescriptor">The controller descriptor.</param>
      <param name="methodInfo">The action-method information.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.ActionName">
      <summary>Gets the name of the action.</summary>
      <returns>The name of the action.</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.ExecuteAsync(System.Web.Http.Controllers.HttpControllerContext,System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.CancellationToken)">
      <summary>Executes the described action and returns a <see cref="T:System.Threading.Tasks.Task`1" /> that once completed will contain the return value of the action.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task`1&quot;" /> that once completed will contain the return value of the action.</returns>
      <param name="controllerContext">The context.</param>
      <param name="arguments">The arguments.</param>
      <param name="cancellationToken">A cancellation token to cancel the action.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.GetCustomAttributes``1">
      <summary>Returns an array of custom attributes defined for this member, identified by type.</summary>
      <returns>An array of custom attributes or an empty array if no custom attributes exist.</returns>
      <typeparam name="T">The type of the custom attributes.</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.GetFilters">
      <summary>Retrieves information about action filters.</summary>
      <returns>The filter information.</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.GetParameters">
      <summary>Retrieves the parameters of the action method.</summary>
      <returns>The parameters of the action method.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.MethodInfo">
      <summary>Gets or sets the action-method information.</summary>
      <returns>The action-method information.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.ReturnType">
      <summary>Gets the return type of this method.</summary>
      <returns>The return type of this method.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpActionDescriptor.SupportedHttpMethods">
      <summary>Gets or sets the supported http methods.</summary>
      <returns>The supported http methods.</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.#ctor(System.Web.Http.Controllers.HttpActionDescriptor,System.Reflection.ParameterInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor" /> class.</summary>
      <param name="actionDescriptor">The action descriptor.</param>
      <param name="parameterInfo">The parameter information.</param>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.DefaultValue">
      <summary>Gets the default value for the parameter.</summary>
      <returns>The default value for the parameter.</returns>
    </member>
    <member name="M:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.GetCustomAttributes``1">
      <summary>Retrieves a collection of the custom attributes from the parameter.</summary>
      <returns>A collection of the custom attributes from the parameter.</returns>
      <typeparam name="T">The type of the custom attributes.</typeparam>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.IsOptional">
      <summary>Gets a value that indicates whether the parameter is optional.</summary>
      <returns>true if the parameter is optional; otherwise false.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.ParameterInfo">
      <summary>Gets or sets the parameter information.</summary>
      <returns>The parameter information.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.ParameterName">
      <summary>Gets the name of the parameter.</summary>
      <returns>The name of the parameter.</returns>
    </member>
    <member name="P:System.Web.Http.Controllers.ReflectedHttpParameterDescriptor.ParameterType">
      <summary>Gets the type of the parameter.</summary>
      <returns>The type of the parameter.</returns>
    </member>
    <member name="T:System.Web.Http.Controllers.ResponseMessageResultConverter">
      <summary>Represents a converter for actions with a return type of <see cref="T:System.Net.Http.HttpResponseMessage" />. </summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ResponseMessageResultConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.ResponseMessageResultConverter" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ResponseMessageResultConverter.Convert(System.Web.Http.Controllers.HttpControllerContext,System.Object)">
      <summary>Converts a <see cref="T:System.Web.Http.Controllers.ResponseMessageResultConverter" /> object to another object.</summary>
      <returns>The converted object.</returns>
      <param name="controllerContext">The controller context.</param>
      <param name="actionResult">The action result.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ServicesContainer">
      <summary>An abstract class that provides a container for services used by ASP.NET Web API.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.ServicesContainer" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Add(System.Type,System.Object)">
      <summary> Adds a service to the end of services list for the given service type.  </summary>
      <param name="serviceType">The service type.</param>
      <param name="service">The service instance.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.AddRange(System.Type,System.Collections.Generic.IEnumerable{System.Object})">
      <summary> Adds the services of the specified collection to the end of the services list for the given service type. </summary>
      <param name="serviceType">The service type.</param>
      <param name="services">The services to add.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Clear(System.Type)">
      <summary> Removes all the service instances of the given service type.  </summary>
      <param name="serviceType">The service type to clear from the services list.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ClearMultiple(System.Type)">
      <summary>Removes all instances of a multi-instance service type.</summary>
      <param name="serviceType">The service type to remove.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ClearSingle(System.Type)">
      <summary>Removes a single-instance service type.</summary>
      <param name="serviceType">The service type to remove.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.FindIndex(System.Type,System.Predicate{System.Object})">
      <summary> Searches for a service that matches the conditions defined by the specified predicate, and returns the zero-based index of the first occurrence. </summary>
      <returns>The zero-based index of the first occurrence, if found; otherwise, -1.</returns>
      <param name="serviceType">The service type.</param>
      <param name="match">The delegate that defines the conditions of the element to search for. </param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.GetService(System.Type)">
      <summary>Gets a service instance of a specified type.</summary>
      <param name="serviceType">The service type.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.GetServiceInstances(System.Type)">
      <summary>Gets a mutable list of service instances of a specified type.</summary>
      <returns>A mutable list of service instances.</returns>
      <param name="serviceType">The service type.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.GetServices(System.Type)">
      <summary>Gets a collection of service instanes of a specified type.</summary>
      <returns>A collection of service instances.</returns>
      <param name="serviceType">The service type.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Insert(System.Type,System.Int32,System.Object)">
      <summary> Inserts a service into the collection at the specified index. </summary>
      <param name="serviceType">The service type.</param>
      <param name="index">The zero-based index at which the service should be inserted. If <see cref="F:System.Int32.MaxValue" /> is passed, ensures the element is added to the end.</param>
      <param name="service">The service to insert.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.InsertRange(System.Type,System.Int32,System.Collections.Generic.IEnumerable{System.Object})">
      <summary> Inserts the elements of the collection into the service list at the specified index. </summary>
      <param name="serviceType">The service type.</param>
      <param name="index">The zero-based index at which the new elements should be inserted. If <see cref="F:System.Int32.MaxValue" /> is passed, ensures the elements are added to the end.</param>
      <param name="services">The collection of services to insert.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.IsSingleService(System.Type)">
      <summary> Determine whether the service type should be fetched with GetService or GetServices.  </summary>
      <returns>true iff the service is singular. </returns>
      <param name="serviceType">type of service to query</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Remove(System.Type,System.Object)">
      <summary> Removes the first occurrence of the given service from the service list for the given service type. </summary>
      <returns>true if the item is successfully removed; otherwise, false.</returns>
      <param name="serviceType">The service type.</param>
      <param name="service">The service instance to remove.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.RemoveAll(System.Type,System.Predicate{System.Object})">
      <summary> Removes all the elements that match the conditions defined by the specified predicate. </summary>
      <returns>The number of elements removed from the list.</returns>
      <param name="serviceType">The service type.</param>
      <param name="match">The delegate that defines the conditions of the elements to remove.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.RemoveAt(System.Type,System.Int32)">
      <summary> Removes the service at the specified index. </summary>
      <param name="serviceType">The service type.</param>
      <param name="index">The zero-based index of the service to remove.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.Replace(System.Type,System.Object)">
      <summary> Replaces all existing services for the given service type with the given service instance. This works for both singular and plural services.  </summary>
      <param name="serviceType">The service type.</param>
      <param name="service">The service instance.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ReplaceMultiple(System.Type,System.Object)">
      <summary>Replaces all instances of a multi-instance service with a new instance.</summary>
      <param name="serviceType">The type of service.</param>
      <param name="service">The service instance that will replace the current services of this type.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ReplaceRange(System.Type,System.Collections.Generic.IEnumerable{System.Object})">
      <summary> Replaces all existing services for the given service type with the given service instances. </summary>
      <param name="serviceType">The service type.</param>
      <param name="services">The service instances.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ReplaceSingle(System.Type,System.Object)">
      <summary>Replaces a single-instance service of a specified type.</summary>
      <param name="serviceType">The service type. </param>
      <param name="service">The service instance.</param>
    </member>
    <member name="M:System.Web.Http.Controllers.ServicesContainer.ResetCache(System.Type)">
      <summary>Removes the cached values for a single service type.</summary>
      <param name="serviceType">The service type.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.ValueResultConverter`1">
      <summary> A converter for creating responses from actions that return an arbitrary <paramref name="T" /> value. </summary>
      <typeparam name="T">The declared return type of an action.</typeparam>
    </member>
    <member name="M:System.Web.Http.Controllers.ValueResultConverter`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.ValueResultConverter`1" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.ValueResultConverter`1.Convert(System.Web.Http.Controllers.HttpControllerContext,System.Object)">
      <summary>Converts the result of an action with arbitrary return type <paramref name="T" /> to an instance of <see cref="T:System.Net.Http.HttpResponseMessage" />.</summary>
      <returns>The newly created <see cref="T:System.Net.Http.HttpResponseMessage" /> object.</returns>
      <param name="controllerContext">The action controller context.</param>
      <param name="actionResult">The execution result.</param>
    </member>
    <member name="T:System.Web.Http.Controllers.VoidResultConverter">
      <summary>Represents a converter for creating a response from actions that do not return a value.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.VoidResultConverter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Controllers.VoidResultConverter" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Controllers.VoidResultConverter.Convert(System.Web.Http.Controllers.HttpControllerContext,System.Object)">
      <summary>Converts the created response from actions that do not return a value.</summary>
      <returns>The converted response.</returns>
      <param name="controllerContext">The context of the controller.</param>
      <param name="actionResult">The result of the action.</param>
    </member>
    <member name="T:System.Web.Http.Dependencies.IDependencyResolver">
      <summary>Represents a dependency injection container.</summary>
    </member>
    <member name="M:System.Web.Http.Dependencies.IDependencyResolver.BeginScope">
      <summary> Starts a resolution scope. </summary>
      <returns>The dependency scope.</returns>
    </member>
    <member name="T:System.Web.Http.Dependencies.IDependencyScope">
      <summary>Represents an interface for the range of the dependencies.</summary>
    </member>
    <member name="M:System.Web.Http.Dependencies.IDependencyScope.GetService(System.Type)">
      <summary>Retrieves a service from the scope.</summary>
      <returns>The retrieved service.</returns>
      <param name="serviceType">The service to be retrieved.</param>
    </member>
    <member name="M:System.Web.Http.Dependencies.IDependencyScope.GetServices(System.Type)">
      <summary>Retrieves a collection of services from the scope.</summary>
      <returns>The retrieved collection of services.</returns>
      <param name="serviceType">The collection of services to be retrieved.</param>
    </member>
    <member name="T:System.Web.Http.Description.ApiDescription">
      <summary> Describes an API defined by relative URI path and HTTP method. </summary>
    </member>
    <member name="M:System.Web.Http.Description.ApiDescription.#ctor">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.Description.ApiDescription" /> class. </summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.ActionDescriptor">
      <summary> Gets or sets the action descriptor that will handle the API. </summary>
      <returns> The action descriptor. </returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.Documentation">
      <summary> Gets or sets the documentation of the API. </summary>
      <returns> The documentation. </returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.HttpMethod">
      <summary> Gets or sets the HTTP method. </summary>
      <returns> The HTTP method. </returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.ID">
      <summary> Gets the ID. The ID is unique within <see cref="T:System.Web.Http.HttpServer" />. </summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.ParameterDescriptions">
      <summary> Gets the parameter descriptions. </summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.RelativePath">
      <summary> Gets or sets the relative path. </summary>
      <returns> The relative path. </returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.Route">
      <summary> Gets or sets the registered route for the API. </summary>
      <returns> The route. </returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.SupportedRequestBodyFormatters">
      <summary> Gets the supported request body formatters. </summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiDescription.SupportedResponseFormatters">
      <summary> Gets the supported response formatters. </summary>
    </member>
    <member name="T:System.Web.Http.Description.ApiExplorer">
      <summary> Explores the URI space of the service based on routes, controllers and actions available in the system. </summary>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorer.#ctor(System.Web.Http.HttpConfiguration)">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.Description.ApiExplorer" /> class. </summary>
      <param name="configuration">The configuration.</param>
    </member>
    <member name="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions">
      <summary> Gets the API descriptions. The descriptions are initialized on the first access. </summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiExplorer.DocumentationProvider">
      <summary> Gets or sets the documentation provider. The provider will be responsible for documenting the API. </summary>
      <returns> The documentation provider. </returns>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorer.GetHttpMethodsSupportedByAction(System.Web.Http.Routing.IHttpRoute,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary> Gets a collection of HttpMethods supported by the action. Called when initializing the <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" />. </summary>
      <returns>A collection of HttpMethods supported by the action.</returns>
      <param name="route">The route.</param>
      <param name="actionDescriptor">The action descriptor.</param>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorer.ShouldExploreAction(System.String,System.Web.Http.Controllers.HttpActionDescriptor,System.Web.Http.Routing.IHttpRoute)">
      <summary> Determines whether the action should be considered for <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> generation. Called when initializing the <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" />. </summary>
      <returns>true if the action should be considered for <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> generation, false otherwise.</returns>
      <param name="actionVariableValue">The action variable value from the route.</param>
      <param name="actionDescriptor">The action descriptor.</param>
      <param name="route">The route.</param>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorer.ShouldExploreController(System.String,System.Web.Http.Controllers.HttpControllerDescriptor,System.Web.Http.Routing.IHttpRoute)">
      <summary> Determines whether the controller should be considered for <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> generation. Called when initializing the <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" />. </summary>
      <returns>true if the controller should be considered for <see cref="P:System.Web.Http.Description.ApiExplorer.ApiDescriptions" /> generation, false otherwise.</returns>
      <param name="controllerVariableValue">The controller variable value from the route.</param>
      <param name="controllerDescriptor">The controller descriptor.</param>
      <param name="route">The route.</param>
    </member>
    <member name="T:System.Web.Http.Description.ApiExplorerSettingsAttribute">
      <summary> This attribute can be used on the controllers and actions to influence the behavior of <see cref="T:System.Web.Http.Description.ApiExplorer" />. </summary>
    </member>
    <member name="M:System.Web.Http.Description.ApiExplorerSettingsAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Description.ApiExplorerSettingsAttribute" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiExplorerSettingsAttribute.IgnoreApi">
      <summary> Gets or sets a value indicating whether to exclude the controller or action from the <see cref="T:System.Web.Http.Description.ApiDescription" /> instances generated by <see cref="T:System.Web.Http.Description.ApiExplorer" />. </summary>
      <returns>true if the controller or action should be ignored; otherwise, false.</returns>
    </member>
    <member name="T:System.Web.Http.Description.ApiParameterDescription">
      <summary> Describes a parameter on the API defined by relative URI path and HTTP method. </summary>
    </member>
    <member name="M:System.Web.Http.Description.ApiParameterDescription.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Description.ApiParameterDescription" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.Description.ApiParameterDescription.Documentation">
      <summary> Gets or sets the documentation. </summary>
      <returns> The documentation. </returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiParameterDescription.Name">
      <summary> Gets or sets the name. </summary>
      <returns> The name. </returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiParameterDescription.ParameterDescriptor">
      <summary> Gets or sets the parameter descriptor. </summary>
      <returns> The parameter descriptor. </returns>
    </member>
    <member name="P:System.Web.Http.Description.ApiParameterDescription.Source">
      <summary> Gets or sets the source of the parameter. It may come from the request URI, request body or other places. </summary>
      <returns> The source. </returns>
    </member>
    <member name="T:System.Web.Http.Description.ApiParameterSource">
      <summary> Describes where the parameter come from. </summary>
    </member>
    <member name="F:System.Web.Http.Description.ApiParameterSource.FromUri">
      <summary>The parameter come from Uri.</summary>
    </member>
    <member name="F:System.Web.Http.Description.ApiParameterSource.FromBody">
      <summary>The parameter come from Body.</summary>
    </member>
    <member name="F:System.Web.Http.Description.ApiParameterSource.Unknown">
      <summary>The location is unknown.</summary>
    </member>
    <member name="T:System.Web.Http.Description.IApiExplorer">
      <summary> Defines the interface for getting a collection of <see cref="T:System.Web.Http.Description.ApiDescription" />. </summary>
    </member>
    <member name="P:System.Web.Http.Description.IApiExplorer.ApiDescriptions">
      <summary> Gets the API descriptions. </summary>
    </member>
    <member name="T:System.Web.Http.Description.IDocumentationProvider">
      <summary> Defines the provider responsible for documenting the service. </summary>
    </member>
    <member name="M:System.Web.Http.Description.IDocumentationProvider.GetDocumentation(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary> Gets the documentation based on <see cref="T:System.Web.Http.Controllers.HttpActionDescriptor" />. </summary>
      <returns>The documentation for the controller.</returns>
      <param name="actionDescriptor">The action descriptor.</param>
    </member>
    <member name="M:System.Web.Http.Description.IDocumentationProvider.GetDocumentation(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary> Gets the documentation based on <see cref="T:System.Web.Http.Controllers.HttpParameterDescriptor" />. </summary>
      <returns>The documentation for the controller.</returns>
      <param name="parameterDescriptor">The parameter descriptor.</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.DefaultAssembliesResolver">
      <summary> Provides an implementation of <see cref="T:System.Web.Http.Dispatcher.IAssembliesResolver" /> with no external dependencies. </summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultAssembliesResolver.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Dispatcher.DefaultAssembliesResolver" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultAssembliesResolver.GetAssemblies">
      <summary> Returns a list of assemblies available for the application. </summary>
      <returns>A &lt;see cref="T:System.Collections.ObjectModel.Collection`1" /&gt; of assemblies.</returns>
    </member>
    <member name="T:System.Web.Http.Dispatcher.DefaultHttpControllerActivator">
      <summary>Represents a default implementation of an <see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" />. A different implementation can be registered via the <see cref="T:System.Web.Http.Services.DependencyResolver" />. We optimize for the case where we have an <see cref="T:System.Web.Http.Controllers.ApiControllerActionInvoker" />  instance per <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> instance but can support cases where there are many <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> instances for one <see cref="T:System.Web.Http.Controllers.ApiControllerActionInvoker" />  as well. In the latter case the lookup is slightly slower because it goes through the <see cref="P:HttpControllerDescriptor.Properties" /> dictionary. </summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerActivator.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Dispatcher.DefaultHttpControllerActivator" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerActivator.Create(System.Net.Http.HttpRequestMessage,System.Web.Http.Controllers.HttpControllerDescriptor,System.Type)">
      <summary> Creates the <see cref="T:System.Web.Http.Controllers.IHttpController" /> specified by <paramref name="controllerType" /> using the given <paramref name="request" />.</summary>
      <returns>An instance of type <paramref name="controllerType" />.</returns>
      <param name="request">The request message.</param>
      <param name="controllerDescriptor">The controller descriptor.</param>
      <param name="controllerType">The type of the controller.</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.DefaultHttpControllerSelector">
      <summary>Represents a default <see cref="T:System.Web.Http.Dispatcher.IHttpControllerSelector" /> instance for choosing a <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> given a <see cref="T:System.Net.Http.HttpRequestMessage" />. A different implementation can be registered via the <see cref="P:System.Web.Http.HttpConfiguration.Services" />. </summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.#ctor(System.Web.Http.HttpConfiguration)">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.Dispatcher.DefaultHttpControllerSelector" /> class.</summary>
      <param name="configuration">The configuration.</param>
    </member>
    <member name="F:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.ControllerSuffix">
      <summary>Specifies the suffix string in the controller name.</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.GetControllerMapping">
      <summary>Returns a map, keyed by controller string, of all <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> that the selector can select. </summary>
      <returns>A map of all <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> that the selector can select, or null if the selector does not have a well-defined mapping of <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" />.</returns>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.GetControllerName(System.Net.Http.HttpRequestMessage)">
      <summary>Gets the name of the controller for the specified <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>The name of the controller for the specified <see cref="T:System.Net.Http.HttpRequestMessage" />.</returns>
      <param name="request">The HTTP request message.</param>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerSelector.SelectController(System.Net.Http.HttpRequestMessage)">
      <summary>Selects a <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> for the given <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>The <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> instance for the given <see cref="T:System.Net.Http.HttpRequestMessage" />.</returns>
      <param name="request">The HTTP request message.</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver">
      <summary> Provides an implementation of <see cref="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver" /> with no external dependencies. </summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver.#ctor(System.Predicate{System.Type})">
      <summary>Initializes a new <see cref="T:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver" /> instance using a predicate to filter controller types. </summary>
      <param name="predicate">The predicate.</param>
    </member>
    <member name="M:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver.GetControllerTypes(System.Web.Http.Dispatcher.IAssembliesResolver)">
      <summary> Returns a list of controllers available for the application. </summary>
      <returns>An &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt; of controllers.</returns>
      <param name="assembliesResolver">The assemblies resolver.</param>
    </member>
    <member name="P:System.Web.Http.Dispatcher.DefaultHttpControllerTypeResolver.IsControllerTypePredicate">
      <summary>Gets a value whether the resolver type is a controller type predicate.</summary>
      <returns>true if the resolver type is a controller type predicate; otherwise, false.</returns>
    </member>
    <member name="T:System.Web.Http.Dispatcher.HttpControllerDispatcher">
      <summary>Dispatches an incoming <see cref="T:System.Net.Http.HttpRequestMessage" /> to an <see cref="T:System.Web.Http.Controllers.IHttpController" /> implementation for processing.</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpControllerDispatcher.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Dispatcher.HttpControllerDispatcher" /> class with the specified configuration.</summary>
      <param name="configuration">The http configuration.</param>
    </member>
    <member name="P:System.Web.Http.Dispatcher.HttpControllerDispatcher.Configuration">
      <summary>Gets the HTTP configuration.</summary>
      <returns>The HTTP configuration.</returns>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpControllerDispatcher.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Dispatches an incoming <see cref="T:System.Net.Http.HttpRequestMessage" /> to an <see cref="T:System.Web.Http.Controllers.IHttpController" />.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> representing the ongoing operation.</returns>
      <param name="request">The request to dispatch</param>
      <param name="cancellationToken">The cancellation token.</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.HttpRoutingDispatcher">
      <summary> This class is the default endpoint message handler which examines the <see cref="T:System.Web.Http.Routing.IHttpRoute" /> of the matched route, and chooses which message handler to call. If <see cref="P:System.Web.Http.Routing.IHttpRoute.Handler" /> is null, then it delegates to <see cref="T:System.Web.Http.Dispatcher.HttpControllerDispatcher" />. </summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpRoutingDispatcher.#ctor(System.Web.Http.HttpConfiguration)">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.Dispatcher.HttpRoutingDispatcher" /> class, using the provided <see cref="T:System.Web.Http.HttpConfiguration" /> and <see cref="T:System.Web.Http.Dispatcher.HttpControllerDispatcher" /> as the default handler. </summary>
      <param name="configuration">The server configuration.</param>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpRoutingDispatcher.#ctor(System.Web.Http.HttpConfiguration,System.Net.Http.HttpMessageHandler)">
      <summary> Initializes a new instance of the <see cref="T:System.Web.Http.Dispatcher.HttpRoutingDispatcher" /> class, using the provided <see cref="T:System.Web.Http.HttpConfiguration" /> and <see cref="T:System.Net.Http.HttpMessageHandler" />. </summary>
      <param name="configuration">The server configuration.</param>
      <param name="defaultHandler">The default handler to use when the <see cref="T:System.Web.Http.Routing.IHttpRoute" /> has no <see cref="P:System.Web.Http.Routing.IHttpRoute.Handler" />.</param>
    </member>
    <member name="M:System.Web.Http.Dispatcher.HttpRoutingDispatcher.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Sends an HTTP request as an asynchronous operation.</summary>
      <returns>The task object representing the asynchronous operation.</returns>
      <param name="request">The HTTP request message to send.</param>
      <param name="cancellationToken">The cancellation token to cancel operation.</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.IAssembliesResolver">
      <summary> Provides an abstraction for managing the assemblies of an application. A different implementation can be registered via the <see cref="T:System.Web.Http.Services.DependencyResolver" />. </summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IAssembliesResolver.GetAssemblies">
      <summary> Returns a list of assemblies available for the application. </summary>
      <returns>An &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt; of assemblies.</returns>
    </member>
    <member name="T:System.Web.Http.Dispatcher.IHttpControllerActivator">
      <summary>Defines the methods that are required for an <see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" />.</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IHttpControllerActivator.Create(System.Net.Http.HttpRequestMessage,System.Web.Http.Controllers.HttpControllerDescriptor,System.Type)">
      <summary>Creates an <see cref="T:System.Web.Http.Controllers.IHttpController" /> object.</summary>
      <returns>An <see cref="T:System.Web.Http.Controllers.IHttpController" /> object.</returns>
      <param name="request">The message request.</param>
      <param name="controllerDescriptor">The HTTP controller descriptor.</param>
      <param name="controllerType">The type of the controller.</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.IHttpControllerSelector">
      <summary> Defines the methods that are required for an <see cref="T:System.Web.Http.Controllers.IHttpController" /> factory. </summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IHttpControllerSelector.GetControllerMapping">
      <summary> Returns a map, keyed by controller string, of all <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> that the selector can select.  This is primarily called by <see cref="T:System.Web.Http.Description.IApiExplorer" /> to discover all the possible controllers in the system. </summary>
      <returns>A map of all <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> that the selector can select, or null if the selector does not have a well-defined mapping of <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" />.</returns>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IHttpControllerSelector.SelectController(System.Net.Http.HttpRequestMessage)">
      <summary> Selects a <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> for the given <see cref="T:System.Net.Http.HttpRequestMessage" />. </summary>
      <returns>An <see cref="T:System.Web.Http.Controllers.HttpControllerDescriptor" /> instance.</returns>
      <param name="request">The request message.</param>
    </member>
    <member name="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver">
      <summary> Provides an abstraction for managing the controller types of an application. A different implementation can be registered via the DependencyResolver.</summary>
    </member>
    <member name="M:System.Web.Http.Dispatcher.IHttpControllerTypeResolver.GetControllerTypes(System.Web.Http.Dispatcher.IAssembliesResolver)">
      <summary> Returns a list of controllers available for the application. </summary>
      <returns>An &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt; of controllers.</returns>
      <param name="assembliesResolver">The resolver for failed assemblies.</param>
    </member>
    <member name="T:System.Web.Http.Filters.ActionDescriptorFilterProvider">
      <summary>Provides information about an action method, such as its name, controller, parameters, attributes, and filters.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ActionDescriptorFilterProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Filters.ActionDescriptorFilterProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ActionDescriptorFilterProvider.GetFilters(System.Web.Http.HttpConfiguration,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>Returns the filters that are associated with this action method.</summary>
      <returns>The filters that are associated with this action method.</returns>
      <param name="configuration">The configuration.</param>
      <param name="actionDescriptor">The action descriptor.</param>
    </member>
    <member name="T:System.Web.Http.Filters.ActionFilterAttribute">
      <summary>Represents the base class for all action-filter attributes.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Filters.ActionFilterAttribute" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.OnActionExecuted(System.Web.Http.Filters.HttpActionExecutedContext)">
      <summary>Occurs after the action method is invoked.</summary>
      <param name="actionExecutedContext">The action executed context.</param>
    </member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.OnActionExecuting(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Occurs before the action method is invoked.</summary>
      <param name="actionContext">The action context.</param>
    </member>
    <member name="M:System.Web.Http.Filters.ActionFilterAttribute.System#Web#Http#Filters#IActionFilter#ExecuteActionFilterAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken,System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
      <summary>Executes the filter action asynchronously.</summary>
      <returns>The newly created task for this operation.</returns>
      <param name="actionContext">The action context.</param>
      <param name="cancellationToken">The cancellation token assigned for this task.</param>
      <param name="continuation">The delegate function to continue after the action method is invoked.</param>
    </member>
    <member name="T:System.Web.Http.Filters.AuthorizationFilterAttribute">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.AuthorizationFilterAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Filters.AuthorizationFilterAttribute" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.AuthorizationFilterAttribute.OnAuthorization(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Calls when a process requests authorization.</summary>
      <param name="actionContext">The action context, which encapsulates information for using <see cref="T:System.Web.Http.Filters.AuthorizationFilterAttribute" />.</param>
    </member>
    <member name="M:System.Web.Http.Filters.AuthorizationFilterAttribute.System#Web#Http#Filters#IAuthorizationFilter#ExecuteAuthorizationFilterAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken,System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
      <summary>Executes the authorization filter during synchronization.</summary>
      <returns>The authorization filter during synchronization.</returns>
      <param name="actionContext">The action context, which encapsulates information for using <see cref="T:System.Web.Http.Filters.AuthorizationFilterAttribute" />.</param>
      <param name="cancellationToken">The cancellation token that cancels the operation.</param>
      <param name="continuation">A continuation of the operation.</param>
    </member>
    <member name="T:System.Web.Http.Filters.ConfigurationFilterProvider">
      <summary>Represents the configuration filter provider.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ConfigurationFilterProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Filters.ConfigurationFilterProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ConfigurationFilterProvider.GetFilters(System.Web.Http.HttpConfiguration,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>Returns the filters that are associated with this configuration method.</summary>
      <returns>The filters that are associated with this configuration method.</returns>
      <param name="configuration">The configuration.</param>
      <param name="actionDescriptor">The action descriptor.</param>
    </member>
    <member name="T:System.Web.Http.Filters.ExceptionFilterAttribute">
      <summary>Represents the attributes for the exception filter.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ExceptionFilterAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Filters.ExceptionFilterAttribute" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.ExceptionFilterAttribute.OnException(System.Web.Http.Filters.HttpActionExecutedContext)">
      <summary>Raises the exception event.</summary>
      <param name="actionExecutedContext">The context for the action.</param>
    </member>
    <member name="M:System.Web.Http.Filters.ExceptionFilterAttribute.System#Web#Http#Filters#IExceptionFilter#ExecuteExceptionFilterAsync(System.Web.Http.Filters.HttpActionExecutedContext,System.Threading.CancellationToken)">
      <summary>Asynchronously executes the exception filter.</summary>
      <returns>The result of the execution.</returns>
      <param name="actionExecutedContext">The context for the action.</param>
      <param name="cancellationToken">The cancellation context.</param>
    </member>
    <member name="T:System.Web.Http.Filters.FilterAttribute">
      <summary>Represents the base class for action-filter attributes.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.FilterAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Filters.FilterAttribute" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.Filters.FilterAttribute.AllowMultiple">
      <summary>Gets a value that indicates whether multiple filters are allowed.</summary>
      <returns>true if multiple filters are allowed; otherwise, false.</returns>
    </member>
    <member name="T:System.Web.Http.Filters.FilterInfo">
      <summary>Provides information about the available action filters.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.FilterInfo.#ctor(System.Web.Http.Filters.IFilter,System.Web.Http.Filters.FilterScope)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Filters.FilterInfo" /> class.</summary>
      <param name="instance">The instance of this class.</param>
      <param name="scope">The scope of this class.</param>
    </member>
    <member name="P:System.Web.Http.Filters.FilterInfo.Instance">
      <summary>Gets or sets an instance of the <see cref="T:System.Web.Http.Filters.FilterInfo" />.</summary>
      <returns>A <see cref="T:System.Web.Http.Filters.FilterInfo" />.</returns>
    </member>
    <member name="P:System.Web.Http.Filters.FilterInfo.Scope">
      <summary>Gets or sets the scope <see cref="T:System.Web.Http.Filters.FilterInfo" />.</summary>
      <returns>The scope of the FilterInfo.</returns>
    </member>
    <member name="T:System.Web.Http.Filters.FilterScope">
      <summary>Defines values that specify the order in which filters run within the same filter type and filter order.</summary>
    </member>
    <member name="F:System.Web.Http.Filters.FilterScope.Global">
      <summary>Specifies an action before Controller.</summary>
    </member>
    <member name="F:System.Web.Http.Filters.FilterScope.Controller">
      <summary>Specifies an order before Action and after Global.</summary>
    </member>
    <member name="F:System.Web.Http.Filters.FilterScope.Action">
      <summary>Specifies an order after Controller. </summary>
    </member>
    <member name="T:System.Web.Http.Filters.HttpActionExecutedContext">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpActionExecutedContext.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Filters.HttpActionExecutedContext" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpActionExecutedContext.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Filters.HttpActionExecutedContext" /> class.</summary>
      <param name="actionContext">The action context.</param>
      <param name="exception">The exception.</param>
    </member>
    <member name="P:System.Web.Http.Filters.HttpActionExecutedContext.ActionContext">
      <summary>Gets or sets the HTTP action context.</summary>
      <returns>The HTTP action context.</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpActionExecutedContext.Exception">
      <summary>Gets or sets the exception that was raised during the execution.</summary>
      <returns>The exception that was raised during the execution.</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpActionExecutedContext.Request">
      <summary>Gets the <see cref="T:System.Net.Http.HttpRequestMessage" /> object for the context.</summary>
      <returns>The <see cref="T:System.Net.Http.HttpRequestMessage" /> object for the context.</returns>
    </member>
    <member name="P:System.Web.Http.Filters.HttpActionExecutedContext.Response">
      <summary>Gets or sets the <see cref="T:System.Net.Http.HttpResponseMessage" /> for the context.</summary>
      <returns>The <see cref="T:System.Net.Http.HttpResponseMessage" /> for the context.</returns>
    </member>
    <member name="T:System.Web.Http.Filters.HttpFilterCollection">
      <summary>Represents a collection of HTTP filters.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Filters.HttpFilterCollection" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.Add(System.Web.Http.Filters.IFilter)">
      <summary>Adds an item at the end of the collection.</summary>
      <param name="filter">The item to add to the collection.</param>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.Clear">
      <summary>Removes all item in the collection.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.Contains(System.Web.Http.Filters.IFilter)">
      <summary>Determines whether the collection contains the specified item.</summary>
      <returns>true if the collection contains the specified item; otherwise, false.</returns>
      <param name="filter">The item to check.</param>
    </member>
    <member name="P:System.Web.Http.Filters.HttpFilterCollection.Count">
      <summary>Gets the number of elements in the collection.</summary>
      <returns>The number of elements in the collection.</returns>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.GetEnumerator">
      <summary>Gets an enumerator that iterates through the collection.</summary>
      <returns>An enumerator object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.Remove(System.Web.Http.Filters.IFilter)">
      <summary>Removes the specified item from the collection.</summary>
      <param name="filter">The item to remove in the collection.</param>
    </member>
    <member name="M:System.Web.Http.Filters.HttpFilterCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gets an enumerator that iterates through the collection.</summary>
      <returns>An enumerator object that can be used to iterate through the collection.</returns>
    </member>
    <member name="T:System.Web.Http.Filters.IActionFilter">
      <summary>Defines the methods that are used in an action filter.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IActionFilter.ExecuteActionFilterAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken,System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
      <summary>Executes the filter action asynchronously.</summary>
      <returns>The newly created task for this operation.</returns>
      <param name="actionContext">The action context.</param>
      <param name="cancellationToken">The cancellation token assigned for this task.</param>
      <param name="continuation">The delegate function to continue after the action method is invoked.</param>
    </member>
    <member name="T:System.Web.Http.Filters.IAuthorizationFilter">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IAuthorizationFilter.ExecuteAuthorizationFilterAsync(System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken,System.Func{System.Threading.Tasks.Task{System.Net.Http.HttpResponseMessage}})">
      <summary>Executes the authorization filter to synchronize.</summary>
      <returns>The authorization filter to synchronize.</returns>
      <param name="actionContext">The action context.</param>
      <param name="cancellationToken">The cancellation token associated with the filter.</param>
      <param name="continuation">The continuation.</param>
    </member>
    <member name="T:System.Web.Http.Filters.IExceptionFilter">
      <summary>Defines the methods that are required for an exception filter.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IExceptionFilter.ExecuteExceptionFilterAsync(System.Web.Http.Filters.HttpActionExecutedContext,System.Threading.CancellationToken)">
      <summary>Executes an asynchronous exception filter.</summary>
      <returns>An asynchronous exception filter.</returns>
      <param name="actionExecutedContext">The action executed context.</param>
      <param name="cancellationToken">The cancellation token.</param>
    </member>
    <member name="T:System.Web.Http.Filters.IFilter">
      <summary>Specifies a server-side component that is used by the indexing system to index documents that have the file format associated with the IFilter.</summary>
    </member>
    <member name="P:System.Web.Http.Filters.IFilter.AllowMultiple">
      <summary>Gets or sets a value indicating whether more than one instance of the indicated attribute can be specified for a single program element.</summary>
      <returns>true if more than one instance is allowed to be specified; otherwise, false. The default is false.</returns>
    </member>
    <member name="T:System.Web.Http.Filters.IFilterProvider">
      <summary>Provides filter information.</summary>
    </member>
    <member name="M:System.Web.Http.Filters.IFilterProvider.GetFilters(System.Web.Http.HttpConfiguration,System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>Returns an enumeration of filters.</summary>
      <returns>An enumeration of filters.</returns>
      <param name="configuration">The HTTP configuration.</param>
      <param name="actionDescriptor">The action descriptor.</param>
    </member>
    <member name="T:System.Web.Http.Hosting.HttpPropertyKeys">
      <summary>Provides common keys for properties stored in the <see cref="P:System.Net.Http.HttpRequestMessage.Properties" />.</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.ClientCertificateKey">
      <summary>Provides a key for the client certificate for this request.</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.DependencyScope">
      <summary> Provides a key for the <see cref="T:System.Web.Http.Dependencies.IDependencyScope" /> associated with this request.</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.DisposableRequestResourcesKey">
      <summary> Provides a key for the collection of resources that should be disposed when a request is disposed. </summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.HttpConfigurationKey">
      <summary> Provides a key for the <see cref="T:System.Web.Http.HttpConfiguration" /> associated with this request. </summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.HttpRouteDataKey">
      <summary> Provides a key for the <see cref="T:System.Web.Http.Routing.IHttpRouteData" /> associated with this request. </summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.IncludeErrorDetailKey">
      <summary>Provides a key that indicates whether error details are to be included in the response for this HTTP request.</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.IsLocalKey">
      <summary>Provides a key that indicates whether the request originates from a local address.</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.RequestCorrelationKey">
      <summary> Provides a key for the <see cref="T:System.Guid" /> stored in <see cref="P:System.Net.Http.HttpRequestMessage.Properties" />. This is the correlation ID for that request. </summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.RequestQueryNameValuePairsKey">
      <summary>Provides a key for the parsed query string stored in <see cref="P:System.Net.Http.HttpRequestMessage.Properties" />.</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.RetrieveClientCertificateDelegateKey">
      <summary>Provides a key for a delegate which can retrieve the client certificate for this request.</summary>
    </member>
    <member name="F:System.Web.Http.Hosting.HttpPropertyKeys.SynchronizationContextKey">
      <summary> Provides a key for the current <see cref="T:System.Threading.SynchronizationContext" /> stored in <see cref="M:HttpRequestMessage.Properties" />. If <see cref="M:SynchronizationContext.Current" /> is null then no context is stored. </summary>
    </member>
    <member name="T:System.Web.Http.Hosting.IHostBufferPolicySelector">
      <summary> Interface for controlling the use of buffering requests and responses in the host. If a host provides support for buffering requests and/or responses then it can use this interface to determine the policy for when buffering is to be used.</summary>
    </member>
    <member name="M:System.Web.Http.Hosting.IHostBufferPolicySelector.UseBufferedInputStream(System.Object)">
      <summary>Determines whether the host should buffer the <see cref="T:System.Net.Http.HttpRequestMessage" /> entity body.</summary>
      <returns>true if buffering should be used; otherwise a streamed request should be used.</returns>
      <param name="hostContext">The host context.</param>
    </member>
    <member name="M:System.Web.Http.Hosting.IHostBufferPolicySelector.UseBufferedOutputStream(System.Net.Http.HttpResponseMessage)">
      <summary>Determines whether the host should buffer the <see cref="T.System.Net.Http.HttpResponseMessage" /> entity body.</summary>
      <returns>true if buffering should be used; otherwise a streamed response should be used.</returns>
      <param name="response">The HTTP response message.</param>
    </member>
    <member name="T:System.Web.Http.Metadata.ModelMetadata">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadata.#ctor(System.Web.Http.Metadata.ModelMetadataProvider,System.Type,System.Func{System.Object},System.Type,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Metadata.ModelMetadata" /> class.</summary>
      <param name="provider">The provider.</param>
      <param name="containerType">The type of the container.</param>
      <param name="modelAccessor">The model accessor.</param>
      <param name="modelType">The type of the model.</param>
      <param name="propertyName">The name of the property.</param>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.AdditionalValues">
      <summary>Gets a dictionary that contains additional metadata about the model.</summary>
      <returns>A dictionary that contains additional metadata about the model.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.ContainerType">
      <summary>Gets or sets the type of the container for the model.</summary>
      <returns>The type of the container for the model.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.ConvertEmptyStringToNull">
      <summary>Gets or sets a value that indicates whether empty strings that are posted back in forms should be converted to null.</summary>
      <returns>true if empty strings that are posted back in forms should be converted to null; otherwise, false. The default value is true.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.Description">
      <summary>Gets or sets the description of the model.</summary>
      <returns>The description of the model. The default value is null.</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadata.GetDisplayName">
      <summary>Gets the display name for the model.</summary>
      <returns>The display name for the model.</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadata.GetValidators(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>Gets a list of validators for the model.</summary>
      <returns>A list of validators for the model.</returns>
      <param name="validatorProviders">The validator providers for the model.</param>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.IsComplexType">
      <summary>Gets or sets a value that indicates whether the model is a complex type.</summary>
      <returns>A value that indicates whether the model is considered a complex.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.IsNullableValueType">
      <summary>Gets a value that indicates whether the type is nullable.</summary>
      <returns>true if the type is nullable; otherwise, false.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.IsReadOnly">
      <summary>Gets or sets a value that indicates whether the model is read-only.</summary>
      <returns>true if the model is read-only; otherwise, false.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.Model">
      <summary>Gets the value of the model.</summary>
      <returns>The model value can be null.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.ModelType">
      <summary>Gets the type of the model.</summary>
      <returns>The type of the model.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.Properties">
      <summary>Gets a collection of model metadata objects that describe the properties of the model.</summary>
      <returns>A collection of model metadata objects that describe the properties of the model.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.PropertyName">
      <summary>Gets the property name.</summary>
      <returns>The property name.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.ModelMetadata.Provider">
      <summary>Gets or sets the provider.</summary>
      <returns>The provider.</returns>
    </member>
    <member name="T:System.Web.Http.Metadata.ModelMetadataProvider">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadataProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadataProvider.GetMetadataForProperties(System.Object,System.Type)">
      <summary>Gets a ModelMetadata object for each property of a model.</summary>
      <returns>A ModelMetadata object for each property of a model.</returns>
      <param name="container">The container.</param>
      <param name="containerType">The type of the container.</param>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadataProvider.GetMetadataForProperty(System.Func{System.Object},System.Type,System.String)">
      <summary>Get metadata for the specified property.</summary>
      <returns>The metadata model for the specified property.</returns>
      <param name="modelAccessor">The model accessor.</param>
      <param name="containerType">The type of the container.</param>
      <param name="propertyName">The property to get the metadata model for.</param>
    </member>
    <member name="M:System.Web.Http.Metadata.ModelMetadataProvider.GetMetadataForType(System.Func{System.Object},System.Type)">
      <summary>Gets the metadata for the specified model accessor and model type.</summary>
      <returns>The metadata.</returns>
      <param name="modelAccessor">The model accessor.</param>
      <param name="modelType">The type of the mode.</param>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1">
      <summary>Provides an abstract class to implement a metadata provider.</summary>
      <typeparam name="TModelMetadata">The type of the model metadata.</typeparam>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.CreateMetadataFromPrototype(`0,System.Func{System.Object})">
      <summary>When overridden in a derived class, creates the model metadata for the property using the specified prototype.</summary>
      <returns>The model metadata for the property.</returns>
      <param name="prototype">The prototype from which to create the model metadata.</param>
      <param name="modelAccessor">The model accessor.</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.CreateMetadataPrototype(System.Collections.Generic.IEnumerable{System.Attribute},System.Type,System.Type,System.String)">
      <summary>When overridden in a derived class, creates the model metadata for the property.</summary>
      <returns>The model metadata for the property.</returns>
      <param name="attributes">The set of attributes.</param>
      <param name="containerType">The type of the container.</param>
      <param name="modelType">The type of the model.</param>
      <param name="propertyName">The name of the property.</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.GetMetadataForProperties(System.Object,System.Type)">
      <summary>Retrieves a list of properties for the model.</summary>
      <returns>A list of properties for the model.</returns>
      <param name="container">The model container.</param>
      <param name="containerType">The type of the container.</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.GetMetadataForProperty(System.Func{System.Object},System.Type,System.String)">
      <summary>Retrieves the metadata for the specified property using the container type and property name.</summary>
      <returns>The metadata for the specified property.</returns>
      <param name="modelAccessor">The model accessor.</param>
      <param name="containerType">The type of the container.</param>
      <param name="propertyName">The name of the property.</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.AssociatedMetadataProvider`1.GetMetadataForType(System.Func{System.Object},System.Type)">
      <summary>Returns the metadata for the specified property using the type of the model.</summary>
      <returns>The metadata for the specified property.</returns>
      <param name="modelAccessor">The model accessor.</param>
      <param name="modelType">The type of the container.</param>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes">
      <summary>Provides prototype cache data for <see cref="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1" />.</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.#ctor(System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes" /> class.</summary>
      <param name="attributes">The attributes that provides data for the initialization.</param>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.Display">
      <summary>Gets or sets the metadata display attribute.</summary>
      <returns>The metadata display attribute.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.DisplayFormat">
      <summary>Gets or sets the metadata display format attribute.</summary>
      <returns>The metadata display format attribute.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.Editable">
      <summary>Gets or sets the metadata editable attribute.</summary>
      <returns>The metadata editable attribute.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedDataAnnotationsMetadataAttributes.ReadOnly">
      <summary>Gets or sets the metadata read-only attribute.</summary>
      <returns>The metadata read-only attribute.</returns>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata">
      <summary>Provides a container for common metadata, for the <see cref="T:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider" /> class, for a data model.</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.#ctor(System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata,System.Func{System.Object})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata" /> class.</summary>
      <param name="prototype">The prototype used to initialize the model metadata.</param>
      <param name="modelAccessor">The model accessor.</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.#ctor(System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider,System.Type,System.Type,System.String,System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata" /> class.</summary>
      <param name="provider">The metadata provider.</param>
      <param name="containerType">The type of the container.</param>
      <param name="modelType">The type of the model.</param>
      <param name="propertyName">The name of the property.</param>
      <param name="attributes">The attributes that provides data for the initialization.</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.ComputeConvertEmptyStringToNull">
      <summary>Retrieves a value that indicates whether empty strings that are posted back in forms should be converted to null.</summary>
      <returns>true if empty strings that are posted back in forms should be converted to null; otherwise, false.</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.ComputeDescription">
      <summary>Retrieves the description of the model.</summary>
      <returns>The description of the model.</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata.ComputeIsReadOnly">
      <summary>Retrieves a value that indicates whether the model is read-only.</summary>
      <returns>true if the model is read-only; otherwise, false.</returns>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1">
      <summary>No content here will be updated; please do not add material here.</summary>
      <typeparam name="TPrototypeCache">The type of prototype cache.</typeparam>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.#ctor(System.Web.Http.Metadata.Providers.CachedModelMetadata{`0},System.Func{System.Object})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1" /> class.</summary>
      <param name="prototype">The prototype.</param>
      <param name="modelAccessor">The model accessor.</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.#ctor(System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider,System.Type,System.Type,System.String,`0)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Metadata.Providers.CachedModelMetadata`1" /> class.</summary>
      <param name="provider">The provider.</param>
      <param name="containerType">The type of container.</param>
      <param name="modelType">The type of the model.</param>
      <param name="propertyName">The name of the property.</param>
      <param name="prototypeCache">The prototype cache.</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ComputeConvertEmptyStringToNull">
      <summary>Indicates whether empty strings that are posted back in forms should be computed and converted to null.</summary>
      <returns>true if empty strings that are posted back in forms should be computed and converted to null; otherwise, false.</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ComputeDescription">
      <summary>Indicates the computation value.</summary>
      <returns>The computation value.</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ComputeIsComplexType">
      <summary>Gets a value that indicates whether the model is a complex type.</summary>
      <returns>A value that indicates whether the model is considered a complex type by the Web API framework.</returns>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ComputeIsReadOnly">
      <summary>Gets a value that indicates whether the model to be computed is read-only.</summary>
      <returns>true if the model to be computed is read-only; otherwise, false.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.ConvertEmptyStringToNull">
      <summary>Gets or sets a value that indicates whether empty strings that are posted back in forms should be converted to null.</summary>
      <returns>true if empty strings that are posted back in forms should be converted to null; otherwise, false. The default value is true.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.Description">
      <summary>Gets or sets the description of the model.</summary>
      <returns>The description of the model.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.IsComplexType">
      <summary>Gets a value that indicates whether the model is a complex type.</summary>
      <returns>A value that indicates whether the model is considered a complex type by the Web API framework.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.IsReadOnly">
      <summary>Gets or sets a value that indicates whether the model is read-only.</summary>
      <returns>true if the model is read-only; otherwise, false.</returns>
    </member>
    <member name="P:System.Web.Http.Metadata.Providers.CachedModelMetadata`1.PrototypeCache">
      <summary>Gets or sets a value that indicates whether the prototype cache is updating.</summary>
      <returns>true if the prototype cache is updating; otherwise, false.</returns>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider">
      <summary>Implements the default model metadata provider.</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider.CreateMetadataFromPrototype(System.Web.Http.Metadata.Providers.CachedDataAnnotationsModelMetadata,System.Func{System.Object})">
      <summary>Creates the metadata from prototype for the specified property.</summary>
      <returns>The metadata for the property.</returns>
      <param name="prototype">The prototype.</param>
      <param name="modelAccessor">The model accessor.</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.DataAnnotationsModelMetadataProvider.CreateMetadataPrototype(System.Collections.Generic.IEnumerable{System.Attribute},System.Type,System.Type,System.String)">
      <summary>Creates the metadata for the specified property.</summary>
      <returns>The metadata for the property.</returns>
      <param name="attributes">The attributes.</param>
      <param name="containerType">The type of the container.</param>
      <param name="modelType">The type of the model.</param>
      <param name="propertyName">The name of the property.</param>
    </member>
    <member name="T:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider.CreateMetadataFromPrototype(System.Web.Http.Metadata.ModelMetadata,System.Func{System.Object})">
      <summary>Creates metadata from prototype.</summary>
      <returns>The metadata.</returns>
      <param name="prototype">The model metadata prototype.</param>
      <param name="modelAccessor">The model accessor.</param>
    </member>
    <member name="M:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider.CreateMetadataPrototype(System.Collections.Generic.IEnumerable{System.Attribute},System.Type,System.Type,System.String)">
      <summary>Creates a prototype of the metadata provider of the <see cref="T:System.Web.Http.Metadata.Providers.EmptyModelMetadataProvider" />.</summary>
      <returns>A prototype of the metadata provider.</returns>
      <param name="attributes">The attributes.</param>
      <param name="containerType">The type of container.</param>
      <param name="modelType">The type of model.</param>
      <param name="propertyName">The name of the property.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.CancellationTokenParameterBinding">
      <summary>Represents the binding directly to the cancellation token.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.CancellationTokenParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.CancellationTokenParameterBinding" /> class.</summary>
      <param name="descriptor">The binding descriptor.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.CancellationTokenParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>Executes the binding during synchronization.</summary>
      <returns>The binding during synchronization.</returns>
      <param name="metadataProvider">The metadata provider.</param>
      <param name="actionContext">The action context.</param>
      <param name="cancellationToken">The notification after the cancellation of the operations.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.CustomModelBinderAttribute">
      <summary>Represents an attribute that invokes a custom model binder.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.CustomModelBinderAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.CustomModelBinderAttribute" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.CustomModelBinderAttribute.GetBinder">
      <summary>Retrieves the associated model binder.</summary>
      <returns>A reference to an object that implements the <see cref="T:System.Web.Http.ModelBinding.IModelBinder" /> interface.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.DefaultActionValueBinder">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.DefaultActionValueBinder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.DefaultActionValueBinder" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.DefaultActionValueBinder.GetBinding(System.Web.Http.Controllers.HttpActionDescriptor)">
      <summary>Default implementation of the <see cref="T:System.Web.Http.Controllers.IActionValueBinder" /> interface. This interface is the primary entry point for binding action parameters.</summary>
      <returns>The <see cref="T:System.Web.Http.Controllers.HttpActionBinding" /> associated with the <see cref="T:System.Web.Http.ModelBinding.DefaultActionValueBinder" />.</returns>
      <param name="actionDescriptor">The action descriptor.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.DefaultActionValueBinder.GetParameterBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>Gets the <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> associated with the <see cref="T:System.Web.Http.ModelBinding.DefaultActionValueBinder" />.</summary>
      <returns>The <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" /> associated with the <see cref="T:System.Web.Http.ModelBinding.DefaultActionValueBinder" />.</returns>
      <param name="parameter">The parameter descriptor.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ErrorParameterBinding">
      <summary>Defines a binding error.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ErrorParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ErrorParameterBinding" /> class.</summary>
      <param name="descriptor">The error descriptor.</param>
      <param name="message">The message.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ErrorParameterBinding.ErrorMessage">
      <summary>Gets the error message.</summary>
      <returns>The error message.</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ErrorParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>Executes the binding method during synchronization.</summary>
      <param name="metadataProvider">The metadata provider.</param>
      <param name="actionContext">The action context.</param>
      <param name="cancellationToken">The cancellation Token value.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.FormatterParameterBinding">
      <summary>Represents parameter binding that will read from the body and invoke the formatters.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormatterParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Web.Http.Validation.IBodyModelValidator)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" /> class.</summary>
      <param name="descriptor">The descriptor.</param>
      <param name="formatters">The formatter.</param>
      <param name="bodyModelValidator">The body model validator.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.FormatterParameterBinding.BodyModelValidator">
      <summary>Gets or sets an interface for the body model validator.</summary>
      <returns>An interface for the body model validator.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.FormatterParameterBinding.ErrorMessage">
      <summary>Gets the error message.</summary>
      <returns>The error message.</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormatterParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>Asynchronously execute the binding of <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" />.</summary>
      <returns>The result of the action.</returns>
      <param name="metadataProvider">The metadata provider.</param>
      <param name="actionContext">The context associated with the action.</param>
      <param name="cancellationToken">The cancellation token.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.FormatterParameterBinding.Formatters">
      <summary>Gets or sets an enumerable object that represents the formatter for the parameter binding.</summary>
      <returns>An enumerable object that represents the formatter for the parameter binding.</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormatterParameterBinding.ReadContentAsync(System.Net.Http.HttpRequestMessage,System.Type,System.Collections.Generic.IEnumerable{System.Net.Http.Formatting.MediaTypeFormatter},System.Net.Http.Formatting.IFormatterLogger)">
      <summary>Asynchronously reads the content of <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" />.</summary>
      <returns>The result of the action.</returns>
      <param name="request">The request.</param>
      <param name="type">The type.</param>
      <param name="formatters">The formatter.</param>
      <param name="formatterLogger">The format logger.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.FormatterParameterBinding.WillReadBody">
      <summary>Gets whether the <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" />will read body.</summary>
      <returns>True if the <see cref="T:System.Web.Http.ModelBinding.FormatterParameterBinding" />will read body; otherwise, false.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.FormDataCollectionExtensions">
      <summary>Represents the extensions for the collection of form data.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs``1(System.Net.Http.Formatting.FormDataCollection)">
      <summary>Reads the collection extensions with specified type.</summary>
      <returns>The read collection extensions.</returns>
      <param name="formData">The form data.</param>
      <typeparam name="T">The generic type.</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs``1(System.Net.Http.Formatting.FormDataCollection,System.String,System.Net.Http.Formatting.IRequiredMemberSelector,System.Net.Http.Formatting.IFormatterLogger)">
      <summary>Reads the collection extensions with specified type.</summary>
      <returns>The collection extensions.</returns>
      <param name="formData">The form data.</param>
      <param name="modelName">The name of the model.</param>
      <param name="requiredMemberSelector">The required member selector.</param>
      <param name="formatterLogger">The formatter logger.</param>
      <typeparam name="T">The generic type.</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs(System.Net.Http.Formatting.FormDataCollection,System.Type)">
      <summary>Reads the collection extensions with specified type.</summary>
      <returns>The collection extensions with specified type.</returns>
      <param name="formData">The form data.</param>
      <param name="type">The type of the object.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.FormDataCollectionExtensions.ReadAs(System.Net.Http.Formatting.FormDataCollection,System.Type,System.String,System.Net.Http.Formatting.IRequiredMemberSelector,System.Net.Http.Formatting.IFormatterLogger)">
      <summary>Reads the collection extensions with specified type and model name.</summary>
      <returns>The collection extensions.</returns>
      <param name="formData">The form data.</param>
      <param name="type">The type of the object.</param>
      <param name="modelName">The name of the model.</param>
      <param name="requiredMemberSelector">The required member selector.</param>
      <param name="formatterLogger">The formatter logger.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.HttpBindingBehavior">
      <summary>Enumerates the behavior of the HTTP binding.</summary>
    </member>
    <member name="F:System.Web.Http.ModelBinding.HttpBindingBehavior.Optional">
      <summary>The optional binding behavior</summary>
    </member>
    <member name="F:System.Web.Http.ModelBinding.HttpBindingBehavior.Never">
      <summary>Never use HTTP binding.</summary>
    </member>
    <member name="F:System.Web.Http.ModelBinding.HttpBindingBehavior.Required">
      <summary>HTTP binding is required.</summary>
    </member>
    <member name="T:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute">
      <summary>Provides a base class for model-binding behavior attributes.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute.#ctor(System.Web.Http.ModelBinding.HttpBindingBehavior)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute" /> class.</summary>
      <param name="behavior">The behavior.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute.Behavior">
      <summary>Gets or sets the behavior category.</summary>
      <returns>The behavior category.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.HttpBindingBehaviorAttribute.TypeId">
      <summary>Gets the unique identifier for this attribute.</summary>
      <returns>The id for this attribute.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.HttpRequestParameterBinding">
      <summary>Parameter binds to the request.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.HttpRequestParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.HttpRequestParameterBinding" /> class.</summary>
      <param name="descriptor">The parameter descriptor.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.HttpRequestParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>Asynchronously executes parameter binding.</summary>
      <returns>The binded parameter.</returns>
      <param name="metadataProvider">The metadata provider.</param>
      <param name="actionContext">The action context.</param>
      <param name="cancellationToken">The cancellation token.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.IModelBinder">
      <summary>Defines the methods that are required for a model binder.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.IModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Binds the model to a value by using the specified controller context and binding context.</summary>
      <returns>The bound value.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.IValueProviderParameterBinding">
      <summary>Represents a value provider for parameter binding.</summary>
    </member>
    <member name="P:System.Web.Http.ModelBinding.IValueProviderParameterBinding.ValueProviderFactories">
      <summary>Gets the <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> instances used by this parameter binding.</summary>
      <returns>The <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> instances used by this parameter binding.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter">
      <summary>Represents the <see cref="T:System.Net.Http.Formatting.MediaTypeFormatter" /> class for handling HTML form URL-ended data, also known as application/x-www-form-urlencoded.  </summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter.CanReadType(System.Type)">
      <summary> Determines whether this <see cref="T:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter" /> can read objects of the specified <paramref name="type" />. </summary>
      <returns>true if objects of this type can be read; otherwise false.</returns>
      <param name="type">The type of object that will be read.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.JQueryMvcFormUrlEncodedFormatter.ReadFromStreamAsync(System.Type,System.IO.Stream,System.Net.Http.HttpContent,System.Net.Http.Formatting.IFormatterLogger)">
      <summary>Reads an object of the specified <paramref name="type" /> from the specified stream. This method is called during deserialization.</summary>
      <returns>A <see cref="T:System.Threading.Tasks.Task" /> whose result will be the object instance that has been read.</returns>
      <param name="type">The type of object to read.</param>
      <param name="readStream">The <see cref="T:System.IO.Stream" /> from which to read.</param>
      <param name="content">The content being read.</param>
      <param name="formatterLogger">The <see cref="T:System.Net.Http.Formatting.IFormatterLogger" /> to log events to.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderAttribute">
      <summary> Specify this parameter uses a model binder. This can optionally specify the specific model binder and  value providers that drive that model binder.  Derived attributes may provide convenience settings for the model binder or value provider.  </summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelBinderAttribute" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelBinderAttribute" /> class.</summary>
      <param name="binderType">The type of model binder.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderAttribute.BinderType">
      <summary>Gets or sets the type of model binder.</summary>
      <returns>The type of model binder. </returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.GetBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary>Gets the binding for a parameter.</summary>
      <returns>The <see cref="T:System.Web.Http.Controllers.HttpParameterBinding" />that contains the binding.</returns>
      <param name="parameter">The parameter to bind.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.GetModelBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary> Get the IModelBinder for this type. </summary>
      <returns> a non-null model binder.</returns>
      <param name="configuration">The configuration.</param>
      <param name="modelType">model type that the binder is expected to bind.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.GetModelBinderProvider(System.Web.Http.HttpConfiguration)">
      <summary>Gets the model binder provider.</summary>
      <returns>The <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> instance.</returns>
      <param name="configuration">The <see cref="T:System.Web.Http.HttpConfiguration" /> configuration object.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderAttribute.GetValueProviderFactories(System.Web.Http.HttpConfiguration)">
      <summary> Gets the value providers that will be fed to the model binder. </summary>
      <returns>A collection of <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> instances.</returns>
      <param name="configuration">The <see cref="T:System.Web.Http.HttpConfiguration" /> configuration object.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderAttribute.Name">
      <summary>Gets or sets the name to consider as the parameter name during model binding.</summary>
      <returns>The parameter name to consider.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderAttribute.SuppressPrefixCheck">
      <summary>Gets or sets a value that specifies whether the prefix check should be suppressed.</summary>
      <returns>true if the prefix check should be suppressed; otherwise, false.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderConfig">
      <summary>Provides a container for model-binder configuration.</summary>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderConfig.ResourceClassKey">
      <summary>Gets or sets the name of the resource file (class key) that contains localized string values.</summary>
      <returns>The name of the resource file (class key).</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderConfig.TypeConversionErrorMessageProvider">
      <summary>Gets or sets the current provider for type-conversion error message.</summary>
      <returns>The current provider for type-conversion error message.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderConfig.ValueRequiredErrorMessageProvider">
      <summary>Gets or sets the current provider for value-required error messages.</summary>
      <returns>The error message provider.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderErrorMessageProvider">
      <summary>Provides a container for model-binder error message provider.</summary>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderParameterBinding">
      <summary> Describes a parameter that gets bound via ModelBinding.   </summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderParameterBinding.#ctor(System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.ModelBinding.IModelBinder,System.Collections.Generic.IEnumerable{System.Web.Http.ValueProviders.ValueProviderFactory})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelBinderParameterBinding" /> class.</summary>
      <param name="descriptor">The parameter descriptor.</param>
      <param name="modelBinder">The model binder.</param>
      <param name="valueProviderFactories">The collection of value provider factory.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderParameterBinding.Binder">
      <summary>Gets the model binder.</summary>
      <returns>The model binder.</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderParameterBinding.ExecuteBindingAsync(System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.Threading.CancellationToken)">
      <summary>Asynchronously executes the parameter binding via the model binder.</summary>
      <returns>The task that is signaled when the binding is complete.</returns>
      <param name="metadataProvider">The metadata provider to use for validation.</param>
      <param name="actionContext">The action context for the binding.</param>
      <param name="cancellationToken">The cancellation token assigned for this task for cancelling the binding operation.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBinderParameterBinding.ValueProviderFactories">
      <summary>Gets the collection of value provider factory.</summary>
      <returns>The collection of value provider factory.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBinderProvider">
      <summary>Provides an abstract base class for model binder providers.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>Finds a binder for the given type.</summary>
      <returns>A binder, which can attempt to bind this type. Or null if the binder knows statically that it will never be able to bind the type.</returns>
      <param name="configuration">A configuration object.</param>
      <param name="modelType">The type of the model to bind against.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelBindingContext">
      <summary>Provides the context in which a model binder functions.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBindingContext.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelBindingContext" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelBindingContext.#ctor(System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelBindingContext" /> class.</summary>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.FallbackToEmptyPrefix">
      <summary>Gets or sets a value that indicates whether the binder should use an empty prefix.</summary>
      <returns>true if the binder should use an empty prefix; otherwise, false.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.Model">
      <summary>Gets or sets the model.</summary>
      <returns>The model.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ModelMetadata">
      <summary>Gets or sets the model metadata.</summary>
      <returns>The model metadata.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ModelName">
      <summary>Gets or sets the name of the model.</summary>
      <returns>The name of the model.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ModelState">
      <summary>Gets or sets the state of the model.</summary>
      <returns>The state of the model.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ModelType">
      <summary>Gets or sets the type of the model.</summary>
      <returns>The type of the model.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.PropertyMetadata">
      <summary>Gets the property metadata.</summary>
      <returns>The property metadata.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ValidationNode">
      <summary>Gets or sets the validation node.</summary>
      <returns>The validation node.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelBindingContext.ValueProvider">
      <summary>Gets or sets the value provider.</summary>
      <returns>The value provider.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelError">
      <summary>Represents an error that occurs during model binding.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelError.#ctor(System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelError" /> class by using the specified exception.</summary>
      <param name="exception">The exception.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelError.#ctor(System.Exception,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelError" /> class by using the specified exception and error message.</summary>
      <param name="exception">The exception.</param>
      <param name="errorMessage">The error message</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelError.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelError" /> class by using the specified error message.</summary>
      <param name="errorMessage">The error message</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelError.ErrorMessage">
      <summary>Gets or sets the error message.</summary>
      <returns>The error message.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelError.Exception">
      <summary>Gets or sets the exception object.</summary>
      <returns>The exception object.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelErrorCollection">
      <summary>Represents a collection of <see cref="T:System.Web.Http.ModelBinding.ModelError" /> instances.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelErrorCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelErrorCollection" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelErrorCollection.Add(System.Exception)">
      <summary>Adds the specified Exception object to the model-error collection.</summary>
      <param name="exception">The exception.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelErrorCollection.Add(System.String)">
      <summary>Adds the specified error message to the model-error collection.</summary>
      <param name="errorMessage">The error message.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelState">
      <summary>Encapsulates the state of model binding to a property of an action-method argument, or to the argument itself.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelState.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelState" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelState.Errors">
      <summary>Gets a <see cref="T:System.Web.Http.ModelBinding.ModelErrorCollection" /> object that contains any errors that occurred during model binding.</summary>
      <returns>The model state errors.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelState.Value">
      <summary>Gets a <see cref="T:System.Web.Http.ValueProviders.ValueProviderResult" /> object that encapsulates the value that was being bound during model binding.</summary>
      <returns>The model state value.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ModelStateDictionary">
      <summary>Represents the state of an attempt to bind a posted form to an action method, which includes validation information.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /> class by using values that are copied from the specified model-state dictionary.</summary>
      <param name="dictionary">The dictionary.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Add(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.ModelBinding.ModelState})">
      <summary>Adds the specified item to the model-state dictionary.</summary>
      <param name="item">The object to add to the model-state dictionary.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Add(System.String,System.Web.Http.ModelBinding.ModelState)">
      <summary>Adds an element that has the specified key and value to the model-state dictionary.</summary>
      <param name="key">The key of the element to add.</param>
      <param name="value">The value of the element to add.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.AddModelError(System.String,System.Exception)">
      <summary>Adds the specified model error to the errors collection for the model-state dictionary that is associated with the specified key.</summary>
      <param name="key">The key.</param>
      <param name="exception">The exception.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.AddModelError(System.String,System.String)">
      <summary>Adds the specified error message to the errors collection for the model-state dictionary that is associated with the specified key.</summary>
      <param name="key">The key.</param>
      <param name="errorMessage">The error message.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Clear">
      <summary>Removes all items from the model-state dictionary.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Contains(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.ModelBinding.ModelState})">
      <summary>Determines whether the model-state dictionary contains a specific value.</summary>
      <returns>true if item is found in the model-state dictionary; otherwise, false.</returns>
      <param name="item">The object to locate in the model-state dictionary.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.ContainsKey(System.String)">
      <summary>Determines whether the model-state dictionary contains the specified key.</summary>
      <returns>true if the model-state dictionary contains the specified key; otherwise, false.</returns>
      <param name="key">The key to locate in the model-state dictionary.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.ModelBinding.ModelState}[],System.Int32)">
      <summary>Copies the elements of the model-state dictionary to an array, starting at a specified index.</summary>
      <param name="array">The array. The array must have zero-based indexing.</param>
      <param name="arrayIndex">The zero-based index in array at which copying starts.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.Count">
      <summary>Gets the number of key/value pairs in the collection.</summary>
      <returns>The number of key/value pairs in the collection.</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.GetEnumerator">
      <summary>Returns an enumerator that can be used to iterate through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.IsReadOnly">
      <summary>Gets a value that indicates whether the collection is read-only.</summary>
      <returns>true if the collection is read-only; otherwise, false.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.IsValid">
      <summary>Gets a value that indicates whether this instance of the model-state dictionary is valid.</summary>
      <returns>true if this instance is valid; otherwise, false.</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.IsValidField(System.String)">
      <summary>Determines whether there are any <see cref="T:System.Web.Http.ModelBinding.ModelError" /> objects that are associated with or prefixed with the specified key.</summary>
      <returns>true if the model-state dictionary contains a value that is associated with the specified key; otherwise, false.</returns>
      <param name="key">The key.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.Item(System.String)">
      <summary>Gets or sets the value that is associated with the specified key.</summary>
      <returns>The model state item.</returns>
      <param name="key">The key.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.Keys">
      <summary>Gets a collection that contains the keys in the dictionary.</summary>
      <returns>A collection that contains the keys of the model-state dictionary.</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Merge(System.Web.Http.ModelBinding.ModelStateDictionary)">
      <summary>Copies the values from the specified <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /> object into this dictionary, overwriting existing values if keys are the same.</summary>
      <param name="dictionary">The dictionary.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Remove(System.Collections.Generic.KeyValuePair{System.String,System.Web.Http.ModelBinding.ModelState})">
      <summary>Removes the first occurrence of the specified object from the model-state dictionary.</summary>
      <returns>true if item was successfully removed the model-state dictionary; otherwise, false. This method also returns false if item is not found in the model-state dictionary.</returns>
      <param name="item">The object to remove from the model-state dictionary.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.Remove(System.String)">
      <summary>Removes the element that has the specified key from the model-state dictionary.</summary>
      <returns>true if the element is successfully removed; otherwise, false. This method also returns false if key was not found in the model-state dictionary.</returns>
      <param name="key">The key of the element to remove.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.SetModelValue(System.String,System.Web.Http.ValueProviders.ValueProviderResult)">
      <summary>Sets the value for the specified key by using the specified value provider dictionary.</summary>
      <param name="key">The key.</param>
      <param name="value">The value.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An IEnumerator object that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ModelStateDictionary.TryGetValue(System.String,System.Web.Http.ModelBinding.ModelState@)">
      <summary>Attempts to gets the value that is associated with the specified key.</summary>
      <returns>true if the object contains an element that has the specified key; otherwise, false.</returns>
      <param name="key">The key of the value to get.</param>
      <param name="value">The value associated with the specified key.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.ModelStateDictionary.Values">
      <summary>Gets a collection that contains the values in the dictionary.</summary>
      <returns>A collection that contains the values of the model-state dictionary.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.ParameterBindingRulesCollection">
      <summary> Collection of functions that can produce a parameter binding for a given parameter.    </summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ParameterBindingRulesCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.ParameterBindingRulesCollection" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ParameterBindingRulesCollection.Add(System.Type,System.Func{System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.Controllers.HttpParameterBinding})">
      <summary> Adds function to the end of the collection.  The function added is a wrapper around funcInner that checks that parameterType matches typeMatch. </summary>
      <param name="typeMatch">type to match against HttpParameterDescriptor.ParameterType</param>
      <param name="funcInner">inner function that is invoked if type match succeeds</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ParameterBindingRulesCollection.Insert(System.Int32,System.Type,System.Func{System.Web.Http.Controllers.HttpParameterDescriptor,System.Web.Http.Controllers.HttpParameterBinding})">
      <summary> Insert a function at the specified index in the collection. /// The function added is a wrapper around funcInner that checks that parameterType matches typeMatch. </summary>
      <param name="index">index to insert at.</param>
      <param name="typeMatch">type to match against HttpParameterDescriptor.ParameterType</param>
      <param name="funcInner">inner function that is invoked if type match succeeds</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.ParameterBindingRulesCollection.LookupBinding(System.Web.Http.Controllers.HttpParameterDescriptor)">
      <summary> Execute each binding function in order until one of them returns a non-null binding.  </summary>
      <returns>the first non-null binding produced for the parameter. Of null if no binding is produced.</returns>
      <param name="parameter">parameter to bind.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1">
      <summary>Maps a browser request to an array.</summary>
      <typeparam name="TElement">The type of the array.</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Indicates whether the model is binded.</summary>
      <returns>true if the specified model is binded; otherwise, false.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinder`1.CreateOrReplaceCollection(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Collections.Generic.IList{`0})">
      <summary>Converts the collection to an array.</summary>
      <returns>true in all cases.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
      <param name="newCollection">The new collection.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ArrayModelBinderProvider">
      <summary>Provides a model binder for arrays.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinderProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.ArrayModelBinderProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ArrayModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>Returns a model binder for arrays.</summary>
      <returns>A model binder object or null if the attempt to get a model binder is unsuccessful.</returns>
      <param name="configuration">The configuration.</param>
      <param name="modelType">The type of model.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1">
      <summary>Maps a browser request to a collection.</summary>
      <typeparam name="TElement">The type of the collection.</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Binds the model by using the specified execution context and binding context.</summary>
      <returns>true if model binding is successful; otherwise, false.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinder`1.CreateOrReplaceCollection(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Collections.Generic.IList{`0})">
      <summary>Provides a way for derived classes to manipulate the collection before returning it from the binder.</summary>
      <returns>true in all cases.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
      <param name="newCollection">The new collection.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.CollectionModelBinderProvider">
      <summary>Provides a model binder for a collection.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinderProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.CollectionModelBinderProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CollectionModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>Retrieves a model binder for a collection.</summary>
      <returns>The model binder.</returns>
      <param name="configuration">The configuration of the model.</param>
      <param name="modelType">The type of the model.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto">
      <summary>Represents a data transfer object (DTO) for a complex model.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDto.#ctor(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Metadata.ModelMetadata})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> class.</summary>
      <param name="modelMetadata">The model metadata.</param>
      <param name="propertyMetadata">The collection of property metadata.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDto.ModelMetadata">
      <summary>Gets or sets the model metadata of the <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" />.</summary>
      <returns>The model metadata of the <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" />.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDto.PropertyMetadata">
      <summary>Gets or sets the collection of property metadata of the <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" />.</summary>
      <returns>The collection of property metadata of the <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" />.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDto.Results">
      <summary>Gets or sets the results of the <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" />.</summary>
      <returns>The results of the <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" />.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinder">
      <summary>Represents a model binder for <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> object.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinder" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Determines whether the specified model is binded.</summary>
      <returns>true if the specified model is binded; otherwise, false.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinderProvider">
      <summary>Represents a complex model that invokes a model binder provider.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinderProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinderProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>Retrieves the associated model binder.</summary>
      <returns>The model binder.</returns>
      <param name="configuration">The configuration.</param>
      <param name="modelType">The type of the model to retrieve.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult">
      <summary>Represents the result for <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDto" /> object.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult.#ctor(System.Object,System.Web.Http.Validation.ModelValidationNode)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult" /> class.</summary>
      <param name="model">The object model.</param>
      <param name="validationNode">The validation node.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult.Model">
      <summary>Gets or sets the model for this object.</summary>
      <returns>The model for this object.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult.ValidationNode">
      <summary>Gets or sets the <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> for this object.</summary>
      <returns>The <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> for this object.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinder">
      <summary>Represents an <see cref="T:System.Web.Http.ModelBinding.IModelBinder" /> that delegates to one of a collection of <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /> instances.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinder.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.ModelBinding.IModelBinder})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinder" /> class.</summary>
      <param name="binders">An enumeration of binders.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinder.#ctor(System.Web.Http.ModelBinding.IModelBinder[])">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinder" /> class.</summary>
      <param name="binders">An array of binders.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Indicates whether the specified model is binded.</summary>
      <returns>true if the model is binded; otherwise, false.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider">
      <summary>Represents the class for composite model binder providers.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.ModelBinding.ModelBinderProvider})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider" /> class.</summary>
      <param name="providers">A collection of <see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /></param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>Gets the binder for the model.</summary>
      <returns>The binder for the model.</returns>
      <param name="configuration">The binder configuration.</param>
      <param name="modelType">The type of the model.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.CompositeModelBinderProvider.Providers">
      <summary>Gets the providers for the composite model binder.</summary>
      <returns>The collection of providers.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.DictionaryModelBinder`2">
      <summary>Maps a browser request to a dictionary data object.</summary>
      <typeparam name="TKey">The type of the key.</typeparam>
      <typeparam name="TValue">The type of the value.</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.DictionaryModelBinder`2.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.DictionaryModelBinder`2" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.DictionaryModelBinder`2.CreateOrReplaceCollection(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Collections.Generic.IList{System.Collections.Generic.KeyValuePair{`0,`1}})">
      <summary>Converts the collection to a dictionary.</summary>
      <returns>true in all cases.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
      <param name="newCollection">The new collection.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.DictionaryModelBinderProvider">
      <summary>Provides a model binder for a dictionary.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.DictionaryModelBinderProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.DictionaryModelBinderProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.DictionaryModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>Retrieves the associated model binder.</summary>
      <returns>The associated model binder.</returns>
      <param name="configuration">The configuration to use.</param>
      <param name="modelType">The type of model.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinder`2">
      <summary>Maps a browser request to a key/value pair data object.</summary>
      <typeparam name="TKey">The type of the key.</typeparam>
      <typeparam name="TValue">The type of the value.</typeparam>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinder`2.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinder`2" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinder`2.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Binds the model by using the specified execution context and binding context.</summary>
      <returns>true if model binding is successful; otherwise, false.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinderProvider">
      <summary>Provides a model binder for a collection of key/value pairs.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinderProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinderProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.KeyValuePairModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>Retrieves the associated model binder.</summary>
      <returns>The associated model binder.</returns>
      <param name="configuration">The configuration.</param>
      <param name="modelType">The type of model.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder">
      <summary>Maps a browser request to a mutable data object.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Binds the model by using the specified action context and binding context.</summary>
      <returns>true if binding is successful; otherwise, false.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.CanUpdateProperty(System.Web.Http.Metadata.ModelMetadata)">
      <summary>Retrieves a value that indicates whether a property can be updated.</summary>
      <returns>true if the property can be updated; otherwise, false.</returns>
      <param name="propertyMetadata">The metadata for the property to be evaluated.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.CreateModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Creates an instance of the model.</summary>
      <returns>The newly created model object.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.EnsureModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Creates a model instance if an instance does not yet exist in the binding context.</summary>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.GetMetadataForProperties(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Retrieves metadata for properties of the model.</summary>
      <returns>The metadata for properties of the model.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinder.SetProperty(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext,System.Web.Http.Metadata.ModelMetadata,System.Web.Http.ModelBinding.Binders.ComplexModelDtoResult,System.Web.Http.Validation.ModelValidator)">
      <summary>Sets the value of a specified property.</summary>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
      <param name="propertyMetadata">The metadata for the property to set.</param>
      <param name="dtoResult">The validation information about the property.</param>
      <param name="requiredValidator">The validator for the model.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinderProvider">
      <summary>Provides a model binder for mutable objects.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinderProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinderProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.MutableObjectModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>Retrieves the model binder for the specified type.</summary>
      <returns>The model binder.</returns>
      <param name="configuration">The configuration.</param>
      <param name="modelType">The type of the model to retrieve.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.#ctor(System.Type,System.Func{System.Web.Http.ModelBinding.IModelBinder})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider" /> class.</summary>
      <param name="modelType">The model type.</param>
      <param name="modelBinderFactory">The model binder factory.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.#ctor(System.Type,System.Web.Http.ModelBinding.IModelBinder)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider" /> class by using the specified model type and the model binder.</summary>
      <param name="modelType">The model type.</param>
      <param name="modelBinder">The model binder.</param>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>Returns a model binder by using the specified execution context and binding context.</summary>
      <returns>The model binder, or null if the attempt to get a model binder is unsuccessful.</returns>
      <param name="configuration">The configuration.</param>
      <param name="modelType">The model type.</param>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.ModelType">
      <summary>Gets the type of the model.</summary>
      <returns>The type of the model.</returns>
    </member>
    <member name="P:System.Web.Http.ModelBinding.Binders.SimpleModelBinderProvider.SuppressPrefixCheck">
      <summary>Gets or sets a value that specifies whether the prefix check should be suppressed.</summary>
      <returns>true if the prefix check should be suppressed; otherwise, false.</returns>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinder">
      <summary>Maps a browser request to a data object. This type is used when model binding requires conversions using a .NET Framework type converter.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinder" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Binds the model by using the specified controller context and binding context.</summary>
      <returns>true if model binding is successful; otherwise, false.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinderProvider">
      <summary>Provides a model binder for a model that requires type conversion.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinderProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinderProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeConverterModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>Retrieve a model binder for a model that requires type conversion.</summary>
      <returns>The model binder, or Nothing if the type cannot be converted or there is no value to convert.</returns>
      <param name="configuration">The configuration of the binder.</param>
      <param name="modelType">The type of the model.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinder">
      <summary>Maps a browser request to a data object. This class is used when model binding does not require type conversion.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinder" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinder.BindModel(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.ModelBinding.ModelBindingContext)">
      <summary>Binds the model by using the specified execution context and binding context.</summary>
      <returns>true if model binding is successful; otherwise, false.</returns>
      <param name="actionContext">The action context.</param>
      <param name="bindingContext">The binding context.</param>
    </member>
    <member name="T:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinderProvider">
      <summary>Provides a model binder for a model that does not require type conversion.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinderProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinderProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ModelBinding.Binders.TypeMatchModelBinderProvider.GetBinder(System.Web.Http.HttpConfiguration,System.Type)">
      <summary>Retrieves the associated model binder.</summary>
      <returns>The associated model binder.</returns>
      <param name="configuration">The configuration.</param>
      <param name="modelType">The type of model.</param>
    </member>
    <member name="T:System.Web.Http.Query.DefaultStructuredQueryBuilder">
      <summary> The <see cref="T:System.Web.Http.Query.DefaultStructuredQueryBuilder" /> understands $filter, $orderby, $top and $skip OData query parameters </summary>
    </member>
    <member name="M:System.Web.Http.Query.DefaultStructuredQueryBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Query.DefaultStructuredQueryBuilder" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Query.DefaultStructuredQueryBuilder.GetStructuredQuery(System.Uri)">
      <summary> Build the <see cref="T:System.Web.Http.Query.StructuredQuery" /> for the given uri. </summary>
      <returns>The <see cref="T:System.Web.Http.Query.StructuredQuery" /></returns>
      <param name="uri">The <see cref="T:System.Uri" /> to build the <see cref="T:System.Web.Http.Query.StructuredQuery" /> from</param>
    </member>
    <member name="T:System.Web.Http.Query.IStructuredQueryBuilder">
      <summary> A <see cref="T:System.Web.Http.Query.IStructuredQueryBuilder" /> is used to extract the query from a Uri. </summary>
    </member>
    <member name="M:System.Web.Http.Query.IStructuredQueryBuilder.GetStructuredQuery(System.Uri)">
      <summary> Build the <see cref="T:System.Web.Http.Query.StructuredQuery" /> for the given uri. Return null if there is no query  in the Uri. </summary>
      <returns>The <see cref="T:System.Web.Http.Query.StructuredQuery" /></returns>
      <param name="uri">The <see cref="T:System.Uri" /> to build the <see cref="T:System.Web.Http.Query.StructuredQuery" /> from</param>
    </member>
    <member name="T:System.Web.Http.Query.IStructuredQueryPart">
      <summary> Represents a query option like $filter, $top etc. </summary>
    </member>
    <member name="M:System.Web.Http.Query.IStructuredQueryPart.ApplyTo(System.Linq.IQueryable)">
      <summary> Applies this <see cref="T:System.Web.Http.Query.IStructuredQueryPart" /> on to an <see cref="T:System.Linq.IQueryable" /> returning the resultant <see cref="T:System.Linq.IQueryable" /></summary>
      <returns>The resultant <see cref="T:System.Linq.IQueryable" /></returns>
      <param name="source">The source <see cref="T:System.Linq.IQueryable" /></param>
    </member>
    <member name="P:System.Web.Http.Query.IStructuredQueryPart.QueryExpression">
      <summary> The value part of the query parameter for this query part. </summary>
    </member>
    <member name="P:System.Web.Http.Query.IStructuredQueryPart.QueryOperator">
      <summary> The query operator that this query parameter is for. </summary>
    </member>
    <member name="T:System.Web.Http.Query.StructuredQuery">
      <summary> Represents an <see cref="T:System.Linq.IQueryable" />. </summary>
    </member>
    <member name="M:System.Web.Http.Query.StructuredQuery.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Query.StructuredQuery" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.Query.StructuredQuery.QueryParts">
      <summary> Gets or sets a list of query parts. </summary>
    </member>
    <member name="T:System.Web.Http.Routing.HttpMethodConstraint">
      <summary>Enables you to define which HTTP verbs are allowed when ASP.NET routing determines whether a URL matches a route.</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpMethodConstraint.#ctor(System.Net.Http.HttpMethod[])">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpMethodConstraint" /> class by using the HTTP verbs that are allowed for the route.</summary>
      <param name="allowedMethods">The HTTP verbs that are valid for the route.</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpMethodConstraint.AllowedMethods">
      <summary>Gets or sets the collection of allowed HTTP verbs for the route.</summary>
      <returns>A collection of allowed HTTP verbs for the route.</returns>
    </member>
    <member name="M:System.Web.Http.Routing.HttpMethodConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>Determines whether the request was made with an HTTP verb that is one of the allowed verbs for the route.</summary>
      <returns>When ASP.NET routing is processing a request, true if the request was made by using an allowed HTTP verb; otherwise, false. When ASP.NET routing is constructing a URL, true if the supplied values contain an HTTP verb that matches one of the allowed HTTP verbs; otherwise, false. The default is true.</returns>
      <param name="request">The request that is being checked to determine whether it matches the URL.</param>
      <param name="route">The object that is being checked to determine whether it matches the URL.</param>
      <param name="parameterName">The name of the parameter that is being checked.</param>
      <param name="values">An object that contains the parameters for a route.</param>
      <param name="routeDirection">An object that indicates whether the constraint check is being performed when an incoming request is processed or when a URL is generated.</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpMethodConstraint.System#Web#Http#Routing#IHttpRouteConstraint#Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>Determines whether the request was made with an HTTP verb that is one of the allowed verbs for the route.</summary>
      <returns>When ASP.NET routing is processing a request, true if the request was made by using an allowed HTTP verb; otherwise, false. When ASP.NET routing is constructing a URL, true if the supplied values contain an HTTP verb that matches one of the allowed HTTP verbs; otherwise, false. The default is true.</returns>
      <param name="request">The request that is being checked to determine whether it matches the URL.</param>
      <param name="route">The object that is being checked to determine whether it matches the URL.</param>
      <param name="parameterName">The name of the parameter that is being checked.</param>
      <param name="values">An object that contains the parameters for a route.</param>
      <param name="routeDirection">An object that indicates whether the constraint check is being performed when an incoming request is processed or when a URL is generated.</param>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRoute">
      <summary>Represents a route class for self-host (i.e. hosted outside of ASP.NET).</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpRoute" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpRoute" /> class.</summary>
      <param name="routeTemplate">The route template.</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String,System.Web.Http.Routing.HttpRouteValueDictionary)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpRoute" /> class.</summary>
      <param name="routeTemplate">The route template.</param>
      <param name="defaults">The default values for the route parameters.</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpRoute" /> class.</summary>
      <param name="routeTemplate">The route template.</param>
      <param name="defaults">The default values for the route parameters.</param>
      <param name="constraints">The constraints for the route parameters.</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpRoute" /> class.</summary>
      <param name="routeTemplate">The route template.</param>
      <param name="defaults">The default values for the route parameters.</param>
      <param name="constraints">The constraints for the route parameters.</param>
      <param name="dataTokens">Any additional tokens for the route parameters.</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.#ctor(System.String,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteValueDictionary,System.Net.Http.HttpMessageHandler)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpRoute" /> class.</summary>
      <param name="routeTemplate">The route template.</param>
      <param name="defaults">The default values for the route parameters.</param>
      <param name="constraints">The constraints for the route parameters.</param>
      <param name="dataTokens">Any additional tokens for the route parameters.</param>
      <param name="handler">The message handler that will be the recipient of the request.</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.Constraints">
      <summary>Gets the constraints for the route parameters.</summary>
      <returns>The constraints for the route parameters.</returns>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.DataTokens">
      <summary>Gets any additional data tokens not used directly to determine whether a route matches an incoming <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>Any additional data tokens not used directly to determine whether a route matches an incoming <see cref="T:System.Net.Http.HttpRequestMessage" />.</returns>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.Defaults">
      <summary>Gets the default values for route parameters if not provided by the incoming <see cref="T:System.Net.Http.HttpRequestMessage" />.</summary>
      <returns>The default values for route parameters if not provided by the incoming <see cref="T:System.Net.Http.HttpRequestMessage" />.</returns>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.GetRouteData(System.String,System.Net.Http.HttpRequestMessage)">
      <summary>Determines whether this route is a match for the incoming request by looking up the <see cref="T:System.Web.Http.Routing.HttpRouteData" /> for the route.</summary>
      <returns>The <see cref="T:System.Web.Http.Routing.HttpRouteData" /> for a route if matches; otherwise null.</returns>
      <param name="virtualPathRoot">The virtual path root.</param>
      <param name="request">The HTTP request.</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.GetVirtualPath(System.Net.Http.HttpRequestMessage,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Attempts to generate a URI that represents the values passed in based on current values from the <see cref="T:System.Web.Http.Routing.HttpRouteData" /> and new values using the specified <see cref="T:System.Web.Http.Routing.HttpRoute" />.</summary>
      <returns>A <see cref="T:System.Web.Http.Routing.HttpVirtualPathData" /> instance or null if URI cannot be generated.</returns>
      <param name="request">The HTTP request message.</param>
      <param name="values">The route values.</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.Handler">
      <summary>Gets or sets the http route handler.</summary>
      <returns>The http route handler.</returns>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRoute.ProcessConstraint(System.Net.Http.HttpRequestMessage,System.Object,System.String,System.Web.Http.Routing.HttpRouteValueDictionary,System.Web.Http.Routing.HttpRouteDirection)">
      <summary>Determines whether this instance equals a specified route.</summary>
      <returns>true if this instance equals a specified route; otherwise, false.</returns>
      <param name="request">The HTTP request.</param>
      <param name="constraint">The constraints for the route parameters.</param>
      <param name="parameterName">The name of the parameter.</param>
      <param name="values">The list of parameter values.</param>
      <param name="routeDirection">One of the enumeration values of the <see cref="System.Web.Http.Routing.HttpRouteDirection" /> enumeration.</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRoute.RouteTemplate">
      <summary>Gets the route template describing the URI pattern to match against.</summary>
      <returns>The route template describing the URI pattern to match against.</returns>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRouteData">
      <summary>Encapsulates information regarding the HTTP route.</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteData.#ctor(System.Web.Http.Routing.IHttpRoute)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpRouteData" /> class.</summary>
      <param name="route">An object that defines the route.</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteData.#ctor(System.Web.Http.Routing.IHttpRoute,System.Web.Http.Routing.HttpRouteValueDictionary)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpRouteData" /> class.</summary>
      <param name="route">An object that defines the route.</param>
      <param name="values">The value.</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRouteData.Route">
      <summary>Gets the object that represents the route.</summary>
      <returns>the object that represents the route.</returns>
    </member>
    <member name="P:System.Web.Http.Routing.HttpRouteData.Values">
      <summary>Gets a collection of URL parameter values and default values for the route.</summary>
      <returns>An object that contains values that are parsed from the URL and from default values.</returns>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRouteDirection">
      <summary>Specifies an enumeration of route direction.</summary>
    </member>
    <member name="F:System.Web.Http.Routing.HttpRouteDirection.UriResolution">
      <summary>The UriResolution direction.</summary>
    </member>
    <member name="F:System.Web.Http.Routing.HttpRouteDirection.UriGeneration">
      <summary>The UriGeneration direction.</summary>
    </member>
    <member name="T:System.Web.Http.Routing.HttpRouteValueDictionary">
      <summary>Represents a route class for self-host of specified key/value pairs.</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteValueDictionary.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpRouteValueDictionary" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteValueDictionary.#ctor(System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpRouteValueDictionary" /> class.</summary>
      <param name="dictionary">The dictionary.</param>
    </member>
    <member name="M:System.Web.Http.Routing.HttpRouteValueDictionary.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpRouteValueDictionary" /> class.</summary>
      <param name="values">The key value.</param>
    </member>
    <member name="T:System.Web.Http.Routing.HttpVirtualPathData">
      <summary>Presents the data regarding the HTTP virtual path.</summary>
    </member>
    <member name="M:System.Web.Http.Routing.HttpVirtualPathData.#ctor(System.Web.Http.Routing.IHttpRoute,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.HttpVirtualPathData" /> class.</summary>
      <param name="route">The route of the virtual path.</param>
      <param name="virtualPath">The URL that was created from the route definition.</param>
    </member>
    <member name="P:System.Web.Http.Routing.HttpVirtualPathData.Route">
      <summary>Gets or sets the route of the virtual path..</summary>
      <returns>The route of the virtual path.</returns>
    </member>
    <member name="P:System.Web.Http.Routing.HttpVirtualPathData.VirtualPath">
      <summary>Gets or sets the URL that was created from the route definition.</summary>
      <returns>The URL that was created from the route definition.</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpRoute">
      <summary>
        <see cref="T:System.Web.Http.Routing.IHttpRoute" /> defines the interface for a route expressing how to map an incoming <see cref="T:System.Net.Http.HttpRequestMessage" /> to a particular controller and action. </summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.Constraints">
      <summary> Gets the constraints for the route parameters. </summary>
      <returns>The constraints for the route parameters.</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.DataTokens">
      <summary> Gets any additional data tokens not used directly to determine whether a route matches an incoming <see cref="T:System.Net.Http.HttpRequestMessage" />. </summary>
      <returns>The additional data tokens.</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.Defaults">
      <summary> Gets the default values for route parameters if not provided by the incoming <see cref="T:System.Net.Http.HttpRequestMessage" />. </summary>
      <returns>The default values for route parameters.</returns>
    </member>
    <member name="M:System.Web.Http.Routing.IHttpRoute.GetRouteData(System.String,System.Net.Http.HttpRequestMessage)">
      <summary> Determine whether this route is a match for the incoming request by looking up the &lt;see cref="!:IRouteData" /&gt; for the route. </summary>
      <returns>The &lt;see cref="!:RouteData" /&gt; for a route if matches; otherwise null.</returns>
      <param name="virtualPathRoot">The virtual path root.</param>
      <param name="request">The request.</param>
    </member>
    <member name="M:System.Web.Http.Routing.IHttpRoute.GetVirtualPath(System.Net.Http.HttpRequestMessage,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Gets a virtual path data based on the route and the values provided.</summary>
      <returns>The virtual path data.</returns>
      <param name="request">The request message.</param>
      <param name="values">The values.</param>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.Handler">
      <summary>Gets the message handler that will be the recipient of the request.</summary>
      <returns>The message handler.</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRoute.RouteTemplate">
      <summary> Gets the route template describing the URI pattern to match against.  </summary>
      <returns>The route template.</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpRouteConstraint">
      <summary>Represents a base class route constraint.</summary>
    </member>
    <member name="M:System.Web.Http.Routing.IHttpRouteConstraint.Match(System.Net.Http.HttpRequestMessage,System.Web.Http.Routing.IHttpRoute,System.String,System.Collections.Generic.IDictionary{System.String,System.Object},System.Web.Http.Routing.HttpRouteDirection)">
      <summary>Determines whether this instance equals a specified route.</summary>
      <returns>True if this instance equals a specified route; otherwise, false.</returns>
      <param name="request">The request.</param>
      <param name="route">The route to compare.</param>
      <param name="parameterName">The name of the parameter.</param>
      <param name="values">A list of parameter values.</param>
      <param name="routeDirection">The route direction.</param>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpRouteData">
      <summary>Provides information about a route.</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRouteData.Route">
      <summary>Gets the object that represents the route.</summary>
      <returns>The object that represents the route.</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpRouteData.Values">
      <summary>Gets a collection of URL parameter values and default values for the route.</summary>
      <returns>The values that are parsed from the URL and from default values.</returns>
    </member>
    <member name="T:System.Web.Http.Routing.IHttpVirtualPathData">
      <summary>Defines the properties for HTTP route.</summary>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpVirtualPathData.Route">
      <summary>Gets the HTTP route.</summary>
      <returns>The HTTP route.</returns>
    </member>
    <member name="P:System.Web.Http.Routing.IHttpVirtualPathData.VirtualPath">
      <summary>Gets the URI that represents the virtual path of the current HTTP route.</summary>
      <returns>The URI that represents the virtual path of the current HTTP route.</returns>
    </member>
    <member name="T:System.Web.Http.Routing.UrlHelper">
      <summary>No content here will be updated; please do not add material here.</summary>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.#ctor(System.Net.Http.HttpRequestMessage)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Routing.UrlHelper" /> class.</summary>
      <param name="request">The HTTP request for this instance.</param>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Link(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Returns a link for the specified route.</summary>
      <returns>A link for the specified route.</returns>
      <param name="routeName">The name of the route.</param>
      <param name="routeValues">An object that contains the parameters for a route.</param>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Link(System.String,System.Object)">
      <summary>Returns a link for the specified route.</summary>
      <returns>A link for the specified route.</returns>
      <param name="routeName">The name of the route.</param>
      <param name="routeValues">A route value.</param>
    </member>
    <member name="P:System.Web.Http.Routing.UrlHelper.Request">
      <summary>Gets or sets the <see cref="T:System.Net.Http.HttpRequestMessage" /> of the current <see cref="T:System.Web.Http.Routing.UrlHelper" /> instance.</summary>
      <returns>The <see cref="T:System.Net.Http.HttpRequestMessage" /> of the current instance.</returns>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Route(System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Returns the route for the <see cref="T:System.Web.Http.Routing.UrlHelper" />.</summary>
      <returns>The route for the <see cref="T:System.Web.Http.Routing.UrlHelper" />.</returns>
      <param name="routeName">The name of the route.</param>
      <param name="routeValues">A list of route values.</param>
    </member>
    <member name="M:System.Web.Http.Routing.UrlHelper.Route(System.String,System.Object)">
      <summary>Returns the route for the <see cref="T:System.Web.Http.Routing.UrlHelper" />.</summary>
      <returns>The route for the <see cref="T:System.Web.Http.Routing.UrlHelper" />.</returns>
      <param name="routeName">The name of the route.</param>
      <param name="routeValues">The route values.</param>
    </member>
    <member name="T:System.Web.Http.Services.DefaultServices">
      <summary>  Represents a container for service instances used by the <see cref="T:System.Web.Http.HttpConfiguration" />. Note that         this container only supports known types, and methods to get or set arbitrary service types will         throw <see cref="T:System.ArgumentException" /> when called. For creation of arbitrary types, please use         <see cref="T:System.Web.Http.Dependencies.IDependencyResolver" /> instead. The supported types for this container are:        <see cref="T:System.Web.Http.Controllers.IActionValueBinder" /><see cref="T:System.Web.Http.Description.IApiExplorer" /><see cref="T:System.Web.Http.Dispatcher.IAssembliesResolver" /><see cref="T:System.Web.Http.Validation.IBodyModelValidator" /><see cref="T:System.Net.Http.Formatting.IContentNegotiator" /><see cref="T:System.Web.Http.Description.IDocumentationProvider" /><see cref="T:System.Web.Http.Filters.IFilterProvider" /><see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /><see cref="T:System.Web.Http.Controllers.IHttpActionInvoker" /><see cref="T:System.Web.Http.Controllers.IHttpActionSelector" /><see cref="T:System.Web.Http.Dispatcher.IHttpControllerActivator" /><see cref="T:System.Web.Http.Dispatcher.IHttpControllerSelector" /><see cref="T:System.Web.Http.Dispatcher.IHttpControllerTypeResolver" /><see cref="T:System.Web.Http.Tracing.ITraceManager" /><see cref="T:System.Web.Http.Tracing.ITraceWriter" /><see cref="T:System.Web.Http.Query.IStructuredQueryBuilder" /><see cref="T:System.Web.Http.ModelBinding.ModelBinderProvider" /><see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /><see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /><see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" />Passing any type which is not on this to any method on this interface will cause         an <see cref="T:System.ArgumentException" /> to be thrown. </summary>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Services.DefaultServices" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.#ctor(System.Web.Http.HttpConfiguration)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Services.DefaultServices" /> class with a specified <see cref="T:System.Web.Http.HttpConfiguration" /> object.</summary>
      <param name="configuration">The <see cref="T:System.Web.Http.HttpConfiguration" /> object.</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.ClearSingle(System.Type)">
      <summary>Removes a single-instance service from the default services.</summary>
      <param name="serviceType">The type of the service.</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.GetService(System.Type)">
      <summary>Gets a service of the specified type.</summary>
      <returns>The first instance of the service, or null if the service is not found.</returns>
      <param name="serviceType">The type of service.</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.GetServiceInstances(System.Type)">
      <summary>Gets the list of service objects for a given service type, and validates the service type.</summary>
      <returns>The list of service objects of the specified type.</returns>
      <param name="serviceType">The service type.</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.GetServices(System.Type)">
      <summary>Gets the list of service objects for a given service type.</summary>
      <returns>The list of service objects of the specified type, or an empty list if the service is not found.</returns>
      <param name="serviceType">The type of service.</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.IsSingleService(System.Type)">
      <summary>Queries whether a service type is single-instance.</summary>
      <returns>true if the service type has at most one instance, or false if the service type supports multiple instances.</returns>
      <param name="serviceType">The service type.</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.ReplaceSingle(System.Type,System.Object)">
      <summary>Replaces a single-instance service object.</summary>
      <param name="serviceType">The service type.</param>
      <param name="service">The service object that replaces the previous instance.</param>
    </member>
    <member name="M:System.Web.Http.Services.DefaultServices.ResetCache(System.Type)">
      <summary>Removes the cached values for a single service type.</summary>
      <param name="serviceType">The service type.</param>
    </member>
    <member name="T:System.Web.Http.Tracing.ITraceManager">
      <summary>Represents a performance tracing class to log method entry/exit and duration.</summary>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceManager.Initialize(System.Web.Http.HttpConfiguration)">
      <summary>Initializes the <see cref="T:System.Web.Http.Tracing.ITraceManager" /> class with a specified configuration.</summary>
      <param name="configuration">The configuration.</param>
    </member>
    <member name="T:System.Web.Http.Tracing.ITraceWriter">
      <summary>Represents the trace writer.</summary>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriter.Trace(System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary> Invokes the specified traceAction to allow setting values in a new <see cref="T:System.Web.Http.Tracing.TraceRecord" /> if and only if tracing is permitted at the given category and level. </summary>
      <param name="request">The current <see cref="T:System.Net.Http.HttpRequestMessage" />.   It may be null but doing so will prevent subsequent trace analysis  from correlating the trace to a particular request.</param>
      <param name="category">The logical category for the trace.  Users can define their own.</param>
      <param name="level">The <see cref="T:System.Web.Http.Tracing.TraceLevel" /> at which to write this trace.</param>
      <param name="traceAction">The action to invoke if tracing is enabled.  The caller is expected to fill in the fields of the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> in this action.</param>
    </member>
    <member name="T:System.Web.Http.Tracing.ITraceWriterExtensions">
      <summary>Represents an extension methods for <see cref="T:System.Web.Http.Tracing.ITraceWriter" />. </summary>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Debug(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>Provides a set of methods and properties that help debug your code with the specified writer, request, category and exception.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="exception">The error occurred during execution.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Debug(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>Provides a set of methods and properties that help debug your code with the specified writer, request, category, exception, message format and argument.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="exception">The error occurred during execution.</param>
      <param name="messageFormat">The format of the message.</param>
      <param name="messageArguments">The message argument.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Debug(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>Provides a set of methods and properties that help debug your code with the specified writer, request, category, exception, message format and argument.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="messageFormat">The format of the message.</param>
      <param name="messageArguments">The message argument.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Error(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>Displays an error message in the list with the specified writer, request, category and exception.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="exception">The error occurred during execution.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Error(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>Displays an error message in the list with the specified writer, request, category, exception, message format and argument.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="exception">The exception.</param>
      <param name="messageFormat">The format of the message.</param>
      <param name="messageArguments">The argument in the message.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Error(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>Displays an error message in the list with the specified writer, request, category, message format and argument.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="messageFormat">The format of the message.</param>
      <param name="messageArguments">The argument in the message.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Fatal(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>Displays an error message in the <see cref="T:System.Web.Http.Tracing.ITraceWriterExtensions" /> class with the specified writer, request, category and exception.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="exception">The exception that appears during execution.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Fatal(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>Displays an error message in the <see cref="T:System.Web.Http.Tracing.ITraceWriterExtensions" /> class with the specified writer, request, category and exception, message format and argument.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="exception">The exception.</param>
      <param name="messageFormat">The format of the message.</param>
      <param name="messageArguments">The message argument.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Fatal(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>Displays an error message in the <see cref="T:System.Web.Http.Tracing.ITraceWriterExtensions" /> class with the specified writer, request, category and message format and argument.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="messageFormat">The format of the message.</param>
      <param name="messageArguments">The message argument.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Info(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>Displays the details in the <see cref="System.Web.Http.Tracing.ITraceWriterExtensions" />.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="exception">The error occurred during execution.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Info(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>Displays the details in the <see cref="System.Web.Http.Tracing.ITraceWriterExtensions" />.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="exception">The error occurred during execution.</param>
      <param name="messageFormat">The format of the message.</param>
      <param name="messageArguments">The message argument.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Info(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>Displays the details in the <see cref="System.Web.Http.Tracing.ITraceWriterExtensions" />.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="messageFormat">The format of the message.</param>
      <param name="messageArguments">The message argument.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Trace(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.Exception)">
      <summary>Indicates the trace listeners in the Listeners collection.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="level">The trace level.</param>
      <param name="exception">The error occurred during execution.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Trace(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.Exception,System.String,System.Object[])">
      <summary>Indicates the trace listeners in the Listeners collection.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="level">The trace level.</param>
      <param name="exception">The error occurred during execution.</param>
      <param name="messageFormat">The format of the message.</param>
      <param name="messageArguments">The message argument.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Trace(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.String,System.Object[])">
      <summary>Indicates the trace listeners in the Listeners collection.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="level">The <see cref="T:System.Web.Http.Tracing.TraceLevel" /> of the trace.</param>
      <param name="messageFormat">The format of the message.</param>
      <param name="messageArguments">The message argument.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.TraceBeginEnd(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.String,System.String,System.Action{System.Web.Http.Tracing.TraceRecord},System.Action,System.Action{System.Web.Http.Tracing.TraceRecord},System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary>Traces both a begin and an end trace around a specified operation.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace.  It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="level">The <see cref="T:System.Web.Http.Tracing.TraceLevel" /> of the trace.</param>
      <param name="operatorName">The name of the object performing the operation. It may be null.</param>
      <param name="operationName">The name of the operation being performed.  It may be null.</param>
      <param name="beginTrace">The <see cref="T:System.Action" /> to invoke prior to performing the operation,  allowing the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> to be filled in. It may be null.</param>
      <param name="execute">An &lt;see cref="T:System.Func`1" /&gt; that returns the <see cref="T:System.Threading.Tasks.Task" /> that will perform the operation.</param>
      <param name="endTrace">The <see cref="T:System.Action" /> to invoke after successfully performing the operation, allowing the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> to be filled in.  It may be null.</param>
      <param name="errorTrace">The <see cref="T:System.Action" /> to invoke if an error was encountered performing the operation, allowing the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> to be filled in. It may be null.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.TraceBeginEndAsync``1(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.String,System.String,System.Action{System.Web.Http.Tracing.TraceRecord},System.Func{System.Threading.Tasks.Task{``0}},System.Action{System.Web.Http.Tracing.TraceRecord,``0},System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary> Traces both a begin and an end trace around a specified operation. </summary>
      <returns>The <see cref="T:System.Threading.Tasks.Task" /> returned by the operation.</returns>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="level">The <see cref="T:System.Web.Http.Tracing.TraceLevel" /> of the trace.</param>
      <param name="operatorName">The name of the object performing the operation. It may be null.</param>
      <param name="operationName">The name of the operation being performed. It may be null.</param>
      <param name="beginTrace">The <see cref="T:System.Action" /> to invoke prior to performing the operation, allowing the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> to be filled in. It may be null.</param>
      <param name="execute">An &lt;see cref="T:System.Func`1" /&gt; that returns the <see cref="T:System.Threading.Tasks.Task" /> that will perform the operation.</param>
      <param name="endTrace">The <see cref="T:System.Action" /> to invoke after successfully performing the operation, allowing the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> to be filled in. The result of the completed task will also be passed to this action. This action may be null.</param>
      <param name="errorTrace">The <see cref="T:System.Action" /> to invoke if an error was encountered performing the operation, allowing the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> to be filled in. It may be null.</param>
      <typeparam name="TResult">The type of result produced by the <see cref="T:System.Threading.Tasks.Task" />.</typeparam>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.TraceBeginEndAsync(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel,System.String,System.String,System.Action{System.Web.Http.Tracing.TraceRecord},System.Func{System.Threading.Tasks.Task},System.Action{System.Web.Http.Tracing.TraceRecord},System.Action{System.Web.Http.Tracing.TraceRecord})">
      <summary> Traces both a begin and an end trace around a specified operation. </summary>
      <returns>The <see cref="T:System.Threading.Tasks.Task" /> returned by the operation.</returns>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace.  It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="level">The <see cref="T:System.Web.Http.Tracing.TraceLevel" /> of the trace.</param>
      <param name="operatorName">The name of the object performing the operation. It may be null.</param>
      <param name="operationName">The name of the operation being performed. It may be null.</param>
      <param name="beginTrace">The <see cref="T:System.Action" /> to invoke prior to performing the operation, allowing the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> to be filled in. It may be null.</param>
      <param name="execute">An &lt;see cref="T:System.Func`1" /&gt; that returns the <see cref="T:System.Threading.Tasks.Task" /> that will perform the operation.</param>
      <param name="endTrace">The <see cref="T:System.Action" /> to invoke after successfully performing the operation, allowing the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> to be filled in. It may be null.</param>
      <param name="errorTrace">The <see cref="T:System.Action" /> to invoke if an error was encountered performing the operation, allowing the given <see cref="T:System.Web.Http.Tracing.TraceRecord" /> to be filled in. It may be null.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Warn(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception)">
      <summary>Indicates the warning level of execution.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="exception">The error occurred during execution.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Warn(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.Exception,System.String,System.Object[])">
      <summary>Indicates the warning level of execution.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="exception">The error occurred during execution.</param>
      <param name="messageFormat">The format of the message.</param>
      <param name="messageArguments">The message argument.</param>
    </member>
    <member name="M:System.Web.Http.Tracing.ITraceWriterExtensions.Warn(System.Web.Http.Tracing.ITraceWriter,System.Net.Http.HttpRequestMessage,System.String,System.String,System.Object[])">
      <summary>Indicates the warning level of execution.</summary>
      <param name="traceWriter">The <see cref="T:System.Web.Http.Tracing.ITraceWriter" />.</param>
      <param name="request">The <see cref="T:System.Net.Http.HttpRequestMessage" /> with which to associate the trace. It may be null.</param>
      <param name="category">The logical category of the trace.</param>
      <param name="messageFormat">The format of the message.</param>
      <param name="messageArguments">The message argument.</param>
    </member>
    <member name="T:System.Web.Http.Tracing.TraceCategories">
      <summary>Specifies an enumeration of tracing categories.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.ActionCategory">
      <summary>An action category.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.ControllersCategory">
      <summary>The controllers category.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.FiltersCategory">
      <summary>The filters category.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.FormattingCategory">
      <summary>The formatting category.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.MessageHandlersCategory">
      <summary>The message handlers category.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.ModelBindingCategory">
      <summary>The model binding category.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.RequestCategory">
      <summary>The request category.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceCategories.RoutingCategory">
      <summary>The routing category.</summary>
    </member>
    <member name="T:System.Web.Http.Tracing.TraceKind">
      <summary>Specifies the kind of tracing operation.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceKind.Trace">
      <summary>Single trace, not part of a Begin/End trace pair.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceKind.Begin">
      <summary>Trace marking the beginning of some operation.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceKind.End">
      <summary>Trace marking the end of some operation.</summary>
    </member>
    <member name="T:System.Web.Http.Tracing.TraceLevel">
      <summary>Specifies an enumeration of tracing level.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Off">
      <summary>Tracing is disabled.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Debug">
      <summary>Trace level for debugging traces.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Info">
      <summary>Trace level for informational traces.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Warn">
      <summary>Trace level for warning traces.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Error">
      <summary>Trace level for error traces.</summary>
    </member>
    <member name="F:System.Web.Http.Tracing.TraceLevel.Fatal">
      <summary>Trace level for fatal traces.</summary>
    </member>
    <member name="T:System.Web.Http.Tracing.TraceRecord">
      <summary>Represents a trace record.</summary>
    </member>
    <member name="M:System.Web.Http.Tracing.TraceRecord.#ctor(System.Net.Http.HttpRequestMessage,System.String,System.Web.Http.Tracing.TraceLevel)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Tracing.TraceRecord" /> class.</summary>
      <param name="request">The message request.</param>
      <param name="category">The trace category.</param>
      <param name="level">The trace level.</param>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Category">
      <summary>Gets or sets the tracing category.</summary>
      <returns>The tracing category.</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Exception">
      <summary>Gets or sets the exception.</summary>
      <returns>The exception.</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Kind">
      <summary>Gets or sets the kind of trace.</summary>
      <returns>The kind of trace.</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Level">
      <summary>Gets or sets the tracing level.</summary>
      <returns>The tracing level.</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Message">
      <summary>Gets or sets the message.</summary>
      <returns>The message.</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Operation">
      <summary>Gets or sets the logical operation name being performed.</summary>
      <returns>The logical operation name being performed.</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Operator">
      <summary>Gets or sets the logical name of the object performing the operation.</summary>
      <returns>The logical name of the object performing the operation.</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Properties">
      <summary>Gets the optional user-defined properties.</summary>
      <returns>The optional user-defined properties.</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Request">
      <summary>Gets the <see cref="T:System.Net.Http.HttpRequestMessage" /> from the record.</summary>
      <returns>The <see cref="T:System.Net.Http.HttpRequestMessage" /> from the record.</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.RequestId">
      <summary>Gets the correlation ID from the <see cref="P:System.Web.Http.Tracing.TraceRecord.Request" />.</summary>
      <returns>The correlation ID from the <see cref="P:System.Web.Http.Tracing.TraceRecord.Request" />.</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Status">
      <summary>Gets or sets the <see cref="T:System.Net.HttpStatusCode" /> associated with the <see cref="T:System.Net.Http.HttpResponseMessage" />.</summary>
      <returns>The <see cref="T:System.Net.HttpStatusCode" /> associated with the <see cref="T:System.Net.Http.HttpResponseMessage" />.</returns>
    </member>
    <member name="P:System.Web.Http.Tracing.TraceRecord.Timestamp">
      <summary>Gets the <see cref="T:System.DateTime" /> of this trace (via <see cref="P:System.DateTime.UtcNow" />).</summary>
      <returns>The <see cref="T:System.DateTime" /> of this trace (via <see cref="P:System.DateTime.UtcNow" />).</returns>
    </member>
    <member name="T:System.Web.Http.Validation.DefaultBodyModelValidator">
      <summary>Represents a class used to recursively validate an object.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.DefaultBodyModelValidator.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.DefaultBodyModelValidator" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.DefaultBodyModelValidator.Validate(System.Object,System.Type,System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.String)">
      <summary> Determines whether the model is valid and adds any validation errors to the actionContext's <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" />.</summary>
      <returns>True if model is valid, false otherwise.</returns>
      <param name="model">The model to be validated.</param>
      <param name="type">The <see cref="T:System.Type" /> to use for validation.</param>
      <param name="metadataProvider">The <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /> used to provide the model metadata.</param>
      <param name="actionContext">The <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> within which the model is being validated.</param>
      <param name="keyPrefix">The <see cref="T:System.String" /> to append to the key for any validation errors.</param>
    </member>
    <member name="T:System.Web.Http.Validation.IBodyModelValidator">
      <summary>Represents an interface for the validation of the models</summary>
    </member>
    <member name="M:System.Web.Http.Validation.IBodyModelValidator.Validate(System.Object,System.Type,System.Web.Http.Metadata.ModelMetadataProvider,System.Web.Http.Controllers.HttpActionContext,System.String)">
      <summary> Determines whether the model is valid and adds any validation errors to the actionContext's <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" /></summary>
      <returns>trueif model is valid, false otherwise.</returns>
      <param name="model">The model to be validated.</param>
      <param name="type">The <see cref="T:System.Type" /> to use for validation.</param>
      <param name="metadataProvider">The <see cref="T:System.Web.Http.Metadata.ModelMetadataProvider" /> used to provide the model metadata.</param>
      <param name="actionContext">The <see cref="T:System.Web.Http.Controllers.HttpActionContext" /> within which the model is being validated.</param>
      <param name="keyPrefix">The <see cref="T:System.String" /> to append to the key for any validation errors.</param>
    </member>
    <member name="T:System.Web.Http.Validation.ModelStateFormatterLogger">
      <summary>This <see cref="T:System.Net.Http.Formatting.IFormatterLogger" /> logs formatter errors to the provided <see cref="T:System.Web.Http.ModelBinding.ModelStateDictionary" />.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelStateFormatterLogger.#ctor(System.Web.Http.ModelBinding.ModelStateDictionary,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.ModelStateFormatterLogger" /> class.</summary>
      <param name="modelState">The model state.</param>
      <param name="prefix">The prefix.</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelStateFormatterLogger.LogError(System.String,System.Exception)">
      <summary>Logs the specified model error.</summary>
      <param name="errorPath">The error path.</param>
      <param name="exception">The error message.</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelStateFormatterLogger.LogError(System.String,System.String)">
      <summary>Logs the specified model error.</summary>
      <param name="errorPath">The error path.</param>
      <param name="errorMessage">The error message.</param>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidatedEventArgs">
      <summary>Provides data for the <see cref="E:System.Web.Http.Validation.ModelValidationNode.Validated" /> event.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidatedEventArgs.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.Validation.ModelValidationNode)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.ModelValidatedEventArgs" /> class.</summary>
      <param name="actionContext">The action context.</param>
      <param name="parentNode">The parent node.</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidatedEventArgs.ActionContext">
      <summary>Gets or sets the context for an action.</summary>
      <returns>The context for an action.</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidatedEventArgs.ParentNode">
      <summary>Gets or sets the parent of this node.</summary>
      <returns>The parent of this node.</returns>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidatingEventArgs">
      <summary>Provides data for the <see cref="E:System.Web.Http.Validation.ModelValidationNode.Validating" /> event.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidatingEventArgs.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.Validation.ModelValidationNode)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.ModelValidatingEventArgs" /> class.</summary>
      <param name="actionContext">The action context.</param>
      <param name="parentNode">The parent node.</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidatingEventArgs.ActionContext">
      <summary>Gets or sets the context for an action.</summary>
      <returns>The context for an action.</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidatingEventArgs.ParentNode">
      <summary>Gets or sets the parent of this node.</summary>
      <returns>The parent of this node.</returns>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidationNode">
      <summary>Provides a container for model validation information.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.#ctor(System.Web.Http.Metadata.ModelMetadata,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> class, using the model metadata and state key.</summary>
      <param name="modelMetadata">The model metadata.</param>
      <param name="modelStateKey">The model state key.</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.#ctor(System.Web.Http.Metadata.ModelMetadata,System.String,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidationNode})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> class, using the model metadata, the model state key, and child model-validation nodes.</summary>
      <param name="modelMetadata">The model metadata.</param>
      <param name="modelStateKey">The model state key.</param>
      <param name="childNodes">The model child nodes.</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.ChildNodes">
      <summary>Gets or sets the child nodes.</summary>
      <returns>The child nodes.</returns>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.CombineWith(System.Web.Http.Validation.ModelValidationNode)">
      <summary>Combines the current <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> instance with a specified <see cref="T:System.Web.Http.Validation.ModelValidationNode" /> instance.</summary>
      <param name="otherNode">The model validation node to combine with the current instance.</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.ModelMetadata">
      <summary>Gets or sets the model metadata.</summary>
      <returns>The model metadata.</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.ModelStateKey">
      <summary>Gets or sets the model state key.</summary>
      <returns>The model state key.</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.SuppressValidation">
      <summary>Gets or sets a value that indicates whether validation should be suppressed.</summary>
      <returns>true if validation should be suppressed; otherwise, false.</returns>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.Validate(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Validates the model using the specified execution context.</summary>
      <param name="actionContext">The action context.</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationNode.Validate(System.Web.Http.Controllers.HttpActionContext,System.Web.Http.Validation.ModelValidationNode)">
      <summary>Validates the model using the specified execution context and parent node.</summary>
      <param name="actionContext">The action context.</param>
      <param name="parentNode">The parent node.</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationNode.ValidateAllProperties">
      <summary>Gets or sets a value that indicates whether all properties of the model should be validated.</summary>
      <returns>true if all properties of the model should be validated, or false if validation should be skipped.</returns>
    </member>
    <member name="E:System.Web.Http.Validation.ModelValidationNode.Validated">
      <summary>Occurs when the model has been validated.</summary>
    </member>
    <member name="E:System.Web.Http.Validation.ModelValidationNode.Validating">
      <summary>Occurs when the model is being validated.</summary>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidationRequiredMemberSelector">
      <summary>Represents the selection of required members by checking for any required ModelValidators associated with the member.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationRequiredMemberSelector.#ctor(System.Web.Http.Metadata.ModelMetadataProvider,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.ModelValidationRequiredMemberSelector" /> class.</summary>
      <param name="metadataProvider">The metadata provider.</param>
      <param name="validatorProviders">The validator providers.</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationRequiredMemberSelector.IsRequiredMember(System.Reflection.MemberInfo)">
      <summary>Indicates whether the member is required for validation.</summary>
      <returns>true if the member is required for validation; otherwise, false.</returns>
      <param name="member">The member.</param>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidationResult">
      <summary>Provides a container for a validation result.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidationResult.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.ModelValidationResult" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationResult.MemberName">
      <summary>Gets or sets the name of the member.</summary>
      <returns>The name of the member.</returns>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidationResult.Message">
      <summary>Gets or sets the validation result message.</summary>
      <returns>The validation result message.</returns>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidator">
      <summary>Provides a base class for implementing validation logic.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidator.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.ModelValidator" /> class.</summary>
      <param name="validatorProviders">The validator providers.</param>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidator.GetModelValidator(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>Returns a composite model validator for the model.</summary>
      <returns>A composite model validator for the model.</returns>
      <param name="validatorProviders">An enumeration of validator providers.</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidator.IsRequired">
      <summary>Gets a value that indicates whether a model property is required.</summary>
      <returns>true if the model property is required; otherwise, false.</returns>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidator.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>Validates a specified object.</summary>
      <returns>A list of validation results.</returns>
      <param name="metadata">The metadata.</param>
      <param name="container">The container.</param>
    </member>
    <member name="P:System.Web.Http.Validation.ModelValidator.ValidatorProviders">
      <summary>Gets or sets an enumeration of validator providers.</summary>
      <returns>An enumeration of validator providers.</returns>
    </member>
    <member name="T:System.Web.Http.Validation.ModelValidatorProvider">
      <summary>Provides a list of validators for a model.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidatorProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.ModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>Gets a list of validators associated with this <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" />.</summary>
      <returns>The list of validators.</returns>
      <param name="metadata">The metadata.</param>
      <param name="validatorProviders">The validator providers.</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.AssociatedValidatorProvider">
      <summary>Provides an abstract class for classes that implement a validation provider.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.AssociatedValidatorProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.Providers.AssociatedValidatorProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.AssociatedValidatorProvider.GetTypeDescriptor(System.Type)">
      <summary>Gets a type descriptor for the specified type.</summary>
      <returns>A type descriptor for the specified type.</returns>
      <param name="type">The type of the validation provider.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.AssociatedValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>Gets the validators for the model using the metadata and validator providers.</summary>
      <returns>The validators for the model.</returns>
      <param name="metadata">The metadata.</param>
      <param name="validatorProviders">An enumeration of validator providers.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.AssociatedValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>Gets the validators for the model using the metadata, the validator providers, and a list of attributes.</summary>
      <returns>The validators for the model.</returns>
      <param name="metadata">The metadata.</param>
      <param name="validatorProviders">An enumeration of validator providers.</param>
      <param name="attributes">The list of attributes.</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.DataAnnotationsModelValidationFactory">
      <summary>Represents the method that creates a <see cref="T:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider" /> instance.</summary>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider">
      <summary>Represents an implementation of <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> which providers validators for attributes which derive from <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" />. It also provides a validator for types which implement <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" />. To support client side validation, you can either register adapters through the static methods on this class, or by having your validation attributes implement <see cref="T:System.Web.Http.Validation.IClientValidatable" />. The logic to support IClientValidatable is implemented in <see cref="T:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator" />. </summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>Gets the validators for the model using the specified metadata, validator provider and attributes.</summary>
      <returns>The validators for the model.</returns>
      <param name="metadata">The metadata.</param>
      <param name="validatorProviders">The validator providers.</param>
      <param name="attributes">The attributes.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterAdapter(System.Type,System.Type)">
      <summary>Registers an adapter to provide client-side validation.</summary>
      <param name="attributeType">The type of the validation attribute.</param>
      <param name="adapterType">The type of the adapter.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterAdapterFactory(System.Type,System.Web.Http.Validation.Providers.DataAnnotationsModelValidationFactory)">
      <summary>Registers an adapter factory for the validation provider.</summary>
      <param name="attributeType">The type of the attribute.</param>
      <param name="factory">The factory that will be used to create the <see cref="T:System.Web.Http.Validation.ModelValidator" /> object for the specified attribute.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterDefaultAdapter(System.Type)">
      <summary>Registers the default adapter.</summary>
      <param name="adapterType">The type of the adapter.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterDefaultAdapterFactory(System.Web.Http.Validation.Providers.DataAnnotationsModelValidationFactory)">
      <summary>Registers the default adapter factory.</summary>
      <param name="factory">The factory that will be used to create the <see cref="T:System.Web.Http.Validation.ModelValidator" /> object for the default adapter.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterDefaultValidatableObjectAdapter(System.Type)">
      <summary>Registers the default adapter type for objects which implement <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" />. The adapter type must derive from <see cref="T:System.Web.Http.Validation.ModelValidator" /> and it must contain a public constructor which takes two parameters of types <see cref="T:System.Web.Http.Metadata.ModelMetadata" /> and <see cref="T:System.Web.Http.Controllers.HttpActionContext" />. </summary>
      <param name="adapterType">The type of the adapter.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterDefaultValidatableObjectAdapterFactory(System.Web.Http.Validation.Providers.DataAnnotationsValidatableObjectAdapterFactory)">
      <summary>Registers the default adapter factory for objects which implement <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" />. </summary>
      <param name="factory">The factory.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterValidatableObjectAdapter(System.Type,System.Type)">
      <summary>Registers an adapter type for the given modelType, which must implement <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" />. The adapter type must derive from <see cref="T:System.Web.Http.Validation.ModelValidator" /> and it must contain a public constructor which takes two parameters of types <see cref="T:System.Web.Http.Metadata.ModelMetadata" /> and <see cref="T:System.Web.Http.Controllers.HttpActionContext" />. </summary>
      <param name="modelType">The model type.</param>
      <param name="adapterType">The type of the adapter.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataAnnotationsModelValidatorProvider.RegisterValidatableObjectAdapterFactory(System.Type,System.Web.Http.Validation.Providers.DataAnnotationsValidatableObjectAdapterFactory)">
      <summary>Registers an adapter factory for the given modelType, which must implement <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" />. </summary>
      <param name="modelType">The model type.</param>
      <param name="factory">The factory.</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.DataAnnotationsValidatableObjectAdapterFactory">
      <summary>Provides a factory for validators that are based on <see cref="T:System.ComponentModel.DataAnnotations.IValidatableObject" />.</summary>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.DataMemberModelValidatorProvider">
      <summary>Represents a validator provider for data member model.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataMemberModelValidatorProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.Providers.DataMemberModelValidatorProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.DataMemberModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>Gets the validators for the model.</summary>
      <returns>The validators for the model.</returns>
      <param name="metadata">The metadata.</param>
      <param name="validatorProviders">An enumerator of validator providers.</param>
      <param name="attributes">A list of attributes.</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider">
      <summary>An implementation of <see cref="T:System.Web.Http.Validation.ModelValidatorProvider" /> which provides validators that throw exceptions when the model is invalid.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.Collections.Generic.IEnumerable{System.Attribute})">
      <summary>Gets a list of validators associated with this <see cref="T:System.Web.Http.Validation.Providers.InvalidModelValidatorProvider" />.</summary>
      <returns>The list of validators.</returns>
      <param name="metadata">The metadata.</param>
      <param name="validatorProviders">The validator providers.</param>
      <param name="attributes">The list of attributes.</param>
    </member>
    <member name="T:System.Web.Http.Validation.Providers.RequiredMemberModelValidatorProvider">
      <summary>Represents the provider for the required member model validator.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.RequiredMemberModelValidatorProvider.#ctor(System.Net.Http.Formatting.IRequiredMemberSelector)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.Providers.RequiredMemberModelValidatorProvider" /> class.</summary>
      <param name="requiredMemberSelector">The required member selector.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Providers.RequiredMemberModelValidatorProvider.GetValidators(System.Web.Http.Metadata.ModelMetadata,System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>Gets the validator for the member model.</summary>
      <returns>The validator for the member model.</returns>
      <param name="metadata">The metadata.</param>
      <param name="validatorProviders">The validator providers</param>
    </member>
    <member name="T:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator">
      <summary>Provides a model validator.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.ComponentModel.DataAnnotations.ValidationAttribute)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator" /> class.</summary>
      <param name="validatorProviders">The validator providers.</param>
      <param name="attribute">The validation attribute for the model.</param>
    </member>
    <member name="P:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator.Attribute">
      <summary>Gets or sets the validation attribute for the model validator.</summary>
      <returns>The validation attribute for the model validator.</returns>
    </member>
    <member name="P:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator.IsRequired">
      <summary>Gets a value that indicates whether model validation is required.</summary>
      <returns>true if model validation is required; otherwise, false.</returns>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.DataAnnotationsModelValidator.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>Validates the model and returns the validation errors if any.</summary>
      <returns>A list of validation error messages for the model, or an empty list if no errors have occurred.</returns>
      <param name="metadata">The model metadata.</param>
      <param name="container">The container for the model.</param>
    </member>
    <member name="T:System.Web.Http.Validation.Validators.ErrorModelValidator">
      <summary>A <see cref="T:System.Web.Http.Validation.ModelValidator" /> to represent an error. This validator will always throw an exception regardless of the actual model value.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.ErrorModelValidator.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider},System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.Validators.ErrorModelValidator" /> class.</summary>
      <param name="validatorProviders">The list of  model validator providers.</param>
      <param name="errorMessage">The error message for the exception.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.ErrorModelValidator.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>Validates a specified object.</summary>
      <returns>A list of validation results.</returns>
      <param name="metadata">The metadata.</param>
      <param name="container">The container.</param>
    </member>
    <member name="T:System.Web.Http.Validation.Validators.RequiredMemberModelValidator">
      <summary>Represents the <see cref="T:System.Web.Http.Validation.ModelValidator" /> for required members. </summary>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.RequiredMemberModelValidator.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.Validators.RequiredMemberModelValidator" /> class.</summary>
      <param name="validatorProviders">The validator providers.</param>
    </member>
    <member name="P:System.Web.Http.Validation.Validators.RequiredMemberModelValidator.IsRequired">
      <summary>Gets or sets a value that instructs the serialization engine that the member must be presents when validating.</summary>
      <returns>true if the member is required; otherwise, false.</returns>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.RequiredMemberModelValidator.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>Validates the object.</summary>
      <returns>A list of validation results.</returns>
      <param name="metadata">The metadata.</param>
      <param name="container">The container.</param>
    </member>
    <member name="T:System.Web.Http.Validation.Validators.ValidatableObjectAdapter">
      <summary>Provides an object adapter that can be validated.</summary>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.ValidatableObjectAdapter.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.Validation.ModelValidatorProvider})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Validation.Validators.ValidatableObjectAdapter" /> class.</summary>
      <param name="validatorProviders">The validation provider.</param>
    </member>
    <member name="M:System.Web.Http.Validation.Validators.ValidatableObjectAdapter.Validate(System.Web.Http.Metadata.ModelMetadata,System.Object)">
      <summary>Validates the specified object.</summary>
      <returns>A list of validation results.</returns>
      <param name="metadata">The metadata.</param>
      <param name="container">The container.</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.IEnumerableValueProvider">
      <summary>Represents the base class for value providers whose values come from a collection that implements the <see cref="T:System.Collections.IEnumerable" /> interface.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.IEnumerableValueProvider.GetKeysFromPrefix(System.String)">
      <summary>Retrieves the keys from the specified <paramref name="prefix" />.</summary>
      <returns>The keys from the specified <paramref name="prefix" />.</returns>
      <param name="prefix">The prefix.</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.IValueProvider">
      <summary>Defines the methods that are required for a value provider in ASP.NET MVC.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.IValueProvider.ContainsPrefix(System.String)">
      <summary>Determines whether the collection contains the specified prefix.</summary>
      <returns>true if the collection contains the specified prefix; otherwise, false.</returns>
      <param name="prefix">The prefix to search for.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.IValueProvider.GetValue(System.String)">
      <summary>Retrieves a value object using the specified key.</summary>
      <returns>The value object for the specified key.</returns>
      <param name="key">The key of the value object to retrieve.</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.ValueProviderAttribute">
      <summary> This attribute is used to specify a custom <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" />. </summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.ValueProviderAttribute" />.</summary>
      <param name="valueProviderFactory">The type of the model binder.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderAttribute.#ctor(System.Type[])">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.ValueProviderAttribute" />.</summary>
      <param name="valueProviderFactories">An array of model binder types.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderAttribute.GetValueProviderFactories(System.Web.Http.HttpConfiguration)">
      <summary>Gets the value provider factories.</summary>
      <returns>A collection of value provider factories.</returns>
      <param name="configuration">A configuration object.</param>
    </member>
    <member name="P:System.Web.Http.ValueProviders.ValueProviderAttribute.ValueProviderFactoryTypes">
      <summary>Gets the types of object returned by the value provider factory.</summary>
      <returns>A collection of types.</returns>
    </member>
    <member name="T:System.Web.Http.ValueProviders.ValueProviderFactory">
      <summary>Represents a factory for creating value-provider objects.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderFactory.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.ValueProviderFactory" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderFactory.GetValueProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Returns a value-provider object for the specified controller context.</summary>
      <returns>A value-provider object.</returns>
      <param name="actionContext">An object that encapsulates information about the current HTTP request.</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.ValueProviderResult">
      <summary>Represents the result of binding a value (such as from a form post or query string) to an action-method argument property, or to the argument itself.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderResult.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.ValueProviderResult" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderResult.#ctor(System.Object,System.String,System.Globalization.CultureInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.ValueProviderResult" /> class.</summary>
      <param name="rawValue">The raw value.</param>
      <param name="attemptedValue">The attempted value.</param>
      <param name="culture">The culture.</param>
    </member>
    <member name="P:System.Web.Http.ValueProviders.ValueProviderResult.AttemptedValue">
      <summary>Gets or sets the raw value that is converted to a string for display.</summary>
      <returns>The raw value that is converted to a string for display.</returns>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderResult.ConvertTo(System.Type)">
      <summary>Converts the value that is encapsulated by this result to the specified type.</summary>
      <returns>The converted value.</returns>
      <param name="type">The target type.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.ValueProviderResult.ConvertTo(System.Type,System.Globalization.CultureInfo)">
      <summary>Converts the value that is encapsulated by this result to the specified type by using the specified culture information.</summary>
      <returns>The converted value.</returns>
      <param name="type">The target type.</param>
      <param name="culture">The culture to use in the conversion.</param>
    </member>
    <member name="P:System.Web.Http.ValueProviders.ValueProviderResult.Culture">
      <summary>Gets or sets the culture.</summary>
      <returns>The culture.</returns>
    </member>
    <member name="P:System.Web.Http.ValueProviders.ValueProviderResult.RawValue">
      <summary>Gets or set the raw value that is supplied by the value provider.</summary>
      <returns>The raw value that is supplied by the value provider.</returns>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.CompositeValueProvider">
      <summary>Represents a value provider whose values come from a list of value providers that implements the <see cref="T:System.Collections.IEnumerable" /> interface.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.Providers.CompositeValueProvider" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.#ctor(System.Collections.Generic.IList{System.Web.Http.ValueProviders.IValueProvider})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.Providers.CompositeValueProvider" /> class.</summary>
      <param name="list">The list of value providers.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.ContainsPrefix(System.String)">
      <summary>Determines whether the collection contains the specified <paramref name="prefix" />.</summary>
      <returns>true if the collection contains the specified <paramref name="prefix" />; otherwise, false.</returns>
      <param name="prefix">The prefix to search for.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.GetKeysFromPrefix(System.String)">
      <summary>Retrieves the keys from the specified <paramref name="prefix" />.</summary>
      <returns>The keys from the specified <paramref name="prefix" />.</returns>
      <param name="prefix">The prefix from which keys are retrieved.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.GetValue(System.String)">
      <summary>Retrieves a value object using the specified <paramref name="key" />.</summary>
      <returns>The value object for the specified <paramref name="key" />.</returns>
      <param name="key">The key of the value object to retrieve.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.InsertItem(System.Int32,System.Web.Http.ValueProviders.IValueProvider)">
      <summary>Inserts an element into the collection at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
      <param name="item">The object to insert.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProvider.SetItem(System.Int32,System.Web.Http.ValueProviders.IValueProvider)">
      <summary>Replaces the element at the specified index.</summary>
      <param name="index">The zero-based index of the element to replace.</param>
      <param name="item">The new value for the element at the specified index.</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.CompositeValueProviderFactory">
      <summary>Represents a factory for creating a list of value-provider objects.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProviderFactory.#ctor(System.Collections.Generic.IEnumerable{System.Web.Http.ValueProviders.ValueProviderFactory})">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.Providers.CompositeValueProviderFactory" /> class.</summary>
      <param name="factories">The collection of value-provider factories.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.CompositeValueProviderFactory.GetValueProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Retrieves a list of value-provider objects for the specified controller context.</summary>
      <returns>The list of value-provider objects for the specified controller context.</returns>
      <param name="actionContext">An object that encapsulates information about the current HTTP request.</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider">
      <summary>A value provider for name/value pairs.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}},System.Globalization.CultureInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider" /> class.</summary>
      <param name="values">The name/value pairs for the provider.</param>
      <param name="culture">The culture used for the name/value pairs.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.#ctor(System.Func{System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}}},System.Globalization.CultureInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider" /> class, using a function delegate to provide the name/value pairs.</summary>
      <param name="valuesFactory">A function delegate that returns a collection of name/value pairs.</param>
      <param name="culture">The culture used for the name/value pairs.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.ContainsPrefix(System.String)">
      <summary>Determines whether the collection contains the specified prefix.</summary>
      <returns>true if the collection contains the specified prefix; otherwise, false.</returns>
      <param name="prefix">The prefix to search for.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.GetKeysFromPrefix(System.String)">
      <summary>Gets the keys from a prefix.</summary>
      <returns>The keys.</returns>
      <param name="prefix">The prefix.</param>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.NameValuePairsValueProvider.GetValue(System.String)">
      <summary>Retrieves a value object using the specified key.</summary>
      <returns>The value object for the specified key.</returns>
      <param name="key">The key of the value object to retrieve.</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.QueryStringValueProvider">
      <summary>Represents a value provider for query strings that are contained in a <see cref="T:System.Collections.Specialized.NameValueCollection" /> object.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.QueryStringValueProvider.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Globalization.CultureInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.Providers.QueryStringValueProvider" /> class.</summary>
      <param name="actionContext">An object that encapsulates information about the current HTTP request.</param>
      <param name="culture">An object that contains information about the target culture.</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.QueryStringValueProviderFactory">
      <summary>Represents a class that is responsible for creating a new instance of a query-string value-provider object.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.QueryStringValueProviderFactory.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.Providers.QueryStringValueProviderFactory" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.QueryStringValueProviderFactory.GetValueProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Retrieves a value-provider object for the specified controller context.</summary>
      <returns>A query-string value-provider object.</returns>
      <param name="actionContext">An object that encapsulates information about the current HTTP request.</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.RouteDataValueProvider">
      <summary>Represents a value provider for route data that is contained in an object that implements the IDictionary(Of TKey, TValue) interface.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.RouteDataValueProvider.#ctor(System.Web.Http.Controllers.HttpActionContext,System.Globalization.CultureInfo)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.Providers.RouteDataValueProvider" /> class.</summary>
      <param name="actionContext">An object that contain information about the HTTP request.</param>
      <param name="culture">An object that contains information about the target culture.</param>
    </member>
    <member name="T:System.Web.Http.ValueProviders.Providers.RouteDataValueProviderFactory">
      <summary>Represents a factory for creating route-data value provider objects.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.RouteDataValueProviderFactory.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.ValueProviders.Providers.RouteDataValueProviderFactory" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.ValueProviders.Providers.RouteDataValueProviderFactory.GetValueProvider(System.Web.Http.Controllers.HttpActionContext)">
      <summary>Retrieves a value-provider object for the specified controller context.</summary>
      <returns>A value-provider object.</returns>
      <param name="actionContext">An object that encapsulates information about the current HTTP request.</param>
    </member>
  </members>
</doc>