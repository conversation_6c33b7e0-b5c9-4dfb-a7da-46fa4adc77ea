<?xml version="1.0" encoding="utf-8" ?>
<configuration>
<!--
	 This is the configuration file for the NUnitTests.nunit test project. You may
	 need to create a similar configuration file for your own test project. 
 -->	 

<!--
	 The <NUnit> section is only needed if you want to use a non-default value
	 for any of the settings. It is commented out below. If you are going to use
   it, you must deifne the NUnit section group and the sections you need.
 
   The syntax shown here works for most runtimes. If NUnit fails at startup, you
   can try specifying the name of the assembly containing the NameValueSectionHandler:
   
      <section name="TestCaseBuilder" type="System.Configuration.NameValueSectionHandler, System" />
      
   If that fails, try the fully qualified name of the assembly:
   
      <section name="TestCaseBuilder" type="System.Configuration.NameValueSectionHandler, System, 
             Version=2.0.50727.832, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
             
   Unfortunately, this last approach makes your config file non-portable across runtimes.
   -->

<!--
  <configSections>
		<sectionGroup name="NUnit">
			<section name="TestCaseBuilder" type="System.Configuration.NameValueSectionHandler"/>
			<section name="TestRunner" type="System.Configuration.NameValueSectionHandler"/>
		</sectionGroup>
	</configSections>
 -->

  <appSettings>
		<!--   User application and configured property settings go here.-->
		<!--   Example: <add key="settingName" value="settingValue"/> -->
		<add key="test.setting" value="54321" />
	</appSettings>

<!-- Sample NUnit section group showing all default values -->
<!--
	<NUnit>
		<TestCaseBuilder>
			<add key="OldStyleTestCases" value="false" />
		</TestCaseBuilder>
		<TestRunner>
			<add key="ApartmentState" value="MTA" />
			<add key="ThreadPriority" value="Normal" />
      <add key="DefaultLogThreshold" value="Info" />
		</TestRunner>
	</NUnit>
-->
  
   <!--
    The following <runtime> section allows running nunit tests under 
    .NET 1.0 by redirecting assemblies. The appliesTo attribute
    causes the section to be ignored except under .NET 1.0.
   --> 
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1"
				appliesTo="v1.0.3705">
			<dependentAssembly>
				<assemblyIdentity name="System" publicKeyToken="b77a5c561934e089" culture="" />
				<bindingRedirect oldVersion="1.0.5000.0" newVersion="1.0.3300.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Data" publicKeyToken="b77a5c561934e089" culture="" />
				<bindingRedirect oldVersion="1.0.5000.0" newVersion="1.0.3300.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Drawing" publicKeyToken="b03f5f7f11d50a3a" culture="" />
				<bindingRedirect oldVersion="1.0.5000.0" newVersion="1.0.3300.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Windows.Forms" publicKeyToken="b77a5c561934e089" culture="" />
				<bindingRedirect oldVersion="1.0.5000.0" newVersion="1.0.3300.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Xml" publicKeyToken="b77a5c561934e089" culture="" />
				<bindingRedirect oldVersion="1.0.5000.0" newVersion="1.0.3300.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
</configuration> 
