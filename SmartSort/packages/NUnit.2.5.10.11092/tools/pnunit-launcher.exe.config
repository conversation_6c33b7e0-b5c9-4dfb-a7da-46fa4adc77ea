<?xml version="1.0" encoding="Windows-1252"?>
<configuration>

  <!-- Set the level for tracing NUnit itself -->
  <!-- 0=Off 1=Error 2=Warning 3=Info 4=Debug -->
  <system.diagnostics>
	  <switches>
      <add name="NTrace" value="0" />
	  </switches>
  </system.diagnostics>
  
  <runtime>
    <!-- We need this so test exceptions don't crash NUnit -->
    <legacyUnhandledExceptionPolicy enabled="1" />

    <!-- Look for addins in the addins directory for now -->
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="framework;lib;addins"/>
   </assemblyBinding>

    <!--
    The following <assemblyBinding> section allows running nunit under 
    .NET 1.0 by redirecting assemblies. The appliesTo attribute
    causes the section to be ignored except under .NET 1.0
    on a machine with only the .NET version 1.0 runtime installed.
    If application and its tests were built for .NET 1.1 you will
    also need to redirect system assemblies in the test config file,
    which controls loading of the tests.
   -->
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1"
			appliesTo="v1.0.3705">

      <dependentAssembly> 
        <assemblyIdentity name="System" 
                          publicKeyToken="b77a5c561934e089" 
                          culture="neutral"/>
        <bindingRedirect  oldVersion="1.0.5000.0" 
                          newVersion="1.0.3300.0"/>
      </dependentAssembly>

      <dependentAssembly> 
        <assemblyIdentity name="System.Data" 
                          publicKeyToken="b77a5c561934e089" 
                          culture="neutral"/>
        <bindingRedirect  oldVersion="1.0.5000.0" 
                          newVersion="1.0.3300.0"/>
      </dependentAssembly>

      <dependentAssembly> 
        <assemblyIdentity name="System.Drawing" 
                          publicKeyToken="b03f5f7f11d50a3a" 
                          culture="neutral"/>
        <bindingRedirect  oldVersion="1.0.5000.0" 
                          newVersion="1.0.3300.0"/>
      </dependentAssembly>

      <dependentAssembly> 
        <assemblyIdentity name="System.Windows.Forms" 
                          publicKeyToken="b77a5c561934e089" 
                          culture="neutral"/>
        <bindingRedirect  oldVersion="1.0.5000.0" 
                          newVersion="1.0.3300.0"/>
      </dependentAssembly>

      <dependentAssembly> 
        <assemblyIdentity name="System.Xml" 
                          publicKeyToken="b77a5c561934e089" 
                          culture="neutral"/>
        <bindingRedirect  oldVersion="1.0.5000.0" 
                          newVersion="1.0.3300.0"/>
      </dependentAssembly>

    </assemblyBinding>
  
  </runtime>
  
</configuration>