<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <startup>
	  <supportedRuntime version="v2.0.50727" />
	  <supportedRuntime version="v2.0.50215" />
	  <supportedRuntime version="v2.0.40607" />
	  <supportedRuntime version="v1.1.4322" />
	  <supportedRuntime version="v1.0.3705" />
	
	  <requiredRuntime version="v1.0.3705" />
  </startup>

<!--
     The following <runtime> section allows running nunit tests under 
     .NET 1.0 by redirecting assemblies. The appliesTo attribute
     causes the section to be ignored except under .NET 1.0.
	-->
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1"
				appliesTo="v1.0.3705">
			<dependentAssembly>
				<assemblyIdentity name="System" publicKeyToken="b77a5c561934e089" culture="" />
				<bindingRedirect oldVersion="1.0.5000.0" newVersion="1.0.3300.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Data" publicKeyToken="b77a5c561934e089" culture="" />
				<bindingRedirect oldVersion="1.0.5000.0" newVersion="1.0.3300.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Drawing" publicKeyToken="b03f5f7f11d50a3a" culture="" />
				<bindingRedirect oldVersion="1.0.5000.0" newVersion="1.0.3300.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Windows.Forms" publicKeyToken="b77a5c561934e089" culture="" />
				<bindingRedirect oldVersion="1.0.5000.0" newVersion="1.0.3300.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Xml" publicKeyToken="b77a5c561934e089" culture="" />
				<bindingRedirect oldVersion="1.0.5000.0" newVersion="1.0.3300.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
</configuration>
