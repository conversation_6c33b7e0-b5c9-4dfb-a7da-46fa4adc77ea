<?xml version="1.0" encoding="utf-8"?>
<VisualState xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ShowCheckBoxes="false">
  <TopNode>[0-1000]D:\Dev\NUnit\nunit-2.5\work\build\net\2.0\release\NUnitTests.nunit</TopNode>
  <SelectedNode>[0-1000]D:\Dev\NUnit\nunit-2.5\work\build\net\2.0\release\NUnitTests.nunit</SelectedNode>
  <ExcludeCategories>false</ExcludeCategories>
  <Nodes>
    <Node UniqueName="[0-1000]D:\Dev\NUnit\nunit-2.5\work\build\net\2.0\release\NUnitTests.nunit" Expanded="true" />
    <Node UniqueName="[0-2832]D:\Dev\NUnit\nunit-2.5\work\build\net\2.0\release\tests/nunit.framework.tests.dll" Expanded="true" />
    <Node UniqueName="[0-2833]NUnit" Expanded="true" />
    <Node UniqueName="[0-2834]NUnit.Framework" Expanded="true" />
    <Node UniqueName="[0-2835]NUnit.Framework.Constraints" Expanded="true" />
    <Node UniqueName="[0-1001]NUnit.Framework.Constraints.AfterConstraintTest" Expanded="true" />
    <Node UniqueName="[0-1023]NUnit.Framework.Constraints.AndTest" Expanded="true" />
    <Node UniqueName="[0-1914]NUnit.Framework.Constraints.AssignableFromTest" Expanded="true" />
    <Node UniqueName="[0-1924]NUnit.Framework.Constraints.AssignableToTest" Expanded="true" />
    <Node UniqueName="[0-1934]NUnit.Framework.Constraints.AttributeExistsConstraintTest" Expanded="true" />
    <Node UniqueName="[0-1697]NUnit.Framework.Constraints.BinarySerializableTest" Expanded="true" />
    <Node UniqueName="[0-1144]NUnit.Framework.Constraints.ComparerTests" Expanded="true" />
    <Node UniqueName="[0-1271]NUnit.Framework.Constraints.EmptyConstraintTest" Expanded="true" />
    <Node UniqueName="[0-1806]NUnit.Framework.Constraints.EndsWithTest" Expanded="true" />
    <Node UniqueName="[0-1826]NUnit.Framework.Constraints.EndsWithTestIgnoringCase" Expanded="true" />
    <Node UniqueName="[0-1300]NUnit.Framework.Constraints.EqualConstraintTest" Expanded="true" />
    <Node UniqueName="[0-1893]NUnit.Framework.Constraints.ExactTypeTest" Expanded="true" />
    <Node UniqueName="[0-1060]NUnit.Framework.Constraints.FalseConstraintTest" Expanded="true" />
    <Node UniqueName="[0-1196]NUnit.Framework.Constraints.GreaterThanOrEqualTest" Expanded="true" />
    <Node UniqueName="[0-1176]NUnit.Framework.Constraints.GreaterThanTest" Expanded="true" />
    <Node UniqueName="[0-1904]NUnit.Framework.Constraints.InstanceOfTypeTest" Expanded="true" />
    <Node UniqueName="[0-1234]NUnit.Framework.Constraints.LessThanOrEqualTest" Expanded="true" />
    <Node UniqueName="[0-1214]NUnit.Framework.Constraints.LessThanTest" Expanded="true" />
    <Node UniqueName="[0-1398]NUnit.Framework.Constraints.MsgUtilTests" Expanded="true" />
    <Node UniqueName="[0-1076]NUnit.Framework.Constraints.NaNConstraintTest" Expanded="true" />
    <Node UniqueName="[0-1429]NUnit.Framework.Constraints.NotTest" Expanded="true" />
    <Node UniqueName="[0-1035]NUnit.Framework.Constraints.NullConstraintTest" Expanded="true" />
    <Node UniqueName="[0-1288]NUnit.Framework.Constraints.NullOrEmptyStringConstraintTest" Expanded="true" />
    <Node UniqueName="[0-1443]NUnit.Framework.Constraints.NumericsTest" Expanded="true" />
    <Node UniqueName="[0-1477]NUnit.Framework.Constraints.OrTest" Expanded="true" />
    <Node UniqueName="[0-1643]NUnit.Framework.Constraints.PropertyExistsTest" Expanded="true" />
    <Node UniqueName="[0-1660]NUnit.Framework.Constraints.PropertyTest" Expanded="true" />
    <Node UniqueName="[0-1252]NUnit.Framework.Constraints.RangeConstraintTest" Expanded="true" />
    <Node UniqueName="[0-1677]NUnit.Framework.Constraints.ReusableConstraintTests" Expanded="true" />
    <Node UniqueName="[0-1684]NUnit.Framework.Constraints.SameAsTest" Expanded="true" />
    <Node UniqueName="[0-1615]NUnit.Framework.Constraints.SamePathOrUnderTest_Linux" Expanded="true" />
    <Node UniqueName="[0-1592]NUnit.Framework.Constraints.SamePathOrUnderTest_Windows" Expanded="true" />
    <Node UniqueName="[0-1507]NUnit.Framework.Constraints.SamePathTest_Linux" Expanded="true" />
    <Node UniqueName="[0-1488]NUnit.Framework.Constraints.SamePathTest_Windows" Expanded="true" />
    <Node UniqueName="[0-1766]NUnit.Framework.Constraints.StartsWithTest" Expanded="true" />
    <Node UniqueName="[0-1786]NUnit.Framework.Constraints.StartsWithTestIgnoringCase" Expanded="true" />
    <Node UniqueName="[0-1559]NUnit.Framework.Constraints.SubPathTest_Linux" Expanded="true" />
    <Node UniqueName="[0-1529]NUnit.Framework.Constraints.SubPathTest_Windows" Expanded="true" />
    <Node UniqueName="[0-1728]NUnit.Framework.Constraints.SubstringTest" Expanded="true" />
    <Node UniqueName="[0-1748]NUnit.Framework.Constraints.SubstringTestIgnoringCase" Expanded="true" />
    <Node UniqueName="[0-1846]NUnit.Framework.Constraints.ThrowsConstraintTest_ExactType" Expanded="true" />
    <Node UniqueName="[0-1859]NUnit.Framework.Constraints.ThrowsConstraintTest_InstanceOfType" Expanded="true" />
    <Node UniqueName="[0-1873]NUnit.Framework.Constraints.ThrowsConstraintTest_WithConstraint" Expanded="true" />
    <Node UniqueName="[0-1044]NUnit.Framework.Constraints.TrueConstraintTest" Expanded="true" />
    <Node UniqueName="[0-1711]NUnit.Framework.Constraints.XmlSerializableTest" Expanded="true" />
    <Node UniqueName="[0-2836]NUnit.Framework.Syntax" Expanded="true" />
    <Node UniqueName="[0-2093]NUnit.Framework.Syntax.InvalidCodeTests" Expanded="true" />
    <Node UniqueName="[0-2837]NUnit.Framework.Tests" Expanded="true" />
    <Node UniqueName="[0-2772]NUnit.Framework.Tests.ValuesAttributeTests" Expanded="true" />
    <Node UniqueName="[0-3878]D:\Dev\NUnit\nunit-2.5\work\build\net\2.0\release\tests/nunit.core.tests.dll" Expanded="true" />
    <Node UniqueName="[0-3879]NUnit" Expanded="true" />
    <Node UniqueName="[0-3880]NUnit.Core" Expanded="true" />
    <Node UniqueName="[0-3881]NUnit.Core.Tests" Expanded="true" />
    <Node UniqueName="[0-2919]NUnit.Core.Tests.CategoryAttributeTests" Expanded="true" />
    <Node UniqueName="[0-2929]NUnit.Core.Tests.CombinatorialTests" Expanded="true" />
    <Node UniqueName="[0-2998]NUnit.Core.Tests.CultureSettingAndDetectionTests" Expanded="true" />
    <Node UniqueName="[0-3021]NUnit.Core.Tests.EventQueueTests" Expanded="true" />
    <Node UniqueName="[0-3882]NUnit.Core.Tests.Generic" Expanded="true" />
    <Node UniqueName="[0-2838]NUnit.Core.Tests.Generic.DeduceTypeArgsFromArgs&lt;T1,T2&gt;" Expanded="true" />
    <Node UniqueName="[0-2839]NUnit.Core.Tests.Generic.DeduceTypeArgsFromArgs&lt;Double,Int32&gt;(100.0d,42)" Expanded="true" />
    <Node UniqueName="[0-2842]NUnit.Core.Tests.Generic.DeduceTypeArgsFromArgs&lt;Int32,Double&gt;(42,100.0d)" Expanded="true" />
    <Node UniqueName="[0-2845]NUnit.Core.Tests.Generic.SimpleGenericFixture&lt;TList&gt;" Expanded="true" />
    <Node UniqueName="[0-2850]NUnit.Core.Tests.Generic.SimpleGenericMethods" Expanded="true" />
    <Node UniqueName="[0-2866]NUnit.Core.Tests.Generic.TypeParameterUsedWithTestMethod&lt;T&gt;" Expanded="true" />
    <Node UniqueName="[0-2867]NUnit.Core.Tests.Generic.TypeParameterUsedWithTestMethod&lt;Double&gt;" Expanded="true" />
    <Node UniqueName="[0-3158]NUnit.Core.Tests.PairwiseTest" Expanded="true" />
    <Node UniqueName="[0-3169]NUnit.Core.Tests.PairwiseTest+LiveTest" Expanded="true" />
    <Node UniqueName="[0-3178]NUnit.Core.Tests.ParameterizedTestFixture" Expanded="true" />
    <Node UniqueName="[0-3195]NUnit.Core.Tests.ParameterizedTestFixtureWithDataSources" Expanded="true" />
    <Node UniqueName="[0-3196]NUnit.Core.Tests.ParameterizedTestFixtureWithDataSources(42)" Expanded="true" />
    <Node UniqueName="[0-3188]NUnit.Core.Tests.ParameterizedTestFixtureWithNullArguments" Expanded="true" />
    <Node UniqueName="[0-3297]NUnit.Core.Tests.RuntimeFrameworkTests" Expanded="true" />
    <Node UniqueName="[0-3466]NUnit.Core.Tests.TestCaseAttributeTests" Expanded="true" />
    <Node UniqueName="[0-3536]NUnit.Core.Tests.TestCaseSourceTests" Expanded="true" />
    <Node UniqueName="[0-3753]NUnit.Core.Tests.TheoryTests" Expanded="true" />
    <Node UniqueName="[0-3775]NUnit.Core.Tests.TheoryTests+SqrtTests_DisplayResults" Expanded="true" />
    <Node UniqueName="[0-3817]NUnit.Core.Tests.TypeHelperTests" Expanded="true" />
    <Node UniqueName="[0-3844]NUnit.Core.Tests.ValueSourceTests" Expanded="true" />
    <Node UniqueName="[0-4250]D:\Dev\NUnit\nunit-2.5\work\build\net\2.0\release\tests/nunit.util.tests.dll" Expanded="true" />
    <Node UniqueName="[0-4251]NUnit" Expanded="true" />
    <Node UniqueName="[0-4252]NUnit.Util" Expanded="true" />
    <Node UniqueName="[0-4134]NUnit.Util.Tests.ServiceManagerSetUpFixture" Expanded="true" />
    <Node UniqueName="[0-4110]NUnit.Util.Tests.RuntimeFrameworkSelectorTests" Expanded="true" />
    <Node UniqueName="[0-4302]D:\Dev\NUnit\nunit-2.5\work\build\net\2.0\release\tests/nunit.mocks.tests.dll" Expanded="true" />
    <Node UniqueName="[0-4303]NUnit" Expanded="true" />
    <Node UniqueName="[0-4304]NUnit.Mocks" Expanded="true" />
    <Node UniqueName="[0-4305]NUnit.Mocks.Tests" Expanded="true" />
    <Node UniqueName="[0-4361]D:\Dev\NUnit\nunit-2.5\work\build\net\2.0\release\tests/nunit-console.tests.dll" Expanded="true" />
    <Node UniqueName="[0-4362]NUnit" Expanded="true" />
    <Node UniqueName="[0-4363]NUnit.ConsoleRunner" Expanded="true" />
    <Node UniqueName="[0-4364]NUnit.ConsoleRunner.Tests" Expanded="true" />
    <Node UniqueName="[0-4345]NUnit.ConsoleRunner.Tests.TestNameParserTests" Expanded="true" />
    <Node UniqueName="[0-4604]D:\Dev\NUnit\nunit-2.5\work\build\net\2.0\release\tests/nunit.uiexception.tests.dll" Expanded="true" />
    <Node UniqueName="[0-4605]NUnit" Expanded="true" />
    <Node UniqueName="[0-4606]NUnit.UiException" Expanded="true" />
    <Node UniqueName="[0-4607]NUnit.UiException.Tests" Expanded="true" />
    <Node UniqueName="[0-4608]NUnit.UiException.Tests.CodeFormatters" Expanded="true" />
    <Node UniqueName="[0-4609]NUnit.UiException.Tests.Controls" Expanded="true" />
    <Node UniqueName="[0-4610]NUnit.UiException.Tests.StackTraceAnalyzers" Expanded="true" />
    <Node UniqueName="[0-4665]D:\Dev\NUnit\nunit-2.5\work\build\net\2.0\release\tests/nunit.uikit.tests.dll" Expanded="true" />
    <Node UniqueName="[0-4666]NUnit" Expanded="true" />
    <Node UniqueName="[0-4667]NUnit.UiKit" Expanded="true" />
    <Node UniqueName="[0-4668]NUnit.UiKit.Tests" Expanded="true" />
    <Node UniqueName="[0-4687]D:\Dev\NUnit\nunit-2.5\work\build\net\2.0\release\tests/nunit-gui.tests.dll" Expanded="true" />
    <Node UniqueName="[0-4688]NUnit" Expanded="true" />
    <Node UniqueName="[0-4689]NUnit.Gui" Expanded="true" />
    <Node UniqueName="[0-4690]NUnit.Gui.Tests" Expanded="true" />
    <Node UniqueName="[0-4699]D:\Dev\NUnit\nunit-2.5\work\build\net\2.0\release\tests/nunit.fixtures.tests.dll" Expanded="true" />
    <Node UniqueName="[0-4700]NUnit" Expanded="true" />
    <Node UniqueName="[0-4701]NUnit.Fixtures" Expanded="true" />
    <Node UniqueName="[0-4702]NUnit.Fixtures.Tests" Expanded="true" />
  </Nodes>
</VisualState>