<log4net>
	<!-- A1 is set to be a ConsoleAppender -->
	<appender name="A1" type="log4net.Appender.ConsoleAppender">

		<!-- A1 uses PatternLayout -->
		<layout type="log4net.Layout.PatternLayout">
			<!-- Print the date in ISO 8601 format -->
			<conversionPattern value="%-5level %logger - %message%newline" />
		</layout>
	</appender>
	
	<!-- Set root logger level to DEBUG and its only appender to A1 -->
	<root>
		<level value="DEBUG" />
		<appender-ref ref="A1" />
	</root>

</log4net>
