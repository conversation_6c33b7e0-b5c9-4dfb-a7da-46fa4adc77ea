﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Data.Edm</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Data.Edm.EdmConcurrencyMode">
      <summary>Enumerates the EDM property concurrency modes.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmConcurrencyMode.None">
      <summary>Denotes a property that should be used for optimistic concurrency checks.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmConcurrencyMode.Fixed">
      <summary>Denotes a property that should not be used for optimistic concurrency checks.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmContainerElementKind">
      <summary>Defines EDM container element types.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmContainerElementKind.None">
      <summary>Represents an element where the container kind is unknown or in error.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmContainerElementKind.EntitySet">
      <summary>Represents an element implementing <see cref="T:Microsoft.Data.Edm.IEdmEntitySet" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmContainerElementKind.FunctionImport">
      <summary>Represents an element implementing <see cref="T:Microsoft.Data.Edm.IEdmFunctionImport" />.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmElementComparer">
      <summary> Contains IsEquivalentTo() extension methods. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmElementComparer.IsEquivalentTo(Microsoft.Data.Edm.Expressions.IEdmExpression,Microsoft.Data.Edm.Expressions.IEdmExpression)">
      <summary> Returns true if the compared expression is semantically equivalent to this expression. </summary>
      <returns>Equivalence of the two expressions.</returns>
      <param name="thisExpression">Reference to the calling object.</param>
      <param name="otherExpression">Expression being compared to.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmElementComparer.IsEquivalentTo(Microsoft.Data.Edm.IEdmEntitySet,Microsoft.Data.Edm.IEdmEntitySet)">
      <summary> Returns true if the compared entity set is semantically equivalent to this entity set. </summary>
      <returns>The equivalence of the entity sets.</returns>
      <param name="thisSet">Reference to the calling object.</param>
      <param name="otherSet">Entity set being compared to.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmElementComparer.IsEquivalentTo(Microsoft.Data.Edm.IEdmFunction,Microsoft.Data.Edm.IEdmFunction)">
      <summary> Returns true if the compared function is semantically equivalent to this function. </summary>
      <returns>Equivalence of the two functions.</returns>
      <param name="thisFunction">Reference to the calling object.</param>
      <param name="otherFunction">Function being compared to.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmElementComparer.IsEquivalentTo(Microsoft.Data.Edm.IEdmFunctionImport,Microsoft.Data.Edm.IEdmFunctionImport)">
      <summary> Returns true if the compared function import is semantically equivalent to this function import. </summary>
      <returns>Equivalence of the two function imports.</returns>
      <param name="thisFunction">Reference to the calling object.</param>
      <param name="otherFunction">Function import being compared to.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmElementComparer.IsEquivalentTo(Microsoft.Data.Edm.IEdmFunctionParameter,Microsoft.Data.Edm.IEdmFunctionParameter)">
      <summary> Returns true if the compared function parameter is semantically equivalent to this function parameter. </summary>
      <returns>Equivalence of the two function parameters.</returns>
      <param name="thisParameter">Reference to the calling object.</param>
      <param name="otherParameter">Function parameter being compared to.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmElementComparer.IsEquivalentTo(Microsoft.Data.Edm.IEdmProperty,Microsoft.Data.Edm.IEdmProperty)">
      <summary> Returns true if the compared property is semantically equivalent to this property. </summary>
      <returns>Equivalence of the two properties.</returns>
      <param name="thisProp">Reference to the calling object.</param>
      <param name="otherProp">Property being compared to.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmElementComparer.IsEquivalentTo(Microsoft.Data.Edm.IEdmType,Microsoft.Data.Edm.IEdmType)">
      <summary> Returns true if the compared type is semantically equivalent to this type. </summary>
      <returns>Equivalence of the two types.</returns>
      <param name="thisType">Reference to the calling object.</param>
      <param name="otherType">Type being compared to.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmElementComparer.IsEquivalentTo(Microsoft.Data.Edm.IEdmTypeReference,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if the compared type is semantically equivalent to this type. </summary>
      <returns>Equivalence of the two types.</returns>
      <param name="thisType">Reference to the calling object.</param>
      <param name="otherType">Type being compared to.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmFunctionParameterMode">
      <summary>Enumerates the modes of parameters of EDM functions.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmFunctionParameterMode.None">
      <summary>Denotes that a parameter with an unknown or error directionality.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmFunctionParameterMode.In">
      <summary>Denotes that a parameter is used for input.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmFunctionParameterMode.Out">
      <summary>Denotes that a parameter is used for output.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmFunctionParameterMode.InOut">
      <summary>Denotes that a parameter is used for input and output.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmLocation">
      <summary>Represents the location of an EDM item.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmLocation.#ctor">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.EdmLocation" /> class.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmLocation.ToString">
      <summary>Gets a string representation of the location.</summary>
      <returns>A string representation of the location.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmMultiplicity">
      <summary> Enumerates the multiplicities of EDM navigation properties. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmMultiplicity.Unknown">
      <summary> The Multiplicity of the association end is unknown. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmMultiplicity.ZeroOrOne">
      <summary> The Multiplicity of the association end is zero or one. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmMultiplicity.One">
      <summary> The Multiplicity of the association end is one. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmMultiplicity.Many">
      <summary> The Multiplicity of the association end is many. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmNavigationTargetMapping">
      <summary> Represents a mapping from an EDM navigation property to an entity set. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmNavigationTargetMapping.#ctor(Microsoft.Data.Edm.IEdmNavigationProperty,Microsoft.Data.Edm.IEdmEntitySet)">
      <summary> Creates a new navigation target mapping. </summary>
      <param name="navigationProperty">The navigation property.</param>
      <param name="target">The entity set that the navigation propertion targets.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.EdmNavigationTargetMapping.NavigationProperty">
      <summary> Gets the navigation property. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.EdmNavigationTargetMapping.Target">
      <summary> Gets the target entity set. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmOnDeleteAction">
      <summary>Enumerates the actions EDM can apply on deletes.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmOnDeleteAction.None">
      <summary>Takes no action on delete.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmOnDeleteAction.Cascade">
      <summary>On delete also delete items on the other end of the association.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmPrimitiveTypeKind">
      <summary>Enumerates the kinds of EDM primitives.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.None">
      <summary>Represents a primitive type of unknown kind.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Binary">
      <summary>Represents a Binary type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Boolean">
      <summary>Represents a Boolean type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Byte">
      <summary>Represents a Byte type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.DateTime">
      <summary>Represents a DateTime type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.DateTimeOffset">
      <summary>Represents a DateTimeOffset type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Decimal">
      <summary>Represents a Decimal type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Double">
      <summary>Represents a Double type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Guid">
      <summary>Represents a Guid type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Int16">
      <summary>Represents an Int16 type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Int32">
      <summary>Represents an Int32 type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Int64">
      <summary>Represents an Int64 type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.SByte">
      <summary>Represents a SByte type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Single">
      <summary>Represents a Single type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.String">
      <summary>Represents a String type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Stream">
      <summary>Represents a Stream type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Time">
      <summary>Represents a Time type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Geography">
      <summary>Represents an arbitrary Geography type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeographyPoint">
      <summary> Represents a geography Point type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeographyLineString">
      <summary> Represents a geography LineString type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeographyPolygon">
      <summary> Represents a geography Polygon type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeographyCollection">
      <summary>Represents a geography GeographyCollection type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeographyMultiPolygon">
      <summary> Represents a geography MultiPolygon type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeographyMultiLineString">
      <summary> Represents a geography MultiLineString type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeographyMultiPoint">
      <summary> Represents a geography MultiPoint type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.Geometry">
      <summary>Represents an arbitrary Geometry type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeometryPoint">
      <summary> Represents a geometry Point type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeometryLineString">
      <summary> Represents a geometry LineString type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeometryPolygon">
      <summary> Represents a geometry Polygon type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeometryCollection">
      <summary>Represents a geometry GeometryCollection type.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeometryMultiPolygon">
      <summary> Represents a geometry MultiPolygon type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeometryMultiLineString">
      <summary> Represents a geometry MultiLineString type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPrimitiveTypeKind.GeometryMultiPoint">
      <summary> Represents a geometry MultiPoint type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsTemporal(Microsoft.Data.Edm.EdmPrimitiveTypeKind)">
      <summary> Returns true if this type kind represents a temporal type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsIntegral(Microsoft.Data.Edm.EdmPrimitiveTypeKind)">
      <summary> Returns true if this primitive type kind represents an integer type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsSpatial(Microsoft.Data.Edm.EdmPrimitiveTypeKind)">
      <summary> Returns true if this type kind represents a spatial type. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmPropertyKind">
      <summary>Defines EDM property types.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPropertyKind.Structural">
      <summary>Represents a property implementing <see cref="T:Microsoft.Data.Edm.IEdmStructuralProperty" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPropertyKind.Navigation">
      <summary>Represents a property implementing <see cref="T:Microsoft.Data.Edm.IEdmNavigationProperty" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmPropertyKind.None">
      <summary>Represents a property with an unknown or error kind.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmSchemaElementKind">
      <summary>Defines EDM schema element types.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmSchemaElementKind.None">
      <summary>Represents a schema element with unknown or error kind.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmSchemaElementKind.TypeDefinition">
      <summary>Represents a schema element implementing <see cref="T:Microsoft.Data.Edm.IEdmSchemaType" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmSchemaElementKind.Function">
      <summary>Represents a schema element implementing <see cref="T:Microsoft.Data.Edm.IEdmFunction" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmSchemaElementKind.ValueTerm">
      <summary>Represents a schema element implementing <see cref="T:Microsoft.Data.Edm.IEdmValueTerm" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmSchemaElementKind.EntityContainer">
      <summary> Represents a schema element implementing <see cref="T:Microsoft.Data.Edm.IEdmEntityContainer" /></summary>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmTermKind">
      <summary>Defines EDM term kinds.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmTermKind.None">
      <summary>Represents a term with unknown or error kind.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmTermKind.Type">
      <summary>Represents a term implementing <see cref="T:Microsoft.Data.Edm.IEdmEntityType" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmTermKind.Value">
      <summary>Represents a term implementing <see cref="T:Microsoft.Data.Edm.IEdmValueTerm" />.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmTypeKind">
      <summary>Defines EDM metatypes.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmTypeKind.None">
      <summary>Represents a type with an unknown or error kind.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmTypeKind.Primitive">
      <summary>Represents a type implementing <see cref="T:Microsoft.Data.Edm.IEdmPrimitiveType" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmTypeKind.Entity">
      <summary>Represents a type implementing <see cref="T:Microsoft.Data.Edm.IEdmEntityType" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmTypeKind.Complex">
      <summary>Represents a type implementing <see cref="T:Microsoft.Data.Edm.IEdmComplexType" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmTypeKind.Row">
      <summary>Represents a type implementing <see cref="T:Microsoft.Data.Edm.IEdmRowType" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmTypeKind.Collection">
      <summary>Represents a type implementing <see cref="T:Microsoft.Data.Edm.IEdmCollectionType" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmTypeKind.EntityReference">
      <summary>Represents a type implementing <see cref="T:Microsoft.Data.Edm.IEdmEntityReferenceType" />.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.EdmTypeKind.Enum">
      <summary>Represents a type implementing <see cref="T:Microsoft.Data.Edm.IEdmEnumType" />.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsStructured(Microsoft.Data.Edm.EdmTypeKind)">
      <summary> Returns true if this type kind represents a structured type. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmTypeSemantics">
      <summary> Provides semantics of the predefined EDM types. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.ApplyType(Microsoft.Data.Edm.IEdmRowType,System.Boolean)">
      <summary> Returns a reference to this row type definition. </summary>
      <returns>A reference to this row type definition.</returns>
      <param name="rowType">Reference to the calling object.</param>
      <param name="isNullable">Flag specifying if the referenced type should be nullable.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsBinary(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of a binary type, this will return a valid binary type reference to the type definition. Otherwise, it will return a bad binary type reference. </summary>
      <returns>A valid binary type reference if the definition of the reference is of a binary type. Otherwise a bad binary type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsCollection(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of a collection type, this will return a valid collection type reference to the type definition. Otherwise, it will return a bad collection type reference. </summary>
      <returns>A valid collection type reference if the definition of the reference is of a collection type. Otherwise a bad collection type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsComplex(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of a complex type, this will return a valid complex type reference to the type definition. Otherwise, it will return a bad complex type reference. </summary>
      <returns>A valid complex type reference if the definition of the reference is of a complex type. Otherwise a bad complex type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsDecimal(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of a decimal type, this will return a valid decimal type reference to the type definition. Otherwise, it will return a bad decimal type reference. </summary>
      <returns>A valid decimal type reference if the definition of the reference is of a decimal type. Otherwise a bad decimal type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsEntity(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of an entity type, this will return a valid entity type reference to the type definition. Otherwise, it will return a bad entity type reference. </summary>
      <returns>A valid entity type reference if the definition of the reference is of an entity type. Otherwise a bad entity type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsEntityReference(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of an entity reference type, this will return a valid entity reference type reference to the type definition. Otherwise, it will return a bad entity reference type reference. </summary>
      <returns>A valid entity reference type reference if the definition of the reference is of an entity reference type. Otherwise a bad entity reference type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsEnum(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of an enumeration type, this will return a valid enumeration type reference to the type definition. Otherwise, it will return a bad enumeration type reference. </summary>
      <returns>A valid enumeration type reference if the definition of the reference is of an enumeration type. Otherwise a bad enumeration type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsPrimitive(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of a primitive type, this will return a valid primitive type reference to the type definition. Otherwise, it will return a bad primitive type reference. </summary>
      <returns>A valid primitive type reference if the definition of the reference is of a primitive type. Otherwise a bad primitive type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsRow(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of a row type, this will return a valid row type reference to the type definition. Otherwise, it will return a bad row type reference. </summary>
      <returns>A valid row type reference if the definition of the reference is of a row type. Otherwise a bad row type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsSpatial(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of a spatial type, this will return a valid spatial type reference to the type definition. Otherwise, it will return a bad spatial type reference. </summary>
      <returns>A valid spatial type reference if the definition of the reference is of a spatial type. Otherwise a bad spatial type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsString(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of a string type, this will return a valid string type reference to the type definition. Otherwise, it will return a bad string type reference. </summary>
      <returns>A valid string type reference if the definition of the reference is of a string type. Otherwise a bad string type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsStructured(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of a structured type, this will return a valid structured type reference to the type definition. Otherwise, it will return a bad structured type reference. </summary>
      <returns>A valid structured type reference if the definition of the reference is of a structured type. Otherwise a bad structured type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.AsTemporal(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> If this reference is of a temporal type, this will return a valid temporal type reference to the type definition. Otherwise, it will return a bad temporal type reference. </summary>
      <returns>A valid temporal type reference if the definition of the reference is of a temporal type. Otherwise a bad temporal type reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.InheritsFrom(Microsoft.Data.Edm.IEdmStructuredType,Microsoft.Data.Edm.IEdmStructuredType)">
      <summary> Determines if the potential base type is in the inheritance hierarchy of the type being tested. </summary>
      <returns>True if and only if the type inherits from the potential base type.</returns>
      <param name="type">Type to be tested for derivation from the other type.</param>
      <param name="potentialBaseType">The potential base type of the type being tested.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsBinary(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a binary type. </summary>
      <returns>This reference refers to a binary type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsBoolean(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a boolean type. </summary>
      <returns>This reference refers to a boolean type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsByte(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a byte type. </summary>
      <returns>This reference refers to a byte type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsCollection(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a collection. </summary>
      <returns>This reference refers to a collection.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsComplex(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a complex type. </summary>
      <returns>This reference refers to a complex type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsDateTime(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a DateTime type. </summary>
      <returns>This reference refers to a DateTime type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsDateTimeOffset(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a DateTimeOffset type. </summary>
      <returns>This reference refers to a DateTimeOffset type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsDecimal(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a decimal type. </summary>
      <returns>This reference refers to a decimal type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsDouble(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a double type. </summary>
      <returns>This reference refers to a double type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsEntity(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to an entity type. </summary>
      <returns>This reference refers to an entity type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsEntityReference(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to an entity type. </summary>
      <returns>This reference refers to an entity type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsEnum(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to an enumeration type. </summary>
      <returns>This reference refers to an enumeration type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsFloating(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a floating point type. </summary>
      <returns>This reference refers to a floating point type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsGuid(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a GUID type. </summary>
      <returns>This reference refers to a GUID type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsInt16(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to an Int16 type. </summary>
      <returns>This reference refers to an Int16 type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsInt32(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to an Int32 type. </summary>
      <returns>This reference refers to an Int32 type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsInt64(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to an Int64 type. </summary>
      <returns>This reference refers to an Int64 type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsIntegral(Microsoft.Data.Edm.EdmPrimitiveTypeKind)">
      <summary> Returns true if this primitive type kind represents an integer type. </summary>
      <returns>This kind refers to an integer type.</returns>
      <param name="primitiveTypeKind">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsIntegral(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to an integer type. </summary>
      <returns>This reference refers to an integer type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsOrInheritsFrom(Microsoft.Data.Edm.IEdmType,Microsoft.Data.Edm.IEdmType)">
      <summary> Determines if a type is equivalent to or derived from another type. </summary>
      <returns>True if and only if the thisType is equivalent to or inherits from otherType.</returns>
      <param name="thisType">Type to be tested for equivalence to or derivation from the other type.</param>
      <param name="otherType">Type that is the other type.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsPrimitive(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a primitive type. </summary>
      <returns>This reference refers to a primitive type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsRow(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a row type. </summary>
      <returns>This reference refers to a row type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsSByte(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to an SByte type. </summary>
      <returns>This reference refers to an SByte type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsSignedIntegral(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a signed integral type. </summary>
      <returns>This reference refers to a signed integral type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsSingle(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a single type. </summary>
      <returns>This reference refers to a single type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsSpatial(Microsoft.Data.Edm.EdmPrimitiveTypeKind)">
      <summary> Returns true if this type kind represents a spatial type. </summary>
      <returns>This kind refers to a spatial type.</returns>
      <param name="typeKind">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsSpatial(Microsoft.Data.Edm.IEdmType)">
      <summary> Returns true if this definition refers to a spatial type. </summary>
      <returns>This definition refers to a spatial type.</returns>
      <param name="type">Definition to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsSpatial(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a spatial type. </summary>
      <returns>This reference refers to a spatial type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsStream(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a stream type. </summary>
      <returns>This reference refers to a stream type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsString(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a string type. </summary>
      <returns>This reference refers to a string type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsStructured(Microsoft.Data.Edm.EdmTypeKind)">
      <summary> Returns true if this type kind represents a structured type. </summary>
      <returns>This kind refers to a structured type.</returns>
      <param name="typeKind">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsStructured(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a structured type. </summary>
      <returns>This reference refers to a structured type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsTemporal(Microsoft.Data.Edm.EdmPrimitiveTypeKind)">
      <summary> Returns true if this type kind represents a temporal type. </summary>
      <returns>This kind refers to a temporal type.</returns>
      <param name="typeKind">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsTemporal(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a temporal type. </summary>
      <returns>This reference refers to a temporal type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.IsTime(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns true if this reference refers to a time type. </summary>
      <returns>This reference refers to a time type.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmTypeSemantics.PrimitiveKind(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns the primitive kind of the definition of this reference. </summary>
      <returns>The primitive kind of the definition of this reference.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.ExtensionMethods">
      <summary> Contains extension methods for <see cref="T:Microsoft.Data.Edm.IEdmModel" /> interfaces. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.BaseComplexType(Microsoft.Data.Edm.IEdmComplexType)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.BaseComplexType(Microsoft.Data.Edm.IEdmComplexTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.BaseEntityType(Microsoft.Data.Edm.IEdmEntityType)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.BaseEntityType(Microsoft.Data.Edm.IEdmEntityTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.BaseType(Microsoft.Data.Edm.IEdmStructuredTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.CollectionDefinition(Microsoft.Data.Edm.IEdmCollectionTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.ComplexDefinition(Microsoft.Data.Edm.IEdmComplexTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.DeclaredNavigationProperties(Microsoft.Data.Edm.IEdmEntityType)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.DeclaredNavigationProperties(Microsoft.Data.Edm.IEdmEntityTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.DeclaredStructuralProperties(Microsoft.Data.Edm.IEdmStructuredType)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.DeclaredStructuralProperties(Microsoft.Data.Edm.IEdmStructuredTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.DeclaringEntityType(Microsoft.Data.Edm.IEdmNavigationProperty)">
      <summary> Gets the entity type declaring this navigation property. </summary>
      <returns>The entity type that declares this navigation property.</returns>
      <param name="property">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.DirectValueAnnotations(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmElement)">
      <summary> Gets the direct value annotations for an element. </summary>
      <returns>The immediate value annotations of the element.</returns>
      <param name="model">The model containing the annotations.</param>
      <param name="element">The annotated element.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.ElementType(Microsoft.Data.Edm.IEdmCollectionTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.EntityContainers(Microsoft.Data.Edm.IEdmModel)">
      <summary> Gets the entity containers belonging to this model. </summary>
      <returns>Entity containers belonging to this model.</returns>
      <param name="model">Model to search for entity containers.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.EntityDefinition(Microsoft.Data.Edm.IEdmEntityTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.EntityReferenceDefinition(Microsoft.Data.Edm.IEdmEntityReferenceTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.EntitySets(Microsoft.Data.Edm.IEdmEntityContainer)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.EntityType(Microsoft.Data.Edm.IEdmEntityReferenceTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.EnumDefinition(Microsoft.Data.Edm.IEdmEnumTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindEntityContainer(Microsoft.Data.Edm.IEdmModel,System.String)">
      <summary> Searches for an entity container with the given name in this model and all referenced models and returns null if no such entity container exists. </summary>
      <returns>The requested entity container, or null if no such entity container exists.</returns>
      <param name="model">The model to search.</param>
      <param name="qualifiedName">The qualified name of the entity container being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindFunctions(Microsoft.Data.Edm.IEdmModel,System.String)">
      <summary> Searches for functions with the given name in this model and all referenced models and returns an empty enumerable if no such functions exist. </summary>
      <returns>The requested functions.</returns>
      <param name="model">The model to search.</param>
      <param name="qualifiedName">The qualified name of the functions being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindNavigationProperty(Microsoft.Data.Edm.IEdmEntityTypeReference,System.String)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindProperty(Microsoft.Data.Edm.Expressions.IEdmRecordExpression,System.String)">
      <summary> Finds a property of a record expression. </summary>
      <returns>The property, if found, otherwise null.</returns>
      <param name="expression">The record expression.</param>
      <param name="name">Name of the property to find.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindProperty(Microsoft.Data.Edm.IEdmStructuredTypeReference,System.String)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindPropertyBinding(Microsoft.Data.Edm.Annotations.IEdmTypeAnnotation,Microsoft.Data.Edm.IEdmProperty)">
      <summary> Gets the binding of a property of the type term of a type annotation. </summary>
      <returns>The binding of the property in the type annotation, or null if no binding exists.</returns>
      <param name="annotation">Annotation to search.</param>
      <param name="property">Property to search for.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindPropertyBinding(Microsoft.Data.Edm.Annotations.IEdmTypeAnnotation,System.String)">
      <summary> Gets the binding of a property of the type term of a type annotation. </summary>
      <returns>The binding of the property in the type annotation, or null if no binding exists.</returns>
      <param name="annotation">Annotation to search.</param>
      <param name="propertyName">Name of the property to search for.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindType(Microsoft.Data.Edm.IEdmModel,System.String)">
      <summary> Searches for a type with the given name in this model and all referenced models and returns null if no such type exists. </summary>
      <returns>The requested type, or null if no such type exists.</returns>
      <param name="model">The model to search.</param>
      <param name="qualifiedName">The qualified name of the type being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindValueTerm(Microsoft.Data.Edm.IEdmModel,System.String)">
      <summary> Searches for a value term with the given name in this model and all referenced models and returns null if no such value term exists. </summary>
      <returns>The requested value term, or null if no such value term exists.</returns>
      <param name="model">The model to search.</param>
      <param name="qualifiedName">The qualified name of the value term being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindVocabularyAnnotations(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmVocabularyAnnotatable)">
      <summary> Gets an annotatable element's vocabulary annotations defined in a specific model and models referenced by that model. </summary>
      <returns>Annotations attached to the element by this model or by models referenced by this model.</returns>
      <param name="model">The model to search.</param>
      <param name="element">Element to check for annotations.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindVocabularyAnnotations``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmVocabularyAnnotatable,Microsoft.Data.Edm.IEdmTerm)">
      <summary> Gets an annotatable element's vocabulary annotations that bind a particular term. </summary>
      <returns>Annotations attached to the element by this model or by models referenced by this model that bind the term.</returns>
      <param name="model">Model to search.</param>
      <param name="element">Element to check for annotations.</param>
      <param name="term">Term to search for.</param>
      <typeparam name="T">Type of the annotation being returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindVocabularyAnnotations``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmVocabularyAnnotatable,Microsoft.Data.Edm.IEdmTerm,System.String)">
      <summary> Gets an annotatable element's vocabulary annotations that bind a particular term. </summary>
      <returns>Annotations attached to the element by this model or by models referenced by this model that bind the term with the given qualifier.</returns>
      <param name="model">Model to search.</param>
      <param name="element">Element to check for annotations.</param>
      <param name="term">Term to search for.</param>
      <param name="qualifier">Qualifier to apply.</param>
      <typeparam name="T">Type of the annotation being returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindVocabularyAnnotations``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmVocabularyAnnotatable,System.String)">
      <summary> Gets an annotatable element's vocabulary annotations that bind a particular term. </summary>
      <returns>Annotations attached to the element by this model or by models referenced by this model that bind the term.</returns>
      <param name="model">Model to search.</param>
      <param name="element">Element to check for annotations.</param>
      <param name="termName">Name of the term to search for.</param>
      <typeparam name="T">Type of the annotation being returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindVocabularyAnnotations``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmVocabularyAnnotatable,System.String,System.String)">
      <summary> Gets an annotatable element's vocabulary annotations that bind a particular term. </summary>
      <returns>Annotations attached to the element by this model or by models referenced by this model that bind the term with the given qualifier.</returns>
      <param name="model">Model to search.</param>
      <param name="element">Element to check for annotations.</param>
      <param name="termName">Name of the term to search for.</param>
      <param name="qualifier">Qualifier to apply.</param>
      <typeparam name="T">Type of the annotation being returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FindVocabularyAnnotationsIncludingInheritedAnnotations(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmVocabularyAnnotatable)">
      <summary> Gets an annotatable element's vocabulary annotations defined in a specific model and models referenced by that model. </summary>
      <returns>Annotations attached to the element (or, if the element is a type, to its base types) by this model or by models referenced by this model.</returns>
      <param name="model">The model to search.</param>
      <param name="element">Element to check for annotations.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FullName(Microsoft.Data.Edm.IEdmSchemaElement)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FullName(Microsoft.Data.Edm.IEdmTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.FunctionImports(Microsoft.Data.Edm.IEdmEntityContainer)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetAnnotationValue``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmElement)">
      <summary> Gets an annotation value from an annotatable element. </summary>
      <param name="model">The model containing the annotation.</param>
      <param name="element">The annotated element.</param>
      <typeparam name="T">Type of the annotation being returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetAnnotationValue``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmElement,System.String,System.String)">
      <summary> Gets an annotation value corresponding to the given namespace and name provided. </summary>
      <param name="model">The model containing the annotation.</param>
      <param name="element">The annotated element.</param>
      <param name="namespaceName">Namespace of the annotation.</param>
      <param name="localName">Name of the annotation inside the namespace.</param>
      <typeparam name="T">Type of the annotation being returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetAnnotationValue(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmElement,System.String,System.String)">
      <summary> Gets an annotation value corresponding to the given namespace and name provided. </summary>
      <returns>The requested annotation value, if it exists. Otherwise, null.</returns>
      <param name="model">The model containing the annotation.</param>
      <param name="element">The annotated element.</param>
      <param name="namespaceName">Namespace of the annotation.</param>
      <param name="localName">Name of the annotation inside the namespace.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetAnnotationValues(Microsoft.Data.Edm.IEdmModel,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationBinding})">
      <summary> Retrieves a set of annotation values. For each requested value, returns null if no annotation with the given name exists for the given element. </summary>
      <param name="model">The model in which to find the annotations.</param>
      <param name="annotations">The set of requested annotations.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetDocumentation(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmElement)">
      <summary> Gets documentation for a specified element. </summary>
      <returns>Documentation that exists on the element. Otherwise, null.</returns>
      <param name="model">The model containing the documentation.</param>
      <param name="element">The element.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetEdmVersion(Microsoft.Data.Edm.IEdmModel)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetPropertyValue(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Values.IEdmStructuredValue,Microsoft.Data.Edm.IEdmProperty,Microsoft.Data.Edm.Evaluation.EdmEvaluator)">
      <summary> Gets the <see cref="T:Microsoft.Data.Edm.Values.IEdmValue" /> of a property of a term type that has been applied to the type of a value. </summary>
      <returns>Value of the property evaluated against the supplied value, or null if no relevant type annotation exists.</returns>
      <param name="model">Model to search for type annotations.</param>
      <param name="context">Value to use as context in evaluation.</param>
      <param name="property">Property to evaluate.</param>
      <param name="evaluator">Evaluator to use to perform expression evaluation.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetPropertyValue``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Values.IEdmStructuredValue,Microsoft.Data.Edm.IEdmProperty,Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator)">
      <summary> Gets the CLR value of a property of a term type that has been applied to the type of a value. </summary>
      <param name="model">Model to search for type annotations.</param>
      <param name="context">Value to use as context in evaluation.</param>
      <param name="property">Property to evaluate.</param>
      <param name="evaluator">Evaluator to use to perform expression evaluation.</param>
      <typeparam name="T">The CLR type of the value to be returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetPropertyValue(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Values.IEdmStructuredValue,Microsoft.Data.Edm.IEdmProperty,System.String,Microsoft.Data.Edm.Evaluation.EdmEvaluator)">
      <summary> Gets the <see cref="T:Microsoft.Data.Edm.Values.IEdmValue" /> of a property of a term type that has been applied to the type of a value. </summary>
      <returns>Value of the property evaluated against the supplied value, or null if no relevant type annotation exists.</returns>
      <param name="model">Model to search for type annotations.</param>
      <param name="context">Value to use as context in evaluation.</param>
      <param name="property">Property to evaluate.</param>
      <param name="qualifier">Qualifier to apply.</param>
      <param name="evaluator">Evaluator to use to perform expression evaluation.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetPropertyValue``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Values.IEdmStructuredValue,Microsoft.Data.Edm.IEdmProperty,System.String,Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator)">
      <summary> Gets the CLR value of a property of a term type that has been applied to the type of a value. </summary>
      <param name="model">Model to search for type annotations.</param>
      <param name="context">Value to use as context in evaluation.</param>
      <param name="property">Property to evaluate.</param>
      <param name="qualifier">Qualifier to apply.</param>
      <param name="evaluator">Evaluator to use to perform expression evaluation.</param>
      <typeparam name="T">The CLR type of the value to be returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetTermValue(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Values.IEdmStructuredValue,Microsoft.Data.Edm.IEdmValueTerm,Microsoft.Data.Edm.Evaluation.EdmEvaluator)">
      <summary> Gets the <see cref="T:Microsoft.Data.Edm.Values.IEdmValue" /> of a vocabulary term that has been applied to the type of a value. </summary>
      <returns>Value of the term evaluated against the supplied value.</returns>
      <param name="model">Model to search for term annotations.</param>
      <param name="context">Value to use as context in evaluation.</param>
      <param name="term">Term to evaluate.</param>
      <param name="evaluator">Evaluator to use to perform expression evaluation.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetTermValue``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Values.IEdmStructuredValue,Microsoft.Data.Edm.IEdmValueTerm,Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator)">
      <summary> Gets the CLR value of a vocabulary term that has been applied to the type of a value. </summary>
      <param name="model">Model to search for term annotations.</param>
      <param name="context">Value to use as context in evaluation.</param>
      <param name="term">Term to evaluate.</param>
      <param name="evaluator">Evaluator to use to perform expression evaluation.</param>
      <typeparam name="T">The CLR type of the value to be returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetTermValue(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Values.IEdmStructuredValue,Microsoft.Data.Edm.IEdmValueTerm,System.String,Microsoft.Data.Edm.Evaluation.EdmEvaluator)">
      <summary> Gets the <see cref="T:Microsoft.Data.Edm.Values.IEdmValue" /> of a vocabulary term that has been applied to the type of a value. </summary>
      <returns>Value of the term evaluated against the supplied value.</returns>
      <param name="model">Model to search for term annotations.</param>
      <param name="context">Value to use as context in evaluation.</param>
      <param name="term">Term to evaluate.</param>
      <param name="qualifier">Qualifier to apply.</param>
      <param name="evaluator">Evaluator to use to perform expression evaluation.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetTermValue``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Values.IEdmStructuredValue,Microsoft.Data.Edm.IEdmValueTerm,System.String,Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator)">
      <summary> Gets the CLR value of a vocabulary term that has been applied to the type of a value. </summary>
      <param name="model">Model to search for term annotations.</param>
      <param name="context">Value to use as context in evaluation.</param>
      <param name="term">Term to evaluate.</param>
      <param name="qualifier">Qualifier to apply.</param>
      <param name="evaluator">Evaluator to use to perform expression evaluation.</param>
      <typeparam name="T">The CLR type of the value to be returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetTermValue(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Values.IEdmStructuredValue,System.String,Microsoft.Data.Edm.Evaluation.EdmEvaluator)">
      <summary> Gets the <see cref="T:Microsoft.Data.Edm.Values.IEdmValue" /> of a vocabulary term that has been applied to the type of a value. </summary>
      <returns>Value of the term evaluated against the supplied value.</returns>
      <param name="model">Model to search for term annotations.</param>
      <param name="context">Value to use as context in evaluation.</param>
      <param name="termName">Name of the term to evaluate.</param>
      <param name="evaluator">Evaluator to use to perform expression evaluation.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetTermValue``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Values.IEdmStructuredValue,System.String,Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator)">
      <summary> Gets the CLR value of a vocabulary term that has been applied to the type of a value. </summary>
      <param name="model">Model to search for term annotations.</param>
      <param name="context">Value to use as context in evaluation.</param>
      <param name="termName">Name of the term to evaluate.</param>
      <param name="evaluator">Evaluator to use to perform expression evaluation.</param>
      <typeparam name="T">The CLR type of the value to be returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetTermValue(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Values.IEdmStructuredValue,System.String,System.String,Microsoft.Data.Edm.Evaluation.EdmEvaluator)">
      <summary> Gets the <see cref="T:Microsoft.Data.Edm.Values.IEdmValue" /> of a vocabulary term that has been applied to the type of a value. </summary>
      <returns>Value of the term evaluated against the supplied value.</returns>
      <param name="model">Model to search for term annotations.</param>
      <param name="context">Value to use as context in evaluation.</param>
      <param name="termName">Name of the term to evaluate.</param>
      <param name="qualifier">Qualifier to apply.</param>
      <param name="evaluator">Evaluator to use to perform expression evaluation.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.GetTermValue``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Values.IEdmStructuredValue,System.String,System.String,Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator)">
      <summary> Gets the CLR value of a vocabulary term that has been applied to the type of a value. </summary>
      <param name="model">Model to search for term annotations.</param>
      <param name="context">Value to use as context in evaluation.</param>
      <param name="termName">Name of the term to evaluate.</param>
      <param name="qualifier">Qualifier to apply.</param>
      <param name="evaluator">Evaluator to use to perform expression evaluation.</param>
      <typeparam name="T">The CLR type of the value to be returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.IsAbstract(Microsoft.Data.Edm.IEdmStructuredTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.IsOpen(Microsoft.Data.Edm.IEdmStructuredTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.Key(Microsoft.Data.Edm.IEdmEntityType)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.Key(Microsoft.Data.Edm.IEdmEntityTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.Location(Microsoft.Data.Edm.IEdmElement)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.Multiplicity(Microsoft.Data.Edm.IEdmNavigationProperty)">
      <summary> Gets the multiplicity of this end of a bidirectional relationship between this navigation property and its partner. </summary>
      <returns>The multiplicity of this end of the relationship.</returns>
      <param name="property">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.NavigationProperties(Microsoft.Data.Edm.IEdmEntityType)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.NavigationProperties(Microsoft.Data.Edm.IEdmEntityTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.PrimitiveDefinition(Microsoft.Data.Edm.IEdmPrimitiveTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.PrimitiveKind(Microsoft.Data.Edm.IEdmPrimitiveTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.Properties(Microsoft.Data.Edm.IEdmStructuredType)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.RowDefinition(Microsoft.Data.Edm.IEdmRowTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.SchemaElementsAcrossModels(Microsoft.Data.Edm.IEdmModel)">
      <summary> Gets all schema elements from the model, and models referenced by it. </summary>
      <returns>Schema elements from the model, and models referenced by it.</returns>
      <param name="model">Model to search for elements</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.SetAnnotationValue(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmElement,System.String,System.String,System.Object)">
      <summary> Sets an annotation value for an EDM element. If the value is null, no annotation is added and an existing annotation with the same name is removed. </summary>
      <param name="model">The model containing the annotation.</param>
      <param name="element">The annotated element.</param>
      <param name="namespaceName">Namespace that the annotation belongs to.</param>
      <param name="localName">Name of the annotation within the namespace.</param>
      <param name="value">Value of the new annotation.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.SetAnnotationValue``1(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmElement,``0)">
      <summary> Sets an annotation value on an annotatable element. </summary>
      <param name="model">The model containing the annotation.</param>
      <param name="element">The annotated element.</param>
      <param name="value">Value of the new annotation.</param>
      <typeparam name="T">Type of the annotation being set.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.SetAnnotationValues(Microsoft.Data.Edm.IEdmModel,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationBinding})">
      <summary> Sets a set of annotation values. If a supplied value is null, no annotation is added and an existing annotation with the same name is removed. </summary>
      <param name="model">The model in which to set the annotations.</param>
      <param name="annotations">The annotations to set.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.SetDocumentation(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmElement,Microsoft.Data.Edm.IEdmDocumentation)">
      <summary> Sets documentation for a specified element. </summary>
      <param name="model">The model containing the documentation.</param>
      <param name="element">The element.</param>
      <param name="documentation">Documentation to set.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.SetEdmVersion(Microsoft.Data.Edm.IEdmModel,System.Version)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.StructuralProperties(Microsoft.Data.Edm.IEdmStructuredType)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.StructuralProperties(Microsoft.Data.Edm.IEdmStructuredTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.StructuredDefinition(Microsoft.Data.Edm.IEdmStructuredTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.ToEntityType(Microsoft.Data.Edm.IEdmNavigationProperty)">
      <summary> Gets the entity type targeted by this navigation property. </summary>
      <returns>The entity type targeted by this navigation property.</returns>
      <param name="property">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.TryGetRelativeEntitySetPath(Microsoft.Data.Edm.IEdmFunctionImport,Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmFunctionParameter@,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.IEdmNavigationProperty}@)">
      <summary> Analyzes <see cref="T:Microsoft.Data.Edm.IEdmFunctionImport" />.EntitySet expression and returns a relative path to an <see cref="T:Microsoft.Data.Edm.IEdmEntitySet" /> if available. The path starts with the parameter and may have optional sequence of <see cref="T:Microsoft.Data.Edm.IEdmNavigationProperty" /> and type casts segments. </summary>
      <returns>True if the entity set expression of the functionImport contains a relative path an <see cref="T:Microsoft.Data.Edm.IEdmEntitySet" />, otherwise false.</returns>
      <param name="functionImport">The function import containing the entity set expression.</param>
      <param name="model">The model containing the function import.</param>
      <param name="parameter">The function import parameter from which the relative entity set path starts.</param>
      <param name="path">The optional sequence of navigation properties.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.TryGetStaticEntitySet(Microsoft.Data.Edm.IEdmFunctionImport,Microsoft.Data.Edm.IEdmEntitySet@)">
      <summary> Analyzes <see cref="T:Microsoft.Data.Edm.IEdmFunctionImport" />.EntitySet expression and returns a static <see cref="T:Microsoft.Data.Edm.IEdmFunctionImport" /> reference if available. </summary>
      <returns>True if the entity set expression of the functionImport contains a static reference to an <see cref="T:Microsoft.Data.Edm.IEdmEntitySet" />, otherwise false.</returns>
      <param name="functionImport">The function import containing the entity set expression.</param>
      <param name="entitySet">The static entity set of the function import.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.TypeKind(Microsoft.Data.Edm.IEdmTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.TypeTerm(Microsoft.Data.Edm.Annotations.IEdmTypeAnnotation)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.ValueTerm(Microsoft.Data.Edm.Annotations.IEdmValueAnnotation)"></member>
    <member name="M:Microsoft.Data.Edm.ExtensionMethods.VocabularyAnnotations(Microsoft.Data.Edm.IEdmVocabularyAnnotatable,Microsoft.Data.Edm.IEdmModel)">
      <summary> Gets an annotatable element's vocabulary annotations as seen from a particular model. </summary>
      <returns>Annotations attached to the element by the model or by models referenced by the model.</returns>
      <param name="element">Reference to the calling object.</param>
      <param name="model">Model to check for annotations.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmBinaryTypeReference">
      <summary>Represents a reference to an EDM binary type.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmBinaryTypeReference.IsFixedLength">
      <summary>Gets a value indicating whether this type specifies fixed length.</summary>
      <returns>True if the type specifies fixed length; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmBinaryTypeReference.IsMaxMaxLength">
      <summary>Gets a value indicating whether this type specifies the maximum allowed max length.</summary>
      <returns>True if the type specifies the maximum allowed max length; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmBinaryTypeReference.MaxLength">
      <summary>Gets the maximum length of this type.</summary>
      <returns>The maximum length of this type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmCheckable">
      <summary>Defines an EDM component who is invalid or whose validity is unknown at construction.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmCheckable.Errors">
      <summary>Gets an error if one exists with the current object.</summary>
      <returns>An error.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmCollectionType">
      <summary>Represents a definition of an EDM collection type.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmCollectionType.ElementType">
      <summary>Gets the element type of this collection.</summary>
      <returns>The element type of this collection.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmCollectionTypeReference">
      <summary>Represents a reference to an EDM collection type.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmComplexType">
      <summary>Represents a definition of an EDM complex type.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmComplexTypeReference">
      <summary>Represents a reference to an EDM complex type.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmDecimalTypeReference">
      <summary>Represents a reference to an EDM decimal type.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmDecimalTypeReference.Precision">
      <summary>Gets the precision of this type.</summary>
      <returns>The precision of this type.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmDecimalTypeReference.Scale">
      <summary>Gets the scale of this type.</summary>
      <returns>The scale of this type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmDocumentation">
      <summary>Represents an EDM documentation.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmDocumentation.LongDescription">
      <summary>Gets a long description of this documentation.</summary>
      <returns>A long description of this documentation.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmDocumentation.Summary">
      <summary>Gets a summary of this documentation.</summary>
      <returns>A summary of this documentation.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmElement">
      <summary>Represents an EDM element.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmEntityContainer">
      <summary>Represents an EDM entity container.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmEntityContainer.Elements">
      <summary>Gets a collection of the elements of this entity container.</summary>
      <returns>A collection of the elements of this entity container.</returns>
    </member>
    <member name="M:Microsoft.Data.Edm.IEdmEntityContainer.FindEntitySet(System.String)">
      <summary>Searches for an entity set with the given name in this entity container and returns null if no such set exists.</summary>
      <returns>The requested element, or null if the element does not exist.</returns>
      <param name="setName">The name of the element being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.IEdmEntityContainer.FindFunctionImports(System.String)">
      <summary>Searches for function imports with the given name in this entity container and returns null if no such function import exists.</summary>
      <returns>A group of the requested function imports, or null if no such function import exists.</returns>
      <param name="functionName">The name of the function import being found.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmEntityContainerElement">
      <summary> Represents the common elements of all EDM entity container elements. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmEntityContainerElement.Container">
      <summary> Gets the container that contains this element. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmEntityContainerElement.ContainerElementKind"></member>
    <member name="T:Microsoft.Data.Edm.IEdmEntityReferenceType">
      <summary>Represents a definition of an EDM entity reference type.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmEntityReferenceType.EntityType">
      <summary>Gets the entity type pointed to by this entity reference.</summary>
      <returns>The entity type pointed to by this entity reference.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmEntityReferenceTypeReference">
      <summary>Represents a reference to an EDM entity reference type.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmEntitySet">
      <summary>Represents an EDM entity set.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmEntitySet.ElementType">
      <summary>Gets the entity type contained in this entity set.</summary>
      <returns>The entity type contained in this entity set.</returns>
    </member>
    <member name="M:Microsoft.Data.Edm.IEdmEntitySet.FindNavigationTarget(Microsoft.Data.Edm.IEdmNavigationProperty)">
      <summary> Finds the entity set that a navigation property targets. </summary>
      <returns>The entity set that the navigation propertion targets, or null if no such entity set exists.</returns>
      <param name="navigationProperty">The navigation property.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmEntitySet.NavigationTargets">
      <summary> Gets the navigation targets of this entity set. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmEntityType">
      <summary>Represents a definition of an EDM entity type.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmEntityType.DeclaredKey">
      <summary>Gets the structural properties of the entity type that make up the entity key.</summary>
      <returns>The structural properties of the entity type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmEntityTypeReference">
      <summary>Represents a reference for the definition of an EDM entity type.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmEnumMember">
      <summary>Represents a definition of an EDM enumeration type member.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmEnumMember.DeclaringType">
      <summary>Gets the type that this member belongs to.</summary>
      <returns>The type that this member belongs to.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmEnumMember.Value">
      <summary>Gets the value of this enumeration type member.</summary>
      <returns>The value of this enumeration type member.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmEnumType">
      <summary>Represents a definition of an EDM enumeration type.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmEnumType.Members">
      <summary>Gets the members of this enumeration type.</summary>
      <returns>The members of this enumeration type.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmEnumType.TreatAsBits">
      <summary>Gets a value indicating whether the enumeration type can be treated as a bit field.</summary>
      <returns>True if the value indicating whether the enumeration type can be treated as a bit field; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmEnumType.UnderlyingType">
      <summary>Gets the underlying type of this enumeration type.</summary>
      <returns>The underlying type of this enumeration type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmEnumTypeReference">
      <summary>Represents a reference for a definition of an EDM enumeration type.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmFunction">
      <summary>Represents an EDM function.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmFunction.DefiningExpression">
      <summary>Gets the defining expression of this function.</summary>
      <returns>The defining expression of this function.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmFunctionBase">
      <summary>Represents the common base type of EDM functions and function imports.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.IEdmFunctionBase.FindParameter(System.String)">
      <summary>Searches for a parameter with the given name, and returns null if no such parameter exists.</summary>
      <returns>The requested parameter or null if no such parameter exists.</returns>
      <param name="name">The name of the parameter being found.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmFunctionBase.Parameters">
      <summary>Gets the collection of parameters for this function.</summary>
      <returns>The collection of parameters for this function.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmFunctionBase.ReturnType">
      <summary>Gets the return type of this function.</summary>
      <returns>The return type of this function.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmFunctionImport">
      <summary>Represents an EDM function import.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmFunctionImport.Bindable">
      <summary>Gets a value indicating whether this function import can be used as an extension method for the type of the first parameter of this function import.</summary>
      <returns>True if the function import can be used as an extension method for the type of the first parameter of this function import; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmFunctionImport.Composable">
      <summary>Gets a value indicating whether this function import can be composed inside expressions. <see cref="P:Microsoft.Data.Edm.IEdmFunctionImport.Composable" /> cannot be set to true if <see cref="P:Microsoft.Data.Edm.IEdmFunctionImport.Composable" /> is set to true.</summary>
      <returns>True if the function import can be composed inside expressions; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmFunctionImport.EntitySet">
      <summary>Gets the entity set where the result of this function import will be contained in.</summary>
      <returns>The entity set where the result of this function import will be contained in.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmFunctionImport.SideEffecting">
      <summary>Gets a value indicating whether this function import has side-effects. <see cref="P:Microsoft.Data.Edm.IEdmFunctionImport.SideEffecting" /> cannot be set to true if <see cref="P:Microsoft.Data.Edm.IEdmFunctionImport.SideEffecting" /> is set to true.</summary>
      <returns>True if the function import has side-effects; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmFunctionParameter">
      <summary>Represents a parameter of an EDM function.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmFunctionParameter.DeclaringFunction">
      <summary> Gets the function or function import that declared this parameter. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmFunctionParameter.Mode">
      <summary>Gets the mode of this function parameter.</summary>
      <returns>The mode of this function parameter.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmFunctionParameter.Type">
      <summary>Gets the type of this function parameter.</summary>
      <returns>The type of this function parameter.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmLocatable">
      <summary>Represents the interface for all EDM elements that can be located.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmLocatable.Location">
      <summary>Gets the location of this element.</summary>
      <returns>The location of this element.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmModel">
      <summary>Semantic representation of an EDM model.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmModel.DirectValueAnnotationsManager">
      <summary> Gets the model's annotations manager. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.IEdmModel.FindDeclaredEntityContainer(System.String)">
      <summary> Searches for an entity container with the given name in this model and returns null if no such entity container exists. </summary>
      <returns>The requested entity container, or null if no such entity container exists.</returns>
      <param name="name">The name of the entity container being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.IEdmModel.FindDeclaredFunctions(System.String)">
      <summary> Searches for functions with the given name in this model and returns an empty enumerable if no such function exists. </summary>
      <returns>A set of functions sharing the specified qualified name, or an empty enumerable if no such function exists.</returns>
      <param name="qualifiedName">The qualified name of the function being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.IEdmModel.FindDeclaredType(System.String)">
      <summary> Searches for a type with the given name in this model and returns null if no such type exists. </summary>
      <returns>The requested type, or null if no such type exists.</returns>
      <param name="qualifiedName">The qualified name of the type being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.IEdmModel.FindDeclaredValueTerm(System.String)">
      <summary> Searches for a value term with the given name in this model and returns null if no such value term exists. </summary>
      <returns>The requested value term, or null if no such value term exists.</returns>
      <param name="qualifiedName">The qualified name of the value term being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.IEdmModel.FindDeclaredVocabularyAnnotations(Microsoft.Data.Edm.IEdmVocabularyAnnotatable)">
      <summary> Searches for vocabulary annotations specified by this model. </summary>
      <returns>The vocabulary annotations for the element.</returns>
      <param name="element">The annotated element.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmModel.References">
      <summary>Gets the collection of models referred to by this model.</summary>
      <returns>The collection of models referred to by this model.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmModel.SchemaElements">
      <summary>Gets the collection of schema elements that are contained in this model.</summary>
      <returns>The collection of schema elements that are contained in this model.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmModel.VocabularyAnnotations">
      <summary>Gets the collection of vocabulary annotations that are contained in this model.</summary>
      <returns>The collection of vocabulary annotations.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmNamedElement">
      <summary>Common base interface for all named EDM elements.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmNamedElement.Name">
      <summary>Gets the name of this element.</summary>
      <returns>The name of this element.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmNavigationProperty">
      <summary>Represents an EDM navigation property.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmNavigationProperty.ContainsTarget">
      <summary> Gets a value indicating whether the navigation target is contained inside the navigation source. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmNavigationProperty.DependentProperties">
      <summary> Gets the dependent properties of this navigation property, returning null if this is the principal end or if there is no referential constraint. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmNavigationProperty.IsPrincipal">
      <summary> Gets whether this navigation property originates at the principal end of an association. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmNavigationProperty.OnDelete">
      <summary> Gets the action to execute on the deletion of this end of a bidirectional association. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmNavigationProperty.Partner">
      <summary> Gets the partner of this navigation property. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmPrimitiveType">
      <summary>Represents a definition of an EDM primitive type.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmPrimitiveType.PrimitiveKind">
      <summary>Gets the primitive kind of this type.</summary>
      <returns>The primitive kind of this type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmPrimitiveTypeReference">
      <summary>Represents a reference to definition of an EDM primitive type.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmProperty">
      <summary>Represents an EDM property.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmProperty.DeclaringType">
      <summary>Gets the type that this property belongs to.</summary>
      <returns>The type that this property belongs to.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmProperty.PropertyKind">
      <summary>Gets the kind of this property.</summary>
      <returns>The kind of this property.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmProperty.Type">
      <summary>Gets the type of this property.</summary>
      <returns>The type of this property.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmRowType">
      <summary>Represents an EDM row type.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmRowTypeReference">
      <summary>Represents a reference to an EDM row type.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmSchemaElement">
      <summary>Common base interface for all named children of EDM schemata.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmSchemaElement.Namespace">
      <summary>Gets the namespace this schema element belongs to.</summary>
      <returns>The namespace this schema element belongs to.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmSchemaElement.SchemaElementKind">
      <summary>Gets the kind of this schema element.</summary>
      <returns>The kind of this schema element.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmSchemaType">
      <summary>Represents an EDM schema type.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmSpatialTypeReference">
      <summary>Represents a reference to an EDM spatial type.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmSpatialTypeReference.SpatialReferenceIdentifier">
      <summary>Gets the spatial reference identifier of this spatial type.</summary>
      <returns>The spatial reference identifier of this spatial type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmStringTypeReference">
      <summary>Represents a reference to an EDM string type.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmStringTypeReference.Collation">
      <summary>Gets a string representing the collation of this string type.</summary>
      <returns>A string representing the collation of this string type.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmStringTypeReference.IsFixedLength">
      <summary>Gets a value indicating whether this string type specifies fixed length.</summary>
      <returns>True if this string type specifies fixed length; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmStringTypeReference.IsMaxMaxLength">
      <summary>Gets a value indicating whether this string type specifies the maximum allowed maximum length.</summary>
      <returns>True if this string type specifies the maximum allowed maximum length; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmStringTypeReference.IsUnicode">
      <summary>Gets a value indicating whether this string type supports Unicode encoding.</summary>
      <returns>True if this string type supports Unicode encoding; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmStringTypeReference.MaxLength">
      <summary>Gets the maximum length of this string type.</summary>
      <returns>The maximum length of this string type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmStructuralProperty">
      <summary>Represents an EDM structural (that is, non-navigation) property.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmStructuralProperty.ConcurrencyMode">
      <summary>Gets the concurrency mode of this property.</summary>
      <returns>The concurrency mode of this property.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmStructuralProperty.DefaultValue">
      <summary>Gets the default value of this property.</summary>
      <returns>The default value of this property.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmStructuredType">
      <summary>Represents the common base interface for definitions of EDM structured types.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmStructuredType.BaseType">
      <summary>Gets the base type of this type.</summary>
      <returns>The base type of this type.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmStructuredType.DeclaredProperties">
      <summary>Gets the properties declared immediately within this type.</summary>
      <returns>The properties declared immediately within this type.</returns>
    </member>
    <member name="M:Microsoft.Data.Edm.IEdmStructuredType.FindProperty(System.String)">
      <summary>Searches for a structural or navigation property with the given name in this type and all base types and returns null if no such property exists.</summary>
      <returns>The requested property, or null if no such property exists.</returns>
      <param name="name">The name of the property being found.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmStructuredType.IsAbstract">
      <summary>Gets a value indicating whether this type is abstract.</summary>
      <returns>True if this type is abstract; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmStructuredType.IsOpen">
      <summary>Gets a value indicating whether this type is open.</summary>
      <returns>True if this type is open; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmStructuredTypeReference">
      <summary>Represents a reference to an EDM structured type.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmTemporalTypeReference">
      <summary>Represents a reference to an EDM temporal (Time, DateTime, DateTimeOffset) type.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmTemporalTypeReference.Precision">
      <summary>Gets the precision of this temporal type.</summary>
      <returns>The precision of this temporal type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmTerm">
      <summary>Represents the term to which an annotation can bind.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmTerm.TermKind">
      <summary>Gets the kind of a term.</summary>
      <returns>The kind of a term.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmType">
      <summary>Represents the definition of an EDM type.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmType.TypeKind">
      <summary>Gets the kind of this type.</summary>
      <returns>The kind of this type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmTypeReference">
      <summary>Represents a reference to an EDM type.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmTypeReference.Definition">
      <summary>Gets the definition to which this type refers.</summary>
      <returns>The definition to which this type refers.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmTypeReference.IsNullable">
      <summary>Gets a value indicating whether this type is nullable.</summary>
      <returns>true of the type is nullable; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmValueTerm">
      <summary>Represents an EDM value term.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.IEdmValueTerm.Type">
      <summary>Gets the type of the term.</summary>
      <returns>The type of the term.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.IEdmVocabularyAnnotatable"></member>
    <member name="T:Microsoft.Data.Edm.ToTraceStringExtensionMethods">
      <summary> Contains ToTraceString() extension methods. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.ToTraceStringExtensionMethods.ToTraceString(Microsoft.Data.Edm.IEdmProperty)">
      <summary> Returns the text representation of the current object. </summary>
      <returns>The text representation of the current object.</returns>
      <param name="property">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ToTraceStringExtensionMethods.ToTraceString(Microsoft.Data.Edm.IEdmSchemaElement)">
      <summary> Returns the text representation of the current object. </summary>
      <returns>The text representation of the current object.</returns>
      <param name="schemaElement">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ToTraceStringExtensionMethods.ToTraceString(Microsoft.Data.Edm.IEdmSchemaType)">
      <summary> Returns the text representation of the current object. </summary>
      <returns>The text representation of the current object.</returns>
      <param name="schemaType">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ToTraceStringExtensionMethods.ToTraceString(Microsoft.Data.Edm.IEdmType)">
      <summary> Returns the text representation of the current object. </summary>
      <returns>The text representation of the current object.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.ToTraceStringExtensionMethods.ToTraceString(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Returns the text representation of the current object. </summary>
      <returns>The text representation of the current object.</returns>
      <param name="type">Reference to the calling object.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation">
      <summary> Represents an EDM annotation with an immediate value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation.NamespaceUri">
      <summary> Gets the namespace Uri of the annotation. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation.Value">
      <summary> Gets the value of this annotation. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationBinding">
      <summary> Represents the combination of an EDM annotation with an immediate value and the element to which it is attached. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationBinding.Element">
      <summary> Gets the element to which the annotation is attached </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationBinding.LocalName">
      <summary> Gets the local name of this element. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationBinding.NamespaceUri">
      <summary> Gets the namespace of the annotation. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationBinding.Value">
      <summary> Gets the value of this annotation. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationsManager">
      <summary> Manages getting and setting direct value annotations on EDM elements. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationsManager.GetAnnotationValue(Microsoft.Data.Edm.IEdmElement,System.String,System.String)">
      <summary> Retrieves an annotation value for an EDM element. Returns null if no annotation with the given name exists for the given element. </summary>
      <returns>Returns the annotation value that corresponds to the provided name. Returns null if no annotation with the given name exists for the given element. </returns>
      <param name="element">The annotated element.</param>
      <param name="namespaceName">Namespace that the annotation belongs to.</param>
      <param name="localName">Local name of the annotation.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationsManager.GetAnnotationValues(System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationBinding})">
      <summary> Retrieves a set of annotation values. For each requested value, returns null if no annotation with the given name exists for the given element. </summary>
      <param name="annotations">The set of requested annotations</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationsManager.GetDirectValueAnnotations(Microsoft.Data.Edm.IEdmElement)">
      <summary> Gets annotations associated with an element. </summary>
      <returns>The direct value annotations for the element.</returns>
      <param name="element">The annotated element.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationsManager.SetAnnotationValue(Microsoft.Data.Edm.IEdmElement,System.String,System.String,System.Object)">
      <summary> Sets an annotation value for an EDM element. If the value is null, no annotation is added and an existing annotation with the same name is removed. </summary>
      <param name="element">The annotated element.</param>
      <param name="namespaceName">Namespace that the annotation belongs to.</param>
      <param name="localName">Name of the annotation within the namespace.</param>
      <param name="value">The value of the annotation.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationsManager.SetAnnotationValues(System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationBinding})">
      <summary> Sets a set of annotation values. If a supplied value is null, no annotation is added and an existing annotation with the same name is removed. </summary>
      <param name="annotations">The annotations to set</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Annotations.IEdmPropertyValueBinding">
      <summary>Represents a property binding specified as part of an EDM Type Annotation.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmPropertyValueBinding.BoundProperty">
      <summary>Gets the property given a value by the annotation.</summary>
      <returns>The property given a value by the annotation.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmPropertyValueBinding.Value">
      <summary>Gets the expression producing the value of the annotation.</summary>
      <returns>The expression producing the value of the annotation.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Annotations.IEdmTypeAnnotation">
      <summary>Represents an EDM Type Annotation.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmTypeAnnotation.Properties">
      <summary>Gets the value annotations for the properties of the type.</summary>
      <returns>The value annotations for the properties of the type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Annotations.IEdmValueAnnotation">
      <summary>Represents an EDM Value Annotation.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmValueAnnotation.Value">
      <summary>Gets the expression producing the value of the annotation.</summary>
      <returns>The expression producing the value of the annotation.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Annotations.IEdmVocabularyAnnotation">
      <summary>Represents an EDM Vocabulary Annotation.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmVocabularyAnnotation.Qualifier">
      <summary>Gets the qualifier used to discriminate between multiple bindings of the same property or type.</summary>
      <returns>The qualifier.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmVocabularyAnnotation.Target">
      <summary>Gets the element the annotation applies to.</summary>
      <returns>The element the annotation applies to.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Annotations.IEdmVocabularyAnnotation.Term">
      <summary> Gets the term bound by the annotation. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Csdl.CsdlConstants">
      <summary>Represents the constants for CSDL XML.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Csdl.CsdlConstants.EdmxVersion1">
      <summary>Version 1.0 of EDMX. Corresponds to EDMX namespace "http://schemas.microsoft.com/ado/2007/06/edmx".</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Csdl.CsdlConstants.EdmxVersion2">
      <summary>Version 2.0 of EDMX. Corresponds to EDMX namespace "http://schemas.microsoft.com/ado/2008/10/edmx".</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Csdl.CsdlConstants.EdmxVersion3">
      <summary>Version 3.0 of EDMX. Corresponds to EDMX namespace "http://schemas.microsoft.com/ado/2009/11/edmx".</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Csdl.CsdlConstants.EdmxVersionLatest">
      <summary>The current latest version of EDMX.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Csdl.CsdlLocation">
      <summary>Defines a location in a XML file.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Csdl.CsdlLocation.LineNumber">
      <summary>Gets the line number in the file.</summary>
      <returns>The line number in the file.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Csdl.CsdlLocation.LinePosition">
      <summary>Gets the position in the line.</summary>
      <returns>The position in the line.</returns>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.CsdlLocation.ToString">
      <summary>Gets a string representation of the location.</summary>
      <returns>A string representation of the location.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Csdl.CsdlReader">
      <summary>Provides CSDL parsing services for EDM models.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.CsdlReader.TryParse(System.Collections.Generic.IEnumerable{System.Xml.XmlReader},Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmModel@,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.EdmError}@)">
      <summary>  Returns an IEdmModel for the given CSDL artifacts. </summary>
      <returns>Success of the parse operation.</returns>
      <param name="readers">Collection of XmlReaders containing the CSDL artifacts.</param>
      <param name="reference">Model to be references by the created model.</param>
      <param name="model">The model generated by parsing.</param>
      <param name="errors">Errors reported while parsing.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.CsdlReader.TryParse(System.Collections.Generic.IEnumerable{System.Xml.XmlReader},Microsoft.Data.Edm.IEdmModel@,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.EdmError}@)">
      <summary>  Returns an IEdmModel for the given CSDL artifacts. </summary>
      <returns>Success of the parse operation.</returns>
      <param name="readers">Collection of XmlReaders containing the CSDL artifacts.</param>
      <param name="model">The model generated by parsing.</param>
      <param name="errors">Errors reported while parsing.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.CsdlReader.TryParse(System.Collections.Generic.IEnumerable{System.Xml.XmlReader},System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.IEdmModel},Microsoft.Data.Edm.IEdmModel@,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.EdmError}@)">
      <summary>  Returns an IEdmModel for the given CSDL artifacts. </summary>
      <returns>Success of the parse operation.</returns>
      <param name="readers">Collection of XmlReaders containing the CSDL artifacts.</param>
      <param name="references">Models to be references by the created model.</param>
      <param name="model">The model generated by parsing.</param>
      <param name="errors">Errors reported while parsing.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Csdl.CsdlWriter">
      <summary>Provides CSDL serialization services for EDM models.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.CsdlWriter.TryWriteCsdl(Microsoft.Data.Edm.IEdmModel,System.Func`2,System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Csdl.CsdlWriter.TryWriteCsdl(Microsoft.Data.Edm.IEdmModel,System.Xml.XmlWriter,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.EdmError}@)">
      <summary> Outputs a CSDL artifact to the provided writer. </summary>
      <returns>A value indicating whether serialization was successful.</returns>
      <param name="model">Model to be written.</param>
      <param name="writer">XmlWriter the generated CSDL will be written to.</param>
      <param name="errors">Errors that prevented successful serialization, or no errors if serialization was successful. </param>
    </member>
    <member name="T:Microsoft.Data.Edm.Csdl.EdmVocabularyAnnotationSerializationLocation">
      <summary>Represents whether a vocabulary annotation should be serialized within the element it applies to or in a separate section of the CSDL.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Csdl.EdmVocabularyAnnotationSerializationLocation.Inline">
      <summary>The annotation should be serialized within the element being annotated.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Csdl.EdmVocabularyAnnotationSerializationLocation.OutOfLine">
      <summary>The annotation should be serialized in a separate section.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Csdl.EdmxReader">
      <summary>Provides EDMX parsing services for EDM models.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.EdmxReader.TryParse(System.Xml.XmlReader,Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmModel@,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.EdmError}@)">
      <summary> Returns an IEdmModel for the given EDMX artifact. </summary>
      <returns>Success of the parse operation.</returns>
      <param name="reader">XmlReader containing the EDMX artifact.</param>
      <param name="reference">Model to be referenced by the created model.</param>
      <param name="model">The model generated by parsing</param>
      <param name="errors">Errors reported while parsing.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.EdmxReader.TryParse(System.Xml.XmlReader,Microsoft.Data.Edm.IEdmModel@,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.EdmError}@)">
      <summary>Returns an IEdmModel for the given EDMX artifact.</summary>
      <returns>Success of the parse operation.</returns>
      <param name="reader">XmlReader containing the EDMX artifact.</param>
      <param name="model">The model generated by parsing</param>
      <param name="errors">Errors reported while parsing.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.EdmxReader.TryParse(System.Xml.XmlReader,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.IEdmModel},Microsoft.Data.Edm.IEdmModel@,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.EdmError}@)">
      <summary> Returns an IEdmModel for the given EDMX artifact. </summary>
      <returns>Success of the parse operation.</returns>
      <param name="reader">XmlReader containing the EDMX artifact.</param>
      <param name="references">Models to be references by the created model.</param>
      <param name="model">The model generated by parsing</param>
      <param name="errors">Errors reported while parsing.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Csdl.EdmxTarget">
      <summary>Specifies what target of an EDMX file.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Csdl.EdmxTarget.EntityFramework">
      <summary>The target is Entity Framework.</summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Csdl.EdmxTarget.OData">
      <summary>The target is OData.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Csdl.EdmxWriter">
      <summary>Provides EDMX serialization services for EDM models.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.EdmxWriter.TryWriteEdmx(Microsoft.Data.Edm.IEdmModel,System.Xml.XmlWriter,Microsoft.Data.Edm.Csdl.EdmxTarget,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.EdmError}@)">
      <summary> Outputs an EDMX artifact to the provided XmlWriter. </summary>
      <returns>A value indicating whether serialization was successful.</returns>
      <param name="model">Model to be written.</param>
      <param name="writer">XmlWriter the generated EDMX will be written to.</param>
      <param name="target">Target implementation of the EDMX being generated.</param>
      <param name="errors">Errors that prevented successful serialization, or no errors if serialization was successfull. </param>
    </member>
    <member name="T:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods">
      <summary> Contains extension methods for <see cref="T:Microsoft.Data.Edm.IEdmModel" /> interfaces that are useful to serialization. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetAssociationAnnotations(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmNavigationProperty,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation}@,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation}@,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation}@,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation}@)">
      <summary> Gets the annotations associated with the association serialized for a navigation property. </summary>
      <param name="model">Model containing the navigation property.</param>
      <param name="property">The navigation property.</param>
      <param name="annotations">The association annotations.</param>
      <param name="end1Annotations">The annotations for association end 1.</param>
      <param name="end2Annotations">The annotations for association end 2.</param>
      <param name="constraintAnnotations">The annotations for the referential constraint.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetAssociationEndName(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmNavigationProperty)">
      <summary> Gets the name used for the association end serialized for a navigation property. </summary>
      <returns>The association end name.</returns>
      <param name="model">Model containing the navigation property.</param>
      <param name="property">The navigation property.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetAssociationFullName(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmNavigationProperty)">
      <summary> Gets the fully-qualified name used for the association serialized for a navigation property. </summary>
      <returns>The fully-qualified association name.</returns>
      <param name="model">Model containing the navigation property.</param>
      <param name="property">The navigation property.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetAssociationName(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmNavigationProperty)">
      <summary> Gets the name used for the association serialized for a navigation property. </summary>
      <returns>The association name.</returns>
      <param name="model">Model containing the navigation property.</param>
      <param name="property">The navigation property.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetAssociationNamespace(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmNavigationProperty)">
      <summary> Gets the namespace used for the association serialized for a navigation property. </summary>
      <returns>The association namespace.</returns>
      <param name="model">Model containing the navigation property.</param>
      <param name="property">The navigation property.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetAssociationSetAnnotations(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmEntitySet,Microsoft.Data.Edm.IEdmNavigationProperty,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation}@,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation}@,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation}@)">
      <summary> Gets the annotations associated with the association serialized for a navigation target of an entity set. </summary>
      <param name="model">Model containing the entity set.</param>
      <param name="entitySet">The entity set.</param>
      <param name="property">The navigation property.</param>
      <param name="annotations">The association set annotations.</param>
      <param name="end1Annotations">The annotations for association set end 1.</param>
      <param name="end2Annotations">The annotations for association set end 2.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetAssociationSetName(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmEntitySet,Microsoft.Data.Edm.IEdmNavigationProperty)">
      <summary> Gets the name used for the association set serialized for a navigation property of an entity set. </summary>
      <returns>The association set name.</returns>
      <param name="model">Model containing the entity set.</param>
      <param name="entitySet">The entity set.</param>
      <param name="property">The navigation property.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetDataServiceVersion(Microsoft.Data.Edm.IEdmModel)"></member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetEdmxVersion(Microsoft.Data.Edm.IEdmModel)"></member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetIsSerializedAsElement(Microsoft.Data.Edm.Values.IEdmValue,Microsoft.Data.Edm.IEdmModel)">
      <summary> Gets an annotation indicating if the value should be serialized as an element. </summary>
      <returns>Value indicating if the string should be serialized as an element.</returns>
      <param name="value">Value the annotation is on.</param>
      <param name="model">Model containing the value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetIsValueExplicit(Microsoft.Data.Edm.IEdmEnumMember,Microsoft.Data.Edm.IEdmModel)">
      <summary> Gets an annotation indicating whether the value of an enum member should be explicitly serialized. </summary>
      <returns>Whether the member should have its value serialized.</returns>
      <param name="member">The member the annotation is on.</param>
      <param name="model">Model containing the member.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetMaxDataServiceVersion(Microsoft.Data.Edm.IEdmModel)"></member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetNamespacePrefixMappings(Microsoft.Data.Edm.IEdmModel)"></member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetPrimary(Microsoft.Data.Edm.IEdmNavigationProperty)">
      <summary> Gets the primary end of a pair of partnered navigation properties, selecting the principal end if there is one and making a stable, arbitrary choice otherwise. </summary>
      <returns>The primary end between the navigation property and its partner.</returns>
      <param name="property">The navigation property.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetSchemaNamespace(Microsoft.Data.Edm.Annotations.IEdmVocabularyAnnotation,Microsoft.Data.Edm.IEdmModel)">
      <summary> Gets the schema an annotation should be serialized in. </summary>
      <returns>Name of the schema the annotation belongs to.</returns>
      <param name="annotation">Reference to the calling annotation.</param>
      <param name="model">Model containing the annotation.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.GetSerializationLocation(Microsoft.Data.Edm.Annotations.IEdmVocabularyAnnotation,Microsoft.Data.Edm.IEdmModel)">
      <summary> Gets the location an annotation should be serialized in. </summary>
      <returns>The location the annotation should be serialized at.</returns>
      <param name="annotation">Reference to the calling annotation.</param>
      <param name="model">Model containing the annotation.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetAssociationAnnotations(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmNavigationProperty,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation},System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation},System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation},System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation})">
      <summary> Sets the annotations for the association serialized for a navigation property. </summary>
      <param name="model">Model containing the navigation property.</param>
      <param name="property">The navigation property.</param>
      <param name="annotations">The association annotations.</param>
      <param name="end1Annotations">The annotations for association end 1.</param>
      <param name="end2Annotations">The annotations for association end 2.</param>
      <param name="constraintAnnotations">The annotations for the referential constraint.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetAssociationEndName(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmNavigationProperty,System.String)">
      <summary> Sets the name used for the association end serialized for a navigation property. </summary>
      <param name="model">Model containing the navigation property.</param>
      <param name="property">The navigation property.</param>
      <param name="association">The association end name.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetAssociationName(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmNavigationProperty,System.String)">
      <summary> Sets the name used for the association serialized for a navigation property. </summary>
      <param name="model">Model containing the navigation property.</param>
      <param name="property">The navigation property.</param>
      <param name="associationName">The association name.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetAssociationNamespace(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmNavigationProperty,System.String)">
      <summary> Sets the namespace used for the association serialized for a navigation property. </summary>
      <param name="model">Model containing the navigation property.</param>
      <param name="property">The navigation property.</param>
      <param name="associationNamespace">The association namespace.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetAssociationSetAnnotations(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmEntitySet,Microsoft.Data.Edm.IEdmNavigationProperty,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation},System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation},System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotation})">
      <summary> Sets the annotations for the association set serialized for a navigation target of an entity set. </summary>
      <param name="model">Model containing the entity set.</param>
      <param name="entitySet">The entity set.</param>
      <param name="property">The navigation property.</param>
      <param name="annotations">The association set annotations.</param>
      <param name="end1Annotations">The annotations for association set end 1.</param>
      <param name="end2Annotations">The annotations for association set end 2.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetAssociationSetName(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.IEdmEntitySet,Microsoft.Data.Edm.IEdmNavigationProperty,System.String)">
      <summary> Sets the name used for the association set serialized for a navigation property of an entity set. </summary>
      <param name="model">Model containing the entity set.</param>
      <param name="entitySet">The entity set</param>
      <param name="property">The navigation property.</param>
      <param name="associationSet">The association set name.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetDataServiceVersion(Microsoft.Data.Edm.IEdmModel,System.Version)"></member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetEdmxVersion(Microsoft.Data.Edm.IEdmModel,System.Version)"></member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetIsSerializedAsElement(Microsoft.Data.Edm.Values.IEdmValue,Microsoft.Data.Edm.IEdmModel,System.Nullable{System.Boolean})">
      <summary> Sets an annotation indicating if the value should be serialized as an element. </summary>
      <param name="value">Value to set the annotation on.</param>
      <param name="model">Model containing the value.</param>
      <param name="isSerializedAsElement">Value indicating if the value should be serialized as an element.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetIsValueExplicit(Microsoft.Data.Edm.IEdmEnumMember,Microsoft.Data.Edm.IEdmModel,System.Nullable{System.Boolean})">
      <summary> Sets an annotation indicating whether the value of an enum member should be explicitly serialized. </summary>
      <param name="member">Member to set the annotation on.</param>
      <param name="model">Model containing the member.</param>
      <param name="isExplicit">If the value of the enum member should be explicitly serialized</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetMaxDataServiceVersion(Microsoft.Data.Edm.IEdmModel,System.Version)"></member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetNamespacePrefixMappings(Microsoft.Data.Edm.IEdmModel,System.Xml.XmlNamespaceManager)"></member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetSchemaNamespace(Microsoft.Data.Edm.Annotations.IEdmVocabularyAnnotation,Microsoft.Data.Edm.IEdmModel,System.String)">
      <summary> Sets the schema an annotation should appear in. </summary>
      <param name="annotation">The annotation the schema should be set for.</param>
      <param name="model">Model containing the annotation.</param>
      <param name="schemaNamespace">The schema the annotation belongs in.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Csdl.SerializationExtensionMethods.SetSerializationLocation(Microsoft.Data.Edm.Annotations.IEdmVocabularyAnnotation,Microsoft.Data.Edm.IEdmModel,System.Nullable{Microsoft.Data.Edm.Csdl.EdmVocabularyAnnotationSerializationLocation})">
      <summary> Sets the location an annotation should be serialized in. </summary>
      <param name="annotation">The annotation the location is being specified for.</param>
      <param name="model">Model containing the annotation.</param>
      <param name="location">The location the annotation should appear.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter">
      <summary>
        <see cref="T:Microsoft.Data.Edm.Values.IEdmValue" /> to CLR value converter. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.#ctor">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter" /> class. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.#ctor(Microsoft.Data.Edm.EdmToClrConversion.TryCreateObjectInstance)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter" /> class. </summary>
      <param name="tryCreateObjectInstanceDelegate">The delegate customizing conversion of structured values.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrBoolean(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.Boolean" /> value. </summary>
      <returns>Converted boolean.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmBooleanValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrByte(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.Byte" /> value. </summary>
      <returns>Converted byte.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmIntegerValue" /></exception>
      <exception cref="T:System.OverflowException">edmValue<see cref="T:System.Byte" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrByteArray(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a CLR byte array value. </summary>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmBinaryValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrChar(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.Char" /> value. </summary>
      <returns>Converted char.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmIntegerValue" /></exception>
      <exception cref="T:System.OverflowException">edmValue<see cref="T:System.Char" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrDateTime(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.DateTime" /> value. </summary>
      <returns>Converted DateTime.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmDateTimeValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrDateTimeOffset(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.DateTimeOffset" /> value. </summary>
      <returns>Converted DateTimeOffset.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmDateTimeOffsetValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrDecimal(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.Decimal" /> value. </summary>
      <returns>Converted decimal.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmDecimalValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrDouble(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.Double" /> value. </summary>
      <returns>Converted double.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmFloatingValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrInt16(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.Int16" /> value. </summary>
      <returns>Converted integer.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmIntegerValue" /></exception>
      <exception cref="T:System.OverflowException">edmValue<see cref="T:System.Int16" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrInt32(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.Int32" /> value. </summary>
      <returns>Converted integer.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmIntegerValue" /></exception>
      <exception cref="T:System.OverflowException">edmValue<see cref="T:System.Int32" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrInt64(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.Int64" /> value. </summary>
      <returns>Converted integer.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmIntegerValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrSingle(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.Single" /> value. </summary>
      <returns>Converted single.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmFloatingValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrString(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.String" /> value. </summary>
      <returns>Converted string.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmStringValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrTime(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.TimeSpan" /> value. </summary>
      <returns>Converted Time.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmTimeValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrValue``1(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a CLR value of the specified type. Supported values for &lt;typeparamref name="T" /&gt; are:      CLR primitive types such as <see cref="T:System.String" /> and <see cref="T:System.Int32" />,     CLR enum types,     &lt;see cref="T:System.Collections.Generic.IEnumerable`1" /&gt;,     &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt;,     &lt;see cref="T:System.Collections.Generic.IList`1" /&gt;,     CLR classes with default constructors and public properties with setters and collection properties of the following shapes:     &lt;see cref="T:System.Collections.Generic.IEnumerable`1" /&gt; EnumerableProperty  { get; set; },     &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt; CollectionProperty  { get; set; },     &lt;see cref="T:System.Collections.Generic.IList`1" /&gt; ListProperty  { get; set; },     &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt; CollectionProperty { get { return this.nonNullCollection; } },     &lt;see cref="T:System.Collections.Generic.IList`1" /&gt; ListProperty { get { return this.nonNullList; } }. </summary>
      <param name="edmValue">The EDM value to be converted.</param>
      <typeparam name="T">The CLR type.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsClrValue(Microsoft.Data.Edm.Values.IEdmValue,System.Type)">
      <summary> Converts edmValue to a CLR value of the specified type. Supported values for clrType are:      CLR primitive types such as <see cref="T:System.String" /> and <see cref="T:System.Int32" />,     CLR enum types,     &lt;see cref="T:System.Collections.Generic.IEnumerable`1" /&gt;,     &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt;,     &lt;see cref="T:System.Collections.Generic.IList`1" /&gt;,     CLR classes with default constructors and public properties with setters and collection properties of the following shapes:     &lt;see cref="T:System.Collections.Generic.IEnumerable`1" /&gt; EnumerableProperty  { get; set; },     &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt; CollectionProperty  { get; set; },     &lt;see cref="T:System.Collections.Generic.IList`1" /&gt; ListProperty  { get; set; },     &lt;see cref="T:System.Collections.Generic.ICollection`1" /&gt; CollectionProperty { get { return this.nonNullCollection; } },     &lt;see cref="T:System.Collections.Generic.IList`1" /&gt; ListProperty { get { return this.nonNullList; } }. </summary>
      <returns>A CLR value converted from edmValue.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <param name="clrType">The CLR type.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsNullableClrBoolean(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a nullable <see cref="T:System.Boolean" /> value. </summary>
      <returns>Converted boolean.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmBooleanValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsNullableClrByte(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a nullable <see cref="T:System.Byte" /> value. </summary>
      <returns>Converted byte.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmIntegerValue" /></exception>
      <exception cref="T:System.OverflowException">edmValue<see cref="T:System.Byte" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsNullableClrChar(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.Char" /> value. </summary>
      <returns>Converted char.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmIntegerValue" /></exception>
      <exception cref="T:System.OverflowException">edmValue<see cref="T:System.Char" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsNullableClrDateTime(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a nullable <see cref="T:System.DateTime" /> value. </summary>
      <returns>Converted DateTime.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmDateTimeValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsNullableClrDateTimeOffset(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a nullable <see cref="T:System.DateTimeOffset" /> value. </summary>
      <returns>Converted DateTimeOffset.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmDateTimeOffsetValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsNullableClrDecimal(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a nullable <see cref="T:System.Decimal" /> value. </summary>
      <returns>Converted decimal.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmDecimalValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsNullableClrDouble(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a nullable <see cref="T:System.Double" /> value. </summary>
      <returns>Converted double.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmFloatingValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsNullableClrInt16(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a nullable <see cref="T:System.Int16" /> value. </summary>
      <returns>Converted integer.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmIntegerValue" /></exception>
      <exception cref="T:System.OverflowException">edmValue<see cref="T:System.Int16" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsNullableClrInt32(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a <see cref="T:System.Int32" /> value. </summary>
      <returns>Converted integer.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmIntegerValue" /></exception>
      <exception cref="T:System.OverflowException">edmValue<see cref="T:System.Int32" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsNullableClrInt64(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a nullable <see cref="T:System.Int64" /> value. </summary>
      <returns>Converted integer.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmIntegerValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsNullableClrSingle(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a nullable <see cref="T:System.Single" /> value. </summary>
      <returns>Converted single.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmFloatingValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.AsNullableClrTime(Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Converts edmValue to a nullable <see cref="T:System.TimeSpan" /> value. </summary>
      <returns>Converted Tim.</returns>
      <param name="edmValue">The EDM value to be converted.</param>
      <exception cref="T:System.InvalidCastException">edmValue<see cref="T:Microsoft.Data.Edm.Values.IEdmTimeValue" /></exception>
    </member>
    <member name="M:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter.RegisterConvertedObject(Microsoft.Data.Edm.Values.IEdmStructuredValue,System.Object)">
      <summary> Registers the clrObject corresponding to the edmValue. All subsequent conversions from this edmValue performed by this instance of <see cref="T:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter" /> will return the specified clrObject. Registration is required to support graph consistency and loops during conversion process.  This method should be called inside the <see cref="T:Microsoft.Data.Edm.EdmToClrConversion.TryCreateObjectInstance" /> delegate if the delegate is calling back into <see cref="T:Microsoft.Data.Edm.EdmToClrConversion.EdmToClrConverter" /> in order to populate properties of the clrObject. </summary>
      <param name="edmValue">The EDM value.</param>
      <param name="clrObject">The CLR object.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.EdmToClrConversion.TryCreateObjectInstance"></member>
    <member name="T:Microsoft.Data.Edm.Evaluation.EdmEvaluator">
      <summary> Expression evaluator. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Evaluation.EdmEvaluator.#ctor(System.Collections.Generic.IDictionary{Microsoft.Data.Edm.IEdmFunction,System.Func`2})"></member>
    <member name="M:Microsoft.Data.Edm.Evaluation.EdmEvaluator.#ctor(System.Collections.Generic.IDictionary{Microsoft.Data.Edm.IEdmFunction,System.Func`2},System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Evaluation.EdmEvaluator.Evaluate(Microsoft.Data.Edm.Expressions.IEdmExpression)">
      <summary> Evaluates an expression with no value context. </summary>
      <returns>The value that results from evaluating the expression in the context of the supplied value.</returns>
      <param name="expression">Expression to evaluate. The expression must not contain paths, because no context for evaluating a path is supplied.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Evaluation.EdmEvaluator.Evaluate(Microsoft.Data.Edm.Expressions.IEdmExpression,Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Evaluates an expression in the context of a value. </summary>
      <returns>The value that results from evaluating the expression in the context of the supplied value.</returns>
      <param name="expression">Expression to evaluate.</param>
      <param name="context">Value to use as context in evaluating the expression.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Evaluation.EdmEvaluator.Evaluate(Microsoft.Data.Edm.Expressions.IEdmExpression,Microsoft.Data.Edm.Values.IEdmValue,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Evaluates an expression in the context of a value and a target type. </summary>
      <returns>The value that results from evaluating the expression in the context of the supplied value, asserted to be of the target type.</returns>
      <param name="expression">Expression to evaluate.</param>
      <param name="context">Value to use as context in evaluating the expression. Cannot be null if the expression contains paths.</param>
      <param name="targetType">Type to which the result value is expected to conform.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator">
      <summary> Expression evaluator capable of producing CLR values. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator.#ctor(System.Collections.Generic.IDictionary{Microsoft.Data.Edm.IEdmFunction,System.Func`2})"></member>
    <member name="M:Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator.#ctor(System.Collections.Generic.IDictionary{Microsoft.Data.Edm.IEdmFunction,System.Func`2},System.Boolean)"></member>
    <member name="P:Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator.EdmToClrConverter">
      <summary> Gets or sets an instance of <see cref="P:Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator.EdmToClrConverter" /> that is used to produce CLR values during evaluation. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator.EvaluateToClrValue``1(Microsoft.Data.Edm.Expressions.IEdmExpression)">
      <summary> Evaluates an expression with no value context. </summary>
      <param name="expression">Expression to evaluate. The expression must not contain paths, because no context for evaluating a path is supplied.</param>
      <typeparam name="T">The CLR type of the value to be returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator.EvaluateToClrValue``1(Microsoft.Data.Edm.Expressions.IEdmExpression,Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Evaluates an expression in the context of a value. </summary>
      <param name="expression">Expression to evaluate.</param>
      <param name="context">Value to use as context in evaluating the expression.</param>
      <typeparam name="T">The CLR type of the value to be returned.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.Evaluation.EdmToClrEvaluator.EvaluateToClrValue``1(Microsoft.Data.Edm.Expressions.IEdmExpression,Microsoft.Data.Edm.Values.IEdmValue,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Evaluates an expression in the context of a value and a target type. </summary>
      <param name="expression">Expression to evaluate.</param>
      <param name="context">Value to use as context in evaluating the expression.</param>
      <param name="targetType">Type to which the result value is expected to conform.</param>
      <typeparam name="T">The CLR type of the value to be returned.</typeparam>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.EdmExpressionKind">
      <summary> Defines EDM expression kinds. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.None">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.BinaryConstant">
      <summary> Represents an expression implementing <see cref="T:Microsoft.Data.Edm.Expressions.IEdmBinaryConstantExpression" />. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.BooleanConstant">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.DateTimeConstant">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.DateTimeOffsetConstant">
      <summary> Represents an expression implementing <see cref="T:Microsoft.Data.Edm.Expressions.IEdmDateTimeOffsetConstantExpression" />. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.DecimalConstant">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.FloatingConstant">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.GuidConstant">
      <summary> Represents an expression implementing <see cref="T:Microsoft.Data.Edm.Expressions.IEdmGuidConstantExpression" />. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.IntegerConstant">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.StringConstant">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.TimeConstant">
      <summary> Represents an expression implementing <see cref="T:Microsoft.Data.Edm.Expressions.IEdmTimeConstantExpression" />. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.Null">
      <summary> Represents an expression implementing <see cref="T:Microsoft.Data.Edm.Expressions.IEdmNullExpression" />. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.Record">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.Collection">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.Path">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.ParameterReference">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.FunctionReference">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.PropertyReference">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.TermPropertyReference">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.EntitySetReference">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.EnumMemberReference">
      <summary> Represents an expression implementing <see cref="T:Microsoft.Data.Edm.Expressions.IEdmEnumMemberReferenceExpression" />. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.If">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.AssertType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.IsType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.FunctionApplication">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.LabeledExpressionReference">
      <summary> Represents an expression implementing <see cref="T:Microsoft.Data.Edm.Expressions.IEdmLabeledExpressionReferenceExpression" />. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Expressions.EdmExpressionKind.Labeled">
      <summary> Represents an expression implementing <see cref="T:Microsoft.Data.Edm.Expressions.IEdmLabeledExpression" /></summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmAssertTypeExpression">
      <summary>Represents an EDM type assertion expression.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmAssertTypeExpression.Operand">
      <summary>Gets the expression for which the type is asserted.</summary>
      <returns>The expression for which the type is asserted.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmAssertTypeExpression.Type">
      <summary>Gets the asserted type.</summary>
      <returns>The asserted type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmBinaryConstantExpression"></member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmBooleanConstantExpression">
      <summary>Represents an EDM boolean constant expression.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmCollectionExpression">
      <summary>Represents an EDM multi-value construction expression.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmCollectionExpression.DeclaredType">
      <summary> Gets the declared type of the collection, or null if there is no declared type. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmCollectionExpression.Elements">
      <summary>Gets the constructed element values.</summary>
      <returns>The constructed element values.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmDateTimeConstantExpression">
      <summary>Represents an EDM DateTime constant expression.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmDateTimeOffsetConstantExpression"></member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmDecimalConstantExpression">
      <summary>Represents an EDM decimal constant expression.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmEntitySetReferenceExpression">
      <summary>Represents an EDM entity set reference expression.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmEntitySetReferenceExpression.Referenced">
      <summary>Gets the referenced entity set.</summary>
      <returns>The referenced entity set.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmEnumMemberReferenceExpression">
      <summary> Represents an EDM enumeration member reference expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmEnumMemberReferenceExpression.Referenced">
      <summary> Gets the referenced enum member. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmExpression">
      <summary>Represents an EDM expression.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmExpression.ExpressionKind">
      <summary>Gets the kind of this expression.</summary>
      <returns>The kind of this expression.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmFloatingConstantExpression">
      <summary> Represents an EDM floating constant expression. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmFunctionApplicationExpression">
      <summary> Represents an EDM function application expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmFunctionApplicationExpression.AppliedFunction">
      <summary> Gets the applied function. </summary>
      <returns>The applied function.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmFunctionApplicationExpression.Arguments">
      <summary> Gets the arguments to the function. </summary>
      <returns>The arguments to the function.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmFunctionReferenceExpression">
      <summary> Represents an EDM function reference expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmFunctionReferenceExpression.Referenced">
      <summary> Gets the referenced function. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmGuidConstantExpression"></member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmIfExpression">
      <summary> Represents an EDM if expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmIfExpression.IfFalse">
      <summary> Gets the expression to evaluate if Test evaluates to False. </summary>
      <returns>The expression to evaluate if Test evaluates to False.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmIfExpression.IfTrue">
      <summary> Gets the expression to evaluate if Test evaluates to True. </summary>
      <returns>The expression to evaluate if Test evaluates to True.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmIfExpression.Test">
      <summary> Gets the test expression. </summary>
      <returns>The test expression.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmIntegerConstantExpression">
      <summary> Represents an EDM integer constant expression. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmIsTypeExpression">
      <summary> Represents an EDM type test expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmIsTypeExpression.Operand">
      <summary> Gets the expression whose type is to be tested. </summary>
      <returns>The expression whose type is to be tested.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmIsTypeExpression.Type">
      <summary> Gets the type to be tested against. </summary>
      <returns>The type to be tested against.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmLabeledExpression">
      <summary> Represents an EDM labeled expression element. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmLabeledExpression.Expression">
      <summary> Gets the underlying expression. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmLabeledExpressionReferenceExpression">
      <summary> Represents a reference to an EDM labeled expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmLabeledExpressionReferenceExpression.Referenced">
      <summary> Gets the referenced expression. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmNullExpression"></member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmParameterReferenceExpression">
      <summary> Represents an EDM parameter reference expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmParameterReferenceExpression.Referenced">
      <summary> Gets the referenced parameter. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmPathExpression">
      <summary> Represents an EDM path expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmPathExpression.Path">
      <summary> Gets the path as a decomposed qualified name. "A.B.C" is { "A", "B", "C" }. </summary>
      <returns>The path as a decomposed qualified name.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmPropertyConstructor">
      <summary> Represents an EDM property constructor specified as part of a CSDL Record expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmPropertyConstructor.Name">
      <summary> Gets the name of the property </summary>
      <returns>The name of the property.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmPropertyConstructor.Value">
      <summary> Gets the expression for the value of the property. </summary>
      <returns>The expression for the value of the property.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmPropertyReferenceExpression">
      <summary> Represents an EDM property reference expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmPropertyReferenceExpression.Base"></member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmPropertyReferenceExpression.Referenced">
      <summary> Gets the referenced property. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmRecordExpression">
      <summary> Represents an EDM record construction expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmRecordExpression.DeclaredType">
      <summary> Gets the declared type of the record, or null if there is no declared type. </summary>
      <returns>The declared type of the record, or null if there is no declared type.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmRecordExpression.Properties">
      <summary> Gets the constructed property values. </summary>
      <returns>The constructed property values.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmStringConstantExpression">
      <summary> Represents an EDM string constant expression. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmTimeConstantExpression"></member>
    <member name="T:Microsoft.Data.Edm.Expressions.IEdmValueTermReferenceExpression">
      <summary> Represents an EDM value term reference expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmValueTermReferenceExpression.Base">
      <summary> Gets the expression for the structured value containing the referenced term property. </summary>
      <returns>The expression for the structured value containing the referenced term property.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmValueTermReferenceExpression.Qualifier">
      <summary> Gets the optional qualifier. </summary>
      <returns>The optional qualifier.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Expressions.IEdmValueTermReferenceExpression.Term">
      <summary> Gets the referenced value term. </summary>
      <returns>The referenced value term.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmBinaryTypeReference">
      <summary> Represents a reference to an EDM binary type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmBinaryTypeReference.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType,System.Boolean)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmBinaryTypeReference" /> class. </summary>
      <param name="definition">The type this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmBinaryTypeReference.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType,System.Boolean,System.Boolean,System.Nullable{System.Int32},System.Nullable{System.Boolean})">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmBinaryTypeReference" /> class. </summary>
      <param name="definition">The type this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
      <param name="isMaxMaxLength">Denotes whether the maximum length is the maximum allowed value.</param>
      <param name="maxLength">The maximum length of a value of this type.</param>
      <param name="isFixedLength">Denotes whether the length can vary. </param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmBinaryTypeReference.IsFixedLength">
      <summary> Gets a value indicating whether this type specifies fixed length. </summary>
      <returns>true if this type specifies fixed length; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmBinaryTypeReference.IsMaxMaxLength">
      <summary> Gets a value indicating whether this type specifies the maximum allowed max length. </summary>
      <returns>true if this type specifies the maximum allowed maximum length; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmBinaryTypeReference.MaxLength">
      <summary> Gets the maximum length of this type. </summary>
      <returns>The maximum length of this type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmCollectionType">
      <summary> Represents a definition of an EDM collection type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCollectionType.#ctor(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Initializes a new instance of the EdmCollectionType class. </summary>
      <param name="elementType">The type of the elements in this collection.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmCollectionType.ElementType"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmCollectionType.TypeKind">
      <summary> Gets the kind of this type. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmCollectionTypeReference">
      <summary> Represents a reference to an EDM collection type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCollectionTypeReference.#ctor(Microsoft.Data.Edm.IEdmCollectionType,System.Boolean)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmCollectionTypeReference" /> class. </summary>
      <param name="collectionType">The type definition this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmCollectionTypeReference.CollectionDefinition">
      <summary> Gets the collection type to which this type refers. </summary>
      <returns>The collection type to which this type refers.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmComplexType">
      <summary> Represents a definition of an EDM complex type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmComplexType.#ctor(System.String,System.String)">
      <summary> Initializes a new instance of the EdmComplexType class. </summary>
      <param name="namespaceName">The namespace this type belongs to.</param>
      <param name="name">The name of this type within its namespace.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmComplexType.#ctor(System.String,System.String,Microsoft.Data.Edm.IEdmComplexType,System.Boolean,System.Boolean)">
      <summary> Initializes a new instance of the EdmComplexType class. Note: Complex type inheritance is not supported in EDM version 3.0 and above. </summary>
      <param name="namespaceName">The namespace this type belongs to.</param>
      <param name="name">The name of this type within its namespace.</param>
      <param name="baseType">The base type of this complex type.</param>
      <param name="isAbstract">Denotes whether this complex type is abstract.</param>
      <param name="isOpen">Denotes whether this type is open.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmComplexType.#ctor(System.String,System.String,System.Boolean)">
      <summary> Initializes a new instance of the EdmComplexType class. </summary>
      <param name="namespaceName">The namespace this type belongs to.</param>
      <param name="name">The name of this type within its namespace.</param>
      <param name="isOpen">Denotes whether this type is open.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmComplexType.Name"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmComplexType.Namespace"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmComplexType.SchemaElementKind"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmComplexType.TypeKind">
      <summary> Gets the kind of this type. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmComplexTypeReference">
      <summary> Represents a reference to an EDM complex type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmComplexTypeReference.#ctor(Microsoft.Data.Edm.IEdmComplexType,System.Boolean)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmComplexTypeReference" /> class. </summary>
      <param name="complexType">The type definition this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmConstants">
      <summary> Contains constant values that apply to the EDM model, regardless of source (for CSDL/EDMX specific constants see <see cref="T:Microsoft.Data.Edm.Csdl.CsdlConstants" />). </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Library.EdmConstants.EdmVersion1">
      <summary> Version 1.0 of EDM. Corresponds to CSDL namespace "http://schemas.microsoft.com/ado/2006/04/edm". </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Library.EdmConstants.EdmVersion1_1">
      <summary> Version 1.1 of EDM. Corresponds to CSDL namespace "http://schemas.microsoft.com/ado/2007/05/edm". </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Library.EdmConstants.EdmVersion1_2">
      <summary> Version 1.2 of EDM. Corresponds to CSDL namespace "http://schemas.microsoft.com/ado/2008/01/edm". </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Library.EdmConstants.EdmVersion2">
      <summary> Version 2.0 of EDM. Corresponds to CSDL namespaces "http://schemas.microsoft.com/ado/2008/09/edm" and "http://schemas.microsoft.com/ado/2009/08/edm". </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Library.EdmConstants.EdmVersion3">
      <summary> Version 3.0 of EDM. Corresponds to CSDL namespace "http://schemas.microsoft.com/ado/2009/11/edm". </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Library.EdmConstants.EdmVersionLatest">
      <summary> The current latest version of EDM. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmCoreModel">
      <summary> Provides predefined declarations relevant to EDM semantics. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmCoreModel.DirectValueAnnotationsManager">
      <summary> Gets the model's annotations manager. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.FindDeclaredEntityContainer(System.String)">
      <summary> Searches for an entity container with the given name in this model and returns null if no such entity container exists. </summary>
      <returns>The requested entity container, or null if no such entity container exists.</returns>
      <param name="name">The name of the entity container being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.FindDeclaredFunctions(System.String)">
      <summary> Searches for functions with the given name in this model and returns an empty enumerable if no such function exists. </summary>
      <returns>A set functions sharing the specified qualified name, or an empty enumerable if no such function exists.</returns>
      <param name="qualifiedName">The qualified name of the function being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.FindDeclaredType(System.String)">
      <summary> Searches for a type with the given name in this model and returns null if no such type exists. </summary>
      <returns>The requested type, or null if no such type exists.</returns>
      <param name="qualifiedName">The qualified name of the type being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.FindDeclaredValueTerm(System.String)">
      <summary> Searches for a value term with the given name in this model and returns null if no such value term exists. </summary>
      <returns>The requested value term, or null if no such value term exists.</returns>
      <param name="qualifiedName">The qualified name of the value term being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.FindDeclaredVocabularyAnnotations(Microsoft.Data.Edm.IEdmVocabularyAnnotatable)">
      <summary> Searches for vocabulary annotations specified by this model or a referenced model for a given element. </summary>
      <returns>The vocabulary annotations for the element.</returns>
      <param name="element">The annotated element.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetBinary(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetBinary(System.Boolean,System.Nullable{System.Int32},System.Nullable{System.Boolean},System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetBoolean(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetByte(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetCollection(Microsoft.Data.Edm.IEdmTypeReference)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetDateTime(System.Boolean)">
      <summary> Gets a reference to a datetime primitive type definition. </summary>
      <returns>A new datetime type reference.</returns>
      <param name="isNullable">Flag specifying if the referenced type should be nullable.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetDateTimeOffset(System.Boolean)">
      <summary> Gets a reference to a datetime with offset primitive type definition. </summary>
      <returns>A new datetime with offset type reference.</returns>
      <param name="isNullable">Flag specifying if the referenced type should be nullable.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetDecimal(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetDecimal(System.Nullable{System.Int32},System.Nullable{System.Int32},System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetDouble(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetGuid(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetInt16(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetInt32(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetInt64(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetPrimitive(Microsoft.Data.Edm.EdmPrimitiveTypeKind,System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetPrimitiveType(Microsoft.Data.Edm.EdmPrimitiveTypeKind)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetPrimitiveTypeKind(System.String)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetSByte(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetSingle(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetSpatial(Microsoft.Data.Edm.EdmPrimitiveTypeKind,System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetSpatial(Microsoft.Data.Edm.EdmPrimitiveTypeKind,System.Nullable{System.Int32},System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetStream(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetString(System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetString(System.Boolean,System.Nullable{System.Int32},System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.String,System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetTemporalType(Microsoft.Data.Edm.EdmPrimitiveTypeKind,System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetTemporalType(Microsoft.Data.Edm.EdmPrimitiveTypeKind,System.Nullable{System.Int32},System.Boolean)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmCoreModel.GetTime(System.Boolean)">
      <summary> Gets a reference to a time primitive type definition. </summary>
      <returns>A new time type reference.</returns>
      <param name="isNullable">Flag specifying if the referenced type should be nullable.</param>
    </member>
    <member name="F:Microsoft.Data.Edm.Library.EdmCoreModel.Instance"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmCoreModel.Namespace"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmCoreModel.References"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmCoreModel.SchemaElements"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmCoreModel.VocabularyAnnotations"></member>
    <member name="T:Microsoft.Data.Edm.Library.EdmDecimalTypeReference">
      <summary>Represents a reference to an EDM decimal type.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmDecimalTypeReference.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType,System.Boolean)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmDecimalTypeReference" /> class.</summary>
      <param name="definition">The type this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmDecimalTypeReference.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType,System.Boolean,System.Nullable{System.Int32},System.Nullable{System.Int32})">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmDecimalTypeReference" /> class.</summary>
      <param name="definition">The type this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
      <param name="precision">The precision of values with this type.</param>
      <param name="scale">The scale of values with this type.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmDecimalTypeReference.Precision">
      <summary>Gets the precision of this type.</summary>
      <returns>The precision of this type.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmDecimalTypeReference.Scale">
      <summary>Gets the scale of this type.</summary>
      <returns>The scale of this type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmDocumentation">
      <summary>Represents an EDM documentation.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmDocumentation.#ctor">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmDocumentation" /> class.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmDocumentation.#ctor(System.String,System.String)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmDocumentation" /> class.</summary>
      <param name="summary">A summary of the documentation.</param>
      <param name="longDescription">A long description of the documentation.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmDocumentation.LongDescription">
      <summary>Gets or sets a long description of this documentation.</summary>
      <returns>A long description of this documentation.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmDocumentation.Summary">
      <summary>Gets or sets a summary of this documentation.</summary>
      <returns>A summary of this documentation.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmElement">
      <summary>Represents the common base class for all EDM elements.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmElement" /> class.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmEntityContainer">
      <summary>Represents an EDM entity container.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityContainer.#ctor(System.String,System.String)">
      <summary> Initializes a new instance of the EdmEntityContainer class. </summary>
      <param name="namespaceName">Namespace of the entity container.</param>
      <param name="name">Name of the entity container.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityContainer.AddElement(Microsoft.Data.Edm.IEdmEntityContainerElement)">
      <summary>Adds an entity container element to this entity container.</summary>
      <param name="element">The element to add.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityContainer.AddEntitySet(System.String,Microsoft.Data.Edm.IEdmEntityType)">
      <summary> Creates and adds an entity set to this entity container. </summary>
      <returns>Created entity set.</returns>
      <param name="name">Name of the entity set.</param>
      <param name="elementType">The entity type of the elements in this entity set.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityContainer.AddFunctionImport(System.String,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Creates and adds a function import to this entity container. </summary>
      <returns>Created function import.</returns>
      <param name="name">Name of the function import.</param>
      <param name="returnType">Return type of the function import.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityContainer.AddFunctionImport(System.String,Microsoft.Data.Edm.IEdmTypeReference,Microsoft.Data.Edm.Expressions.IEdmExpression)">
      <summary> Creates and adds a function import to this entity container. </summary>
      <returns>Created function import.</returns>
      <param name="name">Name of the function import.</param>
      <param name="returnType">Return type of the function import.</param>
      <param name="entitySet">An entity set containing entities returned by this function import.  The two expression kinds supported are <see cref="T:Microsoft.Data.Edm.Expressions.IEdmEntitySetReferenceExpression" /> and <see cref="T:Microsoft.Data.Edm.Expressions.IEdmPathExpression" />.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityContainer.AddFunctionImport(System.String,Microsoft.Data.Edm.IEdmTypeReference,Microsoft.Data.Edm.Expressions.IEdmExpression,System.Boolean,System.Boolean,System.Boolean)">
      <summary> Creates and adds a function import to this entity container. </summary>
      <returns>Created function import.</returns>
      <param name="name">Name of the function import.</param>
      <param name="returnType">Return type of the function import.</param>
      <param name="entitySet">An entity set containing entities returned by this function import.  The two expression kinds supported are <see cref="T:Microsoft.Data.Edm.Expressions.IEdmEntitySetReferenceExpression" /> and <see cref="T:Microsoft.Data.Edm.Expressions.IEdmPathExpression" />.</param>
      <param name="sideEffecting">A value indicating whether this function import has side-effects.</param>
      <param name="composable">A value indicating whether this functon import can be composed inside expressions.</param>
      <param name="bindable">A value indicating whether this function import can be used as an extension method for the type of the first parameter of this function import.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityContainer.Elements">
      <summary>Gets a collection of the elements of this entity container.</summary>
      <returns>A collection of the elements of this entity container.</returns>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityContainer.FindEntitySet(System.String)">
      <summary>Searches for an entity set with the given name in this entity container and returns null if no such set exists.</summary>
      <returns>The requested element, or null if the element does not exist.</returns>
      <param name="setName">The name of the element being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityContainer.FindFunctionImports(System.String)">
      <summary>Searches for function imports with the given name in this entity container and returns null if no such function import exists.</summary>
      <returns>A group of the requested function imports, or null if no such function import exists.</returns>
      <param name="functionName">The name of the function import being found.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityContainer.Name">
      <summary>Gets or sets the name of this entity container.</summary>
      <returns>The name of this entity container.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityContainer.Namespace">
      <summary> Gets the namespace of this entity container. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityContainer.SchemaElementKind">
      <summary> Gets the kind of this schema element. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmEntityReferenceType">
      <summary> Represents a definition of an EDM entity reference type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityReferenceType.#ctor(Microsoft.Data.Edm.IEdmEntityType)"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityReferenceType.EntityType"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityReferenceType.TypeKind">
      <summary> Gets the kind of this type. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmEntityReferenceTypeReference">
      <summary>Represents a reference to an EDM entity reference type.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityReferenceTypeReference.#ctor(Microsoft.Data.Edm.IEdmEntityReferenceType,System.Boolean)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmEntityReferenceTypeReference" /> class.</summary>
      <param name="entityReferenceType">The definition referred to by this reference.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityReferenceTypeReference.EntityReferenceDefinition">
      <summary>Gets the entity reference definition to which this type refers.</summary>
      <returns>The entity reference definition to which this type refers.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmEntitySet">
      <summary> Represents an EDM entity set. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntitySet.#ctor(Microsoft.Data.Edm.IEdmEntityContainer,System.String,Microsoft.Data.Edm.IEdmEntityType)">
      <summary> Initializes a new instance of the EdmEntitySet class. </summary>
      <param name="container">An <see cref="T:Microsoft.Data.Edm.IEdmEntityContainer" /> containing this entity set.</param>
      <param name="name">Name of the entity set.</param>
      <param name="elementType">The entity type of the elements in this entity set.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntitySet.AddNavigationTarget(Microsoft.Data.Edm.IEdmNavigationProperty,Microsoft.Data.Edm.IEdmEntitySet)">
      <summary> Adds a navigation target, specifying the destination entity set of a navigation property of an entity in this entity set. </summary>
      <param name="property">The navigation property the target is being set for.</param>
      <param name="target">The destination entity set of the specified navigation property.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntitySet.Container">
      <summary> Gets or sets the container of this entity set. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntitySet.ContainerElementKind"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntitySet.ElementType"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntitySet.FindNavigationTarget(Microsoft.Data.Edm.IEdmNavigationProperty)">
      <summary> Finds the entity set that a navigation property targets. </summary>
      <returns>The entity set that the navigation propertion targets, or null if no such entity set exists.</returns>
      <param name="property">The navigation property.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntitySet.Name"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntitySet.NavigationTargets">
      <summary> Gets the navigation targets of this entity set. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmEntityType">
      <summary> Represents a definition of an EDM entity type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityType.#ctor(System.String,System.String)">
      <summary> Initializes a new instance of the EdmEntityType class. </summary>
      <param name="namespaceName">Namespace the entity belongs to.</param>
      <param name="name">Name of the entity.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityType.#ctor(System.String,System.String,Microsoft.Data.Edm.IEdmEntityType)">
      <summary> Initializes a new instance of the EdmEntityType class. </summary>
      <param name="namespaceName">Namespace the entity belongs to.</param>
      <param name="name">Name of the entity.</param>
      <param name="baseType">The base type of this entity type.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityType.#ctor(System.String,System.String,Microsoft.Data.Edm.IEdmEntityType,System.Boolean,System.Boolean)">
      <summary> Initializes a new instance of the EdmEntityType class. </summary>
      <param name="namespaceName">Namespace the entity belongs to.</param>
      <param name="name">Name of the entity.</param>
      <param name="baseType">The base type of this entity type.</param>
      <param name="isAbstract">Denotes an entity that cannot be instantiated.</param>
      <param name="isOpen">Denotes if the type is open.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityType.AddKeys(Microsoft.Data.Edm.IEdmStructuralProperty[])">
      <summary> Adds the keyProperties to the key of this entity type. </summary>
      <param name="keyProperties">The key properties.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityType.AddKeys(System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.IEdmStructuralProperty})">
      <summary> Adds the keyProperties to the key of this entity type. </summary>
      <param name="keyProperties">The key properties.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityType.AddNavigation(System.String,Microsoft.Data.Edm.Library.EdmEntityType,Microsoft.Data.Edm.EdmMultiplicity)">
      <summary> Creates and adds a one-side navigation property to this type. Navigation property partner is created, but not added to navigationTargetType. </summary>
      <returns>Created navigation property.</returns>
      <param name="propertyName">Name of the navigation property.</param>
      <param name="navigationTargetType">Type that this navigation property points to.</param>
      <param name="navigationTargetMultiplicity">Multiplicity of the navigation target.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityType.AddNavigation(System.String,Microsoft.Data.Edm.Library.EdmEntityType,Microsoft.Data.Edm.EdmMultiplicity,Microsoft.Data.Edm.EdmMultiplicity)">
      <summary> Creates and adds a one-side navigation property to this type. Navigation property partner is created, but not added to navigationTargetType. </summary>
      <returns>Created navigation property.</returns>
      <param name="propertyName">Name of the navigation property.</param>
      <param name="navigationTargetType">Type that this navigation property points to.</param>
      <param name="navigationTargetMultiplicity">Multiplicity of the navigation target.</param>
      <param name="navigationSourceMultiplicity">Multiplicity of the navigation source.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityType.AddNavigation(System.String,Microsoft.Data.Edm.Library.EdmEntityType,Microsoft.Data.Edm.EdmMultiplicity,System.Boolean,Microsoft.Data.Edm.EdmOnDeleteAction,System.String,Microsoft.Data.Edm.EdmMultiplicity,System.Boolean,Microsoft.Data.Edm.EdmOnDeleteAction)">
      <summary> Creates and adds a navigation property to this type and adds its navigation partner to navigationTargetType. </summary>
      <returns>Created navigation property.</returns>
      <param name="propertyName">Name of the navigation property.</param>
      <param name="navigationTargetType">Type that this navigation property points to.</param>
      <param name="navigationTargetMultiplicity">Multiplicity of the navigation target.</param>
      <param name="containsTarget">A value indicating whether the the navigation target is contained inside the navigation source.</param>
      <param name="onDelete">An action to take when an instance of the navigation source type is deleted.</param>
      <param name="partnerPropertyName">Name of the partner property.</param>
      <param name="navigationSourceMultiplicity">Multiplicity of the navigation source.</param>
      <param name="partnerContainsTarget">A value indicating whether the the navigation source is contained inside the navigation target.</param>
      <param name="partnerOnDelete">An action to take when an instance of the navigation target type is deleted.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityType.AddNavigation(System.String,Microsoft.Data.Edm.Library.EdmEntityType,Microsoft.Data.Edm.EdmMultiplicity,System.String,Microsoft.Data.Edm.EdmMultiplicity)">
      <summary> Creates and adds a navigation property to this type and adds its navigation partner to navigationTargetType. </summary>
      <returns>Created navigation property.</returns>
      <param name="propertyName">Name of the navigation property.</param>
      <param name="navigationTargetType">Type that this navigation property points to.</param>
      <param name="navigationTargetMultiplicity">Multiplicity of the navigation target.</param>
      <param name="partnerPropertyName">Name of the partner property.</param>
      <param name="navigationSourceMultiplicity">Multiplicity of the navigation source.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityType.DeclaredKey"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityType.Name"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityType.Namespace"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityType.SchemaElementKind"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityType.TermKind"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEntityType.TypeKind">
      <summary> Gets the kind of this type. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmEntityTypeReference">
      <summary>Represents a reference to an EDM entity type.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEntityTypeReference.#ctor(Microsoft.Data.Edm.IEdmEntityType,System.Boolean)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmEntityTypeReference" /> class.</summary>
      <param name="entityType">The definition refered to by this reference.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmEnumMember">
      <summary> Represents a member of an EDM enumeration type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEnumMember.#ctor(Microsoft.Data.Edm.IEdmEnumType,System.String,Microsoft.Data.Edm.Values.IEdmPrimitiveValue)">
      <summary> Initializes a new instance of the EdmEnumMember class. </summary>
      <param name="declaringType">The type that declares this member.</param>
      <param name="name">Name of this enumeration member.</param>
      <param name="value">Value of this enumeration member.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEnumMember.DeclaringType">
      <summary> Gets the type that this member belongs to. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEnumMember.Value">
      <summary> Gets the value of this enumeration type member. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmEnumType">
      <summary> Represents the definition of an Edm enumeration type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEnumType.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType)">
      <summary> Initializes a new instance of the EdmEnumType class. </summary>
      <param name="underlyingType">The underlying type of this enumeration type.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEnumType.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType,System.String,System.String,System.Boolean)">
      <summary> Initializes a new instance of the EdmEnumType class. </summary>
      <param name="underlyingType">The underlying type of this enumeration type.</param>
      <param name="namespaceName">Namespace this enumeration type belongs to.</param>
      <param name="name">Name of this enumeration type.</param>
      <param name="treatAsBits">A value indicating whether the enumeration type can be treated as a bit field.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEnumType.Members">
      <summary> Gets the members of this enumeration type. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEnumType.Name">
      <summary> Gets or sets the name of this enumeration type. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEnumType.Namespace">
      <summary> Gets or sets the namespace this schema element belongs to. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEnumType.RemoveMember(Microsoft.Data.Edm.IEdmEnumMember)">
      <summary> Removes an existing member from this enum type </summary>
      <param name="member">The member to add.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEnumType.SchemaElementKind">
      <summary> Gets the kind of this schema element. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEnumType.TreatAsBits">
      <summary> Gets or sets a value indicating whether the enumeration type can be treated as a bit field. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEnumType.TypeKind">
      <summary> Gets the kind of this type. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmEnumType.UnderlyingType">
      <summary> Gets the underlying type of this enumeration type. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmEnumTypeReference">
      <summary>Represents a reference to an EDM enumeration type.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmEnumTypeReference.#ctor(Microsoft.Data.Edm.IEdmEnumType,System.Boolean)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmEnumTypeReference" />class.</summary>
      <param name="enumType">The definition refered to by this reference.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmFunction">
      <summary>Represents an EDM function.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmFunction.#ctor(System.String,System.String,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmFunction" /> class.</summary>
      <param name="namespaceName">Namespace of the function.</param>
      <param name="name">Name of the function.</param>
      <param name="returnType">Return type of the function.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmFunction.#ctor(System.String,System.String,Microsoft.Data.Edm.IEdmTypeReference,System.String)">
      <summary> Initializes a new instance of the EdmFunction class. </summary>
      <param name="namespaceName">Namespace of the function.</param>
      <param name="name">Name of the function.</param>
      <param name="returnType">Return type of the function.</param>
      <param name="definingExpression">Defining expression of the function (for example an eSQL expression).</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunction.DefiningExpression">
      <summary>Gets or sets the defining expression of this function.</summary>
      <returns>The defining expression of this function.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunction.Namespace">
      <summary>Gets or sets the namespace of this function.</summary>
      <returns>The namespace of this function.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunction.SchemaElementKind">
      <summary>Gets the element kind of this function, which is always Function.</summary>
      <returns>The element kind of this function.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmFunctionBase">
      <summary>Represents an EDM function or function import.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmFunctionBase.#ctor(System.String,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmFunctionBase" /> class.</summary>
      <param name="name">The name of the function.</param>
      <param name="returnType">The return type of the function.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmFunctionBase.AddParameter(Microsoft.Data.Edm.IEdmFunctionParameter)">
      <summary>Adds a parameter to this function (as the last parameter).</summary>
      <param name="parameter">The parameter being added.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmFunctionBase.AddParameter(System.String,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary>Adds a parameter to this function (as the last parameter).</summary>
      <param name="name">The name of the parameter being added.</param>
      <param name="type">The type of the parameter being added.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmFunctionBase.AddParameter(System.String,Microsoft.Data.Edm.IEdmTypeReference,Microsoft.Data.Edm.EdmFunctionParameterMode)">
      <summary> Creates and adds a parameter to this function (as the last parameter). </summary>
      <returns>Created parameter.</returns>
      <param name="name">The name of the parameter being added.</param>
      <param name="type">The type of the parameter being added.</param>
      <param name="mode">Mode of the parameter.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmFunctionBase.FindParameter(System.String)">
      <summary>Searches for a parameter with the given name in this function and returns null if no such parameter exists.</summary>
      <returns>The requested parameter, or null if no such parameter exists.</returns>
      <param name="name">The name of the parameter to be found.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunctionBase.Name">
      <summary>Gets or sets the name of this function.</summary>
      <returns>The name of this function.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunctionBase.Parameters">
      <summary>Gets the parameters of this function.</summary>
      <returns>The parameters of this function.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunctionBase.ReturnType">
      <summary>Gets or sets the return type of this function.</summary>
      <returns>The return type of this function.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmFunctionImport">
      <summary> Represents an EDM function import. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmFunctionImport.#ctor(Microsoft.Data.Edm.IEdmEntityContainer,System.String,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Initializes a new instance of <see cref="T:Microsoft.Data.Edm.Library.EdmFunctionImport" /> class (side-effecting, non-composable, non-bindable). </summary>
      <param name="container">An <see cref="T:Microsoft.Data.Edm.IEdmEntityContainer" /> containing this function import.</param>
      <param name="name">Name of the function import.</param>
      <param name="returnType">Return type of the function import.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmFunctionImport.#ctor(Microsoft.Data.Edm.IEdmEntityContainer,System.String,Microsoft.Data.Edm.IEdmTypeReference,Microsoft.Data.Edm.Expressions.IEdmExpression)">
      <summary> Initializes a new instance of <see cref="T:Microsoft.Data.Edm.Library.EdmFunctionImport" /> class (side-effecting, non-composable, non-bindable). </summary>
      <param name="container">An <see cref="T:Microsoft.Data.Edm.IEdmEntityContainer" /> containing this function import.</param>
      <param name="name">Name of the function import.</param>
      <param name="returnType">Return type of the function import.</param>
      <param name="entitySet">An entity set containing entities returned by this function import.  The two expression kinds supported are <see cref="T:Microsoft.Data.Edm.Expressions.IEdmEntitySetReferenceExpression" /> and <see cref="T:Microsoft.Data.Edm.Expressions.IEdmPathExpression" />.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmFunctionImport.#ctor(Microsoft.Data.Edm.IEdmEntityContainer,System.String,Microsoft.Data.Edm.IEdmTypeReference,Microsoft.Data.Edm.Expressions.IEdmExpression,System.Boolean,System.Boolean,System.Boolean)">
      <summary> Initializes a new instance of <see cref="T:Microsoft.Data.Edm.Library.EdmFunctionImport" /> class. </summary>
      <param name="container">An <see cref="T:Microsoft.Data.Edm.IEdmEntityContainer" /> containing this function import.</param>
      <param name="name">Name of the function import.</param>
      <param name="returnType">Return type of the function import.</param>
      <param name="entitySet">An entity set containing entities returned by this function import.  The two expression kinds supported are <see cref="T:Microsoft.Data.Edm.Expressions.IEdmEntitySetReferenceExpression" /> and <see cref="T:Microsoft.Data.Edm.Expressions.IEdmPathExpression" />.</param>
      <param name="sideEffecting">A value indicating whether this function import has side-effects.</param>
      <param name="composable">A value indicating whether this functon import can be composed inside expressions.</param>
      <param name="bindable">A value indicating whether this function import can be used as an extension method for the type of the first parameter of this function import.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunctionImport.Bindable"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunctionImport.Composable"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunctionImport.Container">
      <summary> Gets or sets the container of this function. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunctionImport.ContainerElementKind"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunctionImport.EntitySet"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunctionImport.SideEffecting"></member>
    <member name="T:Microsoft.Data.Edm.Library.EdmFunctionParameter">
      <summary>Represents an EDM function parameter.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmFunctionParameter.#ctor(Microsoft.Data.Edm.IEdmFunctionBase,System.String,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Initializes a new instance of the EdmFunctionParameter class. </summary>
      <param name="declaringFunction">Declaring function of the parameter.</param>
      <param name="name">Name of the parameter.</param>
      <param name="type">Type of the parameter.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmFunctionParameter.#ctor(Microsoft.Data.Edm.IEdmFunctionBase,System.String,Microsoft.Data.Edm.IEdmTypeReference,Microsoft.Data.Edm.EdmFunctionParameterMode)">
      <summary> Initializes a new instance of the EdmFunctionParameter class. </summary>
      <param name="declaringFunction">Declaring function of the parameter.</param>
      <param name="name">Name of the parameter.</param>
      <param name="type">Type of the parameter.</param>
      <param name="mode">Mode of the parameter.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunctionParameter.DeclaringFunction">
      <summary> Gets the function or function import that declared this parameter. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunctionParameter.Mode">
      <summary>Gets the mode of this parameter.</summary>
      <returns>The mode of this parameter.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmFunctionParameter.Type">
      <summary>Gets the type of this parameter.</summary>
      <returns>The type of this parameter.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmModel">
      <summary> Represents an EDM model. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModel.#ctor"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModel.AddElement(Microsoft.Data.Edm.IEdmSchemaElement)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModel.AddElements(System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.IEdmSchemaElement})"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModel.AddReference(Microsoft.Data.Edm.IEdmModel)">
      <summary> Adds a model reference to this model. </summary>
      <param name="model">The model to reference.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModel.AddVocabularyAnnotation(Microsoft.Data.Edm.Annotations.IEdmVocabularyAnnotation)">
      <summary> Adds a vocabulary annotation to this model. </summary>
      <param name="annotation">The annotation to be added.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModel.FindDeclaredVocabularyAnnotations(Microsoft.Data.Edm.IEdmVocabularyAnnotatable)">
      <summary> Searches for vocabulary annotations specified by this model. </summary>
      <returns>The vocabulary annotations for the element.</returns>
      <param name="element">The annotated element.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModel.RemoveElement(Microsoft.Data.Edm.IEdmSchemaElement)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModel.RemoveReference(Microsoft.Data.Edm.IEdmModel)">
      <summary> Removes an model reference from this model. </summary>
      <param name="model">The model reference to be removed</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModel.RemoveVocabularyAnnotation(Microsoft.Data.Edm.Annotations.IEdmVocabularyAnnotation)">
      <summary> Removes a vocabulary annotation from this model. </summary>
      <param name="annotation">The annotation to be removed.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmModel.SchemaElements"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmModel.VocabularyAnnotations">
      <summary> Gets the collection of vocabulary annotations that are contained in this model. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmModelBase">
      <summary> Represents an EDM model. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModelBase.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.IEdmModel},Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationsManager)">
      <summary> Initializes a new instance of the EdmModelBase class. </summary>
      <param name="references">Models to which this model refers.</param>
      <param name="annotationsManager">Annotations manager for the model to use.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModelBase.AddReference(Microsoft.Data.Edm.IEdmModel)">
      <summary> Adds a model reference to this model. </summary>
      <param name="model">The model to reference.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmModelBase.DirectValueAnnotationsManager">
      <summary> Gets the model's annotations manager. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModelBase.FindDeclaredEntityContainer(System.String)">
      <summary> Searches for an entity container with the given name in this model and returns null if no such entity container exists. </summary>
      <returns>The requested entity container, or null if no such entity container exists.</returns>
      <param name="name">The name of the entity container being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModelBase.FindDeclaredFunctions(System.String)">
      <summary> Searches for a function with the given name in this model and returns null if no such function exists. </summary>
      <returns>A group of functions sharing the specified qualified name, or an empty enumerable if no such function exists.</returns>
      <param name="qualifiedName">The qualified name of the function being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModelBase.FindDeclaredType(System.String)">
      <summary> Searches for a type with the given name in this model and returns null if no such type exists. </summary>
      <returns>The requested type, or null if no such type exists.</returns>
      <param name="qualifiedName">The qualified name of the type being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModelBase.FindDeclaredValueTerm(System.String)">
      <summary> Searches for a value term with the given name in this model and returns null if no such value term exists. </summary>
      <returns>The requested value term, or null if no such value term exists.</returns>
      <param name="qualifiedName">The qualified name of the value term being found.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModelBase.FindDeclaredVocabularyAnnotations(Microsoft.Data.Edm.IEdmVocabularyAnnotatable)">
      <summary> Searches for vocabulary annotations specified by this model or a referenced model for a given element. </summary>
      <returns>The vocabulary annotations for the element.</returns>
      <param name="element">The annotated element.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmModelBase.References"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModelBase.RegisterElement(Microsoft.Data.Edm.IEdmSchemaElement)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModelBase.RemoveReference(Microsoft.Data.Edm.IEdmModel)">
      <summary> Removes an model reference from this model. </summary>
      <param name="model">The model reference to be removed</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmModelBase.SchemaElements"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmModelBase.UnregisterElement(Microsoft.Data.Edm.IEdmSchemaElement)"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmModelBase.VocabularyAnnotations"></member>
    <member name="T:Microsoft.Data.Edm.Library.EdmNamedElement">
      <summary>Represents a common base class for all named EDM elements.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmNamedElement.#ctor">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmNamedElement" /> class.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmNamedElement.#ctor(System.String)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmNamedElement" /> class.</summary>
      <param name="name">The name of the element.</param>
    </member>
    <member name="F:Microsoft.Data.Edm.Library.EdmNamedElement.elementName">
      <summary>The name of the element.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmNamedElement.Name">
      <summary>Gets the name of this element.</summary>
      <returns>The name of this element.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmNavigationProperty">
      <summary>Represents an EDM navigation property.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmNavigationProperty.AddDependentProperties(Microsoft.Data.Edm.IEdmStructuralProperty[])">
      <summary> Adds the properties to the list of dependent properties. </summary>
      <param name="properties">The dependent properties.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmNavigationProperty.AddDependentProperties(System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.IEdmStructuralProperty})">
      <summary> Adds the properties to the list of dependent properties. </summary>
      <param name="properties">The dependent properties.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmNavigationProperty.ContainsTarget">
      <summary> Gets a value indicating whether the navigation target is contained inside the navigation source. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmNavigationProperty.CreateNavigation(System.String,Microsoft.Data.Edm.IEdmEntityType,Microsoft.Data.Edm.EdmMultiplicity,System.Boolean,Microsoft.Data.Edm.EdmOnDeleteAction,System.String,Microsoft.Data.Edm.IEdmEntityType,Microsoft.Data.Edm.EdmMultiplicity,System.Boolean,Microsoft.Data.Edm.EdmOnDeleteAction)">
      <summary> Creates two navigation properties representing an association between two entity types. </summary>
      <returns>Navigation property pointing from sourceEntityType to targetEntityType.</returns>
      <param name="propertyName">Navigation property name.</param>
      <param name="targetEntityType">Navigation target type.</param>
      <param name="targetMultiplicity">Navigation target multiplicity.</param>
      <param name="containsTarget">A value indicating whether the navigation source logically contains the navigation target.</param>
      <param name="onDelete">Action to take upon deletion of an instance of the navigation source.</param>
      <param name="partnerPropertyName">Navigation partner property name.</param>
      <param name="sourceEntityType">Navigation source type.</param>
      <param name="sourceMultiplicity">Navigation source multiplicity</param>
      <param name="partnerContainsTarget">A value indicating whether the navigation target logically contains the navigation source.</param>
      <param name="partnerOnDelete">Action to take upon deletion of an instance of the navigation target.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmNavigationProperty.CreateNavigation(System.String,Microsoft.Data.Edm.IEdmEntityType,Microsoft.Data.Edm.EdmMultiplicity,System.String,Microsoft.Data.Edm.IEdmEntityType,Microsoft.Data.Edm.EdmMultiplicity)">
      <summary> Creates two navigation properties representing an association between two entity types. </summary>
      <returns>Navigation property pointing from sourceEntityType to targetEntityType.</returns>
      <param name="propertyName">Navigation property name.</param>
      <param name="targetEntityType">Navigation target type.</param>
      <param name="targetMultiplicity">Navigation target multiplicity.</param>
      <param name="partnerPropertyName">Navigation partner property name.</param>
      <param name="sourceEntityType">Navigation source type.</param>
      <param name="sourceMultiplicity">Navigation source multiplicity</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmNavigationProperty.CreateNavigation(System.String,Microsoft.Data.Edm.IEdmTypeReference,System.Boolean,Microsoft.Data.Edm.EdmOnDeleteAction,System.String,Microsoft.Data.Edm.IEdmTypeReference,System.Boolean,Microsoft.Data.Edm.EdmOnDeleteAction)">
      <summary> Creates two navigation properties representing an association between two entity types. </summary>
      <returns>Navigation property.</returns>
      <param name="propertyName">Navigation property name.</param>
      <param name="propertyType">Type of the navigation property.</param>
      <param name="containsTarget">A value indicating whether the navigation source logically contains the navigation target.</param>
      <param name="onDelete">Action to take upon deletion of an instance of the navigation source.</param>
      <param name="partnerPropertyName">Navigation partner property name.</param>
      <param name="partnerPropertyType">Type of the navigation partner property.</param>
      <param name="partnerContainsTarget">A value indicating whether the navigation target logically contains the navigation source.</param>
      <param name="partnerOnDelete">Action to take upon deletion of an instance of the navigation target.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmNavigationProperty.DeclaringEntityType">
      <summary>Gets the entity type that this navigation property belongs to.</summary>
      <returns>The entity type that this navigation property belongs to.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmNavigationProperty.DependentProperties">
      <summary>Gets or sets the dependent properties of the association this navigation property expresses.</summary>
      <returns>The dependent properties of the association this navigation property expresses.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmNavigationProperty.IsPrincipal">
      <summary> Gets a value indicating whether this navigation property is from the principal end of the association. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmNavigationProperty.Microsoft#Data#Edm#IEdmNavigationProperty#Partner">
      <summary> Gets the partner of this navigation property. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmNavigationProperty.OnDelete">
      <summary>Gets or sets the action to take when an element of the defining type is deleted.</summary>
      <returns>The action to take when an element of the defining type is deleted.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmNavigationProperty.Partner">
      <summary>Gets or sets the navigation property from this properties destination back to the declaring type of this property.</summary>
      <returns>The navigation property from this properties destination back to the declaring type of this property.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmNavigationProperty.PropertyKind">
      <summary>Gets the kind of this property.</summary>
      <returns>The kind of this property.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmPrimitiveTypeReference">
      <summary>Represents a reference to an EDM primitive type.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmPrimitiveTypeReference.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType,System.Boolean)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmPrimitiveTypeReference" /> class.</summary>
      <param name="definition">The type this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmProperty">
      <summary>Represents an EDM property.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmProperty.#ctor(Microsoft.Data.Edm.IEdmStructuredType,System.String,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmProperty" /> class.</summary>
      <param name="declaringType">The type that declares this property.</param>
      <param name="name">Name of the property.</param>
      <param name="type">Type of the property.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmProperty.DeclaringType">
      <summary>Gets the type that this property belongs to.</summary>
      <returns>The type that this property belongs to.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmProperty.Name">
      <summary>Gets or sets the name of this property.</summary>
      <returns>The name of this property.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmProperty.PropertyKind">
      <summary>Gets the kind of this property.</summary>
      <returns>The kind of this property.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmProperty.Type">
      <summary>Gets or sets the type of this property.</summary>
      <returns>The type of this property.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmRowType">
      <summary> Represents a definition of an EDM row type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmRowType.#ctor"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmRowType.TypeKind">
      <summary> Gets the kind of this type. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmRowTypeReference">
      <summary>Represents a reference to an EDM row type.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmRowTypeReference.#ctor(Microsoft.Data.Edm.IEdmRowType,System.Boolean)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmRowTypeReference" /> class.</summary>
      <param name="rowType">Type that describes this value.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmSpatialTypeReference">
      <summary>Represents a reference to an EDM spatial type.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmSpatialTypeReference.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType,System.Boolean)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmSpatialTypeReference" /> class.</summary>
      <param name="definition">The type this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmSpatialTypeReference.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType,System.Boolean,System.Nullable{System.Int32})">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmSpatialTypeReference" /> class.</summary>
      <param name="definition">The type this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
      <param name="spatialReferenceIdentifier">Spatial Reference Identifier for the spatial type being created.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmSpatialTypeReference.SpatialReferenceIdentifier">
      <summary>Gets the precision of this temporal type.</summary>
      <returns>The precision of this temporal type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmStringTypeReference">
      <summary>Represents a reference to an EDM string type.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmStringTypeReference.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType,System.Boolean)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmStringTypeReference" /> class.</summary>
      <param name="definition">The type this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmStringTypeReference.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType,System.Boolean,System.Boolean,System.Nullable{System.Int32},System.Nullable{System.Boolean},System.Nullable{System.Boolean},System.String)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmStringTypeReference" /> class.</summary>
      <param name="definition">The type this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
      <param name="isMaxMaxLength">Denotes whether the max length is the maximum allowed value.</param>
      <param name="maxLength">Maximum length of a value of this type.</param>
      <param name="isFixedLength">Denotes whether the length can vary.</param>
      <param name="isUnicode">Denotes if string is encoded using Unicode.</param>
      <param name="collation">Indicates the collation string to be used by the underlying store.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStringTypeReference.Collation">
      <summary>Gets a string representing the collation of this string type.</summary>
      <returns>The collation of this string type.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStringTypeReference.IsFixedLength">
      <summary>Gets a value indicating whether this string type specifies fixed length.</summary>
      <returns>True if this string type specifies fixed length; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStringTypeReference.IsMaxMaxLength">
      <summary>Gets a value indicating whether this string type specifies the maximum allowed max length.</summary>
      <returns>True if this string type specifies the maximum allowed max length; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStringTypeReference.IsUnicode">
      <summary>Gets a value indicating whether this string type supports unicode encoding.</summary>
      <returns>True if this string type supports Unicode encoding; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStringTypeReference.MaxLength">
      <summary>Gets the maximum length of this string type.</summary>
      <returns>The maximum length of this string type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmStructuralProperty">
      <summary>Represents an EDM structural (i.e. non-navigation) property.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmStructuralProperty.#ctor(Microsoft.Data.Edm.IEdmStructuredType,System.String,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Initializes a new instance of the EdmStructuralProperty class. </summary>
      <param name="declaringType">The type that declares this property.</param>
      <param name="name">Name of the property.</param>
      <param name="type">Type of the property.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmStructuralProperty.#ctor(Microsoft.Data.Edm.IEdmStructuredType,System.String,Microsoft.Data.Edm.IEdmTypeReference,System.String,Microsoft.Data.Edm.EdmConcurrencyMode)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmStructuralProperty" /> class.</summary>
      <param name="declaringType">The type that declares this property.</param>
      <param name="name">The name of the property.</param>
      <param name="type">The type of the property.</param>
      <param name="defaultValue">The default value of this property.</param>
      <param name="concurrencyMode">The concurrency mode of this property.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStructuralProperty.ConcurrencyMode">
      <summary>Gets the concurrency mode of this property.</summary>
      <returns>The concurrency mode of this property.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStructuralProperty.DefaultValue">
      <summary>Gets the default value of this property.</summary>
      <returns>The default value of this property.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStructuralProperty.PropertyKind">
      <summary>Gets the kind of this property.</summary>
      <returns>The kind of this property.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmStructuredType">
      <summary> Common base class for definitions of EDM structured types. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmStructuredType.#ctor">
      <summary> Initializes a new instance of the EdmStructuredType class. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmStructuredType.#ctor(System.Boolean,System.Boolean,Microsoft.Data.Edm.IEdmStructuredType)">
      <summary> Initializes a new instance of the EdmStructuredType class. </summary>
      <param name="isAbstract">Denotes a structured type that cannot be instantiated.</param>
      <param name="isOpen">Denotes if the type is open.</param>
      <param name="baseStructuredType">Base type of the type</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmStructuredType.AddProperty(Microsoft.Data.Edm.IEdmProperty)"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmStructuredType.AddStructuralProperty(System.String,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Creates and adds a structural property to this type. </summary>
      <returns>Created structural property.</returns>
      <param name="name">Name of the property.</param>
      <param name="type">Type of the property.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmStructuredType.AddStructuralProperty(System.String,Microsoft.Data.Edm.IEdmTypeReference,System.String,Microsoft.Data.Edm.EdmConcurrencyMode)">
      <summary> Creates and adds a structural property to this type. </summary>
      <returns>Created structural property.</returns>
      <param name="name">Name of the property.</param>
      <param name="type">Type of the property.</param>
      <param name="defaultValue">The default value of this property.</param>
      <param name="concurrencyMode">The concurrency mode of this property.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStructuredType.BaseType"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmStructuredType.ComputePropertiesDictionary"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStructuredType.DeclaredProperties"></member>
    <member name="M:Microsoft.Data.Edm.Library.EdmStructuredType.FindProperty(System.String)"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStructuredType.IsAbstract"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStructuredType.IsOpen"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmStructuredType.PropertiesDictionary"></member>
    <member name="T:Microsoft.Data.Edm.Library.EdmTemporalTypeReference">
      <summary>Represents a reference to an EDM temporal (Time, DateTime, DateTimeOffset) type.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmTemporalTypeReference.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType,System.Boolean)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmTemporalTypeReference" /> class.</summary>
      <param name="definition">The type this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmTemporalTypeReference.#ctor(Microsoft.Data.Edm.IEdmPrimitiveType,System.Boolean,System.Nullable{System.Int32})">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmTemporalTypeReference" /> class.</summary>
      <param name="definition">The type this reference refers to.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
      <param name="precision">Precision of values with this type.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmTemporalTypeReference.Precision">
      <summary>Gets the precision of this temporal type.</summary>
      <returns>The precision of this temporal type.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmType">
      <summary> Represents the definition of an EDM type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmType.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmType" /> class.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmType.ToString"></member>
    <member name="P:Microsoft.Data.Edm.Library.EdmType.TypeKind"></member>
    <member name="T:Microsoft.Data.Edm.Library.EdmTypeReference">
      <summary>Represents a reference to an EDM type.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmTypeReference.#ctor(Microsoft.Data.Edm.IEdmType,System.Boolean)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Library.EdmTypeReference" /> class.</summary>
      <param name="definition">The type that describes this value.</param>
      <param name="isNullable">Denotes whether the type can be nullable.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmTypeReference.Definition">
      <summary>Gets the definition to which this type refers.</summary>
      <returns>The definition to which this type refers.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmTypeReference.IsNullable">
      <summary>Gets a value indicating whether this type is nullable.</summary>
      <returns>True if this type is nullable; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmTypeReference.ToString">
      <summary>Returns the text representation of the current object.</summary>
      <returns>The text representation of the current object.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.EdmValueTerm">
      <summary>Represents an EDM value term.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmValueTerm.#ctor(System.String,System.String)">
      <summary> Initializes a new instance of the EdmValueTerm class. </summary>
      <param name="namespaceName">Namespace of the term.</param>
      <param name="localName">Name of the term within the namespace.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.EdmValueTerm.#ctor(System.String,System.String,Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Initializes a new instance of the EdmValueTerm class. </summary>
      <param name="namespaceName">Namespace of the term.</param>
      <param name="localName">Name of the term within the namespace.</param>
      <param name="type">Type of the term.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmValueTerm.Name">
      <summary>Gets the local name of this term.</summary>
      <returns>The local name of this term.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmValueTerm.Namespace">
      <summary>Gets the namespace of this term.</summary>
      <returns>The namespace of this term.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmValueTerm.SchemaElementKind">
      <summary>Gets the schema element kind of this term.</summary>
      <returns>The schema element kind of this term.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmValueTerm.TermKind">
      <summary>Gets the kind of this term.</summary>
      <returns>The kind of this term.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.EdmValueTerm.Type">
      <summary>Gets the type of this term.</summary>
      <returns>The type of this term.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotation">
      <summary> Represents an EDM annotation with an immediate native value. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotation.#ctor(System.String,System.String,System.Object)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotation" /> class. </summary>
      <param name="namespaceName">Namespace of the annotation.</param>
      <param name="localName">Name of the annotation within the namespace.</param>
      <param name="value">Value of the annotation</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotation.Name">
      <summary> The name of the annotation </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotation.NamespaceUri">
      <summary> The namespace Uri of the annotation. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotation.Value">
      <summary> Gets the value of this annotation. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationBinding">
      <summary> Represents the combination of an EDM annotation with an immediate value and the element to which it is attached. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationBinding.#ctor">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationBinding" /> class. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationBinding.#ctor(Microsoft.Data.Edm.IEdmElement,System.String,System.String)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationBinding" /> class. </summary>
      <param name="element">Element to which the annotation is attached.</param>
      <param name="namespaceUri">Namespace of the annotation.</param>
      <param name="localName">Name of the annotation within the namespace.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationBinding.#ctor(Microsoft.Data.Edm.IEdmElement,System.String,System.String,System.Object)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationBinding" /> class. </summary>
      <param name="element">Element to which the annotation is attached.</param>
      <param name="namespaceName">Namespace of the annotation.</param>
      <param name="localName">Name of the annotation within the namespace.</param>
      <param name="value">Value of the annotation</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationBinding.Element">
      <summary> Gets or sets the element to which the annotation is attached. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationBinding.LocalName">
      <summary> Gets or sets the local name of the annotation </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationBinding.NamespaceUri">
      <summary> Gets or sets the namespace Uri of the annotation. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationBinding.Value">
      <summary> Gets the value of this annotation. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationsManager">
      <summary> EdmDirectValueAnnotationsManager provides services for setting and getting transient annotations on elements. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationsManager.#ctor">
      <summary> Initializes a new instance of the EdmDirectValueAnnotationsManager class. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationsManager.GetAnnotationValue(Microsoft.Data.Edm.IEdmElement,System.String,System.String)">
      <summary> Retrieves an annotation value for an EDM element. Returns null if no annotation with the given name exists for the given element. </summary>
      <returns>Returns the annotation that corresponds to the provided name. Returns null if no annotation with the given name exists for the given element.</returns>
      <param name="element">The annotated element.</param>
      <param name="namespaceName">Namespace that the annotation belongs to.</param>
      <param name="localName">Local name of the annotation.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationsManager.GetAnnotationValues(System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationBinding})">
      <summary> Retrieves a set of annotation values. For each requested value, returns null if no annotation with the given name exists for the given element. </summary>
      <param name="annotations">The set of requested annotations</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationsManager.GetAttachedAnnotations(Microsoft.Data.Edm.IEdmElement)">
      <summary> Retrieves the annotations that are directly attached to an element. </summary>
      <returns>The annotations that are directly attached to an element (outside the control of the manager).</returns>
      <param name="element">The element in question.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationsManager.GetDirectValueAnnotations(Microsoft.Data.Edm.IEdmElement)">
      <summary> Gets annotations associated with an element. </summary>
      <returns>The immediate value annotations for the element.</returns>
      <param name="element">The annotated element.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationsManager.SetAnnotationValue(Microsoft.Data.Edm.IEdmElement,System.String,System.String,System.Object)">
      <summary> Sets an annotation value for an EDM element. If the value is null, no annotation is added and an existing annotation with the same name is removed. </summary>
      <param name="element">The annotated element.</param>
      <param name="namespaceName">Namespace that the annotation belongs to.</param>
      <param name="localName">Name of the annotation within the namespace.</param>
      <param name="value">New annotation to set.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmDirectValueAnnotationsManager.SetAnnotationValues(System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Annotations.IEdmDirectValueAnnotationBinding})">
      <summary> Sets a set of annotation values. If a supplied value is null, no annotation is added and an existing annotation with the same name is removed. </summary>
      <param name="annotations">The annotations to set</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Annotations.EdmPropertyValueBinding">
      <summary> Represents a property binding specified as part of an EDM type annotation. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmPropertyValueBinding.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Annotations.EdmPropertyValueBinding" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmPropertyValueBinding.BoundProperty">
      <summary> Gets or sets the property that is given a value by the annotation. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmPropertyValueBinding.Value">
      <summary> Gets or sets the expression producing the value of the annotation. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Annotations.EdmTypeAnnotation">
      <summary> Represents an EDM type annotation. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmTypeAnnotation.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Annotations.EdmTypeAnnotation" /> class.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmTypeAnnotation.AddProperty(Microsoft.Data.Edm.Annotations.IEdmPropertyValueBinding)">
      <summary> Adds the property to this type annotation. </summary>
      <param name="property">The property value binding being added.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmTypeAnnotation.Properties">
      <summary> Gets the value annotations for the properties of the type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmTypeAnnotation.RemoveProperty(Microsoft.Data.Edm.Annotations.IEdmPropertyValueBinding)">
      <summary> Removes the property from this type annotation. </summary>
      <param name="property">The property value binding being removed.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Annotations.EdmTypedDirectValueAnnotationBinding`1">
      <summary> Represents the combination of an EDM annotation with an immediate value and the element to which it is attached. </summary>
      <typeparam name="T">Type of the annotation value.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmTypedDirectValueAnnotationBinding`1.#ctor">
      <summary> Initializes a new instance of the EdmTypedDirectValueAnnotationBinding class. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmTypedDirectValueAnnotationBinding`1.#ctor(Microsoft.Data.Edm.IEdmElement)">
      <summary> Initializes a new instance of the EdmTypedDirectValueAnnotationBinding class. </summary>
      <param name="element">Element to which the annotation is attached.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmTypedDirectValueAnnotationBinding`1.#ctor(Microsoft.Data.Edm.IEdmElement,`0)">
      <summary> Initializes a new instance of the EdmTypedDirectValueAnnotationBinding class. </summary>
      <param name="element">Element to which the annotation is attached.</param>
      <param name="value">Value of the annotation</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmTypedDirectValueAnnotationBinding`1.Element">
      <summary> Gets or sets the element to which the annotation is attached. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmTypedDirectValueAnnotationBinding`1.LocalName">
      <summary> Gets the local name of the annotation </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmTypedDirectValueAnnotationBinding`1.NamespaceUri">
      <summary> Gets the namespace Uri of the annotation. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmTypedDirectValueAnnotationBinding`1.Value">
      <summary> Gets or sets the value of this annotation. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Annotations.EdmValueAnnotation">
      <summary> Represents an EDM value annotation. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmValueAnnotation.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Annotations.EdmValueAnnotation" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmValueAnnotation.Value">
      <summary> Gets or sets the expression producing the value of the annotation. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Annotations.EdmVocabularyAnnotation">
      <summary> Represents an EDM annotation with an immediate value. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Annotations.EdmVocabularyAnnotation.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Annotations.EdmVocabularyAnnotation" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmVocabularyAnnotation.Qualifier">
      <summary> Gets or sets the qualifier used to discriminate between multiple bindings of the same property or type. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmVocabularyAnnotation.Target">
      <summary> Gets or sets the element the annotation applies to. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Annotations.EdmVocabularyAnnotation.Term">
      <summary> Gets or sets the term bound by the annotation. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmAssertTypeExpression">
      <summary> Represents an EDM type assertion expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmAssertTypeExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmAssertTypeExpression" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmAssertTypeExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmAssertTypeExpression.Operand">
      <summary> Gets or sets the expression for which the type is asserted. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmAssertTypeExpression.Type">
      <summary> Gets or sets the asserted type. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmCollectionExpression">
      <summary> Represents an EDM multi-value construction expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmCollectionExpression.#ctor(Microsoft.Data.Edm.Expressions.IEdmExpression[])">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmCollectionExpression" /> class. </summary>
      <param name="elements">The constructed element values.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmCollectionExpression.AddElement(Microsoft.Data.Edm.Expressions.IEdmExpression)">
      <summary> Adds the element to this expression. </summary>
      <param name="element">The element being added.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmCollectionExpression.DeclaredType">
      <summary> Gets or sets the declared type of the collection. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmCollectionExpression.Elements">
      <summary> Gets the constructed element values. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmCollectionExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmCollectionExpression.RemoveElement(Microsoft.Data.Edm.Expressions.IEdmExpression)">
      <summary> Removes the element from this expression. </summary>
      <param name="element">The element being removed.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmEntitySetReferenceExpression">
      <summary> Represents an EDM entity set reference expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmEntitySetReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmEntitySetReferenceExpression" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmEntitySetReferenceExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmEntitySetReferenceExpression.Referenced">
      <summary> Gets or sets the referenced entity set. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmEnumConstantReferenceExpression">
      <summary> Represents an EDM enumeration member reference expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmEnumConstantReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmEnumConstantReferenceExpression" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmEnumConstantReferenceExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmEnumConstantReferenceExpression.Referenced">
      <summary> Gets or sets the referenced enum member. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmFunctionApplicationExpression">
      <summary> Represents an EDM function application expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmFunctionApplicationExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmFunctionApplicationExpression" /> class.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmFunctionApplicationExpression.AddArgument(Microsoft.Data.Edm.Expressions.IEdmExpression)">
      <summary> Adds the argument to this function. </summary>
      <param name="argument">The argument being added.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmFunctionApplicationExpression.AppliedFunction">
      <summary> Gets or sets the applied function. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmFunctionApplicationExpression.Arguments">
      <summary> Gets the arguments to the function. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmFunctionApplicationExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmFunctionApplicationExpression.RemoveArgument(Microsoft.Data.Edm.Expressions.IEdmExpression)">
      <summary> Removes the argument from this function. </summary>
      <param name="argument">The argument being removed.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmFunctionReferenceExpression">
      <summary> Represents an EDM function reference expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmFunctionReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmFunctionReferenceExpression" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmFunctionReferenceExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmFunctionReferenceExpression.Referenced">
      <summary> Gets or sets the referenced function. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmIfExpression">
      <summary> Represents an EDM if expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmIfExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmIfExpression" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmIfExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmIfExpression.IfFalse">
      <summary> Gets or sets the expression to evaluate if <see cref="P:Microsoft.Data.Edm.Library.Expressions.EdmIfExpression.Test" /> evaluates to false. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmIfExpression.IfTrue">
      <summary> Gets or sets the expression to evaluate if <see cref="P:Microsoft.Data.Edm.Library.Expressions.EdmIfExpression.Test" /> evaluates to true. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmIfExpression.Test">
      <summary> Gets or sets the test expression. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmIsTypeExpression">
      <summary> Represents an EDM type test expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmIsTypeExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmIsTypeExpression" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmIsTypeExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmIsTypeExpression.Operand">
      <summary> Gets or sets the expression whose type is to be tested. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmIsTypeExpression.Type">
      <summary> Gets or sets the type to be tested against. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmLabeledExpression">
      <summary> Represents an EDM labeled expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmLabeledExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmLabeledExpression" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmLabeledExpression.Expression">
      <summary> Gets or sets the underlying element expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmLabeledExpression.ExpressionKind">
      <summary> Gets the expression kind. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmLabeledExpression.Name">
      <summary> Gets or sets the label. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmLabeledExpressionReferenceExpression">
      <summary> Represents an EDM labeled expression reference expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmLabeledExpressionReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmLabeledExpressionReferenceExpression" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmLabeledExpressionReferenceExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmLabeledExpressionReferenceExpression.Referenced">
      <summary> Gets or sets the referenced labeled element. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmParameterReferenceExpression">
      <summary> Represents an EDM parameter reference expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmParameterReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmParameterReferenceExpression" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmParameterReferenceExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmParameterReferenceExpression.Referenced">
      <summary> Gets or sets the referenced parameter. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmPathExpression">
      <summary> Represents an EDM path expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmPathExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmPathExpression" /> class.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmPathExpression.AddSegment(System.String)">
      <summary> Adds the segment to this path. </summary>
      <param name="segment">The path segment being added.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmPathExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmPathExpression.Path">
      <summary> Gets the path as a decomposed qualified name. "A.B.C" is { "A", "B", "C" }. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmPathExpression.RemoveSegment(System.String)">
      <summary> Removes the segment from this path. </summary>
      <param name="segment">The path segment being removed.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmPropertyConstructor">
      <summary> Represents an EDM property constructor specified as part of a EDM record construction expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmPropertyConstructor.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmPropertyConstructor" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmPropertyConstructor.Name">
      <summary> Gets or sets the name of the property. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmPropertyConstructor.Value">
      <summary> Gets or sets the expression for the value of the property. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmPropertyReferenceExpression">
      <summary> Represents an EDM property reference expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmPropertyReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmPropertyReferenceExpression" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmPropertyReferenceExpression.Base">
      <summary> Gets or sets the expression for the structured value containing the referenced property. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmPropertyReferenceExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmPropertyReferenceExpression.Referenced">
      <summary> Gets or sets the referenced property. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmRecordExpression">
      <summary> Represents an EDM record construction expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmRecordExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmRecordExpression" /> class.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmRecordExpression.AddProperty(Microsoft.Data.Edm.Expressions.IEdmPropertyConstructor)">
      <summary> Adds the property to this record. </summary>
      <param name="property">The property being added.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmRecordExpression.DeclaredType">
      <summary> Gets or sets the declared type of the record, or null if there is no declared type. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmRecordExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmRecordExpression.Properties">
      <summary> Gets the constructed property values. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmRecordExpression.RemoveProperty(Microsoft.Data.Edm.Expressions.IEdmPropertyConstructor)">
      <summary> Removes the property from this record. </summary>
      <param name="property">The property being removed.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Expressions.EdmValueTermReferenceExpression">
      <summary> Represents an EDM value term reference expression. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Expressions.EdmValueTermReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Expressions.EdmValueTermReferenceExpression" /> class.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmValueTermReferenceExpression.Base">
      <summary> Gets or sets the expression for the structured value containing the referenced term property. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmValueTermReferenceExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmValueTermReferenceExpression.Qualifier">
      <summary> Gets or sets the optional qualifier. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Expressions.EdmValueTermReferenceExpression.Term">
      <summary> Gets or sets the referenced value term. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmBinaryConstant">
      <summary> Represents an EDM binary constant. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmBinaryConstant.#ctor(Microsoft.Data.Edm.IEdmBinaryTypeReference,System.Byte[])">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmBinaryConstant" /> class. </summary>
      <param name="type">Type of the integer.</param>
      <param name="value">Integer value represented by this value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmBinaryConstant.#ctor(System.Byte[])">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmBinaryConstant" /> class. </summary>
      <param name="value">Integer value represented by this value.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmBinaryConstant.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmBinaryConstant.Value">
      <summary> Gets the definition of this value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmBinaryConstant.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmBooleanConstant">
      <summary> Represents an EDM boolean constant. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmBooleanConstant.#ctor(Microsoft.Data.Edm.IEdmPrimitiveTypeReference,System.Boolean)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmBooleanConstant" /> class. </summary>
      <param name="type">Type of the boolean.</param>
      <param name="value">Boolean value represented by this value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmBooleanConstant.#ctor(System.Boolean)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmBooleanConstant" /> class. </summary>
      <param name="value">Boolean value represented by this value.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmBooleanConstant.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmBooleanConstant.Value">
      <summary> Gets a value indicating whether the value of this boolean value is true or false. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmBooleanConstant.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmCollectionValue">
      <summary> Represents an EDM collection value. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmCollectionValue.#ctor(Microsoft.Data.Edm.IEdmCollectionTypeReference,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Values.IEdmDelayedValue})">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmCollectionValue" /> class.  </summary>
      <param name="type">A reference to a collection type that describes this collection value</param>
      <param name="elements">The collection of values stored in this collection value</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmCollectionValue.Elements">
      <summary> Gets the values stored in this collection. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmCollectionValue.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmDateTimeConstant">
      <summary> Represents an EDM datetime constant. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmDateTimeConstant.#ctor(Microsoft.Data.Edm.IEdmTemporalTypeReference,System.DateTime)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmDateTimeConstant" /> class. </summary>
      <param name="type">Type of the DateTime.</param>
      <param name="value">DateTime value represented by this value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmDateTimeConstant.#ctor(System.DateTime)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmDateTimeConstant" /> class. </summary>
      <param name="value">DateTime value represented by this value.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmDateTimeConstant.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmDateTimeConstant.Value">
      <summary> Gets the definition of this value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmDateTimeConstant.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmDateTimeOffsetConstant">
      <summary> Represents an EDM datetime with offset constant. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmDateTimeOffsetConstant.#ctor(Microsoft.Data.Edm.IEdmTemporalTypeReference,System.DateTimeOffset)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmDateTimeOffsetConstant" /> class. </summary>
      <param name="type">Type of the DateTimeOffset.</param>
      <param name="value">DateTimeOffset value represented by this value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmDateTimeOffsetConstant.#ctor(System.DateTimeOffset)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmDateTimeOffsetConstant" /> class. </summary>
      <param name="value">DateTimeOffset value represented by this value.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmDateTimeOffsetConstant.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmDateTimeOffsetConstant.Value">
      <summary> Gets the definition of this value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmDateTimeOffsetConstant.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmDecimalConstant">
      <summary> Represents an EDM decimal constant. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmDecimalConstant.#ctor(Microsoft.Data.Edm.IEdmDecimalTypeReference,System.Decimal)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmDecimalConstant" /> class. </summary>
      <param name="type">Type of the decimal.</param>
      <param name="value">Decimal value represented by this value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmDecimalConstant.#ctor(System.Decimal)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmDecimalConstant" /> class. </summary>
      <param name="value">Decimal value represented by this value.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmDecimalConstant.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmDecimalConstant.Value">
      <summary> Gets the definition of this value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmDecimalConstant.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmEnumValue">
      <summary> Represents an EDM enumeration type value. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmEnumValue.#ctor(Microsoft.Data.Edm.IEdmEnumTypeReference,Microsoft.Data.Edm.IEdmEnumMember)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmEnumValue" /> class.  </summary>
      <param name="type">A reference to the enumeration type that describes this value.</param>
      <param name="member">The enumeration type value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmEnumValue.#ctor(Microsoft.Data.Edm.IEdmEnumTypeReference,Microsoft.Data.Edm.Values.IEdmPrimitiveValue)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmEnumValue" /> class.  </summary>
      <param name="type">A reference to the enumeration type that describes this value.</param>
      <param name="value">The underlying type value.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmEnumValue.Value">
      <summary> Gets or sets the underlying type value of the enumeration type. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmEnumValue.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmFloatingConstant">
      <summary> Represents an EDM floating point constant. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmFloatingConstant.#ctor(Microsoft.Data.Edm.IEdmPrimitiveTypeReference,System.Double)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmFloatingConstant" /> class. </summary>
      <param name="type">Type of the floating point.</param>
      <param name="value">Floating point value represented by this value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmFloatingConstant.#ctor(System.Double)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmFloatingConstant" /> class. </summary>
      <param name="value">Floating point value represented by this value.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmFloatingConstant.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmFloatingConstant.Value">
      <summary> Gets the definition of this value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmFloatingConstant.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmGuidConstant">
      <summary> Represents an EDM guid constant. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmGuidConstant.#ctor(Microsoft.Data.Edm.IEdmPrimitiveTypeReference,System.Guid)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmGuidConstant" /> class. </summary>
      <param name="type">Type of the integer.</param>
      <param name="value">Integer value represented by this value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmGuidConstant.#ctor(System.Guid)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmGuidConstant" /> class. </summary>
      <param name="value">Integer value represented by this value.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmGuidConstant.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmGuidConstant.Value">
      <summary> Gets the definition of this value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmGuidConstant.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmIntegerConstant">
      <summary> Represents an EDM integer constant. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmIntegerConstant.#ctor(Microsoft.Data.Edm.IEdmPrimitiveTypeReference,System.Int64)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmIntegerConstant" /> class. </summary>
      <param name="type">Type of the integer.</param>
      <param name="value">Integer value represented by this value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmIntegerConstant.#ctor(System.Int64)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmIntegerConstant" /> class. </summary>
      <param name="value">Integer value represented by this value.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmIntegerConstant.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmIntegerConstant.Value">
      <summary> Gets the definition of this value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmIntegerConstant.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmNullExpression">
      <summary> Represents an EDM null. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmNullExpression.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Library.Values.EdmNullExpression.Instance">
      <summary> Singleton null expression instance. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmNullExpression.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmPropertyValue">
      <summary> Represents a value of an EDM property. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmPropertyValue.#ctor(System.String,Microsoft.Data.Edm.Values.IEdmValue)">
      <summary> Initializes a new instance of the EdmPropertyValue class. </summary>
      <param name="name">Name of the property for which this provides a value.</param>
      <param name="value">Value of the property.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmPropertyValue.Name">
      <summary> Gets the name of the property for which this provides a value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmPropertyValue.Value">
      <summary> Gets the property's value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmStringConstant">
      <summary> Represents an EDM string constant. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmStringConstant.#ctor(Microsoft.Data.Edm.IEdmStringTypeReference,System.String)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmStringConstant" /> class. </summary>
      <param name="type">Type of the string.</param>
      <param name="value">String value represented by this value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmStringConstant.#ctor(System.String)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmStringConstant" /> class. </summary>
      <param name="value">String value represented by this value.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmStringConstant.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmStringConstant.Value">
      <summary> Gets the definition of this value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmStringConstant.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmStructuredValue">
      <summary> Represents an EDM structured value. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmStructuredValue.#ctor(Microsoft.Data.Edm.IEdmStructuredTypeReference,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Values.IEdmPropertyValue})">
      <summary> Initializes a new instance of the EdmStructuredValue class. </summary>
      <param name="type">Type that describes this value.</param>
      <param name="propertyValues">Child values of this value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmStructuredValue.FindPropertyValue(System.String)">
      <summary> Retrieves the value corresponding to the given property name. Returns null if no such value exists. </summary>
      <returns>The requested value, or null if no such value exists.</returns>
      <param name="propertyName">The property that describes the value being found.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmStructuredValue.PropertyValues">
      <summary> Gets the property values of this structured value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmStructuredValue.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmTimeConstant">
      <summary> Represents an EDM time constant. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmTimeConstant.#ctor(Microsoft.Data.Edm.IEdmTemporalTypeReference,System.TimeSpan)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmTimeConstant" /> class. </summary>
      <param name="type">Type of the Time.</param>
      <param name="value">Time value represented by this value.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmTimeConstant.#ctor(System.TimeSpan)">
      <summary> Initializes a new instance of the <see cref="T:Microsoft.Data.Edm.Library.Values.EdmTimeConstant" /> class. </summary>
      <param name="value">Time value represented by this value.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmTimeConstant.ExpressionKind">
      <summary> Gets the kind of this expression. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmTimeConstant.Value">
      <summary> Gets the definition of this value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmTimeConstant.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Library.Values.EdmValue">
      <summary> Represents an EDM value. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Library.Values.EdmValue.#ctor(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Initializes a new instance of the EdmValue class. </summary>
      <param name="type">Type of the value.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmValue.Microsoft#Data#Edm#Values#IEdmDelayedValue#Value">
      <summary>Gets {insert text here}.</summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmValue.Type">
      <summary> Gets the type of this value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Library.Values.EdmValue.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Validation.EdmError">
      <summary>Represents a reportable error in EDM.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.EdmError.#ctor(Microsoft.Data.Edm.EdmLocation,Microsoft.Data.Edm.Validation.EdmErrorCode,System.String)">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Validation.EdmError" /> class.</summary>
      <param name="errorLocation">The location where the error occurred.</param>
      <param name="errorCode">An integer code representing the error.</param>
      <param name="errorMessage">A human readable message describing the error.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Validation.EdmError.ErrorCode">
      <summary>Gets an integer code representing the error.</summary>
      <returns>The code representing the error.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Validation.EdmError.ErrorLocation">
      <summary>Gets the location of the error in the file in which it occurred.</summary>
      <returns>The location of the error in the file in which it occurred.</returns>
    </member>
    <member name="P:Microsoft.Data.Edm.Validation.EdmError.ErrorMessage">
      <summary>Gets a human readable string describing the error.</summary>
      <returns>A human readable string describing the error.</returns>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.EdmError.ToString">
      <summary>Gets a string representation of the error.</summary>
      <returns>A string representation of the error.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Validation.EdmErrorCode">
      <summary> EdmLib validation error codes </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidErrorCodeValue">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.StreamTypeReferencesNotSupportedBeforeV3">
      <summary> References to EDM stream type are not supported before version 3.0. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.SpatialTypeReferencesNotSupportedBeforeV3">
      <summary> References to EDM spatial types are not supported before version 3.0. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.XmlError">
      <summary> An exception was thrown by the underlying xml reader. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.UnexpectedXmlNodeType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.UnexpectedXmlAttribute">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.UnexpectedXmlElement">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.TextNotAllowed">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EmptyFile">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.MissingAttribute">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidName">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.MissingType">
      <summary> An XML attribute or element representing EDM type is missing. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.AlreadyDefined">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidVersionNumber">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidBoolean">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadProperty">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidPropertyType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.PrecisionOutOfRange">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ScaleOutOfRange">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.NameTooLong">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidAssociation">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadNavigationProperty">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidKey">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InterfaceCriticalPropertyValueMustNotBeNull">
      <summary> The value of the property must not be null. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InterfaceCriticalKindValueMismatch">
      <summary> An object with an interface kind property does not implement the interface corresponding to the value of that property. For example this error will be reported for an object that implements <see cref="T:Microsoft.Data.Edm.IEdmType" /> interface with kind property reporting <see cref="T:Microsoft.Data.Edm.EdmTypeKind" />.Entity,  but does not implement <see cref="T:Microsoft.Data.Edm.IEdmEntityType" /> interface. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InterfaceCriticalKindValueUnexpected">
      <summary> The value of an interface kind property is not semantically valid. A semantically valid model must not contain elements of kind 'None'. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InterfaceCriticalEnumerableMustNotHaveNullElements">
      <summary> An enumeration property must not contain null elements. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InterfaceCriticalEnumPropertyValueOutOfRange">
      <summary> The value of the enum type property is out of range. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InterfaceCriticalNavigationPartnerInvalid">
      <summary> If property P1 is a navigation property and P2 is its parnter, then partner property of P2 must be P1. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InterfaceCriticalCycleInTypeHierarchy">
      <summary> A chain of base types is cyclic. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidMultiplicity">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidAction">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidOnDelete">
      <summary> An error occured processing the OnDelete element </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedComplexType">
      <summary> No complex type with that name exists. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidEndEntitySet">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.FunctionImportEntitySetExpressionIsInvalid">
      <summary> Function import specifies an entity set expression which is not supported in this context. Function import entity set expression can be either an entity set reference or a path starting with a function import parameter and traversing navigation properties. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidRoleInRelationshipConstraint">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidPropertyInRelationshipConstraint">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.TypeMismatchRelationshipConstraint">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidMultiplicityOfPrincipalEnd">
      <summary>  Invalid multiplicty of the principal end of a navigation. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.MismatchNumberOfPropertiesInRelationshipConstraint">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidMultiplicityOfDependentEnd">
      <summary>  Invalid multiplicty of the dependent end of a navigation. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.OpenTypesNotSupported">
      <summary> Open types are supported only in version 1.2 and after version 2.0 </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.VocabularyAnnotationsNotSupportedBeforeV3">
      <summary> Vocabulary annotations are not supported before EDM 3.0 </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.SameRoleReferredInReferentialConstraint">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EntityKeyMustBeScalar">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EntityKeyMustNotBeBinary">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EndWithManyMultiplicityCannotHaveOperationsSpecified">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EntitySetTypeHasNoKeys">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidConcurrencyMode">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ConcurrencyRedefinedOnSubtypeOfEntitySetType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.FunctionImportUnsupportedReturnType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ComposableFunctionImportCannotBeSideEffecting">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.FunctionImportReturnsEntitiesButDoesNotSpecifyEntitySet">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.FunctionImportEntityTypeDoesNotMatchEntitySet">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.FunctionImportSpecifiesEntitySetButDoesNotReturnEntityType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ComposableFunctionImportMustHaveReturnType">
      <summary> A composable function import must have return type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.SimilarRelationshipEnd">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.DuplicatePropertySpecifiedInEntityKey">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.NullableComplexTypeProperty">
      <summary>  Nullable complex Type not supported in version 1.0 and 2.0. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.KeyMissingOnEntityType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.SystemNamespaceEncountered">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidNamespaceName">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EnumMemberValueOutOfRange">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.DuplicateEntityContainerMemberName">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidAbstractComplexType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidPolymorphicComplexType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadAmbiguousElementBinding">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedPrimitiveType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadCyclicComplex">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadCyclicEntityContainer">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadCyclicEntity">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.TypeSemanticsCouldNotConvertTypeReference">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ConstructibleEntitySetTypeInvalidFromEntityTypeRemoval">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedEntityContainer">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedEntitySet">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedProperty">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadNonComputableAssociationEnd">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.NavigationPropertyTypeInvalidBecauseOfBadAssociation">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EntityMustHaveEntityBaseType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ComplexTypeMustHaveComplexBaseType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedFunction">
      <summary> Could not find a function with this name. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.RowTypeMustNotHaveBaseType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.AssociationSetEndRoleMustBelongToSetElementType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.KeyPropertyMustBelongToEntity">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ReferentialConstraintPrincipalEndMustBelongToAssociation">
      <summary> The principal end of a referential constraint must be one of the ends of the association that defined the referential constraint. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.DependentPropertiesMustBelongToDependentEntity">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.DeclaringTypeMustBeCorrect">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.FunctionsNotSupportedBeforeV2">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ValueTermsNotSupportedBeforeV3">
      <summary> Value terms are not supported before EDM 3.0 </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidNavigationPropertyType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.FailedToParseExternalAnnotations">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.UnderlyingTypeIsBadBecauseEnumTypeIsBad">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidAssociationSetEndSetWrongType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.OnlyInputParametersAllowedInFunctions">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.FunctionImportParameterIncorrectType">
      <summary> Unsupported function import parameter type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.RowTypeMustHaveProperties">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.DuplicateDependentProperty">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BindableFunctionImportMustHaveParameters">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.FunctionImportSideEffectingNotSupportedBeforeV3">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.FunctionImportComposableNotSupportedBeforeV3">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.FunctionImportBindableNotSupportedBeforeV3">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.MaxLengthOutOfRange">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.PathExpressionHasNoEntityContext">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidSrid">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidMaxLength">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidLong">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidInteger">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidAssociationSet">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidParameterMode">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedEntityType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidValue">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidBinary">
      <summary> Binary value is invalid. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidFloatingPoint">
      <summary> Floating point value is invalid. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidDateTime">
      <summary> DateTime value is invalid. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidDateTimeOffset">
      <summary> DateTimeOffset value is invalid. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidDecimal">
      <summary> Decimal value is invalid. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidGuid">
      <summary> Guid value is invalid. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidTypeKindNone">
      <summary> The type kind None is not semantically valid. A semantically valid model must not contain elements of type kind None. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidIfExpressionIncorrectNumberOfOperands">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EnumsNotSupportedBeforeV3">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EnumMemberTypeMustMatchEnumUnderlyingType">
      <summary />
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidIsTypeExpressionIncorrectNumberOfOperands">
      <summary> The IsType expression is invalid because it does not have 1 element. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidTypeName">
      <summary> The type name is not fully qualified and not a primitive. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidQualifiedName">
      <summary> The term name is not fully qualified. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.NoReadersProvided">
      <summary> No model was parsed because no XmlReaders were provided. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.NullXmlReader">
      <summary> Model could not be parsed because one of the XmlReaders was null. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.MaxMaxLengthCannotBeTrueWhileMaxLengthIsNotNull">
      <summary> IsMaxMaxLength cannot be true if MaxLength is non-null. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidElementAnnotation">
      <summary> ImmediateValueAnnotation is invalid as an element annotation. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidLabeledElementExpressionIncorrectNumberOfOperands">
      <summary> The LabeledElement expression is invalid because it does not have 1 element. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedLabeledElement">
      <summary> Could not find a LabeledElement with that name </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedEnumMember">
      <summary> Could not find a enum member with that name </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidAssertTypeExpressionIncorrectNumberOfOperands">
      <summary> The AssertType expression is invalid because it does not have 1 element. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedParameter">
      <summary> Could not find a Parameter with that name </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.NavigationPropertyWithRecursiveContainmentTargetMustBeOptional">
      <summary> A navigation property with <see cref="P:Microsoft.Data.Edm.IEdmNavigationProperty.ContainsTarget" /> = true must point to an optional target. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.NavigationPropertyWithRecursiveContainmentSourceMustBeFromZeroOrOne">
      <summary> If a navigation property has <see cref="P:Microsoft.Data.Edm.IEdmNavigationProperty.ContainsTarget" /> = true and the target entity type is the same as  the declaring type of the property, then the multiplicity of the source of navigation is Zero-Or-One. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.NavigationPropertyWithNonRecursiveContainmentSourceMustBeFromOne">
      <summary> If a navigation property has <see cref="P:Microsoft.Data.Edm.IEdmNavigationProperty.ContainsTarget" /> = true and the target entity type is defferent than the declaring type of the property, then the multiplicity of the source of navigation is One. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.NavigationPropertyContainsTargetNotSupportedBeforeV3">
      <summary> Navigation properties with <see cref="P:Microsoft.Data.Edm.IEdmNavigationProperty.ContainsTarget" /> setting are not supported before version 3.0. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ImpossibleAnnotationsTarget">
      <summary> The annotation target path cannot possibly refer to an annotable element. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.CannotAssertNullableTypeAsNonNullableType">
      <summary> A nullable type is not valid if a non-nullable type is required. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.CannotAssertPrimitiveExpressionAsNonPrimitiveType">
      <summary> The expression is a primitive constant, and cannot be valid for an non-primitive type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ExpressionPrimitiveKindNotValidForAssertedType">
      <summary> The primitive type is not valid for the requested type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.NullCannotBeAssertedToBeANonNullableType">
      <summary> Null is not valid in a non nullable expression. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ExpressionNotValidForTheAssertedType">
      <summary> The expression is not valid for the asserted type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.CollectionExpressionNotValidForNonCollectionType">
      <summary> A collection expression is not valid for a non-collection type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.RecordExpressionNotValidForNonStructuredType">
      <summary> A record expression is not valid for a non-structured type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.RecordExpressionMissingRequiredProperty">
      <summary> The record expression does not have all of the properties required for the specified type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.RecordExpressionHasExtraProperties">
      <summary> The record expression's type is not open, but the record expression has extra properties. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.DuplicateAnnotation">
      <summary> Target has multiple annotations with the same term and same qualifier. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.IncorrectNumberOfArguments">
      <summary> Function application has wrong number of arguments for the function being applied. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.DuplicateAlias">
      <summary> Is it invalid to have multiple using statements with the same alias in a single schema element. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ReferencedTypeMustHaveValidName">
      <summary> A model cannot be serialized to CSDL if it has references to types without fully qualified names. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.SingleFileExpected">
      <summary> The model could not be serialized because multiple schemas were produced and only a single output stream was found. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.UnknownEdmxVersion">
      <summary> The Edmx version is not valid. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.UnknownEdmVersion">
      <summary> The EdmVersion is not valid. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.NoSchemasProduced">
      <summary> Nothing was written because no schemas were produced. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.DuplicateEntityContainerName">
      <summary> Model has multiple entity containers with the same name. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.ContainerElementContainerNameIncorrect">
      <summary> The container name of a container element must be the full name of the container entity container. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.PrimitiveConstantExpressionNotValidForNonPrimitiveType">
      <summary> A primitive constant expression is not valid for a non-primitive type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.IntegerConstantValueOutOfRange">
      <summary> The value of the integer constant is out of range for the asserted type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.StringConstantLengthOutOfRange">
      <summary> The length of the string constant is too large for the asserted type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BinaryConstantLengthOutOfRange">
      <summary> The length of the binary constant is too large for the asserted type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidFunctionImportParameterMode">
      <summary> None is not a valid mode for a function import parameter. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.TypeMustNotHaveKindOfNone">
      <summary> A type without other errors must not have kind of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.PrimitiveTypeMustNotHaveKindOfNone">
      <summary> A primitive type without other errors must not have kind of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.PropertyMustNotHaveKindOfNone">
      <summary> A property without other errors must not have kind of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.TermMustNotHaveKindOfNone">
      <summary> A term without other errors must not have kind of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.SchemaElementMustNotHaveKindOfNone">
      <summary> A schema element without other errors must not have kind of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EntityContainerElementMustNotHaveKindOfNone">
      <summary> An entity container element without other errors must not have kind of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BinaryValueCannotHaveEmptyValue">
      <summary> A binary value must have content. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EntitySetCanOnlyBeContainedByASingleNavigationProperty">
      <summary> There can only be a single navigation property mapping with containment that targets a particular entity set. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InconsistentNavigationPropertyPartner">
      <summary> The navigation properties partner does not point back to the correct type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EntitySetCanOnlyHaveSingleNavigationPropertyWithContainment">
      <summary> An entity set can only have one navigation property with containment. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EntitySetNavigationMappingMustBeBidirectional">
      <summary> If a navigation property is traversed from an entity set, and then it's partner is traversed from the target of the first mapping, the destination should be the originating entity set. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.DuplicateNavigationPropertyMapping">
      <summary> There can only be a single mapping from a given EntitySet with a particular navigation property. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.AllNavigationPropertiesMustBeMapped">
      <summary> An entity set must have a mapping for all of the navigation properties in its element type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.TypeAnnotationMissingRequiredProperty">
      <summary> Type annotation does not have a property binding for all required properties. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.TypeAnnotationHasExtraProperties">
      <summary> Type annotation has a property binding for a non-existant property and its type is not open. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidTime">
      <summary> Time value is invalid. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidPrimitiveValue">
      <summary> The primitive type is invalid. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.EnumMustHaveIntegerUnderlyingType">
      <summary> An Enum type must have an underlying type of integer. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedTerm">
      <summary> Could not find a term with this name. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadPrincipalPropertiesInReferentialConstraint">
      <summary> The principal properties of a referential constraint must match the key of the referential constraint. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.DuplicateDirectValueAnnotationFullName">
      <summary> A direct value annotation with the same name and namespace already exists. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.NoEntitySetsFoundForType">
      <summary> AssociationSetEnd cannot infer an entity set because no set exists of the given type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.CannotInferEntitySetWithMultipleSetsPerType">
      <summary> AssociationSetEnd cannot infer an entity set because more than one set exists of the given type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidEntitySetPath">
      <summary> Invalid entity set path. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.InvalidEnumMemberPath">
      <summary> Invalid enum member path. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.QualifierMustBeSimpleName">
      <summary> An annotation qualifier must be a simple name. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedEnumType">
      <summary> Enum type could not be resolved. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.EdmErrorCode.BadUnresolvedTarget">
      <summary> Could not find a target with this name. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Validation.EdmValidator">
      <summary>Represents a collection of validation methods.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.EdmValidator.Validate(Microsoft.Data.Edm.IEdmModel,Microsoft.Data.Edm.Validation.ValidationRuleSet,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.EdmError}@)">
      <summary>Validates the <see cref="T:Microsoft.Data.Edm.IEdmModel" /> and all of its properties given certain version.</summary>
      <returns>True if model is valid, otherwise false.</returns>
      <param name="root">The root of the model to be validated.</param>
      <param name="ruleSet">The custom rule set to validate against.</param>
      <param name="errors">The errors encountered while validating the model.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.EdmValidator.Validate(Microsoft.Data.Edm.IEdmModel,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.EdmError}@)">
      <summary>Validates the <see cref="T:Microsoft.Data.Edm.IEdmModel" /> and all of its properties using the current version of the model. If the model has no version, <see cref="T:Microsoft.Data.Edm.IEdmModel" /> is used.</summary>
      <returns>True if model is valid, otherwise false.</returns>
      <param name="root">The root of the model to be validated.</param>
      <param name="errors">The errors encountered while validating the model.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.EdmValidator.Validate(Microsoft.Data.Edm.IEdmModel,System.Version,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.EdmError}@)">
      <summary>Validates the <see cref="T:Microsoft.Data.Edm.IEdmModel" /> and all of its properties given certain version.</summary>
      <returns>True if model is valid, otherwise false.</returns>
      <param name="root">The root of the model to be validated.</param>
      <param name="version">The version of EDM to validate against.</param>
      <param name="errors">The errors encountered while validating the model.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Validation.ExpressionTypeChecker">
      <summary> Collection of extension methods to assert that an expression is of the required type. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ExpressionTypeChecker.TryAssertType(Microsoft.Data.Edm.Expressions.IEdmExpression,Microsoft.Data.Edm.IEdmTypeReference,System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.EdmError}@)">
      <summary> Determines if the type of an expression is compatible with the provided type </summary>
      <returns>A value indicating whether the expression is valid for the given type or not.</returns>
      <param name="expression">The expression to assert the type of.</param>
      <param name="type">The type to assert the expression as.</param>
      <param name="discoveredErrors">Errors produced if the expression does not match the specified type.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Validation.ObjectLocation">
      <summary> Defines an object as a location of itself. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Validation.ObjectLocation.Object">
      <summary> Gets the object. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ObjectLocation.ToString">
      <summary> Gets a string representation of the location. </summary>
      <returns>A string representation of the location.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Validation.ValidationContext">
      <summary>Specifies a context that records errors reported by validation rules.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationContext.AddError(Microsoft.Data.Edm.EdmLocation,Microsoft.Data.Edm.Validation.EdmErrorCode,System.String)">
      <summary>Adds an error with the validation context.</summary>
      <param name="location">The location of the error.</param>
      <param name="errorCode">The value representing the error.</param>
      <param name="errorMessage">The message text describing the error.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationContext.AddError(Microsoft.Data.Edm.Validation.EdmError)">
      <summary>Adds an error with the validation context.</summary>
      <param name="error">The error to register.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationContext.IsBad(Microsoft.Data.Edm.IEdmElement)">
      <summary> Method returns true if the element is known to have structural errors associated with it. </summary>
      <returns>True if the element has structural errors associated with it.</returns>
      <param name="element">The element to test.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Validation.ValidationContext.Model">
      <summary>Gets the model being validated.</summary>
      <returns>The model being validated.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Validation.ValidationExtensionMethods">
      <summary> Contains IsBad() and Errors() extension methods. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationExtensionMethods.Errors(Microsoft.Data.Edm.IEdmElement)">
      <summary> Gets the errors, if any, that belong to this element or elements that this element contains. For example errors for a structural type include the errors of the type itself and errors of its declared properties. The method does not analyze elements referenced by this element. For example errors of a property do not include errors from its type. </summary>
      <returns>Any errors that belong to this element or elements that element contains.</returns>
      <param name="element">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationExtensionMethods.IsBad(Microsoft.Data.Edm.IEdmElement)">
      <summary> Returns true if this element contains errors returned by the &lt;see cref="M:Microsoft.Data.Edm.Validation.ValidationExtensionMethods.Errors(Microsoft.Data.Edm.IEdmElement)" /&gt; method. </summary>
      <returns>This element is an invalid element.</returns>
      <param name="element">Reference to the calling object.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationExtensionMethods.TypeErrors(Microsoft.Data.Edm.IEdmTypeReference)">
      <summary> Gets the errors, if any, that belong to this type reference or its definition. </summary>
      <returns>Any errors that belong to this type reference or its definition.</returns>
      <param name="type">The type reference.</param>
    </member>
    <member name="T:Microsoft.Data.Edm.Validation.ValidationRule">
      <summary>Represents a semantic validation rule.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationRule.#ctor">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Validation.ValidationRule" /> class.</summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Validation.ValidationRule`1">
      <summary>Represents a validation rule that is valid for a specific type.</summary>
      <typeparam name="TItem">The type that the rule is valid for.</typeparam>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationRule`1.#ctor(System.Action`2)"></member>
    <member name="T:Microsoft.Data.Edm.Validation.ValidationRules">
      <summary> Built in Edm validation rules </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.BinaryTypeReferenceBinaryMaxLengthNegative">
      <summary> Validates that the max length of a binary type is not negative. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.BinaryTypeReferenceBinaryMaxMaxLengthNotValidForMaxLength">
      <summary> Validates that IsMaxMaxLength cannot be true if MaxLength is non-null. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.CollectionExpressionAllElementsCorrectType">
      <summary> Validates that all properties of a collection expression are of the correct type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.ComplexTypeInvalidAbstractComplexType">
      <summary> Validates that a complex type is not abstract. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.ComplexTypeInvalidPolymorphicComplexType">
      <summary> Validates that a complex type does not inherit. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.ComposableFunctionImportMustHaveReturnType">
      <summary> Validates that if a function import is composable, it must have a return type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.DecimalTypeReferencePrecisionOutOfRange">
      <summary> Validates that the precision is between 0 and the max precision of the decimal type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.DecimalTypeReferenceScaleOutOfRange">
      <summary> Validates that the scale is between 0 and the precision of the decimal type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.ElementDirectValueAnnotationFullNameMustBeUnique">
      <summary> Validates that no direct value annotations share the same name and namespace. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntityContainerAllElementsHaveCorrectContainerName">
      <summary> The container name of an entity container element must match the full name of the containing entity container. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntityContainerDuplicateEntityContainerMemberName">
      <summary> Validates that there are no duplicate names in an entity container. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntityContainerElementMustNotHaveKindOfNone">
      <summary> An entity container element without other errors must not have kind of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntityReferenceTypeInaccessibleEntityType">
      <summary> Validates that the entity type wrapped in this entity reference can be found through the model being validated. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntitySetCanOnlyBeContainedByASingleNavigationProperty">
      <summary> Validates that an entity set can only have a single navigation property targetting it that has Contains set to true. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntitySetInaccessibleEntityType">
      <summary> Validates that the entity type of an entity set can be found from the model being validated. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntitySetNavigationMappingMustBeBidirectional">
      <summary> Validates that if a navigation property is traversed to another entity set, and then the navigation properties partner is traversed, the destination will be the source entity set. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntitySetNavigationPropertyMappingsMustBeUnique">
      <summary> Validates that no navigation property is mapped to two different entity sets. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntitySetTypeHasNoKeys"></member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntityTypeDuplicatePropertyNameSpecifiedInEntityKey">
      <summary> Validates that there are not duplicate properties in an entity key. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntityTypeEntityKeyMustBeScalar">
      <summary> Validates that all parts of an entity key are scalar. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntityTypeEntityKeyMustNotBeBinaryBeforeV2">
      <summary> Validates that no part of an entity key is a binary primitive type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntityTypeInvalidKeyKeyDefinedInBaseClass">
      <summary> Validates that a key is not defined if there is already a key in the base type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntityTypeInvalidKeyNullablePart">
      <summary> Validates that no part of an entity key is nullable. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntityTypeKeyMissingOnEntityType">
      <summary> Validates that the entity type has a key. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EntityTypeKeyPropertyMustBelongToEntity">
      <summary> Validates that all properties in the key of an entity blong to that entity. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EnumMemberValueMustHaveSameTypeAsUnderlyingType"></member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EnumMustHaveIntegerUnderlyingType">
      <summary> Raises an error if the underlying type of an enum type is not an integer type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EnumTypeEnumMemberNameAlreadyDefined">
      <summary> Validates that there are not duplicate enum members in an enum. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.EnumTypeEnumsNotSupportedBeforeV3">
      <summary> Raises an error if an enum type is found. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionApplicationExpressionParametersMatchAppliedFunction">
      <summary> Validates the types of a function application are correct. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionBaseParameterNameAlreadyDefinedDuplicate">
      <summary> Validates that a function does not have multiple parameters with the same name. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionImportBindableFunctionImportMustHaveParameters">
      <summary> Validates that if a function is bindable, it must have parameters. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionImportComposableFunctionImportCannotBeSideEffecting">
      <summary> Validates that if a function is composable, it is not also sideeffecting. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionImportEntitySetExpressionIsInvalid">
      <summary> Validates that the entity set of a function import is defined using a path or an entity set reference expression. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionImportEntityTypeDoesNotMatchEntitySet"></member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionImportIsBindableNotSupportedBeforeV3">
      <summary> Validates that a function is not bindable. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionImportIsComposableNotSupportedBeforeV3">
      <summary> Validates that a function import is not composable. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionImportIsSideEffectingNotSupportedBeforeV3">
      <summary> Validates that a function import is not sideeffecting. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionImportParametersCannotHaveModeOfNone">
      <summary> Validates that no function import parameters have mode of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionImportParametersIncorrectTypeBeforeV3">
      <summary> Validates that the type of a function imports parameter is correct. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionImportReturnEntitiesButDoesNotSpecifyEntitySet"></member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionImportUnsupportedReturnTypeAfterV1">
      <summary> Validates that a function import has an allowed return type </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionImportUnsupportedReturnTypeV1"></member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionOnlyInputParametersAllowedInFunctions">
      <summary> Validates that no function parameters are output parameters. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.FunctionsNotSupportedBeforeV2"></member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.IfExpressionAssertCorrectTestType">
      <summary> Validates that an if expression has a boolean condition. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.ImmediateValueAnnotationElementAnnotationHasNameAndNamespace">
      <summary> Validates that an immediate value annotation that is flagged to be serialized as an element can be serialized safely. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.ImmediateValueAnnotationElementAnnotationIsValid">
      <summary> Validates that an immediate value annotation has a name and a namespace. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.ModelDuplicateEntityContainerName">
      <summary> Validates that there are not duplicate properties in an entity key. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.ModelDuplicateSchemaElementName">
      <summary> Validates every schema element in the current model is unique across all referenced models. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.ModelDuplicateSchemaElementNameBeforeV3">
      <summary> Validates every schema element in the current model (except for entity containers) is unique across all referenced models. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NamedElementNameIsNotAllowed">
      <summary> Validates that an element name matches the allowed pattern of names according to the CSDL spec. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NamedElementNameIsTooLong">
      <summary> Validates that an element name is not too long according to the CSDL spec. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NamedElementNameMustNotBeEmptyOrWhiteSpace">
      <summary> Validates that a name is not empty or whitespace. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyContainsTargetNotSupportedBeforeV3">
      <summary> Validates that <see cref="P:Microsoft.Data.Edm.IEdmNavigationProperty.ContainsTarget" /> is not set prior to V3. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyCorrectType">
      <summary> Validates that the type of a navigation property corresponds to the other end of the association and the multiplicity of the other end. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyDependentEndMultiplicity">
      <summary> Validates that if the dependent properties are equivalent to the key of the dependent end, the multiplicity of the dependent end cannot be 1 Validates multiplicity of the dependent end according to the following rules: 0..1, 1 - if dependent properties represent the dependent end key.       * - if dependent properties don't represent the dependent end key. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyDependentPropertiesMustBelongToDependentEntity">
      <summary> Validates that all dependent properties of a navigation property belong to the dependent entity type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyDuplicateDependentProperty">
      <summary> Validates that the dependent properties of a navigation property contain no duplicates. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyEndWithManyMultiplicityCannotHaveOperationsSpecified">
      <summary> Validates that the navigation property does not have both a multiplicity of many and an OnDelete operation. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyInvalidOperationMultipleEndsInAssociation">
      <summary> Validates that only one end of an association has an OnDelete operation. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyInvalidToPropertyInRelationshipConstraintBeforeV2">
      <summary> Validates that all dependent properties are a subset of the dependent entity types key. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyPartnerMustPointBackToSourceType">
      <summary> Validates that the target of a navigation property's partner is the declaring type of the original. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyPrincipalEndMultiplicity">
      <summary> Validates multiplicity of the principal end: 0..1 - if some dependent properties are nullable,     1 - if some dependent properties are not nullable.    * - not allowed. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyTypeMismatchRelationshipConstraint">
      <summary> Validates that each pair of properties between the dependent properties and the principal ends key are of the same type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyWithNonRecursiveContainmentSourceMustBeFromOne">
      <summary> Validates that if a navigation property has <see cref="P:Microsoft.Data.Edm.IEdmNavigationProperty.ContainsTarget" /> = true and the target entity type is defferent than the declaring type of the property, then the multiplicity of the source of navigation is One. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyWithRecursiveContainmentSourceMustBeFromZeroOrOne">
      <summary> Validates that if a navigation property has <see cref="P:Microsoft.Data.Edm.IEdmNavigationProperty.ContainsTarget" /> = true and the target entity type is the same as  the declaring type of the property, then the multiplicity of the source of navigation is Zero-Or-One. This depends on there being a targetting cycle. Because of the rule <see cref="F:Microsoft.Data.Edm.Validation.ValidationRules.EntitySetNavigationMappingMustBeBidirectional" />, we know that either this is always true, or there will be an error </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.NavigationPropertyWithRecursiveContainmentTargetMustBeOptional">
      <summary> Validates that if a navigation property has <see cref="P:Microsoft.Data.Edm.IEdmNavigationProperty.ContainsTarget" /> = true and the target entity type is the same as  the declaring type of the property, then the multiplicity of the target of navigation is 0..1 or Many. This depends on there being a targetting cycle. Because of the rule <see cref="F:Microsoft.Data.Edm.Validation.ValidationRules.EntitySetNavigationMappingMustBeBidirectional" />, we know that either this is always true, or there will be an error </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.OpenTypesNotSupported">
      <summary> Open types are supported only in version 1.2 and after version 2.0. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.PrimitiveTypeMustNotHaveKindOfNone">
      <summary> A primtive type without other errors must not have kind of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.PrimitiveValueValidForType">
      <summary> Validates that if a primitive value declares a type, the value is acceptable for the type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.PropertyMustNotHaveKindOfNone">
      <summary> A property without other errors must not have kind of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.PropertyValueBindingValueIsCorrectType">
      <summary> Validates that the value of a property value binding is the correct type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.RecordExpressionPropertiesMatchType">
      <summary> Validates that if a value record expression declares a type, the property types are correct. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.RowTypeBaseTypeMustBeNull">
      <summary> Validates that a row type does not have a base type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.RowTypeMustContainProperties">
      <summary> Validates that a row type contains at least one property. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.SchemaElementMustNotHaveKindOfNone">
      <summary> A schema element without other errors must not have kind of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.SchemaElementNamespaceIsNotAllowed">
      <summary> Validates that an element namespace matches the allowed pattern of namespaces according to the CSDL spec. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.SchemaElementNamespaceIsTooLong">
      <summary> Validates that an element namespace is not too long according to the CSDL spec. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.SchemaElementNamespaceMustNotBeEmptyOrWhiteSpace">
      <summary> Validates that an element namespace is not empty or whitespace. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.SchemaElementSystemNamespaceEncountered">
      <summary> Validates that an element namespace is not a reserved system namespace. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.SpatialTypeReferencesNotSupportedBeforeV3">
      <summary> References to EDM spatial types are not supported before version 3.0. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.StreamTypeReferencesNotSupportedBeforeV3">
      <summary> References to EDM stream type are not supported before version 3.0. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.StringTypeReferenceStringMaxLengthNegative">
      <summary> Validates that the max length of a string is not negative. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.StringTypeReferenceStringMaxMaxLengthNotValidForMaxLength">
      <summary> Validates that IsMaxMaxLength cannot be true if MaxLength is non-null. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.StructuralPropertyInvalidPropertyType">
      <summary> Validates that the property is of an allowed type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.StructuralPropertyInvalidPropertyTypeConcurrencyMode">
      <summary> Validates that if the concurrency mode of a property is fixed, the type is primitive. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.StructuralPropertyNullableComplexType">
      <summary> Validates that any property with a complex type is not nullable. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.StructuredTypeBaseTypeMustBeSameKindAsDerivedKind">
      <summary> Validates that the base type of a complex type is complex, and the base type of an entity type is an entity. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.StructuredTypeInaccessibleBaseType">
      <summary> Validates that the base type of a structured type can be found from the model being validated. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.StructuredTypeInvalidMemberNameMatchesTypeName">
      <summary> Validates that a type does not have a property with the same name as that type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.StructuredTypePropertiesDeclaringTypeMustBeCorrect">
      <summary> Validates that the declaring type of a property contains that property. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.StructuredTypePropertyNameAlreadyDefined">
      <summary> Validates that there are not duplicate properties in a type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.TemporalTypeReferencePrecisionOutOfRange">
      <summary> Validates that the precision is between 0 and the max precision of the temporal type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.TermMustNotHaveKindOfNone">
      <summary> A term without other errors must not have kind of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.TypeAnnotationAssertMatchesTermType">
      <summary> Validates that a type annotation implements its term type properly. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.TypeAnnotationInaccessibleTerm">
      <summary> Validates that a vocabulary annotations term can be found through the model containing the annotation. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.TypeMustNotHaveKindOfNone">
      <summary> A type without other errors must not have kind of none. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.TypeReferenceInaccessibleSchemaType">
      <summary> Validates that a type reference refers to a type that can be found through the model being validated. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.ValueAnnotationAssertCorrectExpressionType">
      <summary> Validates that if a value annotation declares a type, the expression for that annotation has the correct type. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.ValueAnnotationInaccessibleTerm">
      <summary> Validates that a vocabulary annotations term can be found through the model containing the annotation. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.ValueTermsNotSupportedBeforeV3">
      <summary> Value terms are not supported before EDM 3.0. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.VocabularyAnnotatableNoDuplicateAnnotations">
      <summary> Validates that there are no annotations that share the same term and qualifier. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.VocabularyAnnotationInaccessibleTarget">
      <summary> Validates that a vocabulary annotations target can be found through the model containing the annotation. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.VocabularyAnnotationQualifierMustBeSimpleName">
      <summary> Qualifier must be simple name. </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Validation.ValidationRules.VocabularyAnnotationsNotSupportedBeforeV3">
      <summary> Vocabulary annotations are not supported before EDM 3.0. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Validation.ValidationRuleSet">
      <summary>Provides a set of rules to run during validation.</summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationRuleSet.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.ValidationRule})">
      <summary>Creates a new instance of the <see cref="T:Microsoft.Data.Edm.Validation.ValidationRuleSet" /> class.</summary>
      <param name="rules">The rules to be contained in this ruleset.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationRuleSet.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.ValidationRule},System.Collections.Generic.IEnumerable{Microsoft.Data.Edm.Validation.ValidationRule})">
      <summary> Initializes a new instance of the ValidationRuleSet class. </summary>
      <param name="baseSet">Ruleset whose rules should be contained in this set.</param>
      <param name="newRules">Additional rules to add to the set.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationRuleSet.GetEdmModelRuleSet(System.Version)">
      <summary>Gets the default validation ruleset for the given version.</summary>
      <returns>The set of rules to validate that the model conforms to the given version.</returns>
      <param name="version">The EDM version being validated.</param>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationRuleSet.GetEnumerator">
      <summary>Gets all of the rules in this ruleset.</summary>
      <returns>All of the rules in this ruleset.</returns>
    </member>
    <member name="M:Microsoft.Data.Edm.Validation.ValidationRuleSet.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gets all of the rules in this ruleset. </summary>
      <returns>All of the rules in this ruleset.</returns>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.EdmValueKind">
      <summary> Defines Edm values </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.Binary">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmBinaryValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.Boolean">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmBooleanValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.Collection">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmCollectionValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.DateTimeOffset">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmDateTimeOffsetValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.DateTime">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmDateTimeValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.Decimal">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmDecimalValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.Enum">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmEnumValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.Floating">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmFloatingValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.Guid">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmGuidValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.Integer">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmIntegerValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.Null">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmNullValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.String">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmStringValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.Structured">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmStructuredValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.Time">
      <summary> Represents a value implementing <see cref="T:Microsoft.Data.Edm.Values.IEdmTimeValue" />.  </summary>
    </member>
    <member name="F:Microsoft.Data.Edm.Values.EdmValueKind.None">
      <summary> Represents a value with an unknown or error kind. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmBinaryValue">
      <summary> Represents an EDM binary value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmBinaryValue.Value">
      <summary> Gets the definition of this binary value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmBooleanValue">
      <summary> Represents an EDM boolean value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmBooleanValue.Value">
      <summary> Gets a value indicating whether the value of this boolean value is true or false. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmCollectionValue">
      <summary> Represents an EDM collection value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmCollectionValue.Elements">
      <summary> Gets the values stored in this collection. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmDateTimeOffsetValue">
      <summary> Represents an EDM datetime with offset value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmDateTimeOffsetValue.Value">
      <summary> Gets the definition of this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmDateTimeValue">
      <summary> Represents an EDM datetime value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmDateTimeValue.Value">
      <summary> Gets the definition of this datetime value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmDecimalValue">
      <summary> Represents an EDM decimal value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmDecimalValue.Value">
      <summary> Gets the definition of this decimal value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmDelayedValue">
      <summary> Represents a lazily computed value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmDelayedValue.Value">
      <summary> Gets the data stored in this value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmEnumValue">
      <summary> Represents an EDM enumeration type value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmEnumValue.Value">
      <summary> Gets the underlying type value of the enumeration type. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmFloatingValue">
      <summary> Represents an EDM floating point value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmFloatingValue.Value">
      <summary> Gets the definition of this floating value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmGuidValue">
      <summary> Represents an EDM integer value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmGuidValue.Value">
      <summary> Gets the definition of this guid value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmIntegerValue">
      <summary> Represents an EDM integer value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmIntegerValue.Value">
      <summary> Gets the definition of this integer value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmNullValue"></member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmPrimitiveValue"></member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmPropertyValue">
      <summary> Represents a value of an EDM property. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmPropertyValue.Name">
      <summary> Gets the name of the property this value is associated with. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmStringValue">
      <summary> Represents an EDM string value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmStringValue.Value">
      <summary> Gets the definition of this string value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmStructuredValue">
      <summary> Represents an EDM structured value. </summary>
    </member>
    <member name="M:Microsoft.Data.Edm.Values.IEdmStructuredValue.FindPropertyValue(System.String)">
      <summary> Finds the value corresponding to the provided property name. </summary>
      <returns>The found property, or null if no property was found.</returns>
      <param name="propertyName">Property to find the value of.</param>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmStructuredValue.PropertyValues">
      <summary> Gets the property values of this structured value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmTimeValue">
      <summary> Represents an EDM time value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmTimeValue.Value">
      <summary> Gets the definition of this time value. </summary>
    </member>
    <member name="T:Microsoft.Data.Edm.Values.IEdmValue">
      <summary> Represents an EDM value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmValue.Type">
      <summary> Gets the type of this value. </summary>
    </member>
    <member name="P:Microsoft.Data.Edm.Values.IEdmValue.ValueKind">
      <summary> Gets the kind of this value. </summary>
    </member>
  </members>
</doc>