﻿MICROSOFT ENTERPRISE LIBRARY
EXCEPTION HANDLING APPLICATION BLOCK
6.0.1304.0

Summary: The Exception Handling Application Block helps developers, architects and policy makers implement common design patterns and create a consistent strategy for processing exceptions that occur in an application or at layer boundaries. It provides a selection of plug-in exception handlers and formatters that you can use, and is extensible so you can even create your own custom implementations. You can use the block when you want to implement exception shielding, modify exceptions in various ways, or chain exceptions (for example, by logging an exception and then passing it to another layer of your application). The configurable approach means that administrators can change the behavior of the exception management mechanism simply by editing the application configuration without requiring any changes to the code, recompiling, or redeployment.

The most up-to-date version of the release notes and known issues is available online:
http://aka.ms/el6release


Microsoft patterns & practices
http://microsoft.com/practices
