<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Practices.EnterpriseLibrary.ExceptionHandling</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.AddExceptionHandlingSettingsCommandTitle">
            <summary>
              Looks up a localized string similar to Add Exception Handling Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.CustomHandlerDataAddCommand">
            <summary>
              Looks up a localized string similar to Custom Handler (using type picker).
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.CustomHandlerDataDescription">
            <summary>
              Looks up a localized string similar to An Exception Handler implemented as a custom class added to Enterprise Library..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.CustomHandlerDataDisplayName">
            <summary>
              Looks up a localized string similar to Custom Exception Handler.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.CustomHandlerDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Custom Exception Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.CustomHandlerDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.CustomHandlerDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Custom Exception Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.CustomHandlerDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionHandlerDataDescription">
             <summary>
               Looks up a localized string similar to Handles an exception passed to the block and performs an action on it, such as wrapping or logging it, 
            and then passes it on to the next exception handler or returns it to the caller..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionHandlerDataDisplayName">
            <summary>
              Looks up a localized string similar to Exception Handler.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionHandlerDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Exception Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionHandlerDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionHandlerDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Exception Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionHandlerDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionHandlingSettingsDescription">
            <summary>
              Looks up a localized string similar to Configuration settings for the Exception Handling Application Block..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionHandlingSettingsDisplayName">
            <summary>
              Looks up a localized string similar to Exception Handling Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionHandlingSettingsExceptionPoliciesDescription">
            <summary>
              Looks up a localized string similar to Configuration settings for all Exception Handling Policies..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionHandlingSettingsExceptionPoliciesDisplayName">
            <summary>
              Looks up a localized string similar to Policies.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionPolicyDataDescription">
             <summary>
               Looks up a localized string similar to A policy defines the exceptions it will handle, and the set of Exception Handlers that it will execute when 
            one of the specified exceptions is passed to the block..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionPolicyDataDisplayName">
            <summary>
              Looks up a localized string similar to Policy.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionPolicyDataExceptionTypesDescription">
            <summary>
              Looks up a localized string similar to The Exception types configured for the Exception Handling Block..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionPolicyDataExceptionTypesDisplayName">
            <summary>
              Looks up a localized string similar to Exception Types.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionPolicyDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Exception Handling Policy..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionPolicyDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionTypeDataDescription">
             <summary>
               Looks up a localized string similar to Each Exception Type configured for a policy can specify a series of Exception Handlers that will 
            execute when an exception of the specified type is sent to the block for handling by this policy..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionTypeDataDisplayName">
            <summary>
              Looks up a localized string similar to Exception Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionTypeDataExceptionHandlersDescription">
            <summary>
              Looks up a localized string similar to The Exception Handlers configured for this Exception type..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionTypeDataExceptionHandlersDisplayName">
            <summary>
              Looks up a localized string similar to Handlers.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionTypeDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Exception..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionTypeDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionTypeDataPostHandlingActionDescription">
             <summary>
               Looks up a localized string similar to The action the Exception Handling Block will take after executing all of the handlers for this Exception type.  
            None means that the block will return false to the application allowing it to resume execution. 
            NotifyRethrow means that the block will return true to the application, which should re-throw the original exception.
            ThrowNewException means that the block will throw the exception that exists after the final handler runs. 
            However, if you use the overload of the HandleException method that takes [rest of string was truncated]&quot;;.
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionTypeDataPostHandlingActionDisplayName">
            <summary>
              Looks up a localized string similar to Post handling action.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionTypeDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Exception..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ExceptionTypeDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataDescription">
             <summary>
               Looks up a localized string similar to Replaces the exception sent to the block with another exception type. 
            The message can be specified for the replacement exception type..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataDisplayName">
            <summary>
              Looks up a localized string similar to Replace Handler.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataExceptionMessageDescription">
            <summary>
              Looks up a localized string similar to The message for the replacement exception..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataExceptionMessageDisplayName">
            <summary>
              Looks up a localized string similar to Exception Message.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataExceptionMessageResourceNameDescription">
            <summary>
              Looks up a localized string similar to The key in an external resource file for the localized string to use as the message for the replacement exception. .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataExceptionMessageResourceNameDisplayName">
            <summary>
              Looks up a localized string similar to Message Resource Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataExceptionMessageResourceTypeDescription">
            <summary>
              Looks up a localized string similar to The type of the external resource file containing the localized string to use as the message for the replacement exception. .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataExceptionMessageResourceTypeDisplayName">
            <summary>
              Looks up a localized string similar to Message Resource Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Replace Exception Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataReplaceExceptionTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the replacement exception..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataReplaceExceptionTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Replace Exception Type .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Replace Exception Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.ReplaceHandlerDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataDescription">
             <summary>
               Looks up a localized string similar to Wraps the exception sent to the block as the inner exception of a new exception type. 
            The message can be specified for the wrapping exception type..
             </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataDisplayName">
            <summary>
              Looks up a localized string similar to Wrap Handler.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataExceptionMessageDescription">
            <summary>
              Looks up a localized string similar to The message for the wrapping exception..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataExceptionMessageDisplayName">
            <summary>
              Looks up a localized string similar to Exception Message.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataExceptionMessageResourceNameDescription">
            <summary>
              Looks up a localized string similar to The key in an external resource file for the localized string to use as the message for the wrapping exception. .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataExceptionMessageResourceNameDisplayName">
            <summary>
              Looks up a localized string similar to Message Resource Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataExceptionMessageResourceTypeDescription">
            <summary>
              Looks up a localized string similar to The type of the external resource file containing the localized string to use as the message for the wrapping exception. .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataExceptionMessageResourceTypeDisplayName">
            <summary>
              Looks up a localized string similar to Message Resource Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataNameDescription">
            <summary>
              Looks up a localized string similar to The name of the Wrap Exception Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataNameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the Wrap Exception Handler..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataWrapExceptionTypeNameDescription">
            <summary>
              Looks up a localized string similar to The fully qualified type name of the wrapping exception..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.DesignResources.WrapHandlerDataWrapExceptionTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Wrap Exception Type.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ExceptionHandlingConfigurationSourceBuilderExtensions">
            <summary>
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceBuilder"/> extensions to support creation of exception handling configuration sections.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ExceptionHandlingConfigurationSourceBuilderExtensions.ConfigureExceptionHandling(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSourceBuilder)">
            <summary>
            Main entry point to configuration a <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlingSettings"/> section.
            </summary>
            <param name="configurationSourceBuilder">The builder interface to extend.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationGivenPolicyWithName">
            <summary>
            Defines an exception policy with a given name.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationGivenPolicyWithName.GivenPolicyWithName(System.String)">
            <summary>
            Defines new policy with a given name.
            </summary>
            <param name="name">Name of policy</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationForExceptionType">
            <summary>
            Fluent interface extensions for configuring an exception type on a <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy"/>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationForExceptionType.ForExceptionType(System.Type)">
            <summary>
            The <see cref="T:System.Exception"/> handled under the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy"/>.
            </summary>
            <param name="exceptionType">The type of <see cref="T:System.Exception"/> handled for this policy.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationForExceptionType.ForExceptionType``1">
            <summary>
            The <see cref="T:System.Exception"/> handled under the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy"/>.
            </summary>
            <typeparam name="T">The type of <see cref="T:System.Exception"/> handled for this policy.</typeparam>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationAddExceptionHandlers">
            <summary>
            This interface provides the extension point for handlers that provide a fluent configuration interface.
            </summary>
            <remarks>
            Handlers providing a fluent interface should provide extension methods to this interface.
            <example>
             public static class ReplaceWithHandlerLoggingConfigurationSourceBuilderExtensions
             {
                public static IExceptionConfigurationReplaceWithProvider ReplaceWith(this IExceptionConfigurationAddExceptionHandlers context, Type replacingExceptionType)
                { }
             }
            </example>
            
            The context implementer offers additional interfaces that are useful in continuing the configuration of Exception Handling (<see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationForExceptionTypeOrPostHandling"/>
            or in adding your custom handler information to the currently building exception type (<see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IExceptionHandlerExtension"/>).  In lieu of casting to these
            interfaces directly, consider using the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ExceptionHandlerConfigurationExtension"/> as a base class for your custom handler builder.
            </remarks>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationThenDoPostHandlingAction">
            <summary>
            This interface supports the fluent configuration of the Exception Handling Application Block.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationThenDoPostHandlingAction.ThenDoNothing">
            <summary>
            End the current exception handling chain by doing nothing more.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationThenDoPostHandlingAction.ThenNotifyRethrow">
            <summary>
            End the current exception handling chain by notifying the caller that an exception should be rethrown.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationThenDoPostHandlingAction.ThenThrowNewException">
            <summary>
            End the current exception handling chain by throwing a new exception.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationForExceptionTypeOrPostHandling">
            <summary>
            This interface supports the configuration of the Exception Handling Application Block.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IExceptionHandlerExtension">
            <summary>
            Used to provide context to extensions of the Exception Handling fluent configuration interface.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IExceptionHandlerExtension.CurrentExceptionTypeData">
            <summary>
            Retrieves data about the currently built up ExceptionTypeData.  Exception handler configuration extensions will use this to 
            add their handler information to the exception.
            </summary>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ReplaceWithHandlerLoggingConfigurationSourceBuilderExtensions"/>
            
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler"/>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.WrapWithHandlerLoggingConfigurationSourceBuilderExtensions"/>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData">
            <summary>
            Configuration object for Custom Providers.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlerData">
            <summary>
            Represents the configuration for an <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler"/>.
            </summary>    
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlerData.#ctor">
            <summary>
            Initializes an instance of a <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlerData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlerData.#ctor(System.Type)">
            <summary>
            Initializes an instance of an <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlerData"/> class with a name and an <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler"/> type.
            </summary>
            <param name="type">
            The configured <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler"/> type.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlerData.#ctor(System.String,System.Type)">
            <summary>
            Initializes an instance of an <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlerData"/> class with a name and an <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler"/> type.
            </summary>
            <param name="name">
            The name of the configured <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler"/>.
            </param>
            <param name="type">
            The configured <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler"/> type.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlerData.BuildExceptionHandler">
            <summary>
            Builds the exception handler represented by this configuration object.
            </summary>
            <returns>An <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.#ctor">
            <summary>
            Initializes with default values.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.#ctor(System.String,System.Type)">
            <summary>
            Initializes with name and provider type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.#ctor(System.String,System.String)">
            <summary>
            Initializes with name and provider type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.SetAttributeValue(System.String,System.String)">
            <summary>
            Sets the attribute value for a key.
            </summary>
            <param name="key">The attribute name.</param>
            <param name="value">The attribute value.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.Unmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>
            Modifies the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData"/> object to remove all values that should not be saved. 
            </summary>
            <param name="sourceElement">A <see cref="T:System.Configuration.ConfigurationElement"/> object at the current level containing a merged view of the properties.</param>
            <param name="parentElement">A parent <see cref="T:System.Configuration.ConfigurationElement"/> object or <see langword="null"/> if this is the top level.</param>		
            <param name="saveMode">One of the <see cref="T:System.Configuration.ConfigurationSaveMode"/> values.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.Reset(System.Configuration.ConfigurationElement)">
            <summary>
            Resets the internal state of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData"/> object, 
            including the locks and the properties collection.
            </summary>
            <param name="parentElement">The parent element.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.IsModified">
            <summary>
            Indicates whether this configuration element has been modified since it was last 
            saved or loaded when implemented in a derived class.
            </summary>
            <returns><see langword="true"/> if the element has been modified; otherwise, <see langword="false"/>. </returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.OnDeserializeUnrecognizedAttribute(System.String,System.String)">
            <summary>
            Called when an unknown attribute is encountered while deserializing the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData"/> object.
            </summary>
            <param name="name">The name of the unrecognized attribute.</param>
            <param name="value">The value of the unrecognized attribute.</param>
            <returns><see langword="true"/> if the processing of the element should continue; otherwise, <see langword="false"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#ExceptionHandling#Configuration#CustomHandlerData}#BaseGetPropertyValue(System.Configuration.ConfigurationProperty)">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#ExceptionHandling#Configuration#CustomHandlerData}#BaseSetPropertyValue(System.Configuration.ConfigurationProperty,System.Object)">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#ExceptionHandling#Configuration#CustomHandlerData}#BaseUnmerge(System.Configuration.ConfigurationElement,System.Configuration.ConfigurationElement,System.Configuration.ConfigurationSaveMode)">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#ExceptionHandling#Configuration#CustomHandlerData}#BaseReset(System.Configuration.ConfigurationElement)">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#ExceptionHandling#Configuration#CustomHandlerData}#BaseIsModified">
            <summary>Invokes the inherited behavior.</summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.BuildExceptionHandler">
            <summary>
            Builds the exception handler represented by this configuration object.
            </summary>
            <returns>An <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler"/>.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.TypeName">
            <summary>
            Gets or sets the fully qualified name of the <see cref="T:System.Type"/> the element is the configuration for.
            </summary>
            <value>
            the fully qualified name of the <see cref="T:System.Type"/> the element is the configuration for.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.Attributes">
            <summary>
            Gets the custom configuration attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.Properties">
            <summary>
            Gets a <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> of the properties that are defined for 
            this configuration element when implemented in a derived class. 
            </summary>
            <value>
            A <see cref="T:System.Configuration.ConfigurationPropertyCollection"/> of the properties that are defined for this
            configuration element when implemented in a derived class. 
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.CustomHandlerData.Microsoft#Practices#EnterpriseLibrary#Common#Configuration#IHelperAssistedCustomConfigurationData{Microsoft#Practices#EnterpriseLibrary#ExceptionHandling#Configuration#CustomHandlerData}#Helper">
            <summary>
            Gets the helper.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomHandlerLoggingConfigurationBuilderExtensions">
            <summary>
            Defines configuration extensions to <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationAddExceptionHandlers"/> for <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler"/>
            configuration.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomHandlerLoggingConfigurationBuilderExtensions.HandleCustom(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationAddExceptionHandlers,System.Type)">
            <summary>
            Handle the <see cref="T:System.Exception"/> with a custom exception handler.
            </summary>
            <param name="context">Interface to extend to add custom handler options.</param>
            <param name="customHandlerType">The <see cref="T:System.Type"/> of the custom handler.
            <remarks>This must derive from <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler"/></remarks></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomHandlerLoggingConfigurationBuilderExtensions.HandleCustom``1(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationAddExceptionHandlers)">
            <summary>
            Handle the <see cref="T:System.Exception"/> with a custom exception handler.
            </summary>
            <param name="context">Interface to extend to add custom handler options.</param>
            <typeparam name="T">The Type of the custom handler.</typeparam>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.CustomHandlerLoggingConfigurationBuilderExtensions.HandleCustom(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationAddExceptionHandlers,System.Type,System.Collections.Specialized.NameValueCollection)">
             <summary>
             Handle the <see cref="T:System.Exception"/> with a custom exception handler.
             </summary>
             <param name="context">Interface to extend to add custom handler options.</param>
             <param name="customHandlerType">The <see cref="T:System.Type"/> of the custom handler.  </param>
            <param name="customHandlerSettings">Name-Value collection of attributes the custom handler can use to initialize itself.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ExceptionHandlerConfigurationExtension">
            <summary>
            Provides a base extensible class for handler configuration extensions.  This class eases the handling 
            of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationAddExceptionHandlers"/> that is the typical entry point
            for most exception handler's fluent configuration interface.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ExceptionHandlerConfigurationExtension.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationAddExceptionHandlers)">
            <summary>
            Initializes a new instance of the ExceptoinHandlerConfigurationExtensions
            </summary>
            <param name="context">The context for configuration.</param>
            <remarks>
            This constructor expects to the find the implementor of <paramref name="context"/> provide
            the <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationForExceptionTypeOrPostHandling"/> and <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IExceptionHandlerExtension"/> interfaces.
            </remarks>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ExceptionHandlerConfigurationExtension.Context">
            <summary>
            The context for the extending handler in the fluent interface.  The extension interface
            is expected to return this context to enable continuation of configuring ExceptionHandling.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ExceptionHandlerConfigurationExtension.CurrentExceptionTypeData">
            <summary>
            The current exception type being built in the fluent interface.  Inheritors genereally should 
            add their <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlerData"/> to this during construction.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlingSettings">
            <summary>
            Represents the Exception Handling Application Block configuration section in a configuration file.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlingSettings.SectionName">
            <summary>
            Gets the configuration section name for the library.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlingSettings.GetExceptionHandlingSettings(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Gets the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlingSettings"/> section in the configuration source.
            </summary>
            <param name="configurationSource">The <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> to get the section from.</param>
            <returns>The exception handling section.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlingSettings.#ctor">
            <summary>
            Initializes a new instance of an <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlingSettings"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlingSettings.BuildExceptionManager">
            <summary>
            Builds a <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager"/> based on the configuration.
            </summary>
            <returns>An <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlingSettings.BuildExceptionPolicy(System.String)">
            <summary>
            Builds an <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition"/> based on the configuration.
            </summary>
            <param name="policyName">The policy name.</param>
            <returns>The policy instance.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlingSettings.ExceptionPolicies">
            <summary>
            Gets a collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionPolicyData"/> objects.
            </summary>
            <value>
            A collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionPolicyData"/> objects.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionPolicyData">
            <summary>
            Represents the configuration for an <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy"/>.
            </summary> 
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionPolicyData.#ctor">
            <summary>
            Creates a new instance of ExceptionPolicyData.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionPolicyData.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionPolicyData"/> class with a name.
            </summary>
            <param name="name">
            The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionPolicyData"/>.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionPolicyData.BuildExceptionPolicy">
            <summary>
            Builds an <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition"/> based on the configuration.
            </summary>
            <returns>The policy instance.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionPolicyData.ExceptionTypes">
            <summary>
            Gets a collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData"/> objects.
            </summary>
            <value>
            A collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData"/> objects.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData">
            <summary>
            Represents the configuration for an <see cref="T:System.Exception"/>
            that will be handled by an exception policy. 
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.#ctor(System.String,System.Type,Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PostHandlingAction)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData"/> class with a name, the <see cref="T:System.Exception"/> type and a <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.PostHandlingAction"/>.
            </summary>
            <param name="name">The name of the configured exception.</param>
            <param name="type">The <see cref="T:System.Exception"/> type.</param>
            <param name="postHandlingAction">One of the <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.PostHandlingAction"/> values that specifies the action that should occur after the exception is handled.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.#ctor(System.String,System.String,Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PostHandlingAction)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData"/> class with a name, the fully qualified type name of the <see cref="T:System.Exception"/> and a <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.PostHandlingAction"/>.
            </summary>
            <param name="name">The name of the configured exception.</param>
            <param name="typeName">The fully qualified type name of the <see cref="T:System.Exception"/> type.</param>
            <param name="postHandlingAction">One of the <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.PostHandlingAction"/> values that specifies the action that should occur after the exception is handled.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.BuildExceptionType">
            <summary>
            Builds an <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyEntry"/> based on the configuration.
            </summary>
            <returns>The policy entry.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.Name">
            <summary>
            Gets or sets the name of the element.
            </summary>
            <value>
            The name of the element.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.Type">
            <summary>
            Gets or sets the <see cref="T:System.Exception"/> type.
            </summary>
            <value>
            The <see cref="T:System.Exception"/> type
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.TypeName">
            <summary>
            Gets or sets the fully qualified type name of the <see cref="T:System.Exception"/> type.
            </summary>
            <value>
            The fully qualified type name of the <see cref="T:System.Exception"/> type.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.PostHandlingAction">
            <summary>
            Gets or sets the <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.PostHandlingAction"/> for the exception.
            </summary>
            <value>
            One of the <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.PostHandlingAction"/> values.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionTypeData.ExceptionHandlers">
            <summary>
            Gets a collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlerData"/> objects.
            </summary>
            <value>
            A collection of <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ExceptionHandlerData"/> objects.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationReplaceWithProvider">
            <summary>
             This interface supports the fluent configuration of <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler"/>
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationWithMessage">
            <summary>
            Defines interface for adding messages when configuring a <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler"/> for an exception.
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData"/>
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationWithMessage.UsingMessage(System.String)">
            <summary>
            Use the provided message as part of the new exception.
            </summary>
            <param name="message">Message to use when providing an alternative exception, typically through wrapping or replacing.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationWithMessage.UsingResourceMessage(System.Type,System.String)">
            <summary>
            Use the message in the specified resource file and name.
            </summary>
            <param name="resourceType">The type from the assembly with the resource to use for a message</param>
            <param name="resourceName">The name of the resource.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationWrapWithProvider">
            <summary>
             This interface supports the fluent configuration of <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler"/>
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData">
            <summary>
            Represents the configuration data for a <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData"/> class with a name, exception message, and replace exception type name.
            </summary>
            <param name="name">
            The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData"/>.
            </param>
            <param name="exceptionMessage">
            The exception message replacement.
            </param>
            <param name="replaceExceptionTypeName">
            The fully qualified assembly name the type of the replacement exception.
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData.BuildExceptionHandler">
            <summary>
            Builds the exception handler represented by this configuration object.
            </summary>
            <returns>An <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler"/>.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData.ExceptionMessage">
            <summary>
            Gets or sets the message for the replacement exception.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData.ExceptionMessageResourceName">
            <summary/>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData.ExceptionMessageResourceType">
            <summary/>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData.ReplaceExceptionType">
            <summary>
            Gets or sets the type of the replacement exception.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.ReplaceHandlerData.ReplaceExceptionTypeName">
            <summary>
            Gets or sets the fully qualified type name of the replacement exception.
            </summary>
            <value>
            The fully qualified type name of the replacement exception.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ReplaceWithHandlerLoggingConfigurationSourceBuilderExtensions">
            <summary>
            Defines configuration extensions to <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationAddExceptionHandlers"/> for <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler"/>
            configuration.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ReplaceWithHandlerLoggingConfigurationSourceBuilderExtensions.ReplaceWith``1(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationAddExceptionHandlers)">
            <summary>
            Replace exception with new exception type.
            </summary>
            <typeparam name="T">Replacement <see cref="T:System.Exception"/> type.</typeparam>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.ReplaceWithHandlerLoggingConfigurationSourceBuilderExtensions.ReplaceWith(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationAddExceptionHandlers,System.Type)">
            <summary>
            Replace exception with new exception type.
            </summary>
            <param name="context">Interface to extend to add ReplaceWith options.</param>
            <param name="replacingExceptionType">Replacement <see cref="T:System.Exception"/> type.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData">
            <summary>
            Represents the configuration data for a <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData"/> class with a name, an exception message, and the fully qualified assembly name of the type of the wrapping exception.
            </summary>
            <param name="name">
            The name of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData"/>.
            </param>
            <param name="exceptionMessage">
            The exception message replacement.
            </param>
            <param name="wrapExceptionTypeName">
            The fully qualified assembly name of type of the wrapping exception
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData.BuildExceptionHandler">
            <summary>
            Builds the exception handler represented by this configuration object.
            </summary>
            <returns>An <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler"/>.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData.ExceptionMessage">
            <summary>
            Gets or sets the message for the replacement exception.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData.ExceptionMessageResourceType">
            <summary/>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData.ExceptionMessageResourceName">
            <summary/>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData.WrapExceptionType">
            <summary>
            Gets or sets the type of the replacement exception.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData.WrapExceptionTypeName">
            <summary>
            Gets or sets the fully qualified type name of the replacement exception.
            </summary>
            <value>
            The fully qualified type name of the replacement exception.
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.WrapWithHandlerLoggingConfigurationSourceBuilderExtensions">
            <summary>
             Provides <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Configuration.WrapHandlerData"/> configuration extensions to the ExceptionHandling fluent configuration interface.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.WrapWithHandlerLoggingConfigurationSourceBuilderExtensions.WrapWith``1(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationAddExceptionHandlers)">
            <summary>
            Wrap exception with the new exception type.
            </summary>
            <typeparam name="T">Type of <see cref="T:System.Exception"/> to wrap existing exception with.</typeparam>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.WrapWithHandlerLoggingConfigurationSourceBuilderExtensions.WrapWith(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.Fluent.IExceptionConfigurationAddExceptionHandlers,System.Type)">
            <summary>
            Wrap exception with the new exception type.
            </summary>
            <param name="context">The extension context for this handler extension.</param>
            <param name="wrappingExceptionType">Type of <see cref="T:System.Exception"/>to wrap existing exception with.</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter">
            <summary>
            Represents the base class from which all implementations of exception formatters must derive. The formatter provides functionality for formatting <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception"/> objects.
            </summary>	
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.#ctor(System.Exception,System.Guid)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter"/> class with an <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception"/> to format.
            </summary>
            <param name="exception">The <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception"/> object to format.</param>
            <param name="handlingInstanceId">The id of the handling chain.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Format">
            <summary>
            Formats the <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception"/> into the underlying stream.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteException(System.Exception,System.Exception)">
            <summary>
            Formats the exception and all nested inner exceptions.
            </summary>
            <param name="exceptionToFormat">The exception to format.</param>
            <param name="outerException">The outer exception. This 
            value will be null when writing the outer-most exception.</param>
            <remarks>
            <para>This method calls itself recursively until it reaches
            an exception that does not have an inner exception.</para>
            <para>
            This is a template method which calls the following
            methods in order
            <list type="number">
            <item>
            <description><see cref="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteExceptionType(System.Type)"/></description>
            </item>
            <item>
            <description><see cref="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteMessage(System.String)"/></description>
            </item>
            <item>
            <description><see cref="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteSource(System.String)"/></description>
            </item>
            <item>
            <description><see cref="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteHelpLink(System.String)"/></description>
            </item>
            <item>
            <description><see cref="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteReflectionInfo(System.Exception)"/></description>
            </item>
            <item>
            <description><see cref="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteStackTrace(System.String)"/></description>
            </item>
            <item>
            <description>If the specified exception has an inner exception
            then it makes a recursive call. <see cref="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteException(System.Exception,System.Exception)"/></description>
            </item>
            </list>
            </para>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteReflectionInfo(System.Exception)">
            <summary>
            Formats an <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception"/> using reflection to get the information.
            </summary>
            <param name="exceptionToFormat">
            The <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception"/> to be formatted.
            </param>
            <remarks>
            <para>This method reflects over the public, instance properties 
            and public, instance fields
            of the specified exception and prints them to the formatter.  
            Certain property names are ignored
            because they are handled explicitly in other places.</para>
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteDescription">
            <summary>
            When overridden by a class, writes a description of the caught exception.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteDateTime(System.DateTime)">
            <summary>
            When overridden by a class, writes the current time.
            </summary>
            <param name="utcNow">The current time.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteExceptionType(System.Type)">
            <summary>
            When overridden by a class, writes the <see cref="T:System.Type"/> of the current exception.
            </summary>
            <param name="exceptionType">The <see cref="T:System.Type"/> of the exception.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteMessage(System.String)">
            <summary>
            When overridden by a class, writes the <see cref="P:System.Exception.Message"/>.
            </summary>
            <param name="message">The message to write.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteSource(System.String)">
            <summary>
            When overridden by a class, writes the value of the <see cref="P:System.Exception.Source"/> property.
            </summary>
            <param name="source">The source of the exception.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteHelpLink(System.String)">
            <summary>
            When overridden by a class, writes the value of the <see cref="P:System.Exception.HelpLink"/> property.
            </summary>
            <param name="helpLink">The help link for the exception.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteStackTrace(System.String)">
            <summary>
            When overridden by a class, writes the value of the <see cref="P:System.Exception.StackTrace"/> property.
            </summary>
            <param name="stackTrace">The stack trace of the exception.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WritePropertyInfo(System.Reflection.PropertyInfo,System.Object)">
            <summary>
            When overridden by a class, writes the value of a <see cref="T:System.Reflection.PropertyInfo"/> object.
            </summary>
            <param name="propertyInfo">The reflected <see cref="T:System.Reflection.PropertyInfo"/> object.</param>
            <param name="value">The value of the <see cref="T:System.Reflection.PropertyInfo"/> object.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteFieldInfo(System.Reflection.FieldInfo,System.Object)">
            <summary>
            When overridden by a class, writes the value of a <see cref="T:System.Reflection.FieldInfo"/> object.
            </summary>
            <param name="fieldInfo">The reflected <see cref="T:System.Reflection.FieldInfo"/> object.</param>
            <param name="value">The value of the <see cref="T:System.Reflection.FieldInfo"/> object.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.WriteAdditionalInfo(System.Collections.Specialized.NameValueCollection)">
            <summary>
            When overridden by a class, writes additional properties if available.
            </summary>
            <param name="additionalInformation">Additional information to be included with the exception report</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception">
            <summary>
            Gets the <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception"/> to format.
            </summary>
            <value>
            The <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception"/> to format.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.HandlingInstanceId">
            <summary>
            Gets the id of the handling chain requesting a formatting.
            </summary>
            <value>
            The id of the handling chain requesting a formatting, or <see cref="F:System.Guid.Empty"/> if no such id is available.
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.AdditionalInfo">
            <summary>
            Gets additional information related to the <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception"/> but not
            stored in the exception (eg: the time in which the <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception"/> was 
            thrown).
            </summary>
            <value>
            Additional information related to the <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception"/> but not
            stored in the exception (for example, the time when the <see cref="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionFormatter.Exception"/> was 
            thrown).
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionHandlingException">
            <summary>
            An exception that occurred during the exception handling process.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionHandlingException.#ctor">
            <summary>
            Initializes with defaults.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionHandlingException.#ctor(System.String)">
            <summary>
            Initializes with a specified error message.
            </summary>
            <param name="message">A message that describes the error.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionHandlingException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes with a specified error 
            message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.
            </param>
            <param name="innerException">
            <para>The exception that is the cause of the current exception. If the innerException parameter is not a null reference, the current exception is raised in a catch block that handles the inner exception.</para>
            </param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionHandlingException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes with serialized data.
            </summary>
            <param name="info">The object that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.
            </param>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager">
            <summary>
            Non-static entry point to the exception handling functionality.
            </summary>
            <remarks>
            Instances of <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager"/> can be used to replace references to the static <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy"/>
            facade.
            </remarks>
            <seealso cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.#ctor(Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager"/> class with one or more exception policies.
            </summary>
            <param name="exceptionPolicies">The exception policies.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager"/> class with a set of exception policies.
            </summary>
            <param name="exceptionPolicies">The complete set of exception policies.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.#ctor(System.Collections.Generic.IDictionary{System.String,Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition})">
            <summary>
            Initializes a new instance of the class <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager"/> with a set of policies.
            </summary>
            <param name="exceptionPolicies">The complete set of exception policies.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.HandleException(System.Exception,System.String)">
            <summary>
            Handles the specified <see cref="T:System.Exception"/>
            object according to the rules configured for <paramref name="policyName"/>.
            </summary>
            <param name="exceptionToHandle">An <see cref="T:System.Exception"/> object.</param>
            <param name="policyName">The name of the policy to handle.</param>        
            <returns><see langword="true"/> if  rethrowing an exception is recommended; otherwise, <see langword="false"/>.</returns>
            <example>
            The following code shows the usage of the 
            exception handling framework.
            <code>
            try
            {
            	DoWork();
            }
            catch (Exception e)
            {
            	if (exceptionManager.HandleException(e, name)) throw;
            }
            </code>
            </example>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.Process(System.Action,System.String)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.HandleException(System.Exception,System.String,System.Exception@)">
            <summary>
            Handles the specified <see cref="T:System.Exception"/>
            object according to the rules configured for <paramref name="policyName"/>.
            </summary>
            <param name="exceptionToHandle">An <see cref="T:System.Exception"/> object.</param>
            <param name="policyName">The name of the policy to handle.</param>
            <param name="exceptionToThrow">The new <see cref="T:System.Exception"/> to throw, if any.</param>
            <remarks>
            If a rethrow is recommended and <paramref name="exceptionToThrow"/> is <see langword="null"/>,
            then the original exception <paramref name="exceptionToHandle"/> should be rethrown; otherwise,
            the exception returned in <paramref name="exceptionToThrow"/> should be thrown.
            </remarks>
            <returns><see langword="true"/> if  rethrowing an exception is recommended; otherwise, <see langword="false"/>.</returns>
            <example>
            The following code shows the usage of the 
            exception handling framework.
            <code>
            try
            {
            	DoWork();
            }
            catch (Exception e)
            {
                Exception exceptionToThrow;
            	if (exceptionManager.HandleException(e, name, out exceptionToThrow))
            	{
            	  if(exceptionToThrow == null)
            	    throw;
            	  else
            	    throw exceptionToThrow;
            	}
            }
            </code>
            </example>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.HandleException(System.Exception,System.String)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.Process(System.Action,System.String)">
            <summary>
            Excecutes the supplied delegate <paramref name="action"/> and handles 
            any thrown exception according to the rules configured for <paramref name="policyName"/>.
            </summary>
            <param name="action">The delegate to execute.</param>
            <param name="policyName">The name of the policy to handle.</param>        
            <example>
            The following code shows the usage of this method.
            <code>
            	exceptionManager.Process(() =&gt; { DoWork(); }, "policy");
            </code>
            </example>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.HandleException(System.Exception,System.String)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.Process``1(System.Func{``0},``0,System.String)">
            <summary>
            Executes the supplied delegate <paramref name="action"/>, and handles
            any thrown exception according to the rules configured for <paramref name="policyName"/>.
            </summary>
            <typeparam name="TResult">Type of return value from <paramref name="action"/>.</typeparam>
            <param name="action">The delegate to execute.</param>
            <param name="defaultResult">The value to return if an exception is thrown and the
            exception policy swallows it instead of rethrowing.</param>
            <param name="policyName">The name of the policy to handle.</param>
            <returns>If no exception occurs, returns the result from executing <paramref name="action"/>. If
            an exception occurs and the policy does not re-throw, returns <paramref name="defaultResult"/>.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.Process``1(System.Func{``0},System.String)">
            <summary>
            Executes the supplied delegate <paramref name="action"/>, and handles
            any thrown exception according to the rules configured for <paramref name="policyName"/>.
            </summary>
            <typeparam name="TResult">Type of return value from <paramref name="action"/>.</typeparam>
            <param name="action">The delegate to execute.</param>
            <param name="policyName">The name of the policy to handle.</param>
            <returns>If no exception occurs, returns the result from executing <paramref name="action"/>. If
            an exception occurs and the policy does not re-throw, returns the default value for <typeparamref name="TResult"/>.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.Policies">
            <summary>
            Gets the policies for the exception manager.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyFactory">
            <summary>
            Factory for <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition"/> objects. This class is responsible for creating all the internal
            classes needed to implement a <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyFactory.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition"/> class with the default <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyFactory.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition"/> class with the specified <see cref="T:Microsoft.Practices.EnterpriseLibrary.Common.Configuration.IConfigurationSource"/> instance.
            </summary>
            <param name="configurationSource">The source for configuration information.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyFactory.#ctor(System.Func{System.String,System.Configuration.ConfigurationSection})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition"/> class with a configuration accessor.
            </summary>
            <param name="configurationAccessor">The source for configuration information.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyFactory.CreatePolicy(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition"/> class based on the information in the configuration section.
            </summary>
            <param name="name">The name of the required instance.</param>
            <returns>The created <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition"/> object.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="name"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.InvalidOperationException">The configuration section does not exist or cannot be deserialized, or there are no settings for <paramref name="name"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyFactory.CreateManager">
            <summary>
            Creates a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager"/> class based on the information in the configuration section.
            </summary>
            <returns>The created <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager"/> object.</returns>
            <exception cref="T:System.InvalidOperationException">The configuration section does not exist or cannot be deserialized.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyFactory.InitializeExceptionPolicy">
            <summary>
            Initializes the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy"/> class with a new <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager"/> instance created based on the information in the configuration section.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy">
            <summary>
            Represents a policy with exception types and
            exception handlers. 
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy.HandleException(System.Exception,System.String)">
            <summary>
            The main entry point into the Exception Handling Application Block.
            Handles the specified <see cref="T:System.Exception"/>
            object according to the given <paramref name="policyName"></paramref>.
            </summary>
            <param name="exceptionToHandle">An <see cref="T:System.Exception"/> object.</param>
            <param name="policyName">The name of the policy to handle.</param>        
            <returns><see langword="true"/> if  rethrowing an exception is recommended; otherwise, <see langword="false"/>.</returns>
            <example>
            The following code shows the usage of the 
            exception handling framework.
            <code>
            try
            {
            	DoWork();
            }
            catch (Exception e)
            {
            	if (ExceptionPolicy.HandleException(e, name)) throw;
            }
            </code>
            </example>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy.HandleException(System.Exception,System.String,System.Exception@)">
            <summary>
            Handles the specified <see cref="T:System.Exception"/>
            object according to the rules configured for <paramref name="policyName"/>.
            </summary>
            <param name="exceptionToHandle">An <see cref="T:System.Exception"/> object.</param>
            <param name="policyName">The name of the policy to handle.</param>
            <param name="exceptionToThrow">The new <see cref="T:System.Exception"/> to throw, if any.</param>
            <remarks>
            If a rethrow is recommended and <paramref name="exceptionToThrow"/> is <see langword="null"/>,
            then the original exception <paramref name="exceptionToHandle"/> should be rethrown; otherwise,
            the exception returned in <paramref name="exceptionToThrow"/> should be thrown.
            </remarks>
            <returns><see langword="true"/> if  rethrowing an exception is recommended; otherwise, <see langword="false"/>.</returns>
            <example>
            The following code shows the usage of the 
            exception handling framework.
            <code>
            try
            {
            	DoWork();
            }
            catch (Exception e)
            {
                Exception exceptionToThrow;
            	if (ExceptionPolicy.HandleException(e, name, out exceptionToThrow))
            	{
            	  if(exceptionToThrow == null)
            	    throw;
            	  else
            	    throw exceptionToThrow;
            	}
            }
            </code>
            </example>
            <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager.HandleException(System.Exception,System.String)"/>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy.SetExceptionManager(Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionManager,System.Boolean)">
            <summary>
            Sets the global exception manager.
            </summary>
            <param name="exceptionManager">The exception manager.</param>
            <param name="throwIfSet"><see langword="true"/> to throw an exception if the manager is already set; otherwise, <see langword="false"/>. Defaults to <see langword="true"/>.</param>
            <exception cref="T:System.InvalidOperationException">The manager is already set and <paramref name="throwIfSet"/> is <see langword="true"/>.</exception>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy.Reset">
            <summary>
            Resets the global exception manager.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyEntry">
            <summary>
            Represents an entry in an <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy"/> containing
            an exception type as the key and a list of 
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler"/> objects as the value.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyEntry.#ctor(System.Type,Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PostHandlingAction,System.Collections.Generic.IEnumerable{Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler})">
            <summary>
            Initializes a new instance of the 
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyEntry"/> class.
            </summary>
            <param name="exceptionType">Type of exception this policy refers to.</param>
            <param name="postHandlingAction">What to do after the exception is handled.</param>
            <param name="handlers">Handlers to execute on the exception.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyEntry.Handle(System.Exception)">
            <summary>
            Handles all exceptions in the chain.
            </summary>
            <param name="exceptionToHandle">The <c>Exception</c> to handle.</param>
            <returns>Whether or not a rethrow is recommended.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyEntry.IntentionalRethrow(System.Exception,System.Exception)">
            <devdoc>
            Rethrows the given exception.  Placed in a seperate method for
            easier viewing in the stack trace.
            </devdoc>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyEntry.ExceptionType">
            <summary>
             The type of <see cref="T:System.Exception"/> to match this policy entry to.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition">
            <summary>
            Represents a policy for handling exceptions.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition.#ctor(System.String,System.Collections.Generic.IEnumerable{Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyEntry})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition"/> class with the policy name and a set of policy entries.
            </summary>
            <param name="policyName">The policy name.</param>
            <param name="policyEntries">A set of <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyEntry"/> objects.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition.#ctor(System.String,System.Collections.Generic.IDictionary{System.Type,Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyEntry})">
            
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition"/> class with the policy name and a dictionary of policy entries.
            </summary>
            <param name="policyName">The policy name.</param>
            <param name="policyEntries">A set of <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyEntry"/> objects.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition.HandleException(System.Exception)">
            <summary>
            Checks if there is a policy entry that matches
            the type of the exception object specified by the
            <see cref="T:System.Exception"/> parameter,
            and if so, invokes the handlers associated with that entry.
            </summary>
            <param name="exceptionToHandle">The <c>Exception</c> to handle.</param>
            <returns><see langword="true"/> if  rethrowing an exception is recommended; otherwise, <see langword="false"/>.</returns>
            <remarks>
            The algorithm for matching the exception object to a 
            set of handlers mimics that of a standard .NET Framework exception policy.
            The specified exception object will be matched to a single 
            exception policy entry by traversing its inheritance hierarchy. 
            This means that if a <c>FileNotFoundException</c>, for example, is 
            caught, but the only exception type that the exception policy 
            knows how to handle is System.Exception, the event handlers 
            for <c>System.Exception</c> will be invoked because 
            <c>FileNotFoundException</c> ultimately derives from <c>System.Exception</c>.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition.GetPolicyEntry(System.Type)">
            <summary>
            Gets the policy entry associated with the specified key.
            </summary>
            <param name="exceptionType">Type of the exception.</param>
            <returns>The <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyEntry"/> corresponding to this exception type.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition.FindExceptionPolicyEntry(System.Type)">
            <devDoc>
            Traverses the specified type's inheritance hiearchy.
            </devDoc>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicyDefinition.PolicyName">
            <summary>
            Name of this exception policy.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionUtility">
            <summary>
            Provides common functions for the Exception Handling Application Block classes. Cannot inherit from this class.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionUtility.FormatExceptionMessage(System.String,System.Guid)">
            <summary>
            Formats a message by replacing the token "{handlingInstanceID}" with the handlingInstanceID.
            </summary>
            <param name="message">The original message.</param>
            <param name="handlingInstanceId">The handlingInststanceID passed into the exceptionHandlerData.</param>
            <returns>The formatted message.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionUtility.FormatExceptionHandlingExceptionMessage(System.String,System.Exception,System.Exception,System.Exception)">
            <summary>
            Formats an exception message so that it can be sent to the event log later, by someone else.
            </summary>
            <param name="policyName">The policy that is running.</param>
            <param name="offendingException">The exception that occured in the chain.</param>
            <param name="chainException">The exception when the chain failed.</param>
            <param name="originalException">The original exception.</param>		
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler">
            <summary>
            Defines the contract for an ExceptionHandler.  An ExceptionHandler contains specific handling
            logic (i.e. logging the exception, replacing the exception, and so forth.) that is executed in a chain of multiple
            ExceptionHandlers.  A chain of one or more ExceptionHandlers is executed based on the exception type being 
            handled, as well as the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy"/>.  <seealso cref="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ExceptionPolicy.HandleException(System.Exception,System.String)"/>
            </summary>    
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.IExceptionHandler.HandleException(System.Exception,System.Guid)">
            <summary>
            <para>When implemented by a class, handles an <see cref="T:System.Exception"/>.</para>
            </summary>
            <param name="exception"><para>The exception to handle.</para></param>        
            <param name="handlingInstanceId">
            <para>The unique ID attached to the handling chain for this handling instance.</para>
            </param>
            <returns><para>Modified exception to pass to the next exceptionHandlerData in the chain.</para></returns>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PostHandlingAction">
            <summary>
            Determines what action should occur after an exception is handled by the configured exception handling chain. 
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PostHandlingAction.None">
            <summary>
            Indicates that no rethrow should occur.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PostHandlingAction.NotifyRethrow">
            <summary>
            Notify the caller that a rethrow is recommended.
            </summary>
        </member>
        <member name="F:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.PostHandlingAction.ThrowNewException">
            <summary>
            Throws the exception after the exception has been handled by all handlers in the chain.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AddCustomHandlerData">
            <summary>
              Looks up a localized string similar to Custom.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AddExceptionTypeDisplayName">
            <summary>
              Looks up a localized string similar to Exception Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AddExceptionTypeHelpText">
            <summary>
              Looks up a localized string similar to Add a new exception type that is handled by this exception policy..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AdditionalInfo">
            <summary>
              Looks up a localized string similar to Additional Info:.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AddPolicyDisplayDescription">
            <summary>
              Looks up a localized string similar to Add a new policy..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AddPolicyDisplayName">
            <summary>
              Looks up a localized string similar to Policy.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AddReplaceHandlerData">
            <summary>
              Looks up a localized string similar to Replace Handler.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AddReplaceHandlerDataDescription">
            <summary>
              Looks up a localized string similar to Replace the exception with another exception..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AddWrapHandlerData">
            <summary>
              Looks up a localized string similar to Wrap Handler.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AddWrapHandlerDataDescription">
            <summary>
              Looks up a localized string similar to Wrap the exception within another exception..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AttributesDescription">
            <summary>
              Looks up a localized string similar to Attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AttributesDisplayName">
            <summary>
              Looks up a localized string similar to Attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.AuthorizationProviderTypeDescription">
            <summary>
              Looks up a localized string similar to AuthorizationProviderTypeDescription.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.BlockName">
            <summary>
              Looks up a localized string similar to Enterprise Library Exception Handling Application Block.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.CantSwallowNonVoidReturnMessage">
            <summary>
              Looks up a localized string similar to Cannot swallow exceptions for methods with non-null return type..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ChainException">
            <summary>
              Looks up a localized string similar to HANDLING CHAIN EXCEPTION:.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ConfigurationFailureCreatingPolicy">
            <summary>
              Looks up a localized string similar to A configuration failure occurred while creating policy ‘{0}’..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.CustomHandlerInvalidTypeException">
            <summary>
              Looks up a localized string similar to The type &apos;{3}&apos; for custom exception handler with name &apos;{2}&apos; is not a valid type ({0} line {1})..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.CustomHandlerMissingConstructorException">
            <summary>
              Looks up a localized string similar to The type &apos;{3}&apos; for custom exception handler with name &apos;{2}&apos; does not have the expected constructor ({0} line {1})..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.CustomHandlerNotHandlerTypeException">
            <summary>
              Looks up a localized string similar to The type &apos;{3}&apos; for custom exception handler with name &apos;{2}&apos; is not a valid handler type ({0} line {1})..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.CustomHandlerNoTypeException">
            <summary>
              Looks up a localized string similar to The custom exception handler with name &apos;{2}&apos; does not have a type specified ({0} line {1})..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ErrorHandlingExceptionMessage">
            <summary>
              Looks up a localized string similar to The error occurred while handling an exception for policy &quot;{0}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.EventIdDisplayName">
            <summary>
              Looks up a localized string similar to Event id.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionCannotRetrieveConfiguration">
            <summary>
              Looks up a localized string similar to Cannot load the exception handling section from the configuration file..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionExceptionHandlingSectionNotFound">
            <summary>
              Looks up a localized string similar to The configuration section for Exception Handling cannot be found in the configuration source..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionHandlersDisplayName">
            <summary>
              Looks up a localized string similar to Handlers.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionHandlerTypeDescription">
            <summary>
              Looks up a localized string similar to Custom Handler Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionHandlerTypeDisplayName">
            <summary>
              Looks up a localized string similar to Custom Handler Type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionManagerAlreadySet">
            <summary>
              Looks up a localized string similar to The ExceptionManager for the ExceptionPolicy is already set..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionManagerNotSet">
            <summary>
              Looks up a localized string similar to Must set an ExceptionManager in the ExceptionPolicy class using the SetExceptionManager method..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionMessageDisplayName">
            <summary>
              Looks up a localized string similar to Exception message.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionMessageResourceNameDisplayName">
            <summary>
              Looks up a localized string similar to Exception message resource name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionMessageResourceTypeDisplayName">
            <summary>
              Looks up a localized string similar to Exception message resource type.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionNullException">
            <summary>
              Looks up a localized string similar to Unable to rethrow exception: The exception to throw is null..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionPoliciesDisplayName">
            <summary>
              Looks up a localized string similar to Policies.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionPolicyNameInvalid">
            <summary>
              Looks up a localized string similar to Cannot retrieve policy information.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionPolicyNotFound">
            <summary>
              Looks up a localized string similar to The policy with name &apos;{0}&apos; cannot be found. Exception handling aborted..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionPolicyNotFoundInConfigurationException">
            <summary>
              Looks up a localized string similar to The exception handling configuration does not contain a policy with name &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionStringNullOrEmpty">
            <summary>
              Looks up a localized string similar to The value can not be an empty string or null..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionTypeNotException">
            <summary>
              Looks up a localized string similar to The type must be of type Exception..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionTypesDisplayName">
            <summary>
              Looks up a localized string similar to Exception Types.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ExceptionWasCaught">
            <summary>
              Looks up a localized string similar to An exception of type &apos;{0}&apos; occurred and was caught..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.FieldAccessFailed">
            <summary>
              Looks up a localized string similar to Access failed.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.FormatterTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Formatter type name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.HelpLink">
            <summary>
              Looks up a localized string similar to Help link : {0}.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.InnerException">
            <summary>
              Looks up a localized string similar to Inner Exception.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.LogCategoryDisplayName">
            <summary>
              Looks up a localized string similar to Log category.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.Message">
            <summary>
              Looks up a localized string similar to Message : {0}.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.MustBeImplementedBySubclassException">
            <summary>
              Looks up a localized string similar to Must be implemented by subclasses.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.NameDisplayName">
            <summary>
              Looks up a localized string similar to Name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.OffendingException">
            <summary>
              Looks up a localized string similar to OFFENDING EXCEPTION:.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.OriginalException">
            <summary>
              Looks up a localized string similar to ORIGINAL EXCEPTION:.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.PermissionDenied">
            <summary>
              Looks up a localized string similar to Permission Denied.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.PolicyName">
            <summary>
              Looks up a localized string similar to POLICY NAME: {0}.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.PostHandlingActionDisplayName">
            <summary>
              Looks up a localized string similar to Post handling action.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.PriorityDisplayName">
            <summary>
              Looks up a localized string similar to Priority.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.PropertyAccessFailed">
            <summary>
              Looks up a localized string similar to Access failed.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.ReplaceExceptionTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Replace exception type .
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.SectionDisplayName">
            <summary>
              Looks up a localized string similar to Exception Handling Settings.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.SeverityDisplayName">
            <summary>
              Looks up a localized string similar to Severity.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.Source">
            <summary>
              Looks up a localized string similar to Source : {0}.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.StackTrace">
            <summary>
              Looks up a localized string similar to Stack Trace.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.StackTraceUnavailable">
            <summary>
              Looks up a localized string similar to The stack trace is unavailable..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.TitleDisplayName">
            <summary>
              Looks up a localized string similar to Title.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.TypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Type name.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.TypeString">
            <summary>
              Looks up a localized string similar to Type : {0}.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.UnableToHandleException">
            <summary>
              Looks up a localized string similar to Unable to handle exception: &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.UndefinedValue">
            <summary>
              Looks up a localized string similar to &lt;undefined value&gt;.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.UseDefaultLoggerDisplayName">
            <summary>
              Looks up a localized string similar to Use default logger.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.Properties.Resources.WrapExceptionTypeNameDisplayName">
            <summary>
              Looks up a localized string similar to Wrap exception type.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler">
            <summary>
            Replaces the exception in the chain of handlers with a cleansed exception.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler"/> class with an exception message and the type of <see cref="T:System.Exception"/> to use.
            </summary>
            <param name="exceptionMessage">The exception message.</param>
            <param name="replaceExceptionType">The type of <see cref="T:System.Exception"/> to use to replace.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Utility.IStringResolver,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler"/> class with an exception message
            resolver and the type of <see cref="T:System.Exception"/> to use.
            </summary>
            <param name="exceptionMessageResolver">The exception message resolver.</param>
            <param name="replaceExceptionType">The type of <see cref="T:System.Exception"/> to use to replace.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler.HandleException(System.Exception,System.Guid)">
            <summary>
            Replaces the exception with the configured type for the specified policy.
            </summary>
            <param name="exception">The original exception.</param>        
            <param name="handlingInstanceId">The unique identifier attached to the handling chain for this handling instance.</param>
            <returns>Modified exception to pass to the next exceptionHandlerData in the chain.</returns>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler.ReplaceException(System.String)">
            <summary>
            Replaces an exception with a new exception of a specified type.
            </summary>                
            <param name="replaceExceptionMessage">The message for the new exception.</param>
            <returns>The replaced or "cleansed" exception.  Returns null if unable to replace the exception.</returns>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler.ReplaceExceptionType">
            <summary>
            The type of exception to replace.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.ReplaceHandler.ExceptionMessage">
            <summary>
            Gets the message for the new exception.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter">
            <summary>
            Represents an exception formatter that formats exception objects as text.
            </summary>	
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.#ctor(System.IO.TextWriter,System.Exception)">
            <summary>
            Initializes a new instance of the 
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter"/> using the specified
            <see cref="T:System.IO.TextWriter"/> and <see cref="T:System.Exception"/>
            objects.
            </summary>
            <param name="writer">The stream to write formatting information to.</param>
            <param name="exception">The exception to format.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.#ctor(System.IO.TextWriter,System.Exception,System.Guid)">
            <summary>
            Initializes a new instance of the 
            <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter"/> using the specified
            <see cref="T:System.IO.TextWriter"/> and <see cref="T:System.Exception"/>
            objects.
            </summary>
            <param name="writer">The stream to write formatting information to.</param>
            <param name="exception">The exception to format.</param>
            <param name="handlingInstanceId">The id of the handling chain.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.Format">
            <summary>
            Formats the <see cref="T:System.Exception"/> into the underlying stream.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.WriteDescription">
            <summary>
            Writes a generic description to the underlying text stream.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.WriteException(System.Exception,System.Exception)">
            <summary>
            Writes and formats the exception and all nested inner exceptions to the <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="exceptionToFormat">The exception to format.</param>
            <param name="outerException">The outer exception. This 
            value will be null when writing the outer-most exception.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.WriteDateTime(System.DateTime)">
            <summary>
            Writes the current date and time to the <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="utcNow">The current time.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.WriteExceptionType(System.Type)">
            <summary>
            Writes the value of the <see cref="P:System.Type.AssemblyQualifiedName"/>
            property for the specified exception type to the <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="exceptionType">The <see cref="T:System.Type"/> of the exception.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.WriteMessage(System.String)">
            <summary>
            Writes the value of the <see cref="P:System.Exception.Message"/>
            property to the underyling <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="message">The message to write.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.WriteSource(System.String)">
            <summary>
            Writes the value of the specified source taken
            from the value of the <see cref="P:System.Exception.Source"/>
            property to the <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="source">The source of the exception.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.WriteHelpLink(System.String)">
            <summary>
            Writes the value of the specified help link taken
            from the value of the <see cref="P:System.Exception.HelpLink"/>
            property to the <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="helpLink">The exception's help link.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.WritePropertyInfo(System.Reflection.PropertyInfo,System.Object)">
            <summary>
            Writes the name and value of the specified property to the <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="propertyInfo">The reflected <see cref="T:System.Reflection.PropertyInfo"/> object.</param>
            <param name="value">The value of the <see cref="T:System.Reflection.PropertyInfo"/> object.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.WriteFieldInfo(System.Reflection.FieldInfo,System.Object)">
            <summary>
            Writes the name and value of the specified field to the <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="fieldInfo">The reflected <see cref="T:System.Reflection.FieldInfo"/> object.</param>
            <param name="value">The value of the <see cref="T:System.Reflection.FieldInfo"/> object.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.WriteStackTrace(System.String)">
            <summary>
            Writes the value of the <see cref="P:System.Exception.StackTrace"/> property to the <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="stackTrace">The stack trace of the exception.</param>
            <remarks>
            If there is no stack trace available, an appropriate message will be displayed.
            </remarks>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.WriteAdditionalInfo(System.Collections.Specialized.NameValueCollection)">
            <summary>
            Writes the additional properties to the <see cref="T:System.IO.TextWriter"/>.
            </summary>
            <param name="additionalInformation">Additional information to be included with the exception report</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.Indent">
            <summary>
            Indents the <see cref="T:System.IO.TextWriter"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.TextExceptionFormatter.Writer">
            <summary>
            Gets the underlying <see cref="T:System.IO.TextWriter"/>
            that the current formatter is writing to.
            </summary>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler">
            <summary>
            Wraps the current exception in the handling chain with a new exception of a specified type.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler.#ctor(System.String,System.Type)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler"/> class with an exception message and the type of <see cref="T:System.Exception"/> to use.
            </summary>
            <param name="exceptionMessage">The exception message.</param>
            <param name="wrapExceptionType">The type of <see cref="T:System.Exception"/> to use to wrap.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler.#ctor(Microsoft.Practices.EnterpriseLibrary.Common.Utility.IStringResolver,System.Type)">
            <summary>
            Initialize a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler"/> class with an exception message resolver
            and the type of <see cref="T:System.Exception"/> to use.
            </summary>
            <param name="exceptionMessageResolver">The exception message resolver.</param>
            <param name="wrapExceptionType">The type of <see cref="T:System.Exception"/> to use to wrap.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler.HandleException(System.Exception,System.Guid)">
            <summary>
            <para>Wraps the <see cref="T:System.Exception"/> with the configuration exception type.</para>
            </summary>
            <param name="exception"><para>The exception to handle.</para></param>        
            <param name="handlingInstanceId">
            <para>The unique ID attached to the handling chain for this handling instance.</para>
            </param>
            <returns><para>Modified exception to pass to the next exceptionHandlerData in the chain.</para></returns>        
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler.WrapExceptionType">
            <summary>
            <para>Gets the <see cref="T:System.Type"/> of exception to wrap the original exception with.</para>
            </summary>
            <value>
            <para>The <see cref="T:System.Type"/> of exception to wrap the original exception with.</para>
            </value>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.WrapHandler.WrapExceptionMessage">
            <summary>
            <para>Gets the message of the wrapped exception.</para>
            </summary>
            <value>
            <para>The message of the wrapped exception.</para>
            </value>
        </member>
        <member name="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter">
            <summary>
            Represents an exception formatter that formats exception objects as XML.
            </summary>	
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.#ctor(System.Xml.XmlWriter,System.Exception,System.Guid)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter"/> class using the specified <see cref="T:System.Xml.XmlWriter"/> and <see cref="T:System.Exception"/> objects.
            </summary>
            <param name="xmlWriter">The <see cref="T:System.Xml.XmlWriter"/> in which to write the XML.</param>
            <param name="exception">The <see cref="T:System.Exception"/> to format.</param>
            <param name="handlingInstanceId">The id of the handling chain.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.#ctor(System.IO.TextWriter,System.Exception,System.Guid)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter"/> class using the specified <see cref="T:System.IO.TextWriter"/> and <see cref="T:System.Exception"/> objects.
            </summary>
            <param name="writer">The <see cref="T:System.IO.TextWriter"/> in which to write the XML.</param>
            <param name="exception">The <see cref="T:System.Exception"/> to format.</param>
            <remarks>
            An <see cref="T:System.Xml.XmlTextWriter"/> with indented formatting is created from the  specified <see cref="T:System.IO.TextWriter"/>.
            </remarks>
            <param name="handlingInstanceId">The id of the handling chain.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.Format">
            <summary>
            Formats the <see cref="T:System.Exception"/> into the underlying stream.
            </summary>       
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.WriteDateTime(System.DateTime)">
            <summary>
            Writes the current date and time to the <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="utcNow">The current time.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.WriteMessage(System.String)">
            <summary>
            Writes the value of the <see cref="P:System.Exception.Message"/> property to the <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="message">The message to write.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.WriteDescription">
            <summary>
            Writes a generic description to the <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.WriteHelpLink(System.String)">
            <summary>
            Writes the value of the specified help link taken
            from the value of the <see cref="P:System.Exception.HelpLink"/>
            property to the <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="helpLink">The exception's help link.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.WriteStackTrace(System.String)">
            <summary>
            Writes the value of the specified stack trace taken from the value of the <see cref="P:System.Exception.StackTrace"/> property to the <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="stackTrace">The stack trace of the exception.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.WriteSource(System.String)">
            <summary>
            Writes the value of the specified source taken from the value of the <see cref="P:System.Exception.Source"/> property to the <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="source">The source of the exception.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.WriteExceptionType(System.Type)">
            <summary>
            Writes the value of the <see cref="P:System.Type.AssemblyQualifiedName"/>
            property for the specified exception type to the <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="exceptionType">The <see cref="T:System.Type"/> of the exception.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.WriteException(System.Exception,System.Exception)">
            <summary>
            Writes and formats the exception and all nested inner exceptions to the <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="exceptionToFormat">The exception to format.</param>
            <param name="outerException">The outer exception. This value will be null when writing the outer-most exception.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.WritePropertyInfo(System.Reflection.PropertyInfo,System.Object)">
            <summary>
            Writes the name and value of the specified property to the <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="propertyInfo">The reflected <see cref="T:System.Reflection.PropertyInfo"/> object.</param>
            <param name="value">The value of the <see cref="T:System.Reflection.PropertyInfo"/> object.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.WriteFieldInfo(System.Reflection.FieldInfo,System.Object)">
            <summary>
            Writes the name and value of the <see cref="T:System.Reflection.FieldInfo"/> object to the <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="fieldInfo">The reflected <see cref="T:System.Reflection.FieldInfo"/> object.</param>
            <param name="value">The value of the <see cref="T:System.Reflection.FieldInfo"/> object.</param>
        </member>
        <member name="M:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.WriteAdditionalInfo(System.Collections.Specialized.NameValueCollection)">
            <summary>
            Writes additional information to the <see cref="T:System.Xml.XmlWriter"/>.
            </summary>
            <param name="additionalInformation">Additional information to be included with the exception report</param>
        </member>
        <member name="P:Microsoft.Practices.EnterpriseLibrary.ExceptionHandling.XmlExceptionFormatter.Writer">
            <summary>
            Gets the underlying <see cref="T:System.Xml.XmlWriter"/> that the formatted exception is written to.
            </summary>
            <value>
            The underlying <see cref="T:System.Xml.XmlWriter"/> that the formatted exception is written to.
            </value>
        </member>
    </members>
</doc>
