﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Purolator.SmartSort.Common
{
    public class Error
    {
        private string _code;
        public string Code
        {
            get
            {
                return _code;
            }
            set
            {
                _code = value;
            }
        }

        private string _description;
        public string Description
        {
            get
            {
                return _description;
            }
            set
            {
                _description = value;
            }
        }

        private string _additionalInformation;
        public string AdditionalInformation
        {
            get
            {
                return _additionalInformation;
            }
            set
            {
                _additionalInformation = value;
            }
        }

        public Error()
        {
        }

        public Error(string description, string additionalInformation)
        {
            _code = "99999";
            _description = description;
            _additionalInformation = additionalInformation;
        }


        public Error(string code, string description, string additionalInformation)
        {
            _code = code;
            _description = description;
            _additionalInformation = additionalInformation;
        }

    }
}
