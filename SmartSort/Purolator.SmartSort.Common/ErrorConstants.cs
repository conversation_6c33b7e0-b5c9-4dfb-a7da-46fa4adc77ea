﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Purolator.SmartSort.Common
{
    public class ErrorConstants
    {
        public const string SS_CREATE_SHIPMENT_FAILED = "SS_CREATE_SHIPMENT_FAILED";

        
        public const string SS_SERVICE_AVAILABILITY_SUGGESTED_ADDRESS_RETURNED = "SS_SERVICE_AVAILABILITY_SUGGESTED_ADDRESS_RETURNED";

        public const string SS_ERROR_SERVICE_AVAILABILITY_COUNTRY_NOT_FOUND = "SS_ERROR_SERVICE_AVAILABILITY_COUNTRY_NOT_FOUND";
        public const string SS_ERROR_INVALID_POSTAL_CODE = "SS_ERROR_INVALID_POSTAL_CODE";

        public const string SS_GENERIC_ERROR = "SS_ERROR_GENERIC";
        public const string SS_ERROR_CONFIRMATION_NO_REQUIRED = "SS_ERROR_CONFIRMATION_NO_REQUIRED";
        public const string SS_ERROR_PIN_REQUIRED = "SS_ERROR_PIN_REQUIRED";
        public const string SS_ERROR_PICKUP_CONFIRMATION_NUMBER_REQUIRED = "SS_ERROR_PICKUP_CONFIRMATION_NUMBER_REQUIRED";

        public const string SS_ERROR_CITY_REQUIRED = "SS_ERROR_CITY_REQUIRED";
        public const string SS_ERROR_PROVINCECODE_REQUIRED = "SS_ERROR_PROVINCECODE_REQUIRED";
        public const string SS_ERROR_POSTALCODE_REQUIRED = "SS_ERROR_POSTALCODE_REQUIRED";
        public const string SS_ERROR_COUNTRYCODE_REQUIRED = "SS_ERROR_COUNTRYCODE_REQUIRED";
        public const string SS_ERROR_SERVICE_DIRECTORY_INVALID_ADDRESS = "SS_ERROR_SERVICE_DIRECTORY_INVALID_ADDRESS";
        public const string SS_ERROR_FORCED_HOLD_FOR_PICKUP = "SS_ERROR_FORCED_HOLD_FOR_PICKUP";


        public const string SS_ERROR_SHIPMENT_REQUIRED = "SS_ERROR_SHIPMENT_REQUIRED";
        public const string SS_ERROR_PICKUP_REQUIRED = "SS_ERROR_PICKUP_REQUIRED";
        public const string SS_ERROR_PICKUP_INVALID_TYPE = "SS_ERROR_PICKUP_INVALID_TYPE";
        public const string SS_ERROR_FROM_ADDRESS_REQUIRED = "SS_ERROR_FROM_ADDRESS_REQUIRED";
        public const string SS_ERROR_TO_ADDRESS_REQUIRED = "SS_ERROR_TO_ADDRESS_REQUIRED";

        public const string SS_INVALID_CREDIT_CARD_NUMBER = "SS_INVALID_CREDIT_CARD_NUMBER";

        public const string SS_ERROR_ESTIMATE_FROM_CITY_REQUIRED = "SS_ERROR_ESTIMATE_FROM_CITY_REQUIRED";
        public const string SS_ERROR_ESTIMATE_FROM_PROVINCE_REQUIRED = "SS_ERROR_ESTIMATE_FROM_PROVINCE_REQUIRED";
        public const string SS_ERROR_ESTIMATE_FROM_POSTAL_CODE_REQUIRED = "SS_ERROR_ESTIMATE_FROM_POSTAL_CODE_REQUIRED";
        public const string SS_ERROR_ESTIMATE_TO_CITY_REQUIRED = "SS_ERROR_ESTIMATE_TO_CITY_REQUIRED";
        public const string SS_ERROR_ESTIMATE_TO_PROVINCE_REQUIRED = "SS_ERROR_ESTIMATE_TO_PROVINCE_REQUIRED";
        public const string SS_ERROR_ESTIMATE_TO_POSTAL_CODE_REQUIRED = "SS_ERROR_ESTIMATE_TO_POSTAL_CODE_REQUIRED";
        public const string SS_ERROR_ESTIMATE_PRODUCT_REQUIRED = "SS_ERROR_ESTIMATE_PRODUCT_REQUIRED";
        public const string SS_ERROR_ESTIMATE_WEIGHT_REQUIRED = "SS_ERROR_ESTIMATE_WEIGHT_REQUIRED";
        public const string SS_CLIENT_INTEGRATION_CREDIT_CARD_DATA_POWER_ERROR = "SS_CLIENT_INTEGRATION_CREDIT_CARD_DATA_POWER_ERROR";
    }
}
