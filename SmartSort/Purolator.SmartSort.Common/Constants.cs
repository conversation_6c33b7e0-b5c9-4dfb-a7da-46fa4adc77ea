﻿using System;
using System.Text;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;

namespace Purolator.SmartSort.Common
{
    public class Constants
    {
        public const string DB_CONNECTION_NAME = "SSDB";

        public const string CONFIG_DEPLOY_ENVIRONMENT = "DEPLOY_ENVIRONMENT";

        public const string CONFIG_COOKIE_DOMAIN = "COOKIE_DOMAIN_PUROLATOR_COM";

        public const string CONFIG_DEPLOY_LOCAL = "LOCAL";
        public const string CONFIG_DEPLOY_INTEGRATION = "INTEGRATION";
        public const string CONFIG_DEPLOY_DEVELOPMENT = "DEVELOPMENT";
        public const string CONFIG_DEPLOY_CERTIFICATION = "CERTIFICATION";
        public const string CONFIG_DEPLOY_PRODUCTION = "PRODUCTION";
        public const string CONFIG_DECLARED_VALUE_MINIMUM_VALUE = "DECLARED_VALUE_MINIMUM_VALUE";
        public const string CONFIG_DECLARED_VALUE_MAXIMUM_VALUE = "DECLARED_VALUE_MAXIMUM_VALUE";

        // Service endpoints constants
        public const string LoadChartPlanServiceEndpoint = "net.tcp://localhost/SmartSortServices";
        public const string AddressTriagingServiceEndpoint = "net.tcp://localhost/SmartSortServices";
        public const string CourierManifestServiceEndpoint = "net.tcp://localhost/SmartSortServices";
        public const string FacilityMasterServiceEndpoint = "net.tcp://localhost/SmartSortServices";
        public const string RemediateServiceEndpoint = "net.tcp://localhost/SmartSortServices";
        public const string RouteMasterServiceEndpoint = "net.tcp://localhost/SmartSortServices";

        // Language Constants
        public const string LANGUAGE_ENGLISH = "E";
        public const string LANGUAGE_FRENCH = "F";
        public const string LANGUAGE_DEFAULT = "E";

        //Smart Sort Language Constants
        public const string LANGUAGE_SmartSort_FRENCH = "F";
        public const string LANGUAGE_SmartSort_ENGLISH = "E";


        //Report Printing Default
        public const string DEFAULTPRINTER_LASER = "L";
        public const string DEFAULTPRINTER_THERMAL = "T";

        // Yes/No Constants
        public const string YES_CODE = "Y";
        public const string NO_CODE = "N";

        //Canada Us phone constants
        public const string PHONE_COUNTRY_CODE_CANADA_US = "1";
        public const string COUNTRY_CANADA_CODE = "CA";
        public const int MIN_DOM_US_PHONE_LENGTH = 7;
        public const int MAX_DOM_US_PHONE_LENGTH = 7;

        #region Regular Expressions

        // Email
        public const string REGEX_EMAIL = @"^(([a-zA-Z0-9][\w\.-]*[a-zA-Z0-9]|[a-zA-Z0-9])@[a-zA-Z0-9][\w\.-]*[a-zA-Z0-9]\.[a-zA-Z][a-zA-Z\.]*[a-zA-Z])\s*$";

        // Postal Code 
        public const string REGEX_GUI_POSTALCODE = @"^([A-Za-z]\d[A-Za-z]\ {0,1}\-{0,1}\d[A-Za-z]\d)$";
        public const string REGEX_GUI_US_CND_POSTALCODE = @"^\d{5}(-\d{4})?$|^([a-zA-Z]\d[a-zA-Z]( )?\d[a-zA-Z]\d)$";
        public const string REGEX_POSTALCODE = @"[A-Z][0-9][A-Z][0-9][A-Z][0-9]";
        public const string REGEX_US_POSTALCODE = @"^\d{5}(-\d{4})?$";
        public const string REGEX_ZIPCODE = @"^\d{5}$|^\d{9}$";
        public const string REGEX_POSITIVE_INTEGER = @"^\d+$";

        // Phone 
        public const string REGEX_PHONE = @"^\s*\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})\s*$";

        // Phone Extension 
        public const string REGEX_PHONE_EXT = @"^\d{0,6}$";

        // Credit Card (VISA, MC, AE)
        public const string REGEX_CREDIT_CARD = @"^\s*(((4\d{3})|(5[1-5]\d{2}))(-?|\040?)(\d{4}(-?|\040?)){3}|^(3[4,7]\d{2})(-?|\040?)\d{6}(-?|\040?)\d{5})\s*";

        #endregion

        public const string SHORT_DATE_FORMAT_ENGLISH = "MMMM d";
        public const string SHORT_DATE_FORMAT_FRENCH = "d MMMM";

        public const string MEDIUM_DATE_FORMAT_ENGLISH = "ddd, MMMM d";
        public const string MEDIUM_DATE_FORMAT_FRENCH = "ddd, d MMMM";

        public const string LONG_DATE_FORMAT_ENGLISH = "dddd, MMMM dd";
        public const string LONG_DATE_FORMAT_FRENCH = "dddd, d MMMM";

        // Purolator Domain Language Cookie Name
        public const string COOKIE_NAME_PUROLATOR_DOMAIN_LANGUAGE = "PurolatorLanguage";
        public const string COOKIE_SmartSort_USER_AUTHENICATED = "SmartSortUserAuthenicated";

        //Query string parameter
        public const string QUERY_STRING_PARAMETER_LANGUAGE = "lang";

        //Credit Card Type Codes
        public const string CREDIT_CARD_TYPE_VISA = "VISA";
        public const string CREDIT_CARD_TYPE_AMEX = "AMEX";
        public const string CREDIT_CARD_TYPE_MC = "MC";

        public const string regex_AMEXPattern = @"^3[47][0-9]{13}$";
        public const string regex_MasterCardPattern = @"^5[1-5][0-9]{14}$";
        public const string regex_VisaCardPattern = @"^4[0-9]{12}(?:[0-9]{3})?$";

        public const int MIN_CDN_POSTAL_CODE_LENGTH = 6;
        public const int MAX_CDN_POSTAL_CODE_LENGTH = 6;

        public const int MIN_US_POSTAL_CODE_LENGTH = 5;
        public const int MAX_US_POSTAL_CODE_LENGTH = 10;


        public const string LOGGING_CATEGORY_BUSINESS = "Business";
        public const string LOGGING_CATEGORY_CLIENT_INTEGRATION = "Client Integration";
        public const string LOGGING_CATEGORY_DATA_ACCESS = "Data Access";
        public const string LOGGING_CATEGORY_USER_INTERFACE = "User Interface";
        public const string LOGGING_CATEGORY_GENERAL = "General";
        public const string LOGGING_CATEGORY_TRACE = "Trace";


        public const int FROM_ADDRESS_LINE1_MAX_LENGTH = 25;
        public const int FROM_ADDRESS_LINE2_MAX_LENGTH = 25;

        #region CCT Constants
        public const string CCT_LINK_DATE_FORMAT = "yyyy-MM-dd";
        public const string CCT_LINK_DATETIME_FORMAT = "yyyy-MM-dd HH:mm";
        public const string CCT_HTTPREQ_TIME_STAMP = "Time";
        public const string CCT_HTTPREQ_FIRST_NAME = "FrstNm";
        public const string CCT_HTTPREQ_LAST_NAME = "LstNm";
        public const string CCT_HTTPREQ_ADDRESS_COMPANY = "Cmpny";
        public const string CCT_HTTPREQ_ADDRESS_STREET_NUMBER = "StrtNmbr";
        public const string CCT_HTTPREQ_ADDRESS_STREET_SUFFIX_CODE = "StrtSffxCd";
        public const string CCT_HTTPREQ_ADDRESS_STREET_NAME = "StrtNm";
        public const string CCT_HTTPREQ_ADDRESS_STREET_TYPE_CODE = "StrtTypCd";
        public const string CCT_HTTPREQ_ADDRESS_STREET_DIRECTION_CODE = "StrtDrctnCd";
        public const string CCT_HTTPREQ_ADDRESS_FLOOR = "Flr";
        public const string CCT_HTTPREQ_ADDRESS_SUITE = "St";
        public const string CCT_HTTPREQ_ADDRESS_ENTRY_CODE = "EntryCd";
        public const string CCT_HTTPREQ_ADDRESS_LINE_2 = "Addrss2";
        public const string CCT_HTTPREQ_ADDRESS_LINE_3 = "Addrss3";
        public const string CCT_HTTPREQ_ADDRESS_COUNTRY = "Cntry";
        public const string CCT_HTTPREQ_ADDRESS_PROVINCE = "Prvnce";
        public const string CCT_HTTPREQ_ADDRESS_CITY = "Cty";
        public const string CCT_HTTPREQ_ADDRESS_POSTAL_CODE = "PstlCd";
        public const string CCT_HTTPREQ_PHONE_AREA_CODE = "PhAreaCd";
        public const string CCT_HTTPREQ_PHONE_NUMBER = "PhNmbr";
        public const string CCT_HTTPREQ_PHONE_EXTENSION = "PhExt";
        public const string CCT_HTTPREQ_EMAIL_ADDRESS = "Email";
        public const string CCT_HTTPREQ_SYSTEM_EXTERNAL_REFERENCE = "ExtRef";
        public const string CCT_HTTPREQ_SYSTEM_PAGE_SOURCE = "PageSource";
        public const string CCT_HTTPREQ_ACCOUNT_NUMBER = "AccntNmbr";
        public const string CCT_HTTPREQ_SOURCE_ID = "SrcId";
        #endregion CCT Constants

    }

}
