﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Text;
using System.Diagnostics;
using System.Configuration;
using Microsoft.Practices.EnterpriseLibrary.Common.Configuration;
using ELogger = Microsoft.Practices.EnterpriseLibrary.Logging.Logger;
using Microsoft.Practices.EnterpriseLibrary.Logging;




namespace Purolator.SmartSort.Common
{
    
    public class Logger
    {   
        static Logger()
        {
            // create logging folder
            try
            {
                if (!Directory.Exists(@"C:\AppLogs\SmartSort"))
                {
                    Directory.CreateDirectory(@"C:\AppLogs\SmartSort");
                }
            }
            catch 
            {}

            // initialize logging
            IConfigurationSource configurationSource = ConfigurationSourceFactory.Create();
            LogWriterFactory logWriterFactory = new LogWriterFactory(configurationSource);
            ELogger.SetLogWriter(logWriterFactory.Create());           
        }


        private static string MapCategoryName(LogCategories cat)
        {
            switch(cat)
            {                
                case LogCategories.WEB_PORTAL: return "WebPortal";
                case LogCategories.SHIPMENT_INCOMING: return "ShipmentIncoming";
                case LogCategories.COURIER_MANIFEST: return "CourierManifest";
                case LogCategories.SCAN_SERVICE: return "ScanLogSync";
                case LogCategories.ENTERPRISE_EVENT: return "EnterpriseEvent";
                case LogCategories.ADDRESS_VALIDATION: return "AddressValidation";
                case LogCategories.PRINT_SERVICE: return "PrintService";


                default: return "General";
            }       
        }
      
        public static void Debug(string message, LogCategories category = LogCategories.DEFAULT, Exception ex = null)
        {
            Write(message, null, TraceEventType.Verbose, category);
        }

        public static void Info(string message, LogCategories category = LogCategories.DEFAULT, Exception ex = null)
        {
            Write(message, null, TraceEventType.Information, category);
        }

        public static void Warn(string message, LogCategories category = LogCategories.DEFAULT, Exception ex = null)
        {
            Write(message, ex, TraceEventType.Warning, category);
        }

        public static void Error(string message, LogCategories category = LogCategories.DEFAULT, Exception ex = null)
        {
            Write(message, ex, TraceEventType.Error, category);            
        }

        public static void Critical(string message, LogCategories category = LogCategories.DEFAULT, Exception ex = null)
        {
            Write(message, ex, TraceEventType.Critical, category);
        }

        public static void Write(string message, Exception ex, TraceEventType level, LogCategories category)
        {
            LogEntry entry = new LogEntry();
            entry.Categories = new List<string> { MapCategoryName(category) };
            entry.Severity = level;
            entry.Message = message;
            if (ex != null)
            {
                Dictionary<string, object> exProperties = new Dictionary<string, object>();
                exProperties.Add("ExceptionMessage", ex.Message);
                exProperties.Add("ExceptionStackTrace", ex.StackTrace);
                entry.ExtendedProperties = exProperties;
            }

            ELogger.Write(entry);
        }

    }
}
