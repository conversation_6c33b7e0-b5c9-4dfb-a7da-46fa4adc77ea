﻿using System;
using System.Web;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using System.Threading;
using System.Globalization;

using System.Text.RegularExpressions;

namespace Purolator.SmartSort.Common
{
    public class Helpers
    {
        public static string GetDeployEnvironment()
        {
            string retVal = Constants.CONFIG_DEPLOY_LOCAL;
            if (ConfigurationManager.AppSettings[Constants.CONFIG_DEPLOY_ENVIRONMENT].ValueExists())
            {
                retVal = ConfigurationManager.AppSettings[Constants.CONFIG_DEPLOY_ENVIRONMENT];
            }
            return retVal;
        }

        public static bool isProductionEnvironment()
        {
            return GetDeployEnvironment().Equals(Constants.CONFIG_DEPLOY_PRODUCTION);
        }

        public static bool isCertificationEnvironment()
        {
            return GetDeployEnvironment().Equals(Constants.CONFIG_DEPLOY_CERTIFICATION);
        }

        public static bool isIntegrationEnvironment()
        {
            return GetDeployEnvironment().Equals(Constants.CONFIG_DEPLOY_INTEGRATION);
        }

        public static bool isDevelopmentEnvironment()
        {
            return GetDeployEnvironment().Equals(Constants.CONFIG_DEPLOY_DEVELOPMENT);
        }

        public static bool isLocalEnvironment()
        {
            return GetDeployEnvironment().Equals(Constants.CONFIG_DEPLOY_LOCAL);
        }       

        public static int GetDeclaredValueMinimumValue()
        {
            int retVal = 0;
            if (ConfigurationManager.AppSettings[Constants.CONFIG_DECLARED_VALUE_MINIMUM_VALUE].ValueExists())
            {
                if (ConfigurationManager.AppSettings[Constants.CONFIG_DECLARED_VALUE_MINIMUM_VALUE].IsInteger())
                {
                    retVal = ConfigurationManager.AppSettings[Constants.CONFIG_DECLARED_VALUE_MINIMUM_VALUE].GetInteger();
                }
            }
            return retVal;
        }

        public static int GetDeclaredValueMaximumValue()
        {
            int retVal = int.MaxValue;
            if (ConfigurationManager.AppSettings[Constants.CONFIG_DECLARED_VALUE_MAXIMUM_VALUE].ValueExists())
            {
                if (ConfigurationManager.AppSettings[Constants.CONFIG_DECLARED_VALUE_MAXIMUM_VALUE].IsInteger())
                {
                    retVal = ConfigurationManager.AppSettings[Constants.CONFIG_DECLARED_VALUE_MAXIMUM_VALUE].GetInteger();
                }
            }
            return retVal;
        }

        public static bool IsCurrentLanguageEnglish()
        {
            return IsEnglish(GetCurrentLanguage());
        }

        public static bool IsCurrentLanguageFrench()
        {
            return IsFrench(GetCurrentLanguage());
        }

        public static bool IsFrench(string languageCode)
        {
            bool retVal = false;
            if (languageCode.ValueExists() && languageCode.ToUpper().Equals(Constants.LANGUAGE_FRENCH))
            {
                retVal = true;
            }
            return retVal;
        }

        public static bool IsEnglish(string languageCode)
        {
            return !IsFrench(languageCode);
        }

        public static string NullToEmptyString(string text)
        {
            if (text.ValueExists())
                return text.Trim();
            else
                return string.Empty;
        }

        public static string NullToEmptyString(int? inValue)
        {
            if (inValue.HasValue)
                return inValue.ToString();
            else
                return string.Empty;
        }


        public static int RoundDimension(decimal inValue)
        {
            return (int)AdjustValue(inValue, 0);
        }

        public static int RoundDimension(decimal? inValue)
        {
            int retVal = 0;
            if (inValue.HasValue)
            {
                retVal = RoundDimension(inValue.Value);
            }
            return retVal;
        }

        private static decimal AdjustValue(decimal value, int decPlaces)
        {
            decimal retVal = 0;

            if (Math.Round(value, decPlaces) == value)
            {
                //No Rounding Needed
                retVal = value;
            }
            else
            {
                decimal offsetAmount = 0.5m;
                if (value < 0)
                {
                    //Flip Sign when Negative
                    offsetAmount = offsetAmount * -1;
                }

                for (int i = 0; i < decPlaces; i++)
                {
                    offsetAmount = offsetAmount / 10;
                }
                retVal = Math.Round((value + offsetAmount), decPlaces);
            }
            return retVal;
        }

        public static bool IsCanadaCountryCode(string countryCode)
        {
            return (countryCode.ValueExists() && countryCode.ToUpper().Equals(Constants.COUNTRY_CANADA_CODE));
        }

        public static string StripPostalZipCode(string pcode)
        {
            string retVal = string.Empty;
            if (pcode.ValueExists())
            {
                /*trim white spaces and spaces between */
                retVal = pcode.ToUpper().Replace(" ", string.Empty).Replace("-", string.Empty).Trim();
            }
            return retVal;
        }

        public static string StripPhoneNumber(string phoneText)
        {
            string retVal = string.Empty;
            if (phoneText.ValueExists())
            {
                retVal = Regex.Replace(phoneText, @"[\D]", string.Empty);
            }
            return retVal;
        }


        public static bool IsValidCanadianPostalCodeFormat(string pcode)
        {
            bool retVal = false;
            if (pcode.ValueExists())
            {
                //trim white spaces and spaces between
                string postalCode = StripPostalZipCode(pcode);

                //	Check postal code format for A9A9A9
                if (postalCode.Length < Constants.MIN_CDN_POSTAL_CODE_LENGTH ||
                    postalCode.Length > Constants.MAX_CDN_POSTAL_CODE_LENGTH)
                {
                    retVal = false;
                }
                else
                {
                    Regex rx = new Regex(Constants.REGEX_POSTALCODE);
                    retVal = rx.IsMatch(postalCode);
                }
            }
            return retVal;
        }

        public static bool IsValidUSPostalCodeFormat(string pcode)
        {
            bool retVal = false;
            if (pcode.ValueExists())
            {
                //trim white spaces and spaces between
                string postalCode = StripPostalZipCode(pcode);

                //	Check postal code format for A9A9A9
                if (postalCode.Length < Constants.MIN_US_POSTAL_CODE_LENGTH ||
                    postalCode.Length > Constants.MAX_US_POSTAL_CODE_LENGTH)
                {
                    retVal = false;
                }
                else
                {
                    Regex rx = new Regex(Constants.REGEX_US_POSTALCODE);
                    retVal = rx.IsMatch(postalCode);
                }
            }
            return retVal;
        }

        public static bool IsValidZipCodeFormat(string zipCode)
        {
            bool retVal = false;
            if (zipCode.ValueExists())
            {
                //remove spaces and "-"
                zipCode = StripPostalZipCode(zipCode);

                //check whether the length is 5-digit or 9-digit long.
                Regex rx = new Regex(Constants.REGEX_ZIPCODE);
                retVal = rx.IsMatch(zipCode);
            }
            return retVal;
        }

        /// <summary>
        /// Compares content of the two specified string values, ignoring case
        /// </summary>
        /// <param name="string1">Value of the first string</param>
        /// <param name="string2">Value of the second string</param>
        /// <returns>A boolean value indicating whether the 2 specified strings are the same</returns>
        public static bool StringCompare(string string1, string string2)
        {
            return Helpers.StringCompare(string1, string2, true);
        }

        public static bool StringCompare(string string1, string string2, bool ignoreCase)
        {
            StringComparison compareType = StringComparison.InvariantCulture;
            if (ignoreCase) compareType = StringComparison.InvariantCultureIgnoreCase;
            return (string.Compare(NullToEmptyString(string1), NullToEmptyString(string2), compareType) == 0);
        }

        private static string GetShortDateFormat()
        {
            return IsEnvironmentFrench() ?
                Constants.SHORT_DATE_FORMAT_FRENCH :
                Constants.SHORT_DATE_FORMAT_ENGLISH;
        }

        private static string GetMediumDateFormat()
        {
            return IsEnvironmentFrench() ?
                Constants.MEDIUM_DATE_FORMAT_FRENCH :
                Constants.MEDIUM_DATE_FORMAT_ENGLISH;
        }

        private static string GetLongDateFormat()
        {
            return IsEnvironmentFrench() ?
                Constants.LONG_DATE_FORMAT_FRENCH :
                Constants.LONG_DATE_FORMAT_ENGLISH;
        }

        public static string FormatShortDate(DateTime? date)
        {
            return date != null ? date.Value.ToString(GetShortDateFormat()) : string.Empty;
        }

        public static string FormatMediumDate(DateTime? date)
        {
            return date != null ? date.Value.ToString(GetMediumDateFormat()) : string.Empty;
        }

        public static string FormatLongDate(DateTime? date)
        {
            return date != null ? date.Value.ToString(GetLongDateFormat()) : string.Empty;
        }

        private string GetDaySuffixEnglish(int day)
        {
            switch (day)
            {
                case 1:
                case 21:
                case 31:
                    return "st";
                case 2:
                case 22:
                    return "nd";
                case 3:
                case 23:
                    return "rd";
                default:
                    return "th";
            }
        }

        public static string GetSurchargeItemDescription(string code)
        {
            string retVal = code;
            if (Resources.CommonResources.ResourceExists(code))
            {
                retVal = Resources.CommonResources.GetResourceString(code);
            }
            else
            {
                retVal = "Resource Not Found For Code: " + code;
            }
            return retVal;
        }

        public static string GetCurrentLanguage()
        {
            string retVal = Constants.LANGUAGE_ENGLISH;
            if (IsEnvironmentFrench()) retVal = Constants.LANGUAGE_FRENCH;
            return retVal;
        }

        /// <summary>
        /// Returns True if the current culture is French
        /// </summary>
        /// <returns>Boolean</returns>
        public static bool IsEnvironmentFrench()
        {
            if (Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName.Equals("fr"))
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// Format postal code for displaying. 
        /// </summary>
        /// <param name="pcode">the postal code</param>
        /// <returns>the formatted postal code</returns>
        public static string FormatPostalCode(string pcode)
        {
            string retVal = string.Empty;
            if (pcode.ValueExists())
            {
                retVal = StripPostalZipCode(pcode);
                if (retVal.Length > 3)
                {
                    retVal = retVal.Substring(0, 3) + " " + retVal.Substring(3);
                }
            }
            return retVal;
        }

        public static string GetPurolatorLocatorUrl(string location)
        {
            var url = string.Empty;
            if (location.ValueExists())
            {
                var urlFormat = ConfigurationManager.AppSettings["PurolatorLocatorUrl"];
                url = IsCurrentLanguageEnglish() ?
                    string.Format(urlFormat, "en", location) :
                    string.Format(urlFormat, "fr", location);
            }
            return url;
        }

        public static string MaskCreditCardNumber(string creditCardNumber)
        {
            char paddingChar = 'X';
            string maskedCC = string.Empty;
            if (creditCardNumber.ValueExists())
            {
                string cleanCC = creditCardNumber.Trim();
                maskedCC = cleanCC;
                if (cleanCC.Length > 8)
                {
                    //How Many Characters To Mask Out?
                    int maskCount = cleanCC.Length - 8;

                    //Create Masked CC Number
                    maskedCC = cleanCC.Substring(0, 4) + string.Empty.PadLeft(maskCount, paddingChar) + cleanCC.Substring(cleanCC.Length - 4, 4);
                }
            }
            return maskedCC;
        }

        public static string MaskCreditCardCVV(string cvv)
        {
            char paddingChar = 'X';
            string maskedCVV = string.Empty;
            if (cvv.ValueExists())
            {
                int maskCount = cvv.Trim().Length;
                maskedCVV = string.Empty.PadLeft(maskCount, paddingChar);
            }
            return maskedCVV;
        }


        public static string MaskCreditCardHolderName(string cardHolderName)
        {
            char paddingChar = 'X';
            string maskedCardHolderName = string.Empty;
            if (cardHolderName.ValueExists())
            {
                int maskCount = cardHolderName.Trim().Length;
                maskedCardHolderName = string.Empty.PadLeft(maskCount, paddingChar);
            }
            return maskedCardHolderName;
        }

        public static string MoneyFormat(int? amount)
        {
            if (amount.HasValue)
            {
                return amount.Value.ToString("C0");
            }
            return string.Empty;
        }

        public static string MoneyFormat(decimal? amount)
        {
            if (amount.HasValue)
            {
                return amount.Value.ToString("C");
            }
            return string.Empty;
        }

        public static string RemoveDiacritics(string text)
        {
            var normalizedString = text.Normalize(NormalizationForm.FormD);
            var stringBuilder = new StringBuilder();

            foreach (var c in normalizedString)
            {
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
        }


        public static void ParsePhone(string phoneNumber, out string outPhoneCountryCode, out string outPhoneAreaCode, out string outPhoneNumber)
        {
            string dummy = string.Empty;
            ParsePhone(phoneNumber, out outPhoneCountryCode, out outPhoneAreaCode, out outPhoneNumber, out dummy);
        }

        public static void ParsePhone(string phoneNumber, out string outPhoneCountryCode, out string outPhoneAreaCode, out string outPhoneNumber, out string outExtension)
        {
            //Initialize Out Variables
            outPhoneCountryCode = string.Empty;
            outPhoneAreaCode = string.Empty;
            outPhoneNumber = string.Empty;
            outExtension = string.Empty;

            if (phoneNumber.ValueExists())
            {
                outPhoneCountryCode = Constants.PHONE_COUNTRY_CODE_CANADA_US;
                string tempPhone = Helpers.StripPhoneNumber(phoneNumber);

                int areaCodeLength = 3;
                int phoneLength = 7;
                int extLength = 6;

                if (tempPhone.Length >= areaCodeLength)
                {
                    //Extract Area Code [First 3 Digits]
                    outPhoneAreaCode = tempPhone.Substring(0, areaCodeLength);

                    //Chop Off First 3 Digits
                    tempPhone = tempPhone.Substring(areaCodeLength);

                    if (tempPhone.Length >= phoneLength)
                    {
                        //Extract Phone [Next 7 Digits]
                        outPhoneNumber = tempPhone.Substring(0, phoneLength);

                        //Chop Off 7 Digits
                        tempPhone = tempPhone.Substring(phoneLength);

                        if (tempPhone.Length >= extLength)
                        {
                            //Extract Extension
                            outExtension = tempPhone.Substring(0, extLength);

                            //Chop Off 6 Digits
                            tempPhone = tempPhone.Substring(extLength);
                        }
                        else
                        {
                            //Leftovers into Extension
                            outExtension = tempPhone;
                        }
                    }
                    else
                    {
                        outPhoneNumber = tempPhone;
                    }
                }
                else
                {
                    outPhoneNumber = tempPhone;
                }
            }
        }

        public static bool IsInteger(string value)
        {
            bool retVal = false;
            try
            {
                if (value.ValueExists())
                {
                    Convert.ToInt32(value);
                    retVal = true;
                }
            }
            catch
            {
                retVal = false;
            }
            return retVal;
        }

        public static int GetInteger(string value)
        {
            int myInteger = 0;
            if (IsInteger(value))
            {
                myInteger = Convert.ToInt32(value);
            }
            return myInteger;
        }


    }
}
