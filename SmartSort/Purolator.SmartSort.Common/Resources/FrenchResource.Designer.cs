﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.18444
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Purolator.SmartSort.Common.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class FrenchResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal FrenchResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Purolator.SmartSort.Common.Resources.FrenchResource", typeof(FrenchResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fermé entre.
        /// </summary>
        internal static string Closed {
            get {
                return ResourceManager.GetString("Closed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&lt;br&gt;10h30.
        /// </summary>
        internal static string DateFormatEstimate_10_30AM {
            get {
                return ResourceManager.GetString("DateFormatEstimate_10:30AM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&lt;br&gt;9h.
        /// </summary>
        internal static string DateFormatEstimate_9AM {
            get {
                return ResourceManager.GetString("DateFormatEstimate_9AM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&lt;br&gt;20h.
        /// </summary>
        internal static string DateFormatEstimate_ENDOFDAY {
            get {
                return ResourceManager.GetString("DateFormatEstimate_ENDOFDAY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&lt;br&gt;Entre 17h30 et 21h.
        /// </summary>
        internal static string DateFormatEstimate_EVENING {
            get {
                return ResourceManager.GetString("DateFormatEstimate_EVENING", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&lt;br&gt;12h (midi).
        /// </summary>
        internal static string DateFormatEstimate_NOON {
            get {
                return ResourceManager.GetString("DateFormatEstimate_NOON", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendredi.
        /// </summary>
        internal static string dayOfWeekFriday {
            get {
                return ResourceManager.GetString("dayOfWeekFriday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lundi.
        /// </summary>
        internal static string dayOfWeekMonday {
            get {
                return ResourceManager.GetString("dayOfWeekMonday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Samedi.
        /// </summary>
        internal static string dayOfWeekSaturday {
            get {
                return ResourceManager.GetString("dayOfWeekSaturday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dimanche.
        /// </summary>
        internal static string dayOfWeekSunday {
            get {
                return ResourceManager.GetString("dayOfWeekSunday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jeudi.
        /// </summary>
        internal static string dayOfWeekThursday {
            get {
                return ResourceManager.GetString("dayOfWeekThursday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mardi.
        /// </summary>
        internal static string dayOfWeekTuesday {
            get {
                return ResourceManager.GetString("dayOfWeekTuesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mercredi.
        /// </summary>
        internal static string dayOfWeekWednesday {
            get {
                return ResourceManager.GetString("dayOfWeekWednesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code d’entrée.
        /// </summary>
        internal static string entryCodeLabel {
            get {
                return ResourceManager.GetString("entryCodeLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to x.
        /// </summary>
        internal static string extensionLabel {
            get {
                return ResourceManager.GetString("extensionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Étage.
        /// </summary>
        internal static string floorLabel {
            get {
                return ResourceManager.GetString("floorLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TPS/TVH.
        /// </summary>
        internal static string GST {
            get {
                return ResourceManager.GetString("GST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fermé entre.
        /// </summary>
        internal static string HeaderClosedInfo {
            get {
                return ResourceManager.GetString("HeaderClosedInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jour.
        /// </summary>
        internal static string HeaderDay {
            get {
                return ResourceManager.GetString("HeaderDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exceptions.
        /// </summary>
        internal static string HeaderOpenCloseException {
            get {
                return ResourceManager.GetString("HeaderOpenCloseException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ouvert.
        /// </summary>
        internal static string HeaderOpenInfo {
            get {
                return ResourceManager.GetString("HeaderOpenInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TPS/TVH.
        /// </summary>
        internal static string HST {
            get {
                return ResourceManager.GetString("HST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} et {1}.
        /// </summary>
        internal static string LocationTimeFormat {
            get {
                return ResourceManager.GetString("LocationTimeFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Destination Au-Delà.
        /// </summary>
        internal static string PCH_BEYOND_POINT_DEST {
            get {
                return ResourceManager.GetString("PCH_BEYOND_POINT_DEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Origine Au-Delà.
        /// </summary>
        internal static string PCH_BEYOND_POINT_ORIGIN {
            get {
                return ResourceManager.GetString("PCH_BEYOND_POINT_ORIGIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chaîne de signatures.
        /// </summary>
        internal static string PCH_CHAIN_OF_SIGNATURE {
            get {
                return ResourceManager.GetString("PCH_CHAIN_OF_SIGNATURE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Surcharge Port Dû.
        /// </summary>
        internal static string PCH_COLLECT {
            get {
                return ResourceManager.GetString("PCH_COLLECT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marchandises Dangereuses.
        /// </summary>
        internal static string PCH_DANGEROUS_GOODS {
            get {
                return ResourceManager.GetString("PCH_DANGEROUS_GOODS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Valeur Déclarée.
        /// </summary>
        internal static string PCH_DECLARED_VALUE {
            get {
                return ResourceManager.GetString("PCH_DECLARED_VALUE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ExpressCheque.
        /// </summary>
        internal static string PCH_EXPRESS_CHEQUE {
            get {
                return ResourceManager.GetString("PCH_EXPRESS_CHEQUE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplément de Carburant.
        /// </summary>
        internal static string PCH_FUEL {
            get {
                return ResourceManager.GetString("PCH_FUEL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zone Résidentielle.
        /// </summary>
        internal static string PCH_LOW_DENSITY_DEST {
            get {
                return ResourceManager.GetString("PCH_LOW_DENSITY_DEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cueillette résidentielle.
        /// </summary>
        internal static string PCH_LOW_DENSITY_ORIGIN {
            get {
                return ResourceManager.GetString("PCH_LOW_DENSITY_ORIGIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Multipièce.
        /// </summary>
        internal static string PCH_MULTI_PIECE {
            get {
                return ResourceManager.GetString("PCH_MULTI_PIECE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signature requise liv. Résiden.
        /// </summary>
        internal static string PCH_RES_SIG_DOM {
            get {
                return ResourceManager.GetString("PCH_RES_SIG_DOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signature requise liv. rés..
        /// </summary>
        internal static string PCH_RES_SIG_INTL {
            get {
                return ResourceManager.GetString("PCH_RES_SIG_INTL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Le Samedi.
        /// </summary>
        internal static string PCH_SATURDAY_DELIVERY {
            get {
                return ResourceManager.GetString("PCH_SATURDAY_DELIVERY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cueillette Le Samedi.
        /// </summary>
        internal static string PCH_SATURDAY_PICKUP {
            get {
                return ResourceManager.GetString("PCH_SATURDAY_PICKUP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manutention spéciale.
        /// </summary>
        internal static string PCH_SPECIAL_HANDLING {
            get {
                return ResourceManager.GetString("PCH_SPECIAL_HANDLING", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplément pour tierce partie.
        /// </summary>
        internal static string PCH_THIRD_PARTY {
            get {
                return ResourceManager.GetString("PCH_THIRD_PARTY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TVQ.
        /// </summary>
        internal static string PST {
            get {
                return ResourceManager.GetString("PST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bureau.
        /// </summary>
        internal static string suiteLabel {
            get {
                return ResourceManager.GetString("suiteLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Numéro de taxe.
        /// </summary>
        internal static string taxNumberLabel {
            get {
                return ResourceManager.GetString("taxNumberLabel", resourceCulture);
            }
        }
    }
}
