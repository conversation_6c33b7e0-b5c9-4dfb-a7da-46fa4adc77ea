﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Resources;
using System.Reflection;

namespace Purolator.SmartSort.Common.Resources
{
    public class CommonResources
    {
        private static ResourceManager GetResourceManager(string languageCode)
        {
            ResourceManager resourceManager = null;
            if (Purolator.SmartSort.Common.Helpers.IsFrench(languageCode))
            {
                resourceManager = new ResourceManager("Purolator.SmartSort.Common.Resources.FrenchResource", Assembly.GetExecutingAssembly());
            }
            else
            {
                resourceManager = new ResourceManager("Purolator.SmartSort.Common.Resources.EnglishResource", Assembly.GetExecutingAssembly());
            }
            return resourceManager;
        }

        private static bool ResourceExists(ResourceManager rm, string inKey, string lanuageCode)
        {
            bool retVal = false;
            if (rm != null)
            {
                Object resourceObject = rm.GetObject(inKey);
                if (resourceObject != null)
                {
                    retVal = true;
                }
            }
            return retVal;
        }

        public static bool ResourceExists(string inKey)
        {
            return ResourceExists(inKey, Helpers.GetCurrentLanguage());
        }

        public static bool ResourceExists(string inKey, string lanuageCode)
        {
            bool retVal = false;
            ResourceManager rm = GetResourceManager(lanuageCode);
            if (rm != null)
            {
                retVal = ResourceExists(rm, inKey, lanuageCode);
            }
            return retVal;
        }

        public static string GetResourceString(string inKey)
        {
            return GetResourceString(inKey, Helpers.GetCurrentLanguage());
        }

        public static string GetResourceString(string inKey, string languageCode)
        {
            string retVal = string.Empty;
            ResourceManager resourceManager = GetResourceManager( languageCode );
            if (resourceManager != null)
            {
                if (ResourceExists(resourceManager, inKey, languageCode))
                {
                    Object resourceObject = resourceManager.GetObject(inKey);
                    if (resourceObject != null)
                    {
                        retVal = resourceManager.GetString(inKey);
                    }
                }
            }
            return retVal;
        }

    }

}
