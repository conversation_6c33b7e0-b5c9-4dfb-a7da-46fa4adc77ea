﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="QS_ERROR_PIN_REQUIRED" xml:space="preserve">
    <value>Le NIC est requis.</value>
  </data>
  <data name="QS_ERROR_GENERIC" xml:space="preserve">
    <value>Une erreur est survenue dans la couche d’intégration.</value>
  </data>
  <data name="QS_ERROR_PICKUP_CONFIRMATION_NUMBER_REQUIRED" xml:space="preserve">
    <value>Le numéro de la confirmation de la cueillette est requis.</value>
  </data>
  <data name="QS_ERROR_PICKUP_REQUIRED" xml:space="preserve">
    <value>Les renseignements sur la cueillette sont requis.</value>
  </data>
  <data name="QS_ERROR_PICKUP_INVALID_TYPE" xml:space="preserve">
    <value>Type de cueillette non valide.</value>
  </data>
  <data name="QS_ERROR_POSTALCODE_REQUIRED" xml:space="preserve">
    <value>Le code postal est requis.</value>
  </data>
  <data name="QS_ERROR_COUNTRYCODE_REQUIRED" xml:space="preserve">
    <value>Vous devez donner le code de pays.</value>
  </data>
  <data name="QS_ERROR_PROVINCECODE_REQUIRED" xml:space="preserve">
    <value>Le code de la province est requis.</value>
  </data>
  <data name="QS_ERROR_CITY_REQUIRED" xml:space="preserve">
    <value>Vous devez indiquer une ville.</value>
  </data>
  <data name="QS_ERROR_SERVICE_DIRECTORY_INVALID_ADDRESS" xml:space="preserve">
    <value>Une ou plusieurs adresses sont invalides.</value>
  </data>
  <data name="QS_SERVICE_AVAILABILITY_COUNTRY_NOT_FOUND" xml:space="preserve">
    <value>Pays introuvable {0}.</value>
  </data>
  <data name="QS_ERROR_INVALID_POSTAL_CODE" xml:space="preserve">
    <value>Nous ne desservons pas le code postal {0} que vous avez saisi. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct.</value>
  </data>
  <data name="QS_SERVICE_AVAILABILITY_SUGGESTED_ADDRESS_RETURNED" xml:space="preserve">
    <value>Le code postal {0} est seulement valide pour {1}{2}.</value>
  </data>
  <data name="QS_ERROR_FROM_ADDRESS_REQUIRED" xml:space="preserve">
    <value>L'adresse de l’expéditeur est requise.</value>
  </data>
  <data name="QS_ERROR_TO_ADDRESS_REQUIRED" xml:space="preserve">
    <value>L'adresse du destinataire est requise.</value>
  </data>
  <data name="QS_INVALID_CREDIT_CARD_NUMBER" xml:space="preserve">
    <value>Le numéro de la carte de crédit n’est pas valide.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_FROM_CITY_REQUIRED" xml:space="preserve">
    <value>La ville de l’expéditeur est requise pour produire une estimation.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_FROM_PROVINCE_REQUIRED" xml:space="preserve">
    <value>La province de l’expéditeur est requise pour produire une estimation.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_FROM_POSTAL_CODE_REQUIRED" xml:space="preserve">
    <value>Le code postal de l’expéditeur est requis pour produire une estimation.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_TO_CITY_REQUIRED" xml:space="preserve">
    <value>La ville du destinataire est requise pour produire une estimation.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_TO_PROVINCE_REQUIRED" xml:space="preserve">
    <value>La province du destinataire est requise pour produire une estimation.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_TO_POSTAL_CODE_REQUIRED" xml:space="preserve">
    <value>Le code postal du destinataire est requis pour produire une estimation.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_PRODUCT_REQUIRED" xml:space="preserve">
    <value>Le produit est requis pour produire une estimation.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_WEIGHT_REQUIRED" xml:space="preserve">
    <value>Le poids est requis pour produire une estimation.</value>
  </data>
  <data name="QS_ERROR_FORCED_HOLD_FOR_PICKUP" xml:space="preserve">
    <value>Le code postal {0} du destinataire n'est pas une adresse où nous pouvons livrer. Veuillez sélectionner une adresse À retenir pour cueillette valide.</value>
  </data>
  <data name="QS_CREATE_SHIPMENT_FAILED" xml:space="preserve">
    <value>Échec de la création de l’envoi.</value>
  </data>
  <data name="QS_ERROR_CONFIRMATION_NO_REQUIRED" xml:space="preserve">
    <value>Le numéro de confirmation de la cueillette est requis.</value>
  </data>
  <data name="100468" xml:space="preserve">
    <value>Nous sommes incapables de traiter votre transaction, car l’autorisation de facturer votre carte de crédit a été refusée. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct pour obtenir de l’aide.</value>
  </data>
  <data name="100532" xml:space="preserve">
    <value>Veuillez saisir un numéro de carte de crédit valide.</value>
  </data>
  <data name="100514" xml:space="preserve">
    <value>Le poids de votre envoi est supérieur au poids maximum autorisé pour ce type de service. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct.</value>
  </data>
  <data name="100512" xml:space="preserve">
    <value>Votre envoi dépasse les dimensions maximales autorisées. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct.</value>
  </data>
  <data name="100509" xml:space="preserve">
    <value>Votre envoi dépasse les dimensions maximales autorisées. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct.</value>
  </data>
  <data name="100467" xml:space="preserve">
    <value>Le poids de votre envoi est supérieur au poids maximum autorisé pour la destination. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct.</value>
  </data>
  <data name="100704" xml:space="preserve">
    <value>Notre système indique que des renseignements supplémentaires, comme un numéro de case postale ou un numéro de route rurale, sont requis. Veuillez saisir ces renseignements dans le champ de détails supplémentaires de l’adresse.</value>
  </data>
  <data name="100699" xml:space="preserve">
    <value>Nous sommes dans l’incapacité de prévoir une cueillette pour le moment que vous avez saisi. Veuillez changer l’heure de la cueillette ou du dépôt de votre colis à un centre d’expédition de Purolator.</value>
  </data>
  <data name="100768" xml:space="preserve">
    <value>Les cueillettes ne peuvent être prévues pour le dimanche. Veuillez sélectionner l’option de dépôt pour trouver le centre d’expédition de Purolator le plus près.</value>
  </data>
  <data name="100702" xml:space="preserve">
    <value>Le code postal se situe à l’extérieur de notre zone de cueillette. Veuillez appeler au 1-888-SHIP-123 ou déposer votre colis dans un Centre d’expédition de Purolator.</value>
  </data>
  <data name="100497" xml:space="preserve">
    <value>Nous ne pouvons pas  traiter votre requête pour l’instant. Veuillez essayer de nouveau ou composer le 1 888 SHIP-123 pour obtenir de l’aide.</value>
  </data>
  <data name="100759" xml:space="preserve">
    <value>Vos options de livraison ont été modifiées depuis la création de votre envoi. Veuillez saisir à nouveau vos coordonnées. Merci.</value>
  </data>
  <data name="100760" xml:space="preserve">
    <value>Vos options de livraison ont été modifiées depuis la création de votre envoi. Veuillez saisir à nouveau vos coordonnées. Merci.</value>
  </data>
  <data name="100632" xml:space="preserve">
    <value>Le code postal de l'expéditeur se trouve dans une zone non couverte par nos services. Veuillez appeler au 1-888-SHIP-123 ou utiliser la fonction de clavardage.</value>
  </data>
  <data name="100700" xml:space="preserve">
    <value>Nous sommes dans l’incapacité de prévoir une cueillette pour le moment que vous avez saisi. Veuillez changer l’heure de la cueillette ou du dépôt de votre colis à un centre d’expédition de Purolator.</value>
  </data>
  <data name="100432" xml:space="preserve">
    <value>Nous n’avons pas pu exécuter votre demande d’annulation. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct pour obtenir de l’aide.</value>
  </data>
  <data name="100769" xml:space="preserve">
    <value>Les envois créés avant aujourd'hui ne peuvent pas être annulés. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct.</value>
  </data>
  <data name="QS_CLIENT_INTEGRATION_CREDIT_CARD_DATA_POWER_ERROR" xml:space="preserve">
    <value>Nous sommes incapables de traiter votre transaction, car l'autorisation de facturer votre carte de crédit a été refusée. Veuillez composer le 1-888-SHIP-123 ou utiliser le clavardage en direct pour obtenir de l'aide.</value>
  </data>
</root>