﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.1026
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Purolator.SmartSort.Common.Resources.Errors {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class FrenchResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal FrenchResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Purolator.SmartSort.Common.Resources.Errors.FrenchResource", typeof(FrenchResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nous n’avons pas pu exécuter votre demande d’annulation. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct pour obtenir de l’aide..
        /// </summary>
        internal static string _100432 {
            get {
                return ResourceManager.GetString("100432", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le poids de votre envoi est supérieur au poids maximum autorisé pour la destination. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct..
        /// </summary>
        internal static string _100467 {
            get {
                return ResourceManager.GetString("100467", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nous sommes incapables de traiter votre transaction, car l’autorisation de facturer votre carte de crédit a été refusée. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct pour obtenir de l’aide..
        /// </summary>
        internal static string _100468 {
            get {
                return ResourceManager.GetString("100468", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nous ne pouvons pas  traiter votre requête pour l’instant. Veuillez essayer de nouveau ou composer le 1 888 SHIP-123 pour obtenir de l’aide..
        /// </summary>
        internal static string _100497 {
            get {
                return ResourceManager.GetString("100497", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Votre envoi dépasse les dimensions maximales autorisées. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct..
        /// </summary>
        internal static string _100509 {
            get {
                return ResourceManager.GetString("100509", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Votre envoi dépasse les dimensions maximales autorisées. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct..
        /// </summary>
        internal static string _100512 {
            get {
                return ResourceManager.GetString("100512", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le poids de votre envoi est supérieur au poids maximum autorisé pour ce type de service. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct..
        /// </summary>
        internal static string _100514 {
            get {
                return ResourceManager.GetString("100514", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Veuillez saisir un numéro de carte de crédit valide..
        /// </summary>
        internal static string _100532 {
            get {
                return ResourceManager.GetString("100532", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le code postal de l&apos;expéditeur se trouve dans une zone non couverte par nos services. Veuillez appeler au 1-888-SHIP-123 ou utiliser la fonction de clavardage..
        /// </summary>
        internal static string _100632 {
            get {
                return ResourceManager.GetString("100632", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nous sommes dans l’incapacité de prévoir une cueillette pour le moment que vous avez saisi. Veuillez changer l’heure de la cueillette ou du dépôt de votre colis à un centre d’expédition de Purolator..
        /// </summary>
        internal static string _100699 {
            get {
                return ResourceManager.GetString("100699", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nous sommes dans l’incapacité de prévoir une cueillette pour le moment que vous avez saisi. Veuillez changer l’heure de la cueillette ou du dépôt de votre colis à un centre d’expédition de Purolator..
        /// </summary>
        internal static string _100700 {
            get {
                return ResourceManager.GetString("100700", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le code postal se situe à l’extérieur de notre zone de cueillette. Veuillez appeler au 1-888-SHIP-123 ou déposer votre colis dans un Centre d’expédition de Purolator..
        /// </summary>
        internal static string _100702 {
            get {
                return ResourceManager.GetString("100702", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notre système indique que des renseignements supplémentaires, comme un numéro de case postale ou un numéro de route rurale, sont requis. Veuillez saisir ces renseignements dans le champ de détails supplémentaires de l’adresse..
        /// </summary>
        internal static string _100704 {
            get {
                return ResourceManager.GetString("100704", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vos options de livraison ont été modifiées depuis la création de votre envoi. Veuillez saisir à nouveau vos coordonnées. Merci..
        /// </summary>
        internal static string _100759 {
            get {
                return ResourceManager.GetString("100759", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vos options de livraison ont été modifiées depuis la création de votre envoi. Veuillez saisir à nouveau vos coordonnées. Merci..
        /// </summary>
        internal static string _100760 {
            get {
                return ResourceManager.GetString("100760", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Les cueillettes ne peuvent être prévues pour le dimanche. Veuillez sélectionner l’option de dépôt pour trouver le centre d’expédition de Purolator le plus près..
        /// </summary>
        internal static string _100768 {
            get {
                return ResourceManager.GetString("100768", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Les envois créés avant aujourd&apos;hui ne peuvent pas être annulés. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct..
        /// </summary>
        internal static string _100769 {
            get {
                return ResourceManager.GetString("100769", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nous sommes incapables de traiter votre transaction, car l&apos;autorisation de facturer votre carte de crédit a été refusée. Veuillez composer le 1-888-SHIP-123 ou utiliser le clavardage en direct pour obtenir de l&apos;aide..
        /// </summary>
        internal static string QS_CLIENT_INTEGRATION_CREDIT_CARD_DATA_POWER_ERROR {
            get {
                return ResourceManager.GetString("QS_CLIENT_INTEGRATION_CREDIT_CARD_DATA_POWER_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Échec de la création de l’envoi..
        /// </summary>
        internal static string QS_CREATE_SHIPMENT_FAILED {
            get {
                return ResourceManager.GetString("QS_CREATE_SHIPMENT_FAILED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vous devez indiquer une ville..
        /// </summary>
        internal static string QS_ERROR_CITY_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_CITY_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le numéro de confirmation de la cueillette est requis..
        /// </summary>
        internal static string QS_ERROR_CONFIRMATION_NO_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_CONFIRMATION_NO_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vous devez donner le code de pays..
        /// </summary>
        internal static string QS_ERROR_COUNTRYCODE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_COUNTRYCODE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to La ville de l’expéditeur est requise pour produire une estimation..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_FROM_CITY_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_FROM_CITY_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le code postal de l’expéditeur est requis pour produire une estimation..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_FROM_POSTAL_CODE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_FROM_POSTAL_CODE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to La province de l’expéditeur est requise pour produire une estimation..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_FROM_PROVINCE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_FROM_PROVINCE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le produit est requis pour produire une estimation..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_PRODUCT_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_PRODUCT_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to La ville du destinataire est requise pour produire une estimation..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_TO_CITY_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_TO_CITY_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le code postal du destinataire est requis pour produire une estimation..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_TO_POSTAL_CODE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_TO_POSTAL_CODE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to La province du destinataire est requise pour produire une estimation..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_TO_PROVINCE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_TO_PROVINCE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le poids est requis pour produire une estimation..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_WEIGHT_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_WEIGHT_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le code postal {0} du destinataire n&apos;est pas une adresse où nous pouvons livrer. Veuillez sélectionner une adresse À retenir pour cueillette valide..
        /// </summary>
        internal static string QS_ERROR_FORCED_HOLD_FOR_PICKUP {
            get {
                return ResourceManager.GetString("QS_ERROR_FORCED_HOLD_FOR_PICKUP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to L&apos;adresse de l’expéditeur est requise..
        /// </summary>
        internal static string QS_ERROR_FROM_ADDRESS_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_FROM_ADDRESS_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Une erreur est survenue dans la couche d’intégration..
        /// </summary>
        internal static string QS_ERROR_GENERIC {
            get {
                return ResourceManager.GetString("QS_ERROR_GENERIC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nous ne desservons pas le code postal {0} que vous avez saisi. Veuillez appeler au 1-888-SHIP-123 ou clavarder en direct..
        /// </summary>
        internal static string QS_ERROR_INVALID_POSTAL_CODE {
            get {
                return ResourceManager.GetString("QS_ERROR_INVALID_POSTAL_CODE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le numéro de la confirmation de la cueillette est requis..
        /// </summary>
        internal static string QS_ERROR_PICKUP_CONFIRMATION_NUMBER_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_PICKUP_CONFIRMATION_NUMBER_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type de cueillette non valide..
        /// </summary>
        internal static string QS_ERROR_PICKUP_INVALID_TYPE {
            get {
                return ResourceManager.GetString("QS_ERROR_PICKUP_INVALID_TYPE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Les renseignements sur la cueillette sont requis..
        /// </summary>
        internal static string QS_ERROR_PICKUP_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_PICKUP_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le NIC est requis..
        /// </summary>
        internal static string QS_ERROR_PIN_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_PIN_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le code postal est requis..
        /// </summary>
        internal static string QS_ERROR_POSTALCODE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_POSTALCODE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le code de la province est requis..
        /// </summary>
        internal static string QS_ERROR_PROVINCECODE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_PROVINCECODE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Une ou plusieurs adresses sont invalides..
        /// </summary>
        internal static string QS_ERROR_SERVICE_DIRECTORY_INVALID_ADDRESS {
            get {
                return ResourceManager.GetString("QS_ERROR_SERVICE_DIRECTORY_INVALID_ADDRESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to L&apos;adresse du destinataire est requise..
        /// </summary>
        internal static string QS_ERROR_TO_ADDRESS_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_TO_ADDRESS_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le numéro de la carte de crédit n’est pas valide..
        /// </summary>
        internal static string QS_INVALID_CREDIT_CARD_NUMBER {
            get {
                return ResourceManager.GetString("QS_INVALID_CREDIT_CARD_NUMBER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pays introuvable {0}..
        /// </summary>
        internal static string QS_SERVICE_AVAILABILITY_COUNTRY_NOT_FOUND {
            get {
                return ResourceManager.GetString("QS_SERVICE_AVAILABILITY_COUNTRY_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Le code postal {0} est seulement valide pour {1}{2}..
        /// </summary>
        internal static string QS_SERVICE_AVAILABILITY_SUGGESTED_ADDRESS_RETURNED {
            get {
                return ResourceManager.GetString("QS_SERVICE_AVAILABILITY_SUGGESTED_ADDRESS_RETURNED", resourceCulture);
            }
        }
    }
}
