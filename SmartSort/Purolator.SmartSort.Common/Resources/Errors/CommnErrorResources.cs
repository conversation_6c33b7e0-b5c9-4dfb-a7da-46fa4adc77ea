﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Resources;
using System.Reflection;

namespace Purolator.SmartSort.Common.Resources.Errors
{
    public class CommonErrorResources
    {
        private static ResourceManager GetResourceManager(string languageCode)
        {
            ResourceManager resourceManager = null;
            if (Purolator.SmartSort.Common.Helpers.IsFrench(languageCode))
            {
                resourceManager = new ResourceManager("Purolator.SmartSort.Common.Resources.Errors.FrenchResource", Assembly.GetExecutingAssembly());
            }
            else
            {
                resourceManager = new ResourceManager("Purolator.SmartSort.Common.Resources.Errors.EnglishResource", Assembly.GetExecutingAssembly());
            }
            return resourceManager;
        }

        public static bool ResourceExists(ResourceManager rm, string inKey, string lanuageCode)
        {
            bool retVal = false;
            if (rm != null)
            {
                Object resourceObject = rm.GetObject(inKey);
                if (resourceObject != null)
                {
                    retVal = true;
                }
            }
            return retVal;
        }

        public static bool ResourceExists(string inKey, string lanuageCode)
        {
            bool retVal = false;
            ResourceManager rm = GetResourceManager(lanuageCode);
            if (rm != null)
            {
                retVal = ResourceExists(rm, inKey, lanuageCode);
            }
            return retVal;
        }

        public static string GetResourceString(string inKey, string languageCode)
        {
            string retVal = string.Empty;
            ResourceManager resourceManager = GetResourceManager( languageCode );
            if (resourceManager != null)
            {
                if (ResourceExists(resourceManager, inKey, languageCode))
                {
                    Object resourceObject = resourceManager.GetObject(inKey);
                    if (resourceObject != null)
                    {
                        retVal = resourceManager.GetString(inKey);
                    }
                }
            }
            return retVal;
        }


        public static Error CreateError(string errorCode, string errorDescription, string languageCode, params string[] args)
        {
            Purolator.SmartSort.Common.Error beError = new Purolator.SmartSort.Common.Error();
            if (errorCode.ValueExists())
            {
                if (ResourceExists(errorCode, languageCode))
                {
                    //Is Code Mapped Locally (Over-Ride Text)
                    beError.Code = errorCode;
                    string errorText = GetResourceString(errorCode, languageCode);
                    if (errorText.ValueEmpty())
                        errorText = string.Format(errorDescription, args);
                    else
                        errorText = string.Format(errorText, args);

                    beError.Description = errorText;
                }
                else
                {
                    //Use Existing Text and Error Code
                    beError.Code = errorCode;
                    beError.Description = string.Format(errorDescription, args);
                }
            }
            else
            {
                //Use Existing Text and Error Code
                beError.Code = ErrorConstants.SS_GENERIC_ERROR;
                beError.Description = "No Error Text FOund";
            }
            return beError;
        }



    }

}
