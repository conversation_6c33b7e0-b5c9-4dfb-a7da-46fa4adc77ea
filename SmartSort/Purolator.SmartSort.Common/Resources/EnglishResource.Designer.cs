﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.18444
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Purolator.SmartSort.Common.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class EnglishResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal EnglishResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Purolator.SmartSort.Common.Resources.EnglishResource", typeof(EnglishResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&lt;br&gt;10:30AM.
        /// </summary>
        internal static string DateFormatEstimate_10_30AM {
            get {
                return ResourceManager.GetString("DateFormatEstimate_10:30AM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&lt;br&gt; 9AM.
        /// </summary>
        internal static string DateFormatEstimate_9AM {
            get {
                return ResourceManager.GetString("DateFormatEstimate_9AM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&lt;br&gt;8PM.
        /// </summary>
        internal static string DateFormatEstimate_ENDOFDAY {
            get {
                return ResourceManager.GetString("DateFormatEstimate_ENDOFDAY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&lt;br&gt;5:30PM - 9PM.
        /// </summary>
        internal static string DateFormatEstimate_EVENING {
            get {
                return ResourceManager.GetString("DateFormatEstimate_EVENING", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&lt;br&gt;12PM (noon).
        /// </summary>
        internal static string DateFormatEstimate_NOON {
            get {
                return ResourceManager.GetString("DateFormatEstimate_NOON", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Friday.
        /// </summary>
        internal static string dayOfWeekFriday {
            get {
                return ResourceManager.GetString("dayOfWeekFriday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monday.
        /// </summary>
        internal static string dayOfWeekMonday {
            get {
                return ResourceManager.GetString("dayOfWeekMonday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saturday.
        /// </summary>
        internal static string dayOfWeekSaturday {
            get {
                return ResourceManager.GetString("dayOfWeekSaturday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sunday.
        /// </summary>
        internal static string dayOfWeekSunday {
            get {
                return ResourceManager.GetString("dayOfWeekSunday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thursday.
        /// </summary>
        internal static string dayOfWeekThursday {
            get {
                return ResourceManager.GetString("dayOfWeekThursday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tuesday.
        /// </summary>
        internal static string dayOfWeekTuesday {
            get {
                return ResourceManager.GetString("dayOfWeekTuesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wednesday.
        /// </summary>
        internal static string dayOfWeekWednesday {
            get {
                return ResourceManager.GetString("dayOfWeekWednesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entry Code.
        /// </summary>
        internal static string entryCodeLabel {
            get {
                return ResourceManager.GetString("entryCodeLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to x.
        /// </summary>
        internal static string extensionLabel {
            get {
                return ResourceManager.GetString("extensionLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Floor #.
        /// </summary>
        internal static string floorLabel {
            get {
                return ResourceManager.GetString("floorLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GST/HST.
        /// </summary>
        internal static string GST {
            get {
                return ResourceManager.GetString("GST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed.
        /// </summary>
        internal static string HeaderClosedInfo {
            get {
                return ResourceManager.GetString("HeaderClosedInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day.
        /// </summary>
        internal static string HeaderDay {
            get {
                return ResourceManager.GetString("HeaderDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exceptions.
        /// </summary>
        internal static string HeaderOpenCloseException {
            get {
                return ResourceManager.GetString("HeaderOpenCloseException", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open.
        /// </summary>
        internal static string HeaderOpenInfo {
            get {
                return ResourceManager.GetString("HeaderOpenInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GST/HST.
        /// </summary>
        internal static string HST {
            get {
                return ResourceManager.GetString("HST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} - {1}.
        /// </summary>
        internal static string LocationTimeFormat {
            get {
                return ResourceManager.GetString("LocationTimeFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beyond Destination.
        /// </summary>
        internal static string PCH_BEYOND_POINT_DEST {
            get {
                return ResourceManager.GetString("PCH_BEYOND_POINT_DEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beyond Origin.
        /// </summary>
        internal static string PCH_BEYOND_POINT_ORIGIN {
            get {
                return ResourceManager.GetString("PCH_BEYOND_POINT_ORIGIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chain of Signature.
        /// </summary>
        internal static string PCH_CHAIN_OF_SIGNATURE {
            get {
                return ResourceManager.GetString("PCH_CHAIN_OF_SIGNATURE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Collect Surcharge .
        /// </summary>
        internal static string PCH_COLLECT {
            get {
                return ResourceManager.GetString("PCH_COLLECT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dangerous Goods.
        /// </summary>
        internal static string PCH_DANGEROUS_GOODS {
            get {
                return ResourceManager.GetString("PCH_DANGEROUS_GOODS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Declared Value Surcharge.
        /// </summary>
        internal static string PCH_DECLARED_VALUE {
            get {
                return ResourceManager.GetString("PCH_DECLARED_VALUE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ExpressCheque.
        /// </summary>
        internal static string PCH_EXPRESS_CHEQUE {
            get {
                return ResourceManager.GetString("PCH_EXPRESS_CHEQUE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fuel Surcharge.
        /// </summary>
        internal static string PCH_FUEL {
            get {
                return ResourceManager.GetString("PCH_FUEL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Residential Area.
        /// </summary>
        internal static string PCH_LOW_DENSITY_DEST {
            get {
                return ResourceManager.GetString("PCH_LOW_DENSITY_DEST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Residential Pickup.
        /// </summary>
        internal static string PCH_LOW_DENSITY_ORIGIN {
            get {
                return ResourceManager.GetString("PCH_LOW_DENSITY_ORIGIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Multipiece.
        /// </summary>
        internal static string PCH_MULTI_PIECE {
            get {
                return ResourceManager.GetString("PCH_MULTI_PIECE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Residential Signature.
        /// </summary>
        internal static string PCH_RES_SIG_DOM {
            get {
                return ResourceManager.GetString("PCH_RES_SIG_DOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Residential Signature.
        /// </summary>
        internal static string PCH_RES_SIG_INTL {
            get {
                return ResourceManager.GetString("PCH_RES_SIG_INTL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saturday Service.
        /// </summary>
        internal static string PCH_SATURDAY_DELIVERY {
            get {
                return ResourceManager.GetString("PCH_SATURDAY_DELIVERY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saturday Pickup.
        /// </summary>
        internal static string PCH_SATURDAY_PICKUP {
            get {
                return ResourceManager.GetString("PCH_SATURDAY_PICKUP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Special Handling.
        /// </summary>
        internal static string PCH_SPECIAL_HANDLING {
            get {
                return ResourceManager.GetString("PCH_SPECIAL_HANDLING", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Third Party Surcharge .
        /// </summary>
        internal static string PCH_THIRD_PARTY {
            get {
                return ResourceManager.GetString("PCH_THIRD_PARTY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QST.
        /// </summary>
        internal static string PST {
            get {
                return ResourceManager.GetString("PST", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suite #.
        /// </summary>
        internal static string suiteLabel {
            get {
                return ResourceManager.GetString("suiteLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Number.
        /// </summary>
        internal static string taxNumberLabel {
            get {
                return ResourceManager.GetString("taxNumberLabel", resourceCulture);
            }
        }
    }
}
