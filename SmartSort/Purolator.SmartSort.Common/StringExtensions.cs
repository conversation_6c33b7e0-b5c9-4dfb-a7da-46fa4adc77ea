﻿using System;
using System.Linq;

namespace Purolator.SmartSort.Common
{
    public static class StringExtensions
    {
        public static decimal? ToNullableDecimal(this string value)
        {
            return value.IsDecimal() ? (decimal?)decimal.Parse(value) : null;
        }

        public static bool IsInteger(this string value)
        {
            int result = int.MinValue;
            return int.TryParse(value, out result);
        }

        public static int GetInteger(this string value)
        {
            int myInteger = 0;
            if (value.IsInteger())
            {
                myInteger = Convert.ToInt32(value);
            }
            return myInteger;
        }

        public static bool IsDecimal(this string value)
        {
            bool retVal = false;
            try
            {
                if (value.ValueExists())
                {
                    Convert.ToDecimal(value);
                    retVal = true;
                }
            }
            catch
            {
                retVal = false;
            }
            return retVal;
        }

        public static decimal GetDecimal(this string value)
        {
            decimal myDecimal = 0;
            if (value.IsDecimal())
            {
                myDecimal = Convert.ToDecimal(value);
            }
            return myDecimal;
        }


        public static int? ToNullableInteger(this string value)
        {
            return value.IsInteger() ? (int?)int.Parse(value) : null;
        }

        public static bool ValueExists(this string value)
        {
            return (!string.IsNullOrEmpty(value) && value.Trim().Length > 0);
        }

        public static bool ValueEmpty(this string value)
        {
            return (string.IsNullOrEmpty(value) || value.Trim().Length == 0);
        }

    }
}