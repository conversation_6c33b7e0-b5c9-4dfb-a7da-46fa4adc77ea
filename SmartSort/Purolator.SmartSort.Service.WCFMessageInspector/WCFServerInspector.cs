﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ServiceModel.Dispatcher;
using System.IO;
using System.Xml;
using System.ServiceModel.Channels;
using System.ServiceModel;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Common;



namespace Purolator.SmartSort.Service.WCFMessageInspector
{
    public class WCFServerInspector : IDispatchMessageInspector
    {
        public object AfterReceiveRequest(ref Message request, IClientChannel channel, System.ServiceModel.InstanceContext instanceContext)
        {
            MemoryStream ms = new MemoryStream();
            XmlWriter writer = XmlWriter.Create(ms);
            request.WriteMessage(writer); // the message was consumed here
            writer.Flush();
            ms.Position = 0;
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.Load(ms);
            this.SaveRequest(xmlDoc.ToString());

            //Now recreating the message
            ms = new MemoryStream();
            xmlDoc.Save(ms);
            ms.Position = 0;
            XmlReader reader = XmlReader.Create(ms);
            Message newMessage = Message.CreateMessage(reader, int.MaxValue, request.Version);
            newMessage.Properties.CopyProperties(request.Properties);
            request = newMessage;

            return null;
        }

        public void BeforeSendReply(ref Message reply, object correlationState)
        {
            MemoryStream ms = new MemoryStream();
            XmlWriter writer = XmlWriter.Create(ms);
            reply.WriteMessage(writer); // the message was consumed here
            writer.Flush();
            ms.Position = 0;
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.Load(ms);
            this.SaveResponse(xmlDoc.ToString());

            //Now recreating the message
            ms = new MemoryStream();
            xmlDoc.Save(ms);
            ms.Position = 0;
            XmlReader reader = XmlReader.Create(ms);
            Message newMessage = Message.CreateMessage(reader, int.MaxValue, reply.Version);
            newMessage.Properties.CopyProperties(reply.Properties);
            reply = newMessage;                     
        }

        public void SaveRequest(string doc)
        {
            CallerContext ctx = ContextManager.Instance.Get();
            if (ctx == null)
            {
                ctx = new CallerContext("en");
                ContextManager.Instance.Add(ctx);
            }

            ctx.RequestXML = doc;
        }

        public void SaveResponse(string doc)
        {
            CallerContext ctx = ContextManager.Instance.Get();
            if (ctx != null)
            {
                if (ctx.ShouldLog)
                {
                    Logger.Error("Message in WCF");
                }
            }                        
        }
    }
}
