﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ServiceModel.Dispatcher;
using System.IO;
using System.Xml;
using System.ServiceModel.Description;
using System.ServiceModel.Channels;
using System.ServiceModel;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Common;
using System.ServiceModel.Configuration;


namespace Purolator.SmartSort.Service.WCFMessageInspector
{
    public class WCFServerLoggingExtensionElement : BehaviorExtensionElement
    {
        protected override object CreateBehavior()
        {
            return new WCFServerLoggingBehaviour();
        }

        public override Type BehaviorType
        {
            get
            {
                return typeof(WCFServerLoggingBehaviour);
            }
        }
    }
}
