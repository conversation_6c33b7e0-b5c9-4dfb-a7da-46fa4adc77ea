﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ServiceModel.Dispatcher;
using System.IO;
using System.Xml;
using System.ServiceModel.Channels;
using System.ServiceModel;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Common;


namespace Purolator.SmartSort.Service.WCFMessageInspector
{
    public class WCFClientInspector : IClientMessageInspector
    {
        public void AfterReceiveReply(ref System.ServiceModel.Channels.Message reply, object correlationState)
        {
            // this doesn't work when returning error 400
            //Console.WriteLine("IClientMessageInspector.AfterReceiveReply called.");
            //Console.WriteLine("Message: {0}", reply.ToString());
        }

        public object BeforeSendRequest(ref System.ServiceModel.Channels.Message request, IClientChannel channel)
        {
            SaveRequest(request.ToString());
            return null;
        }

        public void SaveRequest(string doc)
        {
            CallerContext ctx = ContextManager.Instance.Get();
            if (ctx == null)
            {
                ctx = new CallerContext("en");
                ContextManager.Instance.Add(ctx);
            }

            ctx.RequestXML = doc;
        }

        public void SaveResponse(string doc)
        {
            CallerContext ctx = ContextManager.Instance.Get();
            if (ctx == null)
            {
                ctx = new CallerContext("en");
                ContextManager.Instance.Add(ctx);
            }

            ctx.ResponseXML = doc;
        }
    }
}
