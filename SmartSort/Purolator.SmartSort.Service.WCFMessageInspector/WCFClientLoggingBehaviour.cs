﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ServiceModel.Dispatcher;
using System.IO;
using System.Xml;
using System.ServiceModel.Description;
using System.ServiceModel.Channels;
using System.ServiceModel;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Common;

namespace Purolator.SmartSort.Service.WCFMessageInspector
{
    public class WCFClientLoggingBehaviour : IEndpointBehavior
    {        
        public void AddBindingParameters(ServiceEndpoint endpoint, BindingParameterCollection bindingParameters) { return; }

        public void ApplyClientBehavior(ServiceEndpoint endpoint, ClientRuntime clientRuntime)
        {
            clientRuntime.MessageInspectors.Add(new WCFClientInspector());            
        }

        public void ApplyDispatchBehavior(ServiceEndpoint endpoint, EndpointDispatcher endpointDispatcher)
        {                       
        }

        public void Validate(ServiceEndpoint endpoint) { return; }
    }
}
