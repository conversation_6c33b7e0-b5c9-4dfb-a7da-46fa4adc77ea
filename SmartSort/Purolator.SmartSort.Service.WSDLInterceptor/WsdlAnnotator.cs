﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Service.WSDLInterceptor
{
    internal static class WsdlAnnotator
    {
        internal static string Annotate(string xml)
        {
            XDocument document = XDocument.Parse(xml);
            XNamespace ns = document.Root.Name.Namespace;
            try
            {                
                var reqNodes = from el in document.Root.Descendants()
                               where el.Name.LocalName == "element"
                               select el;

                foreach (var element in reqNodes)
                {
                    var val = element.Attribute("nillable");
                    if (val != null)
                    {
                        element.SetAttributeValue("nillable", "false");
                    }

                    val = element.Attribute("maxOccurs");
                    if (val != null && val.Value.Equals("unbounded"))
                    {
                        element.SetAttributeValue("maxOccurs", "10");
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(
                    ex.Message + " Document: " + document.ToString(), ex);
            }

            return document.ToString(SaveOptions.None);
        }
    }
}
