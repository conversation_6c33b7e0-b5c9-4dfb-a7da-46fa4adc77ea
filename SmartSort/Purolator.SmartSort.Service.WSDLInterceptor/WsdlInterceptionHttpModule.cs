﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Purolator.SmartSort.Service.WSDLInterceptor
{
    public class WsdlInterceptionHttpModule: IHttpModule
    {
        public void Init(HttpApplication application)
        {
            application.BeginRequest += (sender, e) =>
            {
                var context = application.Context;

                if (IsRequestForWsdl(context.Request))
                {
                    context.Response.Filter =
                        new WsdlAnnotationsFilterDecorator(context.Response.Filter);
                }
            };
        }
        public void Dispose()
        {
        }

        private static bool IsRequestForWsdl(HttpRequest request) 
        {
            // only for DP
            bool val1 = request.RawUrl.Contains("ShipmentEvent.svc") &&
                (request.RawUrl.Contains("?singleWsdl") || request.RawUrl.Contains("?wsdl"));
          

            return val1;
        }
    }
}
