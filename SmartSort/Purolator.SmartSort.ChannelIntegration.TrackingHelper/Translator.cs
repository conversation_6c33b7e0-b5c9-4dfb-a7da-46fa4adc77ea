﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.ChannelIntegration.TrackingHelper
{
    class Translator
    {
        private static string DF = "yyyy-MM-ddTHH:mm:ss.fffzzz";
        private static string ToDate = "yyyy-MM-dd";
        private static string ToTime = "HHmm";

        public static THService.CreateOFDScanRequest TranslateOFDEvent(Entities.OFDEvent from)
        {
            var to = new THService.CreateOFDScanRequest();

            // idd TODO - get these values from config
            to.ChannelIntegrationRequestInformation = new THService.RequestInformation();
            to.ChannelIntegrationRequestInformation.RequestVersion = "1.0.0.0"; // idd TODO
            to.ChannelIntegrationRequestInformation.RequestLanguage = "EN";
            to.ChannelIntegrationRequestInformation.TransactionGroupID = String.Empty;

            to.Source = "32"; // idd TODO

            to.Scans = new THService.Scan();
            to.Scans.PIN = new THService.PIN(); 
            to.Scans.PIN.Text = from.PiecePin;
            to.Scans.PIN.PINType = String.Empty;
            to.Scans.Depot = new THService.Depot();
            to.Scans.Depot.ID = from.Terminal;
            to.Scans.Depot.Name = String.Empty;
            to.Route = from.Route.PadRight(3).Substring(0, 3);

            DateTime dt = getDate(from.EventDateTime);
            to.Scans.ScanDate = dt.ToString(ToDate);
            to.Scans.ScanTime = dt.ToString(ToTime);

            return to;
        }

        private static DateTime getDate(string strDate)
        {
            return DateTime.Parse(DateTime.Parse(strDate).ToString(DF));
        }
    }
}
