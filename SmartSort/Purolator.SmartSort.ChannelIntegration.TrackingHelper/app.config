﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <system.serviceModel>
        <bindings>
            <basicHttpBinding>
                <binding name="TrackingHelperServiceSoap" />
            </basicHttpBinding>
        </bindings>
        <client>
            <endpoint address="http://**************/TrackingHelperService/TrackingHelperService.asmx"
                binding="basicHttpBinding" bindingConfiguration="TrackingHelperServiceSoap"
                contract="THService.TrackingHelperServiceSoap" name="TrackingHelperServiceSoap" />
        </client>
    </system.serviceModel>
</configuration>
