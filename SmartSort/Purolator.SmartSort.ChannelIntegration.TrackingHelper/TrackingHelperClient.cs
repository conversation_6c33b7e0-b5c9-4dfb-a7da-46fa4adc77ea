﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Threading.Tasks;
using System.Configuration;
using System.ServiceModel.Channels;
using System.Xml.Serialization;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Common;


namespace Purolator.SmartSort.ChannelIntegration.TrackingHelper
{
    public class TrackingHelperClient
    {
        public Boolean SendOFDEvent(Entities.OFDEvent ev, out string error)
        {
            var client = GetTrackingHelperService();
            var request = Translator.TranslateOFDEvent(ev);

            error = String.Empty;
            Boolean success = false;

            THService.CreateOFDScanResponse response = client.CreateOFDScan(request);
            if (response != null && response.ChannelIntegrationResponseInformation != null)
            {
                if (response.ChannelIntegrationResponseInformation.Errors != null && response.ChannelIntegrationResponseInformation.Errors.Length > 0)
                {
                    success = false;
                    error = response.ChannelIntegrationResponseInformation.Errors[0].RunTimeErrorMessage;
                }
                else
                {
                    success = true;
                }
            }

            Logger.Debug("SendOFDEvent Error: " + error, LogCategories.ENTERPRISE_EVENT);
            return success;
        }

        private THService.TrackingHelperServiceSoapClient GetTrackingHelperService()
        {
            var client = new THService.TrackingHelperServiceSoapClient("TrackingHelperServiceSoap");

            AppSettingsReader settingsReader = new AppSettingsReader();
            client.ClientCredentials.UserName.UserName = (string)settingsReader.GetValue("IL_UserName", typeof(String));
            client.ClientCredentials.UserName.Password = (string)settingsReader.GetValue("IL_Password", typeof(String));

            // BindingElementCollection elements = client.Endpoint.Binding.CreateBindingElements();
            // elements.Find<SecurityBindingElement>().IncludeTimestamp = false;
            // client.Endpoint.Binding = new CustomBinding(elements);

            return client;
        }
    }
}

