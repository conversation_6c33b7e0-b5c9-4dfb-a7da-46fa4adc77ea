﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://purolator/channelintegration/TrackingHelperService/", ConfigurationName="THService.TrackingHelperServiceSoap")]
    public interface TrackingHelperServiceSoap {
        
        // CODEGEN: Generating message contract since message part namespace (http://purolator/channelintegration/business.entities) does not match the default value (http://purolator/channelintegration/TrackingHelperService/)
        [System.ServiceModel.OperationContractAttribute(Action="http://purolator/channelintegration/TrackingHelperService/CreateUSInTransitScan", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanResponse1 CreateUSInTransitScan(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://purolator/channelintegration/TrackingHelperService/CreateUSInTransitScan", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanResponse1> CreateUSInTransitScanAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanRequest1 request);
        
        // CODEGEN: Generating message contract since message part namespace (http://purolator/channelintegration/business.entities) does not match the default value (http://purolator/channelintegration/TrackingHelperService/)
        [System.ServiceModel.OperationContractAttribute(Action="http://purolator/channelintegration/TrackingHelperService/CreateDeliveryOrUndeliv" +
            "eryScan", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanResponse1 CreateDeliveryOrUndeliveryScan(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://purolator/channelintegration/TrackingHelperService/CreateDeliveryOrUndeliv" +
            "eryScan", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanResponse1> CreateDeliveryOrUndeliveryScanAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanRequest1 request);
        
        // CODEGEN: Generating message contract since message part namespace (http://purolator/channelintegration/business.entities) does not match the default value (http://purolator/channelintegration/TrackingHelperService/)
        [System.ServiceModel.OperationContractAttribute(Action="http://purolator/channelintegration/TrackingHelperService/CreateCrossRefScan", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanResponse1 CreateCrossRefScan(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://purolator/channelintegration/TrackingHelperService/CreateCrossRefScan", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanResponse1> CreateCrossRefScanAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanRequest1 request);
        
        // CODEGEN: Generating message contract since message part namespace (http://purolator/channelintegration/business.entities) does not match the default value (http://purolator/channelintegration/TrackingHelperService/)
        [System.ServiceModel.OperationContractAttribute(Action="http://purolator/channelintegration/TrackingHelperService/CreateShipmentTracking", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingResponse1 CreateShipmentTracking(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://purolator/channelintegration/TrackingHelperService/CreateShipmentTracking", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingResponse1> CreateShipmentTrackingAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingRequest1 request);
        
        // CODEGEN: Generating message contract since message part namespace (http://purolator/channelintegration/business.entities) does not match the default value (http://purolator/channelintegration/TrackingHelperService/)
        [System.ServiceModel.OperationContractAttribute(Action="http://purolator/channelintegration/TrackingHelperService/DeleteShipmentTracking", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingResponse1 DeleteShipmentTracking(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://purolator/channelintegration/TrackingHelperService/DeleteShipmentTracking", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingResponse1> DeleteShipmentTrackingAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingRequest1 request);
        
        // CODEGEN: Generating message contract since message part namespace (http://purolator/channelintegration/business.entities) does not match the default value (http://purolator/channelintegration/TrackingHelperService/)
        [System.ServiceModel.OperationContractAttribute(Action="http://purolator/channelintegration/TrackingHelperService/CreateOFDScan", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanResponse1 CreateOFDScan(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanRequest1 request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://purolator/channelintegration/TrackingHelperService/CreateOFDScan", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanResponse1> CreateOFDScanAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanRequest1 request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CreateUSInTransitScanRequest : object, System.ComponentModel.INotifyPropertyChanged {
        
        private Scan[] scansField;
        
        private RequestInformation channelIntegrationRequestInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public Scan[] Scans {
            get {
                return this.scansField;
            }
            set {
                this.scansField = value;
                this.RaisePropertyChanged("Scans");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public RequestInformation ChannelIntegrationRequestInformation {
            get {
                return this.channelIntegrationRequestInformationField;
            }
            set {
                this.channelIntegrationRequestInformationField = value;
                this.RaisePropertyChanged("ChannelIntegrationRequestInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CrossRefScan))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ScanProofOfPickUp))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ScanInternationalOutbound))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ScanOnDelivery))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ScanUndeliverable))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ScanDelivery))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Scan : object, System.ComponentModel.INotifyPropertyChanged {
        
        private PIN pINField;
        
        private Depot depotField;
        
        private string scanDateField;
        
        private string scanTimeField;
        
        private string descriptionField;
        
        private string commentField;
        
        private string summaryScanIndicatorField;
        
        private string scanTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public PIN PIN {
            get {
                return this.pINField;
            }
            set {
                this.pINField = value;
                this.RaisePropertyChanged("PIN");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public Depot Depot {
            get {
                return this.depotField;
            }
            set {
                this.depotField = value;
                this.RaisePropertyChanged("Depot");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string ScanDate {
            get {
                return this.scanDateField;
            }
            set {
                this.scanDateField = value;
                this.RaisePropertyChanged("ScanDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public string ScanTime {
            get {
                return this.scanTimeField;
            }
            set {
                this.scanTimeField = value;
                this.RaisePropertyChanged("ScanTime");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
                this.RaisePropertyChanged("Description");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
        public string Comment {
            get {
                return this.commentField;
            }
            set {
                this.commentField = value;
                this.RaisePropertyChanged("Comment");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
        public string SummaryScanIndicator {
            get {
                return this.summaryScanIndicatorField;
            }
            set {
                this.summaryScanIndicatorField = value;
                this.RaisePropertyChanged("SummaryScanIndicator");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ScanType {
            get {
                return this.scanTypeField;
            }
            set {
                this.scanTypeField = value;
                this.RaisePropertyChanged("ScanType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class PIN : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string textField;
        
        private string pINTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Text {
            get {
                return this.textField;
            }
            set {
                this.textField = value;
                this.RaisePropertyChanged("Text");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string PINType {
            get {
                return this.pINTypeField;
            }
            set {
                this.pINTypeField = value;
                this.RaisePropertyChanged("PINType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CreateOFDScanResponse : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ResponseInformation channelIntegrationResponseInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public ResponseInformation ChannelIntegrationResponseInformation {
            get {
                return this.channelIntegrationResponseInformationField;
            }
            set {
                this.channelIntegrationResponseInformationField = value;
                this.RaisePropertyChanged("ChannelIntegrationResponseInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ResponseInformation : object, System.ComponentModel.INotifyPropertyChanged {
        
        private RunTimeError[] errorsField;
        
        private string debugInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Error", IsNullable=false)]
        public RunTimeError[] Errors {
            get {
                return this.errorsField;
            }
            set {
                this.errorsField = value;
                this.RaisePropertyChanged("Errors");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string DebugInformation {
            get {
                return this.debugInformationField;
            }
            set {
                this.debugInformationField = value;
                this.RaisePropertyChanged("DebugInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class RunTimeError : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string errorCodeField;
        
        private string errorMessageField;
        
        private string runTimeErrorMessageField;
        
        private string runTimeErrorMessageStackField;
        
        private string runTimeErrorContextField;
        
        private string runTimeErrorDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string ErrorCode {
            get {
                return this.errorCodeField;
            }
            set {
                this.errorCodeField = value;
                this.RaisePropertyChanged("ErrorCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string ErrorMessage {
            get {
                return this.errorMessageField;
            }
            set {
                this.errorMessageField = value;
                this.RaisePropertyChanged("ErrorMessage");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string RunTimeErrorMessage {
            get {
                return this.runTimeErrorMessageField;
            }
            set {
                this.runTimeErrorMessageField = value;
                this.RaisePropertyChanged("RunTimeErrorMessage");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public string RunTimeErrorMessageStack {
            get {
                return this.runTimeErrorMessageStackField;
            }
            set {
                this.runTimeErrorMessageStackField = value;
                this.RaisePropertyChanged("RunTimeErrorMessageStack");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
        public string RunTimeErrorContext {
            get {
                return this.runTimeErrorContextField;
            }
            set {
                this.runTimeErrorContextField = value;
                this.RaisePropertyChanged("RunTimeErrorContext");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
        public string RunTimeErrorDetails {
            get {
                return this.runTimeErrorDetailsField;
            }
            set {
                this.runTimeErrorDetailsField = value;
                this.RaisePropertyChanged("RunTimeErrorDetails");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CreateOFDScanRequest : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string sourceField;
        
        private ScanOption scanOptionsField;
        
        private Scan scansField;
        
        private string routeField;
        
        private RequestInformation channelIntegrationRequestInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Source {
            get {
                return this.sourceField;
            }
            set {
                this.sourceField = value;
                this.RaisePropertyChanged("Source");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public ScanOption ScanOptions {
            get {
                return this.scanOptionsField;
            }
            set {
                this.scanOptionsField = value;
                this.RaisePropertyChanged("ScanOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public Scan Scans {
            get {
                return this.scansField;
            }
            set {
                this.scansField = value;
                this.RaisePropertyChanged("Scans");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string Route {
            get {
                return this.routeField;
            }
            set {
                this.routeField = value;
                this.RaisePropertyChanged("Route");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
        public RequestInformation ChannelIntegrationRequestInformation {
            get {
                return this.channelIntegrationRequestInformationField;
            }
            set {
                this.channelIntegrationRequestInformationField = value;
                this.RaisePropertyChanged("ChannelIntegrationRequestInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ScanOption : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string transactionTypeField;
        
        private string originScanTypeField;
        
        private string locationField;
        
        private string subScanTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string TransactionType {
            get {
                return this.transactionTypeField;
            }
            set {
                this.transactionTypeField = value;
                this.RaisePropertyChanged("TransactionType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string OriginScanType {
            get {
                return this.originScanTypeField;
            }
            set {
                this.originScanTypeField = value;
                this.RaisePropertyChanged("OriginScanType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string Location {
            get {
                return this.locationField;
            }
            set {
                this.locationField = value;
                this.RaisePropertyChanged("Location");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string SubScanType {
            get {
                return this.subScanTypeField;
            }
            set {
                this.subScanTypeField = value;
                this.RaisePropertyChanged("SubScanType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class RequestInformation : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string requestVersionField;
        
        private string requestLanguageField;
        
        private string transactionGroupIDField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string RequestVersion {
            get {
                return this.requestVersionField;
            }
            set {
                this.requestVersionField = value;
                this.RaisePropertyChanged("RequestVersion");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string RequestLanguage {
            get {
                return this.requestLanguageField;
            }
            set {
                this.requestLanguageField = value;
                this.RaisePropertyChanged("RequestLanguage");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string TransactionGroupID {
            get {
                return this.transactionGroupIDField;
            }
            set {
                this.transactionGroupIDField = value;
                this.RaisePropertyChanged("TransactionGroupID");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class DeleteShipmentTrackingResponse : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ResponseInformation channelIntegrationResponseInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public ResponseInformation ChannelIntegrationResponseInformation {
            get {
                return this.channelIntegrationResponseInformationField;
            }
            set {
                this.channelIntegrationResponseInformationField = value;
                this.RaisePropertyChanged("ChannelIntegrationResponseInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class DeleteShipmentTrackingRequest : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ShipmentIdentification shipmentIdentificationField;
        
        private RequestInformation channelIntegrationRequestInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public ShipmentIdentification ShipmentIdentification {
            get {
                return this.shipmentIdentificationField;
            }
            set {
                this.shipmentIdentificationField = value;
                this.RaisePropertyChanged("ShipmentIdentification");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public RequestInformation ChannelIntegrationRequestInformation {
            get {
                return this.channelIntegrationRequestInformationField;
            }
            set {
                this.channelIntegrationRequestInformationField = value;
                this.RaisePropertyChanged("ChannelIntegrationRequestInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ShipmentIdentification : object, System.ComponentModel.INotifyPropertyChanged {
        
        private PIN shipmentPINField;
        
        private string shipperAccountNumberField;
        
        private string shipmentDateField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public PIN ShipmentPIN {
            get {
                return this.shipmentPINField;
            }
            set {
                this.shipmentPINField = value;
                this.RaisePropertyChanged("ShipmentPIN");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string ShipperAccountNumber {
            get {
                return this.shipperAccountNumberField;
            }
            set {
                this.shipperAccountNumberField = value;
                this.RaisePropertyChanged("ShipperAccountNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string ShipmentDate {
            get {
                return this.shipmentDateField;
            }
            set {
                this.shipmentDateField = value;
                this.RaisePropertyChanged("ShipmentDate");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CreateShipmentTrackingResponse : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ResponseInformation channelIntegrationResponseInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public ResponseInformation ChannelIntegrationResponseInformation {
            get {
                return this.channelIntegrationResponseInformationField;
            }
            set {
                this.channelIntegrationResponseInformationField = value;
                this.RaisePropertyChanged("ChannelIntegrationResponseInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class TradeInformation : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string uSInTransitScanIndicatorField;
        
        private string shipmentDescriptionField;
        
        private string shipmentHarmonizedCodeField;
        
        private string manufacturerCountryCodeField;
        
        private string dutiableIndicatorField;
        
        private Amount shipmentCustomValueField;
        
        private string taxesPaidCodeField;
        
        private string customImportTypeField;
        
        private string extraChargeCodeField;
        
        private Amount extraChargeAmountField;
        
        private string specialInstructionIndicatorField;
        
        private string senderTaxIDField;
        
        private string receiverTaxIDField;
        
        private string originIATACodeField;
        
        private string destinationIATACodeField;
        
        private string preferredBrokerField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string USInTransitScanIndicator {
            get {
                return this.uSInTransitScanIndicatorField;
            }
            set {
                this.uSInTransitScanIndicatorField = value;
                this.RaisePropertyChanged("USInTransitScanIndicator");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string ShipmentDescription {
            get {
                return this.shipmentDescriptionField;
            }
            set {
                this.shipmentDescriptionField = value;
                this.RaisePropertyChanged("ShipmentDescription");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string ShipmentHarmonizedCode {
            get {
                return this.shipmentHarmonizedCodeField;
            }
            set {
                this.shipmentHarmonizedCodeField = value;
                this.RaisePropertyChanged("ShipmentHarmonizedCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public string ManufacturerCountryCode {
            get {
                return this.manufacturerCountryCodeField;
            }
            set {
                this.manufacturerCountryCodeField = value;
                this.RaisePropertyChanged("ManufacturerCountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
        public string DutiableIndicator {
            get {
                return this.dutiableIndicatorField;
            }
            set {
                this.dutiableIndicatorField = value;
                this.RaisePropertyChanged("DutiableIndicator");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
        public Amount ShipmentCustomValue {
            get {
                return this.shipmentCustomValueField;
            }
            set {
                this.shipmentCustomValueField = value;
                this.RaisePropertyChanged("ShipmentCustomValue");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
        public string TaxesPaidCode {
            get {
                return this.taxesPaidCodeField;
            }
            set {
                this.taxesPaidCodeField = value;
                this.RaisePropertyChanged("TaxesPaidCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
        public string CustomImportType {
            get {
                return this.customImportTypeField;
            }
            set {
                this.customImportTypeField = value;
                this.RaisePropertyChanged("CustomImportType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
        public string ExtraChargeCode {
            get {
                return this.extraChargeCodeField;
            }
            set {
                this.extraChargeCodeField = value;
                this.RaisePropertyChanged("ExtraChargeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
        public Amount ExtraChargeAmount {
            get {
                return this.extraChargeAmountField;
            }
            set {
                this.extraChargeAmountField = value;
                this.RaisePropertyChanged("ExtraChargeAmount");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
        public string SpecialInstructionIndicator {
            get {
                return this.specialInstructionIndicatorField;
            }
            set {
                this.specialInstructionIndicatorField = value;
                this.RaisePropertyChanged("SpecialInstructionIndicator");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
        public string SenderTaxID {
            get {
                return this.senderTaxIDField;
            }
            set {
                this.senderTaxIDField = value;
                this.RaisePropertyChanged("SenderTaxID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
        public string ReceiverTaxID {
            get {
                return this.receiverTaxIDField;
            }
            set {
                this.receiverTaxIDField = value;
                this.RaisePropertyChanged("ReceiverTaxID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=13)]
        public string OriginIATACode {
            get {
                return this.originIATACodeField;
            }
            set {
                this.originIATACodeField = value;
                this.RaisePropertyChanged("OriginIATACode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=14)]
        public string DestinationIATACode {
            get {
                return this.destinationIATACodeField;
            }
            set {
                this.destinationIATACodeField = value;
                this.RaisePropertyChanged("DestinationIATACode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=15)]
        public string PreferredBroker {
            get {
                return this.preferredBrokerField;
            }
            set {
                this.preferredBrokerField = value;
                this.RaisePropertyChanged("PreferredBroker");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Amount : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string valueField;
        
        private string currencyCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string CurrencyCode {
            get {
                return this.currencyCodeField;
            }
            set {
                this.currencyCodeField = value;
                this.RaisePropertyChanged("CurrencyCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Merchant : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string idField;
        
        private string merchantTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string ID {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
                this.RaisePropertyChanged("ID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string MerchantType {
            get {
                return this.merchantTypeField;
            }
            set {
                this.merchantTypeField = value;
                this.RaisePropertyChanged("MerchantType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class AddressVerify : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string streetNumberField;
        
        private string streetNameField;
        
        private string postalCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string StreetNumber {
            get {
                return this.streetNumberField;
            }
            set {
                this.streetNumberField = value;
                this.RaisePropertyChanged("StreetNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string StreetName {
            get {
                return this.streetNameField;
            }
            set {
                this.streetNameField = value;
                this.RaisePropertyChanged("StreetName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string PostalCode {
            get {
                return this.postalCodeField;
            }
            set {
                this.postalCodeField = value;
                this.RaisePropertyChanged("PostalCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CreditCard : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string numberField;
        
        private string expiryDateField;
        
        private string securityNumberField;
        
        private string nameOnCreditCardField;
        
        private AddressVerify addressVerificationInformationField;
        
        private string merchantApplicationIdField;
        
        private string creditCardTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Number {
            get {
                return this.numberField;
            }
            set {
                this.numberField = value;
                this.RaisePropertyChanged("Number");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string ExpiryDate {
            get {
                return this.expiryDateField;
            }
            set {
                this.expiryDateField = value;
                this.RaisePropertyChanged("ExpiryDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string SecurityNumber {
            get {
                return this.securityNumberField;
            }
            set {
                this.securityNumberField = value;
                this.RaisePropertyChanged("SecurityNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public string NameOnCreditCard {
            get {
                return this.nameOnCreditCardField;
            }
            set {
                this.nameOnCreditCardField = value;
                this.RaisePropertyChanged("NameOnCreditCard");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public AddressVerify AddressVerificationInformation {
            get {
                return this.addressVerificationInformationField;
            }
            set {
                this.addressVerificationInformationField = value;
                this.RaisePropertyChanged("AddressVerificationInformation");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
        public string MerchantApplicationId {
            get {
                return this.merchantApplicationIdField;
            }
            set {
                this.merchantApplicationIdField = value;
                this.RaisePropertyChanged("MerchantApplicationId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string CreditCardType {
            get {
                return this.creditCardTypeField;
            }
            set {
                this.creditCardTypeField = value;
                this.RaisePropertyChanged("CreditCardType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Payment : object, System.ComponentModel.INotifyPropertyChanged {
        
        private Amount amountField;
        
        private CreditCard creditCardField;
        
        private Merchant merchantField;
        
        private string authorizationNumberField;
        
        private string confirmationNumberField;
        
        private string transactionIdField;
        
        private string paymentIdField;
        
        private string cardDataSourceField;
        
        private string paymentTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public Amount Amount {
            get {
                return this.amountField;
            }
            set {
                this.amountField = value;
                this.RaisePropertyChanged("Amount");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public CreditCard CreditCard {
            get {
                return this.creditCardField;
            }
            set {
                this.creditCardField = value;
                this.RaisePropertyChanged("CreditCard");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public Merchant Merchant {
            get {
                return this.merchantField;
            }
            set {
                this.merchantField = value;
                this.RaisePropertyChanged("Merchant");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string AuthorizationNumber {
            get {
                return this.authorizationNumberField;
            }
            set {
                this.authorizationNumberField = value;
                this.RaisePropertyChanged("AuthorizationNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string ConfirmationNumber {
            get {
                return this.confirmationNumberField;
            }
            set {
                this.confirmationNumberField = value;
                this.RaisePropertyChanged("ConfirmationNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string TransactionId {
            get {
                return this.transactionIdField;
            }
            set {
                this.transactionIdField = value;
                this.RaisePropertyChanged("TransactionId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string PaymentId {
            get {
                return this.paymentIdField;
            }
            set {
                this.paymentIdField = value;
                this.RaisePropertyChanged("PaymentId");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string CardDataSource {
            get {
                return this.cardDataSourceField;
            }
            set {
                this.cardDataSourceField = value;
                this.RaisePropertyChanged("CardDataSource");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string PaymentType {
            get {
                return this.paymentTypeField;
            }
            set {
                this.paymentTypeField = value;
                this.RaisePropertyChanged("PaymentType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class NotificationParameter : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string nameField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class NotificationTemplate : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string numberField;
        
        private NotificationParameter[] parametersField;
        
        private string notificationTemplateTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Number {
            get {
                return this.numberField;
            }
            set {
                this.numberField = value;
                this.RaisePropertyChanged("Number");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("Parameter")]
        public NotificationParameter[] Parameters {
            get {
                return this.parametersField;
            }
            set {
                this.parametersField = value;
                this.RaisePropertyChanged("Parameters");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string NotificationTemplateType {
            get {
                return this.notificationTemplateTypeField;
            }
            set {
                this.notificationTemplateTypeField = value;
                this.RaisePropertyChanged("NotificationTemplateType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Notification : object, System.ComponentModel.INotifyPropertyChanged {
        
        private EmailAddress[] emailAddressesField;
        
        private NotificationTemplate notificationTemplateField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public EmailAddress[] EmailAddresses {
            get {
                return this.emailAddressesField;
            }
            set {
                this.emailAddressesField = value;
                this.RaisePropertyChanged("EmailAddresses");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public NotificationTemplate NotificationTemplate {
            get {
                return this.notificationTemplateField;
            }
            set {
                this.notificationTemplateField = value;
                this.RaisePropertyChanged("NotificationTemplate");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class EmailAddress : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string textField;
        
        private string emailAddressTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Text {
            get {
                return this.textField;
            }
            set {
                this.textField = value;
                this.RaisePropertyChanged("Text");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string EmailAddressType {
            get {
                return this.emailAddressTypeField;
            }
            set {
                this.emailAddressTypeField = value;
                this.RaisePropertyChanged("EmailAddressType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Service : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string codeField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Code {
            get {
                return this.codeField;
            }
            set {
                this.codeField = value;
                this.RaisePropertyChanged("Code");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Height : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string valueField;
        
        private string unitOfMeasureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string UnitOfMeasure {
            get {
                return this.unitOfMeasureField;
            }
            set {
                this.unitOfMeasureField = value;
                this.RaisePropertyChanged("UnitOfMeasure");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Width : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string valueField;
        
        private string unitOfMeasureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string UnitOfMeasure {
            get {
                return this.unitOfMeasureField;
            }
            set {
                this.unitOfMeasureField = value;
                this.RaisePropertyChanged("UnitOfMeasure");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Length : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string valueField;
        
        private string unitOfMeasureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string UnitOfMeasure {
            get {
                return this.unitOfMeasureField;
            }
            set {
                this.unitOfMeasureField = value;
                this.RaisePropertyChanged("UnitOfMeasure");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Dimensions : object, System.ComponentModel.INotifyPropertyChanged {
        
        private Length lengthField;
        
        private Width widthField;
        
        private Height heightField;
        
        private string unitOfMeasureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public Length Length {
            get {
                return this.lengthField;
            }
            set {
                this.lengthField = value;
                this.RaisePropertyChanged("Length");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public Width Width {
            get {
                return this.widthField;
            }
            set {
                this.widthField = value;
                this.RaisePropertyChanged("Width");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public Height Height {
            get {
                return this.heightField;
            }
            set {
                this.heightField = value;
                this.RaisePropertyChanged("Height");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string UnitOfMeasure {
            get {
                return this.unitOfMeasureField;
            }
            set {
                this.unitOfMeasureField = value;
                this.RaisePropertyChanged("UnitOfMeasure");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class PackageIdentification : object, System.ComponentModel.INotifyPropertyChanged {
        
        private PIN shipmentPINField;
        
        private string shipperAccountNumberField;
        
        private string shipmentDateField;
        
        private PIN pINField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public PIN ShipmentPIN {
            get {
                return this.shipmentPINField;
            }
            set {
                this.shipmentPINField = value;
                this.RaisePropertyChanged("ShipmentPIN");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string ShipperAccountNumber {
            get {
                return this.shipperAccountNumberField;
            }
            set {
                this.shipperAccountNumberField = value;
                this.RaisePropertyChanged("ShipperAccountNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string ShipmentDate {
            get {
                return this.shipmentDateField;
            }
            set {
                this.shipmentDateField = value;
                this.RaisePropertyChanged("ShipmentDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public PIN PIN {
            get {
                return this.pINField;
            }
            set {
                this.pINField = value;
                this.RaisePropertyChanged("PIN");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Package : object, System.ComponentModel.INotifyPropertyChanged {
        
        private PackageIdentification identificationField;
        
        private CustomerReference[] customerReferencesField;
        
        private Dimensions dimensionsField;
        
        private Weight weightField;
        
        private Service[] servicesField;
        
        private Notification[] notificationsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public PackageIdentification Identification {
            get {
                return this.identificationField;
            }
            set {
                this.identificationField = value;
                this.RaisePropertyChanged("Identification");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public CustomerReference[] CustomerReferences {
            get {
                return this.customerReferencesField;
            }
            set {
                this.customerReferencesField = value;
                this.RaisePropertyChanged("CustomerReferences");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public Dimensions Dimensions {
            get {
                return this.dimensionsField;
            }
            set {
                this.dimensionsField = value;
                this.RaisePropertyChanged("Dimensions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public Weight Weight {
            get {
                return this.weightField;
            }
            set {
                this.weightField = value;
                this.RaisePropertyChanged("Weight");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=4)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public Service[] Services {
            get {
                return this.servicesField;
            }
            set {
                this.servicesField = value;
                this.RaisePropertyChanged("Services");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=5)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public Notification[] Notifications {
            get {
                return this.notificationsField;
            }
            set {
                this.notificationsField = value;
                this.RaisePropertyChanged("Notifications");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CustomerReference : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string codeField;
        
        private string sequenceNumberField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Code {
            get {
                return this.codeField;
            }
            set {
                this.codeField = value;
                this.RaisePropertyChanged("Code");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string SequenceNumber {
            get {
                return this.sequenceNumberField;
            }
            set {
                this.sequenceNumberField = value;
                this.RaisePropertyChanged("SequenceNumber");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Weight : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string valueField;
        
        private string unitOfMeasureField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string UnitOfMeasure {
            get {
                return this.unitOfMeasureField;
            }
            set {
                this.unitOfMeasureField = value;
                this.RaisePropertyChanged("UnitOfMeasure");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Telecom : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string countryCodeField;
        
        private string areaCodeField;
        
        private string numberField;
        
        private string extensionField;
        
        private string telecomTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string CountryCode {
            get {
                return this.countryCodeField;
            }
            set {
                this.countryCodeField = value;
                this.RaisePropertyChanged("CountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string AreaCode {
            get {
                return this.areaCodeField;
            }
            set {
                this.areaCodeField = value;
                this.RaisePropertyChanged("AreaCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string Number {
            get {
                return this.numberField;
            }
            set {
                this.numberField = value;
                this.RaisePropertyChanged("Number");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public string Extension {
            get {
                return this.extensionField;
            }
            set {
                this.extensionField = value;
                this.RaisePropertyChanged("Extension");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string TelecomType {
            get {
                return this.telecomTypeField;
            }
            set {
                this.telecomTypeField = value;
                this.RaisePropertyChanged("TelecomType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Name : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string firstNameField;
        
        private string middleNameField;
        
        private string lastNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
                this.RaisePropertyChanged("FirstName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string MiddleName {
            get {
                return this.middleNameField;
            }
            set {
                this.middleNameField = value;
                this.RaisePropertyChanged("MiddleName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string LastName {
            get {
                return this.lastNameField;
            }
            set {
                this.lastNameField = value;
                this.RaisePropertyChanged("LastName");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Contact : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string companyNameField;
        
        private string departmentNameField;
        
        private string accountNumberField;
        
        private Name contactNameField;
        
        private Address[] addressesField;
        
        private EmailAddress[] emailAddressesField;
        
        private Telecom[] telecomsField;
        
        private string contactTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string CompanyName {
            get {
                return this.companyNameField;
            }
            set {
                this.companyNameField = value;
                this.RaisePropertyChanged("CompanyName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string DepartmentName {
            get {
                return this.departmentNameField;
            }
            set {
                this.departmentNameField = value;
                this.RaisePropertyChanged("DepartmentName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string AccountNumber {
            get {
                return this.accountNumberField;
            }
            set {
                this.accountNumberField = value;
                this.RaisePropertyChanged("AccountNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public Name ContactName {
            get {
                return this.contactNameField;
            }
            set {
                this.contactNameField = value;
                this.RaisePropertyChanged("ContactName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=4)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public Address[] Addresses {
            get {
                return this.addressesField;
            }
            set {
                this.addressesField = value;
                this.RaisePropertyChanged("Addresses");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=5)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public EmailAddress[] EmailAddresses {
            get {
                return this.emailAddressesField;
            }
            set {
                this.emailAddressesField = value;
                this.RaisePropertyChanged("EmailAddresses");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=6)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public Telecom[] Telecoms {
            get {
                return this.telecomsField;
            }
            set {
                this.telecomsField = value;
                this.RaisePropertyChanged("Telecoms");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string ContactType {
            get {
                return this.contactTypeField;
            }
            set {
                this.contactTypeField = value;
                this.RaisePropertyChanged("ContactType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Address : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string streetNumberField;
        
        private string streetAddress1Field;
        
        private string streetAddress2Field;
        
        private string streetAddress3Field;
        
        private string unitSuiteAptField;
        
        private string floorNumberField;
        
        private string cityField;
        
        private string cityCodeField;
        
        private string provinceCodeField;
        
        private string countryCodeField;
        
        private string postalCodeField;
        
        private string addressTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string StreetNumber {
            get {
                return this.streetNumberField;
            }
            set {
                this.streetNumberField = value;
                this.RaisePropertyChanged("StreetNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string StreetAddress1 {
            get {
                return this.streetAddress1Field;
            }
            set {
                this.streetAddress1Field = value;
                this.RaisePropertyChanged("StreetAddress1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string StreetAddress2 {
            get {
                return this.streetAddress2Field;
            }
            set {
                this.streetAddress2Field = value;
                this.RaisePropertyChanged("StreetAddress2");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public string StreetAddress3 {
            get {
                return this.streetAddress3Field;
            }
            set {
                this.streetAddress3Field = value;
                this.RaisePropertyChanged("StreetAddress3");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
        public string UnitSuiteApt {
            get {
                return this.unitSuiteAptField;
            }
            set {
                this.unitSuiteAptField = value;
                this.RaisePropertyChanged("UnitSuiteApt");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
        public string FloorNumber {
            get {
                return this.floorNumberField;
            }
            set {
                this.floorNumberField = value;
                this.RaisePropertyChanged("FloorNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
                this.RaisePropertyChanged("City");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
        public string CityCode {
            get {
                return this.cityCodeField;
            }
            set {
                this.cityCodeField = value;
                this.RaisePropertyChanged("CityCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
        public string ProvinceCode {
            get {
                return this.provinceCodeField;
            }
            set {
                this.provinceCodeField = value;
                this.RaisePropertyChanged("ProvinceCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
        public string CountryCode {
            get {
                return this.countryCodeField;
            }
            set {
                this.countryCodeField = value;
                this.RaisePropertyChanged("CountryCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
        public string PostalCode {
            get {
                return this.postalCodeField;
            }
            set {
                this.postalCodeField = value;
                this.RaisePropertyChanged("PostalCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute()]
        public string AddressType {
            get {
                return this.addressTypeField;
            }
            set {
                this.addressTypeField = value;
                this.RaisePropertyChanged("AddressType");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Shipment : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ShipmentIdentification identificationField;
        
        private CustomerReference[] customerReferencesField;
        
        private Contact[] shipmentAddressesField;
        
        private string modeOfTransportField;
        
        private Weight totalWeightField;
        
        private string totalPackagesField;
        
        private Package[] packagesField;
        
        private string receiverAccountNumberField;
        
        private string paymentMethodField;
        
        private Payment paymentField;
        
        private string voidIndicatorField;
        
        private string consolidationIndicatorField;
        
        private string returnIndicatorField;
        
        private string returnPINField;
        
        private string expressChequePINField;
        
        private string productNumberField;
        
        private string noteField;
        
        private TradeInformation tradeInformationField;
        
        private Service[] shipmentServicesField;
        
        private string notificationCodeField;
        
        private Notification[] notificationsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public ShipmentIdentification Identification {
            get {
                return this.identificationField;
            }
            set {
                this.identificationField = value;
                this.RaisePropertyChanged("Identification");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public CustomerReference[] CustomerReferences {
            get {
                return this.customerReferencesField;
            }
            set {
                this.customerReferencesField = value;
                this.RaisePropertyChanged("CustomerReferences");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public Contact[] ShipmentAddresses {
            get {
                return this.shipmentAddressesField;
            }
            set {
                this.shipmentAddressesField = value;
                this.RaisePropertyChanged("ShipmentAddresses");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public string ModeOfTransport {
            get {
                return this.modeOfTransportField;
            }
            set {
                this.modeOfTransportField = value;
                this.RaisePropertyChanged("ModeOfTransport");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
        public Weight TotalWeight {
            get {
                return this.totalWeightField;
            }
            set {
                this.totalWeightField = value;
                this.RaisePropertyChanged("TotalWeight");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
        public string TotalPackages {
            get {
                return this.totalPackagesField;
            }
            set {
                this.totalPackagesField = value;
                this.RaisePropertyChanged("TotalPackages");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=6)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public Package[] Packages {
            get {
                return this.packagesField;
            }
            set {
                this.packagesField = value;
                this.RaisePropertyChanged("Packages");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
        public string ReceiverAccountNumber {
            get {
                return this.receiverAccountNumberField;
            }
            set {
                this.receiverAccountNumberField = value;
                this.RaisePropertyChanged("ReceiverAccountNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
        public string PaymentMethod {
            get {
                return this.paymentMethodField;
            }
            set {
                this.paymentMethodField = value;
                this.RaisePropertyChanged("PaymentMethod");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
        public Payment Payment {
            get {
                return this.paymentField;
            }
            set {
                this.paymentField = value;
                this.RaisePropertyChanged("Payment");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
        public string VoidIndicator {
            get {
                return this.voidIndicatorField;
            }
            set {
                this.voidIndicatorField = value;
                this.RaisePropertyChanged("VoidIndicator");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
        public string ConsolidationIndicator {
            get {
                return this.consolidationIndicatorField;
            }
            set {
                this.consolidationIndicatorField = value;
                this.RaisePropertyChanged("ConsolidationIndicator");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
        public string ReturnIndicator {
            get {
                return this.returnIndicatorField;
            }
            set {
                this.returnIndicatorField = value;
                this.RaisePropertyChanged("ReturnIndicator");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=13)]
        public string ReturnPIN {
            get {
                return this.returnPINField;
            }
            set {
                this.returnPINField = value;
                this.RaisePropertyChanged("ReturnPIN");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=14)]
        public string ExpressChequePIN {
            get {
                return this.expressChequePINField;
            }
            set {
                this.expressChequePINField = value;
                this.RaisePropertyChanged("ExpressChequePIN");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=15)]
        public string ProductNumber {
            get {
                return this.productNumberField;
            }
            set {
                this.productNumberField = value;
                this.RaisePropertyChanged("ProductNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=16)]
        public string Note {
            get {
                return this.noteField;
            }
            set {
                this.noteField = value;
                this.RaisePropertyChanged("Note");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=17)]
        public TradeInformation TradeInformation {
            get {
                return this.tradeInformationField;
            }
            set {
                this.tradeInformationField = value;
                this.RaisePropertyChanged("TradeInformation");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=18)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public Service[] ShipmentServices {
            get {
                return this.shipmentServicesField;
            }
            set {
                this.shipmentServicesField = value;
                this.RaisePropertyChanged("ShipmentServices");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=19)]
        public string NotificationCode {
            get {
                return this.notificationCodeField;
            }
            set {
                this.notificationCodeField = value;
                this.RaisePropertyChanged("NotificationCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=20)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public Notification[] Notifications {
            get {
                return this.notificationsField;
            }
            set {
                this.notificationsField = value;
                this.RaisePropertyChanged("Notifications");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CreateShipmentTrackingRequest : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string shippingChannelField;
        
        private string consumerIDField;
        
        private Shipment shipmentField;
        
        private RequestInformation channelIntegrationRequestInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string ShippingChannel {
            get {
                return this.shippingChannelField;
            }
            set {
                this.shippingChannelField = value;
                this.RaisePropertyChanged("ShippingChannel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string ConsumerID {
            get {
                return this.consumerIDField;
            }
            set {
                this.consumerIDField = value;
                this.RaisePropertyChanged("ConsumerID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public Shipment Shipment {
            get {
                return this.shipmentField;
            }
            set {
                this.shipmentField = value;
                this.RaisePropertyChanged("Shipment");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public RequestInformation ChannelIntegrationRequestInformation {
            get {
                return this.channelIntegrationRequestInformationField;
            }
            set {
                this.channelIntegrationRequestInformationField = value;
                this.RaisePropertyChanged("ChannelIntegrationRequestInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CreateCrossRefScanResponse : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ResponseInformation channelIntegrationResponseInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public ResponseInformation ChannelIntegrationResponseInformation {
            get {
                return this.channelIntegrationResponseInformationField;
            }
            set {
                this.channelIntegrationResponseInformationField = value;
                this.RaisePropertyChanged("ChannelIntegrationResponseInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CreateCrossRefScanRequest : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string sourceField;
        
        private CrossRefScan[] crossRefScansField;
        
        private RequestInformation channelIntegrationRequestInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Source {
            get {
                return this.sourceField;
            }
            set {
                this.sourceField = value;
                this.RaisePropertyChanged("Source");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public CrossRefScan[] CrossRefScans {
            get {
                return this.crossRefScansField;
            }
            set {
                this.crossRefScansField = value;
                this.RaisePropertyChanged("CrossRefScans");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public RequestInformation ChannelIntegrationRequestInformation {
            get {
                return this.channelIntegrationRequestInformationField;
            }
            set {
                this.channelIntegrationRequestInformationField = value;
                this.RaisePropertyChanged("ChannelIntegrationRequestInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CrossRefScan : Scan {
        
        private PIN crossRefPINField;
        
        private string scanRouteField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public PIN CrossRefPIN {
            get {
                return this.crossRefPINField;
            }
            set {
                this.crossRefPINField = value;
                this.RaisePropertyChanged("CrossRefPIN");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string ScanRoute {
            get {
                return this.scanRouteField;
            }
            set {
                this.scanRouteField = value;
                this.RaisePropertyChanged("ScanRoute");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CreateDeliveryOrUndeliveryScanResponse : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ResponseInformation channelIntegrationResponseInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public ResponseInformation ChannelIntegrationResponseInformation {
            get {
                return this.channelIntegrationResponseInformationField;
            }
            set {
                this.channelIntegrationResponseInformationField = value;
                this.RaisePropertyChanged("ChannelIntegrationResponseInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CreateDeliveryOrUndeliveryScanRequest : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string sourceField;
        
        private ScanOption[] scanOptionsField;
        
        private Scan[] scansField;
        
        private RequestInformation channelIntegrationRequestInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Source {
            get {
                return this.sourceField;
            }
            set {
                this.sourceField = value;
                this.RaisePropertyChanged("Source");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public ScanOption[] ScanOptions {
            get {
                return this.scanOptionsField;
            }
            set {
                this.scanOptionsField = value;
                this.RaisePropertyChanged("ScanOptions");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=2)]
        [System.Xml.Serialization.XmlArrayItemAttribute(IsNullable=false)]
        public Scan[] Scans {
            get {
                return this.scansField;
            }
            set {
                this.scansField = value;
                this.RaisePropertyChanged("Scans");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public RequestInformation ChannelIntegrationRequestInformation {
            get {
                return this.channelIntegrationRequestInformationField;
            }
            set {
                this.channelIntegrationRequestInformationField = value;
                this.RaisePropertyChanged("ChannelIntegrationRequestInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class CreateUSInTransitScanResponse : object, System.ComponentModel.INotifyPropertyChanged {
        
        private ResponseInformation channelIntegrationResponseInformationField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public ResponseInformation ChannelIntegrationResponseInformation {
            get {
                return this.channelIntegrationResponseInformationField;
            }
            set {
                this.channelIntegrationResponseInformationField = value;
                this.RaisePropertyChanged("ChannelIntegrationResponseInformation");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ScanProofOfPickUpScanDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string pickUpConfirmationNumberField;
        
        private Address pickUpAddressField;
        
        private string pickUpContactNameField;
        
        private string pickUpCompanyNameField;
        
        private string pickUpLocationCodeField;
        
        private string pickUpLocationField;
        
        private string commitedDeliveryDateField;
        
        private string premiumServiceCodeField;
        
        private string premiumServiceTextField;
        
        private string productTypeCodeField;
        
        private string productTypeTextField;
        
        private string specialHandlingCodeField;
        
        private string specialHandlingTextField;
        
        private string paymentTypeCodeField;
        
        private string paymentTypeTextField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string PickUpConfirmationNumber {
            get {
                return this.pickUpConfirmationNumberField;
            }
            set {
                this.pickUpConfirmationNumberField = value;
                this.RaisePropertyChanged("PickUpConfirmationNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public Address PickUpAddress {
            get {
                return this.pickUpAddressField;
            }
            set {
                this.pickUpAddressField = value;
                this.RaisePropertyChanged("PickUpAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string PickUpContactName {
            get {
                return this.pickUpContactNameField;
            }
            set {
                this.pickUpContactNameField = value;
                this.RaisePropertyChanged("PickUpContactName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string PickUpCompanyName {
            get {
                return this.pickUpCompanyNameField;
            }
            set {
                this.pickUpCompanyNameField = value;
                this.RaisePropertyChanged("PickUpCompanyName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string PickUpLocationCode {
            get {
                return this.pickUpLocationCodeField;
            }
            set {
                this.pickUpLocationCodeField = value;
                this.RaisePropertyChanged("PickUpLocationCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
        public string PickUpLocation {
            get {
                return this.pickUpLocationField;
            }
            set {
                this.pickUpLocationField = value;
                this.RaisePropertyChanged("PickUpLocation");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
        public string CommitedDeliveryDate {
            get {
                return this.commitedDeliveryDateField;
            }
            set {
                this.commitedDeliveryDateField = value;
                this.RaisePropertyChanged("CommitedDeliveryDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
        public string PremiumServiceCode {
            get {
                return this.premiumServiceCodeField;
            }
            set {
                this.premiumServiceCodeField = value;
                this.RaisePropertyChanged("PremiumServiceCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
        public string PremiumServiceText {
            get {
                return this.premiumServiceTextField;
            }
            set {
                this.premiumServiceTextField = value;
                this.RaisePropertyChanged("PremiumServiceText");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
        public string ProductTypeCode {
            get {
                return this.productTypeCodeField;
            }
            set {
                this.productTypeCodeField = value;
                this.RaisePropertyChanged("ProductTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
        public string ProductTypeText {
            get {
                return this.productTypeTextField;
            }
            set {
                this.productTypeTextField = value;
                this.RaisePropertyChanged("ProductTypeText");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
        public string SpecialHandlingCode {
            get {
                return this.specialHandlingCodeField;
            }
            set {
                this.specialHandlingCodeField = value;
                this.RaisePropertyChanged("SpecialHandlingCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
        public string SpecialHandlingText {
            get {
                return this.specialHandlingTextField;
            }
            set {
                this.specialHandlingTextField = value;
                this.RaisePropertyChanged("SpecialHandlingText");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=13)]
        public string PaymentTypeCode {
            get {
                return this.paymentTypeCodeField;
            }
            set {
                this.paymentTypeCodeField = value;
                this.RaisePropertyChanged("PaymentTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=14)]
        public string PaymentTypeText {
            get {
                return this.paymentTypeTextField;
            }
            set {
                this.paymentTypeTextField = value;
                this.RaisePropertyChanged("PaymentTypeText");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ScanInternationalOutboundScanDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string destinationPostalCodeField;
        
        private string airportCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string DestinationPostalCode {
            get {
                return this.destinationPostalCodeField;
            }
            set {
                this.destinationPostalCodeField = value;
                this.RaisePropertyChanged("DestinationPostalCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string AirportCode {
            get {
                return this.airportCodeField;
            }
            set {
                this.airportCodeField = value;
                this.RaisePropertyChanged("AirportCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ScanOnDeliveryScanDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string deliveryAddressField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string DeliveryAddress {
            get {
                return this.deliveryAddressField;
            }
            set {
                this.deliveryAddressField = value;
                this.RaisePropertyChanged("DeliveryAddress");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ScanUndeliverableScanDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string attemptedDeliveryAddressField;
        
        private string deliveryCompanyNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string AttemptedDeliveryAddress {
            get {
                return this.attemptedDeliveryAddressField;
            }
            set {
                this.attemptedDeliveryAddressField = value;
                this.RaisePropertyChanged("AttemptedDeliveryAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string DeliveryCompanyName {
            get {
                return this.deliveryCompanyNameField;
            }
            set {
                this.deliveryCompanyNameField = value;
                this.RaisePropertyChanged("DeliveryCompanyName");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ScanDeliveryScanDetails : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string deliverySignatureField;
        
        private string signatureImageField;
        
        private string signatureImageSizeField;
        
        private string signatureFormatCodeField;
        
        private string deliveryAddressField;
        
        private string deliveryCompanyNameField;
        
        private string premiumServiceCodeField;
        
        private string premiumServiceTextField;
        
        private string productTypeCodeField;
        
        private string productTypeTextField;
        
        private string specialHandlingCodeField;
        
        private string specialHandlingTextField;
        
        private string paymentTypeCodeField;
        
        private string paymentTypeTextField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string DeliverySignature {
            get {
                return this.deliverySignatureField;
            }
            set {
                this.deliverySignatureField = value;
                this.RaisePropertyChanged("DeliverySignature");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string SignatureImage {
            get {
                return this.signatureImageField;
            }
            set {
                this.signatureImageField = value;
                this.RaisePropertyChanged("SignatureImage");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string SignatureImageSize {
            get {
                return this.signatureImageSizeField;
            }
            set {
                this.signatureImageSizeField = value;
                this.RaisePropertyChanged("SignatureImageSize");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string SignatureFormatCode {
            get {
                return this.signatureFormatCodeField;
            }
            set {
                this.signatureFormatCodeField = value;
                this.RaisePropertyChanged("SignatureFormatCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string DeliveryAddress {
            get {
                return this.deliveryAddressField;
            }
            set {
                this.deliveryAddressField = value;
                this.RaisePropertyChanged("DeliveryAddress");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string DeliveryCompanyName {
            get {
                return this.deliveryCompanyNameField;
            }
            set {
                this.deliveryCompanyNameField = value;
                this.RaisePropertyChanged("DeliveryCompanyName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
        public string PremiumServiceCode {
            get {
                return this.premiumServiceCodeField;
            }
            set {
                this.premiumServiceCodeField = value;
                this.RaisePropertyChanged("PremiumServiceCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
        public string PremiumServiceText {
            get {
                return this.premiumServiceTextField;
            }
            set {
                this.premiumServiceTextField = value;
                this.RaisePropertyChanged("PremiumServiceText");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
        public string ProductTypeCode {
            get {
                return this.productTypeCodeField;
            }
            set {
                this.productTypeCodeField = value;
                this.RaisePropertyChanged("ProductTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
        public string ProductTypeText {
            get {
                return this.productTypeTextField;
            }
            set {
                this.productTypeTextField = value;
                this.RaisePropertyChanged("ProductTypeText");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
        public string SpecialHandlingCode {
            get {
                return this.specialHandlingCodeField;
            }
            set {
                this.specialHandlingCodeField = value;
                this.RaisePropertyChanged("SpecialHandlingCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
        public string SpecialHandlingText {
            get {
                return this.specialHandlingTextField;
            }
            set {
                this.specialHandlingTextField = value;
                this.RaisePropertyChanged("SpecialHandlingText");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
        public string PaymentTypeCode {
            get {
                return this.paymentTypeCodeField;
            }
            set {
                this.paymentTypeCodeField = value;
                this.RaisePropertyChanged("PaymentTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=13)]
        public string PaymentTypeText {
            get {
                return this.paymentTypeTextField;
            }
            set {
                this.paymentTypeTextField = value;
                this.RaisePropertyChanged("PaymentTypeText");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class Depot : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string idField;
        
        private string nameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string ID {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
                this.RaisePropertyChanged("ID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ScanProofOfPickUp : Scan {
        
        private ScanProofOfPickUpScanDetails scanDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ScanProofOfPickUpScanDetails ScanDetails {
            get {
                return this.scanDetailsField;
            }
            set {
                this.scanDetailsField = value;
                this.RaisePropertyChanged("ScanDetails");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ScanInternationalOutbound : Scan {
        
        private ScanInternationalOutboundScanDetails scanDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ScanInternationalOutboundScanDetails ScanDetails {
            get {
                return this.scanDetailsField;
            }
            set {
                this.scanDetailsField = value;
                this.RaisePropertyChanged("ScanDetails");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ScanOnDelivery : Scan {
        
        private ScanOnDeliveryScanDetails scanDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ScanOnDeliveryScanDetails ScanDetails {
            get {
                return this.scanDetailsField;
            }
            set {
                this.scanDetailsField = value;
                this.RaisePropertyChanged("ScanDetails");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ScanUndeliverable : Scan {
        
        private ScanUndeliverableScanDetails scanDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ScanUndeliverableScanDetails ScanDetails {
            get {
                return this.scanDetailsField;
            }
            set {
                this.scanDetailsField = value;
                this.RaisePropertyChanged("ScanDetails");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.6.1067.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://purolator/channelintegration/business.entities")]
    public partial class ScanDelivery : Scan {
        
        private ScanDeliveryScanDetails scanDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public ScanDeliveryScanDetails ScanDetails {
            get {
                return this.scanDetailsField;
            }
            set {
                this.scanDetailsField = value;
                this.RaisePropertyChanged("ScanDetails");
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="CreateUSInTransitScan", WrapperNamespace="http://purolator/channelintegration/TrackingHelperService/", IsWrapped=true)]
    public partial class CreateUSInTransitScanRequest1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://purolator/channelintegration/business.entities", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://purolator/channelintegration/business.entities", IsNullable=true)]
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanRequest createUSInTransitScanRequest;
        
        public CreateUSInTransitScanRequest1() {
        }
        
        public CreateUSInTransitScanRequest1(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanRequest createUSInTransitScanRequest) {
            this.createUSInTransitScanRequest = createUSInTransitScanRequest;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="CreateUSInTransitScanResponse", WrapperNamespace="http://purolator/channelintegration/TrackingHelperService/", IsWrapped=true)]
    public partial class CreateUSInTransitScanResponse1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://purolator/channelintegration/business.entities", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://purolator/channelintegration/business.entities", IsNullable=true)]
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanResponse CreateUSInTransitScanResult;
        
        public CreateUSInTransitScanResponse1() {
        }
        
        public CreateUSInTransitScanResponse1(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanResponse CreateUSInTransitScanResult) {
            this.CreateUSInTransitScanResult = CreateUSInTransitScanResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="CreateDeliveryOrUndeliveryScan", WrapperNamespace="http://purolator/channelintegration/TrackingHelperService/", IsWrapped=true)]
    public partial class CreateDeliveryOrUndeliveryScanRequest1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://purolator/channelintegration/business.entities", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://purolator/channelintegration/business.entities", IsNullable=true)]
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanRequest createDeliveryOrUndeliveryScanRequest;
        
        public CreateDeliveryOrUndeliveryScanRequest1() {
        }
        
        public CreateDeliveryOrUndeliveryScanRequest1(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanRequest createDeliveryOrUndeliveryScanRequest) {
            this.createDeliveryOrUndeliveryScanRequest = createDeliveryOrUndeliveryScanRequest;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="CreateDeliveryOrUndeliveryScanResponse", WrapperNamespace="http://purolator/channelintegration/TrackingHelperService/", IsWrapped=true)]
    public partial class CreateDeliveryOrUndeliveryScanResponse1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://purolator/channelintegration/business.entities", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://purolator/channelintegration/business.entities", IsNullable=true)]
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanResponse CreateDeliveryOrUndeliveryScanResult;
        
        public CreateDeliveryOrUndeliveryScanResponse1() {
        }
        
        public CreateDeliveryOrUndeliveryScanResponse1(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanResponse CreateDeliveryOrUndeliveryScanResult) {
            this.CreateDeliveryOrUndeliveryScanResult = CreateDeliveryOrUndeliveryScanResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="CreateCrossRefScan", WrapperNamespace="http://purolator/channelintegration/TrackingHelperService/", IsWrapped=true)]
    public partial class CreateCrossRefScanRequest1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://purolator/channelintegration/business.entities", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://purolator/channelintegration/business.entities", IsNullable=true)]
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanRequest createCrossRefScanRequest;
        
        public CreateCrossRefScanRequest1() {
        }
        
        public CreateCrossRefScanRequest1(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanRequest createCrossRefScanRequest) {
            this.createCrossRefScanRequest = createCrossRefScanRequest;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="CreateCrossRefScanResponse", WrapperNamespace="http://purolator/channelintegration/TrackingHelperService/", IsWrapped=true)]
    public partial class CreateCrossRefScanResponse1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://purolator/channelintegration/business.entities", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://purolator/channelintegration/business.entities", IsNullable=true)]
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanResponse CreateCrossRefScanResult;
        
        public CreateCrossRefScanResponse1() {
        }
        
        public CreateCrossRefScanResponse1(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanResponse CreateCrossRefScanResult) {
            this.CreateCrossRefScanResult = CreateCrossRefScanResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="CreateShipmentTracking", WrapperNamespace="http://purolator/channelintegration/TrackingHelperService/", IsWrapped=true)]
    public partial class CreateShipmentTrackingRequest1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://purolator/channelintegration/business.entities", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://purolator/channelintegration/business.entities", IsNullable=true)]
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingRequest createShipmentTrackingRequest;
        
        public CreateShipmentTrackingRequest1() {
        }
        
        public CreateShipmentTrackingRequest1(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingRequest createShipmentTrackingRequest) {
            this.createShipmentTrackingRequest = createShipmentTrackingRequest;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="CreateShipmentTrackingResponse", WrapperNamespace="http://purolator/channelintegration/TrackingHelperService/", IsWrapped=true)]
    public partial class CreateShipmentTrackingResponse1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://purolator/channelintegration/business.entities", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://purolator/channelintegration/business.entities", IsNullable=true)]
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingResponse CreateShipmentTrackingResult;
        
        public CreateShipmentTrackingResponse1() {
        }
        
        public CreateShipmentTrackingResponse1(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingResponse CreateShipmentTrackingResult) {
            this.CreateShipmentTrackingResult = CreateShipmentTrackingResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="DeleteShipmentTracking", WrapperNamespace="http://purolator/channelintegration/TrackingHelperService/", IsWrapped=true)]
    public partial class DeleteShipmentTrackingRequest1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://purolator/channelintegration/business.entities", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://purolator/channelintegration/business.entities", IsNullable=true)]
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingRequest deleteShipmentTrackingRequest;
        
        public DeleteShipmentTrackingRequest1() {
        }
        
        public DeleteShipmentTrackingRequest1(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingRequest deleteShipmentTrackingRequest) {
            this.deleteShipmentTrackingRequest = deleteShipmentTrackingRequest;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="DeleteShipmentTrackingResponse", WrapperNamespace="http://purolator/channelintegration/TrackingHelperService/", IsWrapped=true)]
    public partial class DeleteShipmentTrackingResponse1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://purolator/channelintegration/business.entities", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://purolator/channelintegration/business.entities", IsNullable=true)]
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingResponse DeleteShipmentTrackingResult;
        
        public DeleteShipmentTrackingResponse1() {
        }
        
        public DeleteShipmentTrackingResponse1(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingResponse DeleteShipmentTrackingResult) {
            this.DeleteShipmentTrackingResult = DeleteShipmentTrackingResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="CreateOFDScan", WrapperNamespace="http://purolator/channelintegration/TrackingHelperService/", IsWrapped=true)]
    public partial class CreateOFDScanRequest1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://purolator/channelintegration/business.entities", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://purolator/channelintegration/business.entities", IsNullable=true)]
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanRequest createOFDScanRequest;
        
        public CreateOFDScanRequest1() {
        }
        
        public CreateOFDScanRequest1(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanRequest createOFDScanRequest) {
            this.createOFDScanRequest = createOFDScanRequest;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="CreateOFDScanResponse", WrapperNamespace="http://purolator/channelintegration/TrackingHelperService/", IsWrapped=true)]
    public partial class CreateOFDScanResponse1 {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://purolator/channelintegration/business.entities", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Namespace="http://purolator/channelintegration/business.entities", IsNullable=true)]
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanResponse CreateOFDScanResult;
        
        public CreateOFDScanResponse1() {
        }
        
        public CreateOFDScanResponse1(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanResponse CreateOFDScanResult) {
            this.CreateOFDScanResult = CreateOFDScanResult;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface TrackingHelperServiceSoapChannel : Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class TrackingHelperServiceSoapClient : System.ServiceModel.ClientBase<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap>, Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap {
        
        public TrackingHelperServiceSoapClient() {
        }
        
        public TrackingHelperServiceSoapClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public TrackingHelperServiceSoapClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public TrackingHelperServiceSoapClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public TrackingHelperServiceSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanResponse1 Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap.CreateUSInTransitScan(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanRequest1 request) {
            return base.Channel.CreateUSInTransitScan(request);
        }
        
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanResponse CreateUSInTransitScan(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanRequest createUSInTransitScanRequest) {
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanRequest1 inValue = new Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanRequest1();
            inValue.createUSInTransitScanRequest = createUSInTransitScanRequest;
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanResponse1 retVal = ((Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap)(this)).CreateUSInTransitScan(inValue);
            return retVal.CreateUSInTransitScanResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanResponse1> Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap.CreateUSInTransitScanAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanRequest1 request) {
            return base.Channel.CreateUSInTransitScanAsync(request);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanResponse1> CreateUSInTransitScanAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanRequest createUSInTransitScanRequest) {
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanRequest1 inValue = new Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateUSInTransitScanRequest1();
            inValue.createUSInTransitScanRequest = createUSInTransitScanRequest;
            return ((Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap)(this)).CreateUSInTransitScanAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanResponse1 Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap.CreateDeliveryOrUndeliveryScan(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanRequest1 request) {
            return base.Channel.CreateDeliveryOrUndeliveryScan(request);
        }
        
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanResponse CreateDeliveryOrUndeliveryScan(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanRequest createDeliveryOrUndeliveryScanRequest) {
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanRequest1 inValue = new Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanRequest1();
            inValue.createDeliveryOrUndeliveryScanRequest = createDeliveryOrUndeliveryScanRequest;
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanResponse1 retVal = ((Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap)(this)).CreateDeliveryOrUndeliveryScan(inValue);
            return retVal.CreateDeliveryOrUndeliveryScanResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanResponse1> Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap.CreateDeliveryOrUndeliveryScanAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanRequest1 request) {
            return base.Channel.CreateDeliveryOrUndeliveryScanAsync(request);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanResponse1> CreateDeliveryOrUndeliveryScanAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanRequest createDeliveryOrUndeliveryScanRequest) {
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanRequest1 inValue = new Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateDeliveryOrUndeliveryScanRequest1();
            inValue.createDeliveryOrUndeliveryScanRequest = createDeliveryOrUndeliveryScanRequest;
            return ((Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap)(this)).CreateDeliveryOrUndeliveryScanAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanResponse1 Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap.CreateCrossRefScan(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanRequest1 request) {
            return base.Channel.CreateCrossRefScan(request);
        }
        
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanResponse CreateCrossRefScan(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanRequest createCrossRefScanRequest) {
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanRequest1 inValue = new Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanRequest1();
            inValue.createCrossRefScanRequest = createCrossRefScanRequest;
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanResponse1 retVal = ((Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap)(this)).CreateCrossRefScan(inValue);
            return retVal.CreateCrossRefScanResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanResponse1> Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap.CreateCrossRefScanAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanRequest1 request) {
            return base.Channel.CreateCrossRefScanAsync(request);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanResponse1> CreateCrossRefScanAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanRequest createCrossRefScanRequest) {
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanRequest1 inValue = new Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateCrossRefScanRequest1();
            inValue.createCrossRefScanRequest = createCrossRefScanRequest;
            return ((Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap)(this)).CreateCrossRefScanAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingResponse1 Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap.CreateShipmentTracking(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingRequest1 request) {
            return base.Channel.CreateShipmentTracking(request);
        }
        
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingResponse CreateShipmentTracking(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingRequest createShipmentTrackingRequest) {
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingRequest1 inValue = new Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingRequest1();
            inValue.createShipmentTrackingRequest = createShipmentTrackingRequest;
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingResponse1 retVal = ((Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap)(this)).CreateShipmentTracking(inValue);
            return retVal.CreateShipmentTrackingResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingResponse1> Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap.CreateShipmentTrackingAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingRequest1 request) {
            return base.Channel.CreateShipmentTrackingAsync(request);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingResponse1> CreateShipmentTrackingAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingRequest createShipmentTrackingRequest) {
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingRequest1 inValue = new Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateShipmentTrackingRequest1();
            inValue.createShipmentTrackingRequest = createShipmentTrackingRequest;
            return ((Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap)(this)).CreateShipmentTrackingAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingResponse1 Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap.DeleteShipmentTracking(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingRequest1 request) {
            return base.Channel.DeleteShipmentTracking(request);
        }
        
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingResponse DeleteShipmentTracking(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingRequest deleteShipmentTrackingRequest) {
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingRequest1 inValue = new Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingRequest1();
            inValue.deleteShipmentTrackingRequest = deleteShipmentTrackingRequest;
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingResponse1 retVal = ((Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap)(this)).DeleteShipmentTracking(inValue);
            return retVal.DeleteShipmentTrackingResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingResponse1> Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap.DeleteShipmentTrackingAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingRequest1 request) {
            return base.Channel.DeleteShipmentTrackingAsync(request);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingResponse1> DeleteShipmentTrackingAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingRequest deleteShipmentTrackingRequest) {
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingRequest1 inValue = new Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.DeleteShipmentTrackingRequest1();
            inValue.deleteShipmentTrackingRequest = deleteShipmentTrackingRequest;
            return ((Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap)(this)).DeleteShipmentTrackingAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanResponse1 Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap.CreateOFDScan(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanRequest1 request) {
            return base.Channel.CreateOFDScan(request);
        }
        
        public Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanResponse CreateOFDScan(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanRequest createOFDScanRequest) {
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanRequest1 inValue = new Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanRequest1();
            inValue.createOFDScanRequest = createOFDScanRequest;
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanResponse1 retVal = ((Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap)(this)).CreateOFDScan(inValue);
            return retVal.CreateOFDScanResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanResponse1> Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap.CreateOFDScanAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanRequest1 request) {
            return base.Channel.CreateOFDScanAsync(request);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanResponse1> CreateOFDScanAsync(Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanRequest createOFDScanRequest) {
            Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanRequest1 inValue = new Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.CreateOFDScanRequest1();
            inValue.createOFDScanRequest = createOFDScanRequest;
            return ((Purolator.SmartSort.ChannelIntegration.TrackingHelper.THService.TrackingHelperServiceSoap)(this)).CreateOFDScanAsync(inValue);
        }
    }
}
