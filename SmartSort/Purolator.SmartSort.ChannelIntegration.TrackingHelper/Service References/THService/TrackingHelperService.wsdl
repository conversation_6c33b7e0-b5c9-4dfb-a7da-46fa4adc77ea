<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://purolator/channelintegration/TrackingHelperService/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:s1="http://purolator/channelintegration/business.entities" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://purolator/channelintegration/TrackingHelperService/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://purolator/channelintegration/TrackingHelperService/">
      <s:import namespace="http://purolator/channelintegration/business.entities" />
      <s:element name="CreateUSInTransitScan">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" ref="s1:createUSInTransitScanRequest" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateUSInTransitScanResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" ref="s1:CreateUSInTransitScanResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateDeliveryOrUndeliveryScan">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" ref="s1:createDeliveryOrUndeliveryScanRequest" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateDeliveryOrUndeliveryScanResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" ref="s1:CreateDeliveryOrUndeliveryScanResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateCrossRefScan">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" ref="s1:createCrossRefScanRequest" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateCrossRefScanResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" ref="s1:CreateCrossRefScanResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateShipmentTracking">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" ref="s1:createShipmentTrackingRequest" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateShipmentTrackingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" ref="s1:CreateShipmentTrackingResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteShipmentTracking">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" ref="s1:deleteShipmentTrackingRequest" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteShipmentTrackingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" ref="s1:DeleteShipmentTrackingResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateOFDScan">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" ref="s1:createOFDScanRequest" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateOFDScanResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" ref="s1:CreateOFDScanResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
    <s:schema elementFormDefault="qualified" targetNamespace="http://purolator/channelintegration/business.entities">
      <s:element name="createUSInTransitScanRequest" nillable="true" type="s1:CreateUSInTransitScanRequest" />
      <s:complexType name="CreateUSInTransitScanRequest">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Scans" nillable="true" type="s1:ArrayOfScan" />
          <s:element minOccurs="1" maxOccurs="1" name="ChannelIntegrationRequestInformation" nillable="true" type="s1:RequestInformation" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfScan">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Scan" type="s1:Scan" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Scan">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="PIN" nillable="true" type="s1:PIN" />
          <s:element minOccurs="1" maxOccurs="1" name="Depot" nillable="true" type="s1:Depot" />
          <s:element minOccurs="1" maxOccurs="1" name="ScanDate" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ScanTime" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Description" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Comment" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="SummaryScanIndicator" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="ScanType" type="s:string" />
      </s:complexType>
      <s:complexType name="PIN">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Text" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="PINType" type="s:string" />
      </s:complexType>
      <s:complexType name="Depot">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ID" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Name" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ScanDelivery">
        <s:complexContent mixed="false">
          <s:extension base="s1:Scan">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="ScanDetails" type="s1:ScanDeliveryScanDetails" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ScanDeliveryScanDetails">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="DeliverySignature" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="SignatureImage" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="SignatureImageSize" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="SignatureFormatCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DeliveryAddress" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DeliveryCompanyName" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PremiumServiceCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PremiumServiceText" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ProductTypeCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ProductTypeText" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="SpecialHandlingCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="SpecialHandlingText" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PaymentTypeCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PaymentTypeText" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ScanUndeliverable">
        <s:complexContent mixed="false">
          <s:extension base="s1:Scan">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="ScanDetails" type="s1:ScanUndeliverableScanDetails" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ScanUndeliverableScanDetails">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="AttemptedDeliveryAddress" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DeliveryCompanyName" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ScanOnDelivery">
        <s:complexContent mixed="false">
          <s:extension base="s1:Scan">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="ScanDetails" type="s1:ScanOnDeliveryScanDetails" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ScanOnDeliveryScanDetails">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="DeliveryAddress" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ScanInternationalOutbound">
        <s:complexContent mixed="false">
          <s:extension base="s1:Scan">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="ScanDetails" type="s1:ScanInternationalOutboundScanDetails" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ScanInternationalOutboundScanDetails">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DestinationPostalCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="AirportCode" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ScanProofOfPickUp">
        <s:complexContent mixed="false">
          <s:extension base="s1:Scan">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="ScanDetails" type="s1:ScanProofOfPickUpScanDetails" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ScanProofOfPickUpScanDetails">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="PickUpConfirmationNumber" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PickUpAddress" nillable="true" type="s1:Address" />
          <s:element minOccurs="1" maxOccurs="1" name="PickUpContactName" nillable="true" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PickUpCompanyName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PickUpLocationCode" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PickUpLocation" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="CommitedDeliveryDate" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PremiumServiceCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PremiumServiceText" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ProductTypeCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ProductTypeText" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="SpecialHandlingCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="SpecialHandlingText" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PaymentTypeCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PaymentTypeText" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Address">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="StreetNumber" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="StreetAddress1" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="StreetAddress2" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="StreetAddress3" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="UnitSuiteApt" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="FloorNumber" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="City" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="CityCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ProvinceCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="CountryCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PostalCode" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="AddressType" type="s:string" />
      </s:complexType>
      <s:complexType name="RequestInformation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="RequestVersion" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="RequestLanguage" nillable="true" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransactionGroupID" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="CreateUSInTransitScanResult" nillable="true" type="s1:CreateUSInTransitScanResponse" />
      <s:complexType name="CreateUSInTransitScanResponse">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ChannelIntegrationResponseInformation" nillable="true" type="s1:ResponseInformation" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ResponseInformation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Errors" nillable="true" type="s1:ArrayOfError" />
          <s:element minOccurs="0" maxOccurs="1" name="DebugInformation" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfError">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Error" type="s1:RunTimeError" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="RunTimeError">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ErrorCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ErrorMessage" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="RunTimeErrorMessage" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="RunTimeErrorMessageStack" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="RunTimeErrorContext" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="RunTimeErrorDetails" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="createDeliveryOrUndeliveryScanRequest" nillable="true" type="s1:CreateDeliveryOrUndeliveryScanRequest" />
      <s:complexType name="CreateDeliveryOrUndeliveryScanRequest">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Source" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ScanOptions" type="s1:ArrayOfScanOption" />
          <s:element minOccurs="1" maxOccurs="1" name="Scans" nillable="true" type="s1:ArrayOfScan" />
          <s:element minOccurs="1" maxOccurs="1" name="ChannelIntegrationRequestInformation" nillable="true" type="s1:RequestInformation" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfScanOption">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ScanOption" type="s1:ScanOption" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ScanOption">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="TransactionType" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="OriginScanType" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Location" nillable="true" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="SubScanType" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="CreateDeliveryOrUndeliveryScanResult" nillable="true" type="s1:CreateDeliveryOrUndeliveryScanResponse" />
      <s:complexType name="CreateDeliveryOrUndeliveryScanResponse">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ChannelIntegrationResponseInformation" nillable="true" type="s1:ResponseInformation" />
        </s:sequence>
      </s:complexType>
      <s:element name="createCrossRefScanRequest" nillable="true" type="s1:CreateCrossRefScanRequest" />
      <s:complexType name="CreateCrossRefScanRequest">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Source" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="CrossRefScans" nillable="true" type="s1:ArrayOfCrossRefScan" />
          <s:element minOccurs="1" maxOccurs="1" name="ChannelIntegrationRequestInformation" nillable="true" type="s1:RequestInformation" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfCrossRefScan">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="CrossRefScan" type="s1:CrossRefScan" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="CrossRefScan">
        <s:complexContent mixed="false">
          <s:extension base="s1:Scan">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CrossRefPIN" nillable="true" type="s1:PIN" />
              <s:element minOccurs="1" maxOccurs="1" name="ScanRoute" nillable="true" type="s:string" />
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="CreateCrossRefScanResult" nillable="true" type="s1:CreateCrossRefScanResponse" />
      <s:complexType name="CreateCrossRefScanResponse">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ChannelIntegrationResponseInformation" nillable="true" type="s1:ResponseInformation" />
        </s:sequence>
      </s:complexType>
      <s:element name="createShipmentTrackingRequest" nillable="true" type="s1:CreateShipmentTrackingRequest" />
      <s:complexType name="CreateShipmentTrackingRequest">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ShippingChannel" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ConsumerID" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Shipment" nillable="true" type="s1:Shipment" />
          <s:element minOccurs="1" maxOccurs="1" name="ChannelIntegrationRequestInformation" nillable="true" type="s1:RequestInformation" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Shipment">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Identification" nillable="true" type="s1:ShipmentIdentification" />
          <s:element minOccurs="1" maxOccurs="1" name="CustomerReferences" nillable="true" type="s1:ArrayOfCustomerReference" />
          <s:element minOccurs="1" maxOccurs="1" name="ShipmentAddresses" nillable="true" type="s1:ArrayOfContact" />
          <s:element minOccurs="1" maxOccurs="1" name="ModeOfTransport" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="TotalWeight" nillable="true" type="s1:Weight" />
          <s:element minOccurs="1" maxOccurs="1" name="TotalPackages" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Packages" nillable="true" type="s1:ArrayOfPackage" />
          <s:element minOccurs="1" maxOccurs="1" name="ReceiverAccountNumber" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PaymentMethod" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Payment" nillable="true" type="s1:Payment" />
          <s:element minOccurs="1" maxOccurs="1" name="VoidIndicator" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ConsolidationIndicator" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ReturnIndicator" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ReturnPIN" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ExpressChequePIN" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ProductNumber" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Note" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="TradeInformation" nillable="true" type="s1:TradeInformation" />
          <s:element minOccurs="1" maxOccurs="1" name="ShipmentServices" nillable="true" type="s1:ArrayOfService" />
          <s:element minOccurs="1" maxOccurs="1" name="NotificationCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Notifications" nillable="true" type="s1:ArrayOfNotification" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ShipmentIdentification">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ShipmentPIN" nillable="true" type="s1:PIN" />
          <s:element minOccurs="1" maxOccurs="1" name="ShipperAccountNumber" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ShipmentDate" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfCustomerReference">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="CustomerReference" type="s1:CustomerReference" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="CustomerReference">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Code" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="SequenceNumber" type="s:string" />
      </s:complexType>
      <s:complexType name="ArrayOfContact">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Contact" type="s1:Contact" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Contact">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="CompanyName" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DepartmentName" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="AccountNumber" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ContactName" nillable="true" type="s1:Name" />
          <s:element minOccurs="1" maxOccurs="1" name="Addresses" nillable="true" type="s1:ArrayOfAddress" />
          <s:element minOccurs="1" maxOccurs="1" name="EmailAddresses" nillable="true" type="s1:ArrayOfEmailAddress" />
          <s:element minOccurs="1" maxOccurs="1" name="Telecoms" nillable="true" type="s1:ArrayOfTelecom" />
        </s:sequence>
        <s:attribute form="unqualified" name="ContactType" type="s:string" />
      </s:complexType>
      <s:complexType name="Name">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="FirstName" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="MiddleName" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="LastName" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfAddress">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Address" type="s1:Address" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfEmailAddress">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="EmailAddress" type="s1:EmailAddress" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="EmailAddress">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Text" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="EmailAddressType" type="s:string" />
      </s:complexType>
      <s:complexType name="ArrayOfTelecom">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Telecom" type="s1:Telecom" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Telecom">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="CountryCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="AreaCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Number" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Extension" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="TelecomType" type="s:string" />
      </s:complexType>
      <s:complexType name="Weight">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Value" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="UnitOfMeasure" type="s:string" />
      </s:complexType>
      <s:complexType name="ArrayOfPackage">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Package" type="s1:Package" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Package">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Identification" nillable="true" type="s1:PackageIdentification" />
          <s:element minOccurs="1" maxOccurs="1" name="CustomerReferences" nillable="true" type="s1:ArrayOfCustomerReference" />
          <s:element minOccurs="1" maxOccurs="1" name="Dimensions" nillable="true" type="s1:Dimensions" />
          <s:element minOccurs="1" maxOccurs="1" name="Weight" nillable="true" type="s1:Weight" />
          <s:element minOccurs="1" maxOccurs="1" name="Services" nillable="true" type="s1:ArrayOfService" />
          <s:element minOccurs="1" maxOccurs="1" name="Notifications" nillable="true" type="s1:ArrayOfNotification" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="PackageIdentification">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ShipmentPIN" nillable="true" type="s1:PIN" />
          <s:element minOccurs="1" maxOccurs="1" name="ShipperAccountNumber" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ShipmentDate" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PIN" nillable="true" type="s1:PIN" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Dimensions">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Length" nillable="true" type="s1:Length" />
          <s:element minOccurs="1" maxOccurs="1" name="Width" nillable="true" type="s1:Width" />
          <s:element minOccurs="1" maxOccurs="1" name="Height" nillable="true" type="s1:Height" />
        </s:sequence>
        <s:attribute form="unqualified" name="UnitOfMeasure" type="s:string" />
      </s:complexType>
      <s:complexType name="Length">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Value" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="UnitOfMeasure" type="s:string" />
      </s:complexType>
      <s:complexType name="Width">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Value" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="UnitOfMeasure" type="s:string" />
      </s:complexType>
      <s:complexType name="Height">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Value" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="UnitOfMeasure" type="s:string" />
      </s:complexType>
      <s:complexType name="ArrayOfService">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Service" type="s1:Service" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Service">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Code" nillable="true" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Value" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfNotification">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Notification" type="s1:Notification" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Notification">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="EmailAddresses" nillable="true" type="s1:ArrayOfEmailAddress" />
          <s:element minOccurs="1" maxOccurs="1" name="NotificationTemplate" nillable="true" type="s1:NotificationTemplate" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="NotificationTemplate">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Number" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Parameters" nillable="true" type="s1:ArrayOfNotificationParameter" />
        </s:sequence>
        <s:attribute form="unqualified" name="NotificationTemplateType" type="s:string" />
      </s:complexType>
      <s:complexType name="ArrayOfNotificationParameter">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Parameter" nillable="true" type="s1:NotificationParameter" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="NotificationParameter">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Name" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="Value" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Payment">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Amount" nillable="true" type="s1:Amount" />
          <s:element minOccurs="0" maxOccurs="1" name="CreditCard" type="s1:CreditCard" />
          <s:element minOccurs="0" maxOccurs="1" name="Merchant" type="s1:Merchant" />
          <s:element minOccurs="0" maxOccurs="1" name="AuthorizationNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ConfirmationNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="TransactionId" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="PaymentId" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CardDataSource" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="PaymentType" type="s:string" />
      </s:complexType>
      <s:complexType name="Amount">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Value" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="CurrencyCode" type="s:string" />
      </s:complexType>
      <s:complexType name="CreditCard">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Number" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ExpiryDate" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="SecurityNumber" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="NameOnCreditCard" nillable="true" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="AddressVerificationInformation" type="s1:AddressVerify" />
          <s:element minOccurs="1" maxOccurs="1" name="MerchantApplicationId" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="CreditCardType" type="s:string" />
      </s:complexType>
      <s:complexType name="AddressVerify">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="StreetNumber" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="StreetName" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PostalCode" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="Merchant">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ID" nillable="true" type="s:string" />
        </s:sequence>
        <s:attribute form="unqualified" name="MerchantType" type="s:string" />
      </s:complexType>
      <s:complexType name="TradeInformation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="USInTransitScanIndicator" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ShipmentDescription" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ShipmentHarmonizedCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ManufacturerCountryCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DutiableIndicator" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ShipmentCustomValue" nillable="true" type="s1:Amount" />
          <s:element minOccurs="1" maxOccurs="1" name="TaxesPaidCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="CustomImportType" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ExtraChargeCode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ExtraChargeAmount" nillable="true" type="s1:Amount" />
          <s:element minOccurs="1" maxOccurs="1" name="SpecialInstructionIndicator" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="SenderTaxID" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ReceiverTaxID" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="OriginIATACode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DestinationIATACode" nillable="true" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PreferredBroker" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="CreateShipmentTrackingResult" nillable="true" type="s1:CreateShipmentTrackingResponse" />
      <s:complexType name="CreateShipmentTrackingResponse">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ChannelIntegrationResponseInformation" nillable="true" type="s1:ResponseInformation" />
        </s:sequence>
      </s:complexType>
      <s:element name="deleteShipmentTrackingRequest" nillable="true" type="s1:DeleteShipmentTrackingRequest" />
      <s:complexType name="DeleteShipmentTrackingRequest">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ShipmentIdentification" nillable="true" type="s1:ShipmentIdentification" />
          <s:element minOccurs="1" maxOccurs="1" name="ChannelIntegrationRequestInformation" nillable="true" type="s1:RequestInformation" />
        </s:sequence>
      </s:complexType>
      <s:element name="DeleteShipmentTrackingResult" nillable="true" type="s1:DeleteShipmentTrackingResponse" />
      <s:complexType name="DeleteShipmentTrackingResponse">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ChannelIntegrationResponseInformation" nillable="true" type="s1:ResponseInformation" />
        </s:sequence>
      </s:complexType>
      <s:element name="createOFDScanRequest" nillable="true" type="s1:CreateOFDScanRequest" />
      <s:complexType name="CreateOFDScanRequest">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Source" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ScanOptions" type="s1:ScanOption" />
          <s:element minOccurs="1" maxOccurs="1" name="Scans" nillable="true" type="s1:Scan" />
          <s:element minOccurs="0" maxOccurs="1" name="Route" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ChannelIntegrationRequestInformation" nillable="true" type="s1:RequestInformation" />
        </s:sequence>
      </s:complexType>
      <s:element name="CreateOFDScanResult" nillable="true" type="s1:CreateOFDScanResponse" />
      <s:complexType name="CreateOFDScanResponse">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ChannelIntegrationResponseInformation" nillable="true" type="s1:ResponseInformation" />
        </s:sequence>
      </s:complexType>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="CreateUSInTransitScanSoapIn">
    <wsdl:part name="parameters" element="tns:CreateUSInTransitScan" />
  </wsdl:message>
  <wsdl:message name="CreateUSInTransitScanSoapOut">
    <wsdl:part name="parameters" element="tns:CreateUSInTransitScanResponse" />
  </wsdl:message>
  <wsdl:message name="CreateDeliveryOrUndeliveryScanSoapIn">
    <wsdl:part name="parameters" element="tns:CreateDeliveryOrUndeliveryScan" />
  </wsdl:message>
  <wsdl:message name="CreateDeliveryOrUndeliveryScanSoapOut">
    <wsdl:part name="parameters" element="tns:CreateDeliveryOrUndeliveryScanResponse" />
  </wsdl:message>
  <wsdl:message name="CreateCrossRefScanSoapIn">
    <wsdl:part name="parameters" element="tns:CreateCrossRefScan" />
  </wsdl:message>
  <wsdl:message name="CreateCrossRefScanSoapOut">
    <wsdl:part name="parameters" element="tns:CreateCrossRefScanResponse" />
  </wsdl:message>
  <wsdl:message name="CreateShipmentTrackingSoapIn">
    <wsdl:part name="parameters" element="tns:CreateShipmentTracking" />
  </wsdl:message>
  <wsdl:message name="CreateShipmentTrackingSoapOut">
    <wsdl:part name="parameters" element="tns:CreateShipmentTrackingResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteShipmentTrackingSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteShipmentTracking" />
  </wsdl:message>
  <wsdl:message name="DeleteShipmentTrackingSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteShipmentTrackingResponse" />
  </wsdl:message>
  <wsdl:message name="CreateOFDScanSoapIn">
    <wsdl:part name="parameters" element="tns:CreateOFDScan" />
  </wsdl:message>
  <wsdl:message name="CreateOFDScanSoapOut">
    <wsdl:part name="parameters" element="tns:CreateOFDScanResponse" />
  </wsdl:message>
  <wsdl:portType name="TrackingHelperServiceSoap">
    <wsdl:operation name="CreateUSInTransitScan">
      <wsdl:input message="tns:CreateUSInTransitScanSoapIn" />
      <wsdl:output message="tns:CreateUSInTransitScanSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateDeliveryOrUndeliveryScan">
      <wsdl:input message="tns:CreateDeliveryOrUndeliveryScanSoapIn" />
      <wsdl:output message="tns:CreateDeliveryOrUndeliveryScanSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateCrossRefScan">
      <wsdl:input message="tns:CreateCrossRefScanSoapIn" />
      <wsdl:output message="tns:CreateCrossRefScanSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateShipmentTracking">
      <wsdl:input message="tns:CreateShipmentTrackingSoapIn" />
      <wsdl:output message="tns:CreateShipmentTrackingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteShipmentTracking">
      <wsdl:input message="tns:DeleteShipmentTrackingSoapIn" />
      <wsdl:output message="tns:DeleteShipmentTrackingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateOFDScan">
      <wsdl:input message="tns:CreateOFDScanSoapIn" />
      <wsdl:output message="tns:CreateOFDScanSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="TrackingHelperServiceSoap" type="tns:TrackingHelperServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="CreateUSInTransitScan">
      <soap:operation soapAction="http://purolator/channelintegration/TrackingHelperService/CreateUSInTransitScan" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateDeliveryOrUndeliveryScan">
      <soap:operation soapAction="http://purolator/channelintegration/TrackingHelperService/CreateDeliveryOrUndeliveryScan" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateCrossRefScan">
      <soap:operation soapAction="http://purolator/channelintegration/TrackingHelperService/CreateCrossRefScan" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateShipmentTracking">
      <soap:operation soapAction="http://purolator/channelintegration/TrackingHelperService/CreateShipmentTracking" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteShipmentTracking">
      <soap:operation soapAction="http://purolator/channelintegration/TrackingHelperService/DeleteShipmentTracking" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateOFDScan">
      <soap:operation soapAction="http://purolator/channelintegration/TrackingHelperService/CreateOFDScan" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="TrackingHelperServiceSoap12" type="tns:TrackingHelperServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="CreateUSInTransitScan">
      <soap12:operation soapAction="http://purolator/channelintegration/TrackingHelperService/CreateUSInTransitScan" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateDeliveryOrUndeliveryScan">
      <soap12:operation soapAction="http://purolator/channelintegration/TrackingHelperService/CreateDeliveryOrUndeliveryScan" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateCrossRefScan">
      <soap12:operation soapAction="http://purolator/channelintegration/TrackingHelperService/CreateCrossRefScan" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateShipmentTracking">
      <soap12:operation soapAction="http://purolator/channelintegration/TrackingHelperService/CreateShipmentTracking" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteShipmentTracking">
      <soap12:operation soapAction="http://purolator/channelintegration/TrackingHelperService/DeleteShipmentTracking" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateOFDScan">
      <soap12:operation soapAction="http://purolator/channelintegration/TrackingHelperService/CreateOFDScan" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="TrackingHelperService">
    <wsdl:port name="TrackingHelperServiceSoap" binding="tns:TrackingHelperServiceSoap">
      <soap:address location="http://**************/TrackingHelperService/TrackingHelperService.asmx" />
    </wsdl:port>
    <wsdl:port name="TrackingHelperServiceSoap12" binding="tns:TrackingHelperServiceSoap12">
      <soap12:address location="http://**************/TrackingHelperService/TrackingHelperService.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>