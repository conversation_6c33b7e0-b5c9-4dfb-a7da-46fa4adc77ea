﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;TrackingHelperServiceSoap&quot; /&gt;" bindingType="basicHttpBinding" name="TrackingHelperServiceSoap" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://**************/TrackingHelperService/TrackingHelperService.asmx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;TrackingHelperServiceSoap&quot; contract=&quot;THService.TrackingHelperServiceSoap&quot; name=&quot;TrackingHelperServiceSoap&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://**************/TrackingHelperService/TrackingHelperService.asmx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;TrackingHelperServiceSoap&quot; contract=&quot;THService.TrackingHelperServiceSoap&quot; name=&quot;TrackingHelperServiceSoap&quot; /&gt;" contractName="THService.TrackingHelperServiceSoap" name="TrackingHelperServiceSoap" />
  </endpoints>
</configurationSnapshot>