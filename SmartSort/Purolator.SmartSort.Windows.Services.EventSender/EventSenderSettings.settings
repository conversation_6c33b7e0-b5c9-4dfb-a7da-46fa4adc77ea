﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="Purolator.SmartSort.Windows.Services.EventSender" GeneratedClassName="EventSenderSettings">
  <Profiles />
  <Settings>
    <Setting Name="FetchAmount" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">100</Value>
    </Setting>
    <Setting Name="ProcessPeriodInSeconds" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">30</Value>
    </Setting>
    <Setting Name="ConcurrentWorkers" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">2</Value>
    </Setting>
    <Setting Name="EVENTS_SNS_TOPIC" Type="System.String" Scope="Application">
      <Value Profile="(Default)">arn:aws:sns:us-east-1:853215655166:infohub-events-dev-ScanEventsIntake</Value>
    </Setting>
  </Settings>
</SettingsFile>