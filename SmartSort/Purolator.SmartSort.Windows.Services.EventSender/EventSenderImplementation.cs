﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Configuration;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Common;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Serializer;
using Purolator.SmartSort.ChannelIntegration.EnterpriseEvent;

namespace Purolator.SmartSort.Windows.Services.EventSender
{
    class EventSenderImplementation
    {
        public static void ResetDatabaseQueue(string connectionInfo)
        {            
            try
            {
                using (SqlConnection conn = new SqlConnection(connectionInfo))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("dbo.EventResetData", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("server_name", Environment.MachineName));

                        int result = cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error resetting queue", LogCategories.ENTERPRISE_EVENT, ex);                            
            }           
        }
        

        public static List<Entities.EnterpriseEvent> GetWorkItems(string connectionInfo, int fetchAmount)
        {
            List<Entities.EnterpriseEvent> result = new List<Entities.EnterpriseEvent>();
            
            using (SqlConnection conn = new SqlConnection(connectionInfo))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand("dbo.EventGetData", conn))
                {    
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Add(new SqlParameter("fetchNo", fetchAmount));
                    cmd.Parameters.Add(new SqlParameter("server_name", Environment.MachineName));

                    using (var reader = cmd.ExecuteReader())
                    {

                        while (reader.Read())
                        {
                            object dataObj = reader["data"];

                            if (dataObj != null)
                            {
                                Entities.EnterpriseEvent ev = new Entities.EnterpriseEvent();
                                try
                                {
                                    ev = Serializer.Serializer.Deserialize<Entities.EnterpriseEvent>(dataObj.ToString());
                                    ev.SSBERequestID = int.Parse(reader["SSBERequestID"].ToString());
                                    result.Add(ev);
                                }
                                catch (Exception ex)
                                {
                                    Logger.Error("Error loading enterprise event XML:" + dataObj.ToString(), LogCategories.ENTERPRISE_EVENT, ex);
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }

        private static void UpdateEvent(string connectionInfo, int eventId, char status, string errorCode)
        {
            try
            {                
                using (SqlConnection conn = new SqlConnection(connectionInfo))
                {
                    conn.Open();

                    using (SqlCommand cmd = new SqlCommand("dbo.EventUpdateItem", conn))
                    {
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add(new SqlParameter("SSBERequestID", eventId));
                        cmd.Parameters.Add(new SqlParameter("Status", status));
                        cmd.Parameters.Add(new SqlParameter("Error_Reason_Cd", errorCode));

                        int result = cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error updating enterprise event " + eventId, LogCategories.ENTERPRISE_EVENT, ex);
            }
        }


        public static void Process(string connectionInfo, Entities.EnterpriseEvent item)
        {
            bool success = false;
            string errorMessage = string.Empty;
            
            try
            {
                Logger.Debug("Processing enterprise event " + item.SSBERequestID, LogCategories.ENTERPRISE_EVENT);
                var client = new EnterpriseEventClient();
                client.SendEvent(item);
                success = true;
            }
            catch (Exception ex)
            {
                CallerContext ctx = ContextManager.Instance.Get();
                if (ctx != null)
                {
                    Console.WriteLine("Request" + ctx.RequestXML);
                }

                Logger.Error("Error sending enterprise event" + item.SSBERequestID + " Request=" + ctx.RequestXML, LogCategories.ENTERPRISE_EVENT, ex);
                errorMessage = ex.Message + ": " + ex.StackTrace;
            }
            finally
            {                
                try
                {
                    char status =  success  ? 'S' : 'E';
                    Logger.Debug("Finished sending enterprise event" + item.SSBERequestID + " status=" + status, LogCategories.ENTERPRISE_EVENT);
                    if (errorMessage.Length > 255)
                    {
                        errorMessage = errorMessage.Substring(0, 254);
                    }
                    UpdateEvent(connectionInfo, item.SSBERequestID, status, errorMessage);
                }
                catch (Exception ex)
                {
                    Logger.Error("Error updating error status on enterprise event:" + item.SSBERequestID, LogCategories.ENTERPRISE_EVENT, ex);
                }             
            }

        }
    }
}
