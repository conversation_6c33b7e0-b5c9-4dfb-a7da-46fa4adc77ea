﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Threading.Tasks;

using Purolator.SmartSort.ChannelIntegration.EnterpriseEvent;

namespace Purolator.SmartSort.Windows.Services.EventSender
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        static void Main()
        {            
            ServiceBase[] ServicesToRun;
            ServicesToRun = new ServiceBase[] 
            { 
                new EventSender() 
            };
            ServiceBase.Run(ServicesToRun);         

            
            /*string ConnectionInfo = "Data Source= W-00821-D2C\\SmartSort,14330;Initial Catalog= SSBE;User ID= smartsortapp;Password=***********;Pooling=true;Max Pool Size=100;Min Pool Size=0;Connection Lifetime=60";
            EnterpriseEventClient client = new EnterpriseEventClient();
            var items = EventSenderImplementation.GetWorkItems(ConnectionInfo, 2);

            foreach (var item in items)
            {
                client.SendEvent(item);
            } 
            */
        }
    }
}
