﻿using System;
using System.Data;
using System.Configuration;
using System.Xml.Serialization;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Numerics;


namespace Purolator.SmartSort.Business.Entities
{
    [Serializable()]
    [XmlRoot("SAPMessage")]
    public class ShipmentEventStaging
    {
        [XmlElement("Message")]
        public ShipmentEventStagingMessage Message { get; set; }

        [XmlArray("PINServiceArray")]
        [XmlArrayItem(typeof(ShipmentService), ElementName = "PINService")]
        public List<ShipmentService> ShipmentServices { get; set; }
    }

    [Serializable()]
    public class ShipmentEventStagingMessage
    {
        [XmlElement("MessageGUID")] 
        public string MessageGUID { get; set; }

        [XmlElement("FWODocumentNo")]
        public long FWODocumentNo { get; set; }
       
        [XmlElement("EventExternalCode")]
        public string EventExternalCode { get; set; }

        [XmlElement("EventDateTimeTZ")]
        public string lastUpdatedTimeForXml // format: 2011-11-11T15:05:46.473+01:00
        {
            get { return EventDateTimeTZ.ToString("yyyy-MM-ddTHH:mm:ss.fffzzz"); }
            set { EventDateTimeTZ = DateTimeOffset.Parse(value); }
        }

        [XmlIgnore] 
        public DateTimeOffset EventDateTimeTZ { get; set; }
		
        [XmlElement("SenderCodeSet")]
        public string SenderCodeSet { get; set; }

        [XmlElement("SenderID")]
        public string SenderID { get; set; }

        [XmlElement("SenderName")]
        public string SenderName { get; set; }

        [XmlElement("FreightUnit")]
        public long FreightUnit { get; set; }
	
		[XmlElement("PiecePin")]
        public string PiecePin { get; set; }

		[XmlElement("ShipmentPin")]
        public string ShipmentPin { get; set; }

        [XmlElement("ServiceLevel")]
        public string ServiceLevel { get; set; }

        [XmlElement("TransportMode")]
        public string TransportMode { get; set; }

        [XmlElement("ServicePriority")]
        public string ServicePriority { get; set; }
        
        [XmlElement("PackageType")]
        public string PackageType { get; set; }

		[XmlElement("MaterialHandlingType")]
        public string MaterialHandlingType { get; set; }
        
        [XmlElement("ReturnType")]
        public string ReturnType { get; set; }
	
        [XmlElement("ProductNumber")]
        public string ProductNumber { get; set; }

        [XmlElement("ShipmentDate")]
        public string ShipmentDate { get; set; }

        [XmlElement("Weight")]
        public Decimal Weight { get; set; }

        [XmlElement("WeightUnit")]
        public string WeightUnit { get; set; }

        [XmlElement("OriginTerminal")]
        public string OriginTerminal { get; set; }

        [XmlElement("OriginSiteID")]
        public string OriginSiteID { get; set; }

        [XmlElement("DestinationTerminal")]
        public string DestinationTerminal { get; set; }

        [XmlElement("DestinationSiteID")]
        public string DestinationSiteID { get; set; }

        [XmlElement("ExpectedDeliveryDate")]
        public string ExpectedDeliveryDate { get; set; }

        [XmlElement("DeclareAddressLine1")]
        public string DeclareAddressLine1 { get; set; }
        
        [XmlElement("DeclareAddressLine2")]
        public string DeclareAddressLine2 { get; set; }

        [XmlElement("DeclareAddressLine3")]
        public string DeclareAddressLine3 { get; set; }

        [XmlElement("DeclareCompanyName")]
        public string DeclareCompanyName { get; set; }

        [XmlElement("DeclareReceiverAttnName")]
        public string DeclareReceiverAttnName { get; set; }

        [XmlElement("DeclareCity")]
        public string DeclareCity { get; set; }

        [XmlElement("DeclareProvince")]
        public string DeclareProvince { get; set; }
      
        [XmlElement("DeclarePostalCode")]
        public string DeclarePostalCode { get; set; }

        [XmlElement("DeclareCountry")]
        public string DeclareCountry { get; set; }

        [XmlElement("CurrentSuiteNum")]
        public string CurrentSuiteNum { get; set; }

        [XmlElement("CurrentStreetNum")]
        public string CurrentStreetNum { get; set; }

        [XmlElement("CurrentStreetNumSuf")]
        public string CurrentStreetNumSuf { get; set; }

        [XmlElement("CurrentStreetName")]
        public string CurrentStreetName { get; set; }

        [XmlElement("CurrentStreetType")]
        public string CurrentStreetType { get; set; }

        [XmlElement("CurrentStreetDir")]
        public string CurrentStreetDir { get; set; }

        [XmlElement("CurrentCity")]
        public string CurrentCity { get; set; }
				
        [XmlElement("CurrentProvince")]
        public string CurrentProvince { get; set; }
				
        [XmlElement("CurrentPostalCode")]
        public string CurrentPostalCode { get; set; }
				
        [XmlElement("CurrentCountry")]
        public string CurrentCountry { get; set; }
				
        [XmlElement("CurrentCompanyName")]
        public string CurrentCompanyName { get; set; }
				
        [XmlElement("CurrentAddressLine2")]
        public string CurrentAddressLine2 { get; set; }
				
        [XmlElement("CurrentAddressLine3")]
        public string CurrentAddressLine3 { get; set; }
				
        [XmlElement("AddressType")]
        public string AddressType { get; set; }
				
        [XmlElement("PurolatorAVInfo")]
        public string PurolatorAVInfo { get; set; }
				
        [XmlElement("PurolatorAVStatus")]
        public string PurolatorAVStatus { get; set; }				       
    }

    [Serializable()]
    public class ShipmentService
    {
        [XmlElement("ServiceScopeInd")]
        public string ServiceScopeInd { get; set; }

        [XmlElement("ServiceType")]
        public string ServiceType { get; set; }

        [XmlElement("ServiceTypeValue")]
        public string ServiceValue { get; set; }    
    }
}
