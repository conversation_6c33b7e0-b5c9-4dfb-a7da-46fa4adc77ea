﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;


namespace Purolator.SmartSort.Business.Entities
{
    [XmlRoot(ElementName = "Package")]
    public class CourierManifestPackage
    {
        [XmlElement("Pin")]
        public string PiecePin { get; set; }
        
        [XmlElement("CustomerName")]
        public string CustomerName { get; set; }
        
        [XmlElement("UnitNumber")]
        public string UnitNumber { get; set; }

        [XmlElement("PremiumService")]
        public string ServiceTime { get; set; }

        [XmlElement("DangerousGoods")]
        public string DangerousGoods { get; set; }
        
        [XmlElement("ChainOfSignature")]
        public string ChainOfSignature { get; set; }

        [XmlElement("HeavyWeight")]
        public string HeavyWeight { get; set; }
        
        [XmlElement("HoldForPickup")]
        public string HoldForPickup { get; set; }
        
        [XmlElement("Packagetype")]
        public string PackageType { get; set; }
        
        [XmlElement("AlternateAddressFlag")]
        public string AlternateAddressFlag { get; set; }
        
        [XmlElement("ShelfNumber")]
        public string ShelfNumber { get; set; }
        
        [XmlElement("PackageLocation")]
        public string PackageLocation { get; set; }
    }
}
