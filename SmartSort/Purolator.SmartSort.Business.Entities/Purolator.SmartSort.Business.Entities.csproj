﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{862103A3-8DAB-46EE-8890-2F192A71ED66}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Purolator.SmartSort.Business.Entities</RootNamespace>
    <AssemblyName>Purolator.SmartSort.Business.Entities</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>..\SmartSortRuleSet.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Caching">
      <HintPath>C:\Mangesh\TFS Data\SMARTSORTAPP-Dev-2015A\SmartSort\bin\Microsoft.Practices.EnterpriseLibrary.Caching.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Address.cs" />
    <Compile Include="AmazonShipment.cs" />
    <Compile Include="LookupWSLog.cs" />
    <Compile Include="OFDEvent.cs" />
    <Compile Include="SplitPlanPrintItem.cs" />
    <Compile Include="SplitPlanPrint.cs" />
    <Compile Include="RoutePlanPrint.cs" />
    <Compile Include="RoutePlanPrintItem.cs" />
    <Compile Include="RoutePrintAddress.cs" />
    <Compile Include="RoutePrintWorkItem.cs" />
    <Compile Include="SmartSortTimeZone.cs" />
    <Compile Include="RouteDetailsAddressValidation.cs" />
    <Compile Include="SmartSortLookupAddressValidation.cs" />
    <Compile Include="PrePrintDetailsUpdate.cs" />
    <Compile Include="SmartSortLookup.cs" />
    <Compile Include="SmartSortScan.cs" />
    <Compile Include="CourierManifest.cs" />
    <Compile Include="CourierManifestPackage.cs" />
    <Compile Include="CourierManifestRow.cs" />
    <Compile Include="EnterpriseEvent.cs" />
    <Compile Include="Enums.cs" />
    <Compile Include="LoadDeviationEvent.cs" />
    <Compile Include="PrePrintMaster.cs" />
    <Compile Include="PrePrintDetails.cs" />
    <Compile Include="RouteDetails.cs" />
    <Compile Include="ShipmentEventStaging.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Purolator.SmartSort.Business.Common\Purolator.SmartSort.Business.Common.csproj">
      <Project>{7b3557d3-dc6a-484d-bbf3-e48b5832208e}</Project>
      <Name>Purolator.SmartSort.Business.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Service.DataContracts\Purolator.SmartSort.Service.DataContracts.csproj">
      <Project>{1807d0b9-d240-4adc-a5b6-d8fa290f1171}</Project>
      <Name>Purolator.SmartSort.Service.DataContracts</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>