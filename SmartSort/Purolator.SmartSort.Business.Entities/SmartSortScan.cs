﻿using System;
using System.Data;
using System.Configuration;
using System.Xml.Serialization;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Numerics;


namespace Purolator.SmartSort.Business.Entities
{
    [Serializable()]
    [XmlRoot("SSLog")]
    public class SmartSortScan
    {
        //[XmlElement("Message")]
        public SmartSortScanLog SSLog { get; set; }
    }

    [Serializable()]
    [XmlRoot("SSLog")]
    public class SmartSortScanLog
    {
        [XmlElement("Terminal")]
        public String Terminal { get; set; }

        [XmlElement("PiecePin")]
        public String PiecePin { get; set; }

        [XmlElement("SmartSortDeviceID")]
        public String SmartSortDeviceID { get; set; }

        [XmlElement("UserLoginID")]
        public String UserLoginID { get; set; }

        [XmlElement("RoutePlanVersionID")]
        public String RoutePlanVersionID { get; set; }

        [XmlElement("ParkingPlanCodeID")]
        public String ParkingPlanCodeID { get; set; }

        [XmlElement("PrimarySort")]
        public String PrimarySort { get; set; }

        [XmlElement("BeltSide")]
        public String BeltSide { get; set; }

        [XmlElement("Route")]
        public String Route { get; set; }

        [XmlElement("SSStatusReason")]
        public String SSStatusReason { get; set; }

        [XmlElement("Shelf")]
        public String Shelf { get; set; }

        [XmlElement("DeliverySeqID")]
        public String DeliverySeqID { get; set; }

        [XmlElement("TruckShelfOverride")]
        public String TruckShelfOverride { get; set; }

        [XmlElement("SmartSortMode")]
        public String SmartSortMode { get; set; }

        [XmlElement("ScanDateTimeTZ")]
        public String ScanDateTimeTZ { get; set; }

        [XmlElement("PostalCode")]
        public String PostalCode { get; set; }

        [XmlElement("Province")]
        public String Province { get; set; }

        [XmlElement("City")]
        public String City { get; set; }

        [XmlElement("StreetNumber")]
        public String StreetNumber { get; set; }

        [XmlElement("StreetNumSuf")]
        public String StreetNumSuf { get; set; }

        [XmlElement("StreetName")]
        public String StreetName { get; set; }

        [XmlElement("StreetType")]
        public String StreetType { get; set; }

        [XmlElement("StreetDirection")]
        public String StreetDirection { get; set; }

        [XmlElement("UnitNumber")]
        public String UnitNumber { get; set; }

        [XmlElement("CustomerName")]
        public String CustomerName { get; set; }

        [XmlElement("BarcodeType")]
        public String BarcodeType { get; set; }

        [XmlElement("DeliveryTime")]
        public String DeliveryTime { get; set; }

        [XmlElement("DiversionCode")]
        public String DiversionCode { get; set; }

        [XmlElement("PackageType")]
        public String PackageType { get; set; }

        [XmlElement("PreprintID")]
        public String PreprintID { get; set; }

        [XmlElement("PrintDateTimeTZ")]
        public String PrintDateTimeTZ { get; set; }

        [XmlElement("ShipmentType")]
        public String ShipmentType { get; set; }

        [XmlElement("HandlingClassType")]
        public String HandlingClassType { get; set; }

        [XmlElement("DeliveryType")]
        public String DeliveryType { get; set; }

        [XmlElement("ResolvedBy")]
        public String ResolvedBy { get; set; }

        [XmlElement("AlternateAddressFlag")]
        public String AlternateAddressFlag { get; set; }


        public Boolean Status { get; set; }


        public String ErrorMessage { get; set; }

    }   


}
