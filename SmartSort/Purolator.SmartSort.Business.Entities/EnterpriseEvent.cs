﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Business.Entities
{
    [XmlRoot(ElementName = "EnterpriseEvent")]
    public class EnterpriseEvent
    {
        [XmlIgnore]
        public int SSBERequestID { get; set; }

        [XmlElement("EnterpriseEventID")]
        public int EnterpriseEventID { get; set; }

        [XmlElement("EventExternalCode")]
        public int EventExternalCode { get; set; }

        [XmlElement("Pin")]
        public string Pin { get; set; }

        [XmlElement("PiecePin")]
        public string PiecePin { get; set; }

        [XmlElement("SS")]
        public string SS { get; set; }

        [XmlElement("SSID")]
        public string SSID { get; set; }

        [XmlElement("Source")]
        public string Source { get; set; }

        [XmlElement("EventDateTimeTZ")]
        public string EventDateTimeTZ { get; set; }

        [XmlElement("TransmissionDate")]
        public string TransmissionDate { get; set; }
       
        [XmlElement("StatusReason")]
        public string StatusReason { get; set; }

        [XmlElement("Terminal")]
        public string Terminal { get; set; }

        [XmlElement("UnitNumber")]
        public string UnitNumber { get; set; }

        [XmlElement("StreetNumber")]
        public string StreetNumber { get; set; }

        [XmlElement("StreetName")]
        public string StreetName { get; set; }

        [XmlElement("StreetDirection")]
        public string StreetDirection { get; set; }

        [XmlElement("StreetNumSuf")]   // Requested by Hitesh & Company.
        public string StreetNumSuf { get; set; }

        [XmlElement("StreetType")]    // Requested by Hitesh & Company.
        public string StreetType { get; set; }

        [XmlElement("City")]
        public string City { get; set; }

        [XmlElement("PostalCode")]
        public string PostalCode { get; set; }

        [XmlElement("Province")]
        public string Province { get; set; }

        [XmlElement("Route")]
        public string Route { get; set; }

        [XmlElement("BarcodeType")]
        public string BarcodeType { get; set; }

        [XmlElement("RouteID")]
        public string RouteID { get; set; }

        [XmlElement("UserName")]
        public string UserName { get; set; }

        [XmlElement("CustomerName")]
        public string CustomerName { get; set; }

        [XmlElement("Shelf")]
        public string Shelf { get; set; }
    }
}
