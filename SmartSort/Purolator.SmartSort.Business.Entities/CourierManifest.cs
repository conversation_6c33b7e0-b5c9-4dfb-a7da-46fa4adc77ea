﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Business.Entities
{
    
    public class CourierManifest
    {
        [XmlElement("IsSuccess")]
        public bool IsSuccess { get; set; }

        [XmlElement("ErrorCode")]
        public string ErrorCode { get; set; }

        [XmlElement("ErrorMessage")]
        public string ErrorMessage { get; set; }

        [XmlElement("Terminal")]
        public string Terminal { get; set; }

        [XmlElement("Route")]
        public string Route { get; set; }

        [XmlArray("VehicleStops")]
        [XmlArrayItem(typeof(CourierManifestRow), ElementName = "VehicleStop")]
        public List<CourierManifestRow> ManifestRows { get; set; }        
    }

}
