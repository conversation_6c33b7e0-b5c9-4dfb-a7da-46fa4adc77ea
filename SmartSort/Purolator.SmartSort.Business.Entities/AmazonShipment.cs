﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Business.Entities
{
    [XmlRoot(ElementName = "AmazonShipments")]
    public class AmazonShipment
    {
        [XmlIgnore]
        public string FWODocumentNo { get; set; }

        [XmlElement("BillToAccountNumber")]
        public string BillToAccountNumber { get; set; }

        [XmlElement("Pickup_Company")]
        public string Pickup_Company { get; set; }

        [XmlElement("Pickup_Address1")]
        public string Pickup_Address1 { get; set; }

        [XmlElement("Pickup_Address2")]
        public string Pickup_Address2 { get; set; }

        [XmlElement("Pickup_Address3")]
        public string Pickup_Address3 { get; set; }

        [XmlElement("Pickup_City")]
        public string Pickup_City { get; set; }

        [XmlElement("Pickup_ProvState")]
        public string Pickup_ProvState { get; set; }

        [XmlElement("Pickup_PostalZip")]
        public string Pickup_PostalZip { get; set; }

        [XmlElement("Pickup_Country")]
        public string Pickup_Country { get; set; }

        [XmlElement("Delivery_Company")]
        public string Delivery_Company { get; set; }

        [XmlElement("Delivery_Address1")]
        public string Delivery_Address1 { get; set; }

        [XmlElement("Delivery_Address2")]
        public string Delivery_Address2 { get; set; }

        [XmlElement("Delivery_Address3")]
        public string Delivery_Address3 { get; set; }

        [XmlElement("Delivery_City")]
        public string Delivery_City { get; set; }

        [XmlElement("Delivery_ProvState")]
        public string Delivery_ProvState { get; set; }

        [XmlElement("Delivery_PostalZip")]
        public string Delivery_PostalZip { get; set; }

        [XmlElement("Delivery_Country")]
        public string Delivery_Country { get; set; }

        [XmlElement("Delivery_Phone")]
        public string Delivery_Phone { get; set; }

        [XmlElement("Delivery_Email")]
        public string Delivery_Email { get; set; }

        [XmlElement("ShipperReference")]
        public string ShipperReference { get; set; }

        [XmlElement("CarrierReferenceNumber")]
        public string CarrierReferenceNumber { get; set; }

        [XmlElement("Notes")]
        public string Notes { get; set; }

        [XmlElement("ServiceCode")]
        public string ServiceCode { get; set; }

        [XmlElement("RouteCode")]   
        public string RouteCode { get; set; }

        [XmlElement("Barcode")]
        public string Barcode { get; set; }

        [XmlElement("Piece")]
        public string Piece { get; set; }

        [XmlElement("UnitOfMeasure")]
        public string UnitOfMeasure { get; set; }

        [XmlElement("Weight")]
        public decimal Weight { get; set; }

        [XmlElement("Description")]
        public string Description { get; set; }
        
    }
}
