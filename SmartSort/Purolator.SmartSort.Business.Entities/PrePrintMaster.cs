﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Purolator.SmartSort.Business.Entities
{
    public class PrePrintMaster
    {
        private int _prePrintStgId;
        private string _destinationStreetNumber;
        private string _destinationStreetNumSuf;
        private string _destinationStreetName;
        private string _destinationUnitNumber;
        private string _destinationStreetType;
        private string _destinationStreetDirection;
        private string _destinationCity;
        private string _destinationProvince;
        private string _destinationPostalCode;
        private string _destinationCompanyName;
        private string _statusCd;

        public int PrePrintStgId
        {
            get { return _prePrintStgId; }
            set { _prePrintStgId = value; }
        }

        public string DestinationStreetNumber
        {
            get { return _destinationStreetNumber; }
            set { _destinationStreetNumber = value; }
        }

        public string DestinationStreetNumSuf
        {
            get { return _destinationStreetNumSuf; }
            set { _destinationStreetNumSuf = value; }
        }

        public string DestinationStreetName
        {
            get { return _destinationStreetName; }
            set { _destinationStreetName = value; }
        }

        public string DestinationUnitNumber
        {
            get { return _destinationUnitNumber; }
            set { _destinationUnitNumber = value; }
        }

        public string DestinationStreetType
        {
            get { return _destinationStreetType; }
            set { _destinationStreetType = value; }
        }

        public string DestinationStreetDirection
        {
            get { return _destinationStreetDirection; }
            set { _destinationStreetDirection = value; }
        }

        public string DestinationCity
        {
            get { return _destinationCity; }
            set { _destinationCity = value; }
        }

        public string DestinationProvince
        {
            get { return _destinationProvince; }
            set { _destinationProvince = value; }
        }

        public string DestinationPostalCode
        {
            get { return _destinationPostalCode; }
            set { _destinationPostalCode = value; }
        }

        public string DestinationCompanyName
        {
            get { return _destinationCompanyName; }
            set { _destinationCompanyName = value; }
        }

        public string StatusCd
        {
            get { return _statusCd; }
            set { _statusCd = value; }
        }
    }
}
