﻿using System;
using System.Data;
using System.Configuration;
using System.Xml.Serialization;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Numerics;

using Purolator.SmartSort.Service.DataContracts;

namespace Purolator.SmartSort.Business.Entities
{
 
    [Serializable()]
    [XmlRoot("PINLookup")]
    public class SmartSortLookupAddressValidation
    {
        [XmlElement("TerminalID")]
        public String TerminalID { get; set; }

        [XmlElement("PIN")]
        public String PIN { get; set; }

        [XmlElement("DeviceID")]
        public String DeviceID { get; set; }


        [XmlElement("CustomerName")]
        public String CustomerName { get; set; }

        [XmlElement("UnitSuiteNumber")]
        public String UnitSuiteNumber { get; set; }

        [XmlElement("StreetNumber")]
        public String StreetNumber { get; set; }

        [XmlElement("AddressLine1")]
        public String AddressLine1 { get; set; }

        [XmlElement("AddressLine2")]
        public String AddressLine2 { get; set; }

        [XmlElement("City")]
        public String City { get; set; }

        [XmlElement("Province")]
        public String Province { get; set; }

        [XmlElement("PostalCode")]
        public String PostalCode { get; set; }

        [XmlElement("DeliveryType")]        
        public DeliveryType DeliveryType { get; set; }
        
        
        [XmlElement("PremiumService")]
        public string PremiumService { get; set; }
        
        [XmlElement("ShipmentType")]
        public ShipmentType ShipmentType { get; set; }

        [XmlElement("DiversionCode")]
        public DiversionCode DiversionCode { get; set; }

        [XmlElement("HandlingClassType")]
        public string HandlingClassType { get; set; }

        [XmlElement("PurolatorAVInfo")]
        public String PurolatorAVInfo { get; set; }

        [XmlElement("PurolatorAVStatus")]
        public String PurolatorAVStatus { get; set; }
        
        [XmlElement("CorrectUnit")]
        public String CorrectUnit { get; set; }

        [XmlElement("CorrectStreetNum")]
        public String CorrectStreetNum { get; set; }

        [XmlElement("CorrectStreetNumSuffix")]
        public String CorrectStreetNumSuffix { get; set; }

        [XmlElement("CorrectStreetName")]
        public String CorrectStreetName { get; set; }

        [XmlElement("CorrectStreetType")]
        public String CorrectStreetType { get; set; }

        [XmlElement("CorrectStreetDir")]
        public String CorrectStreetDir { get; set; }

        [XmlElement("CorrectCity")]
        public String CorrectCity { get; set; }

        [XmlElement("CorrectPostalCode")]
        public String CorrectPostalCode { get; set; }

        [XmlElement("CorrectProvince")]
        public String CorrectProvince { get; set; }
        

        public String Status { get; set; }
        //public Boolean Status { get; set; }
        public String ErrorMessage { get; set; }

    }   


}
