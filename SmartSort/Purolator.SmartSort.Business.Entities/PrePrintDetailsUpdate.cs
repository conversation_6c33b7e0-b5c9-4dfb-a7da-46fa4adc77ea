﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.ComponentModel;


namespace Purolator.SmartSort.Business.Entities
{
    public enum PrePrintDetailsStatusEnum
    {
        [DescriptionAttribute("New")] New,
        [DescriptionAttribute("Running")] Running,
        [DescriptionAttribute("Error")] Error,
        [DescriptionAttribute("Complete")] Complete
    }

    public class PrePrintDetailsUpdate
    {
        private int _prePrintStgId;

        private PrePrintDetailsStatusEnum _prePrintDetailsStatusEnum = PrePrintDetailsStatusEnum.New;

        public int PrePrintStgId
        {
            get { return _prePrintStgId; }
            set { _prePrintStgId = value; }
        }

        public PrePrintDetailsStatusEnum Status
        {
            get { return _prePrintDetailsStatusEnum; }
            set { _prePrintDetailsStatusEnum = value; }
        }
        
    }
}
