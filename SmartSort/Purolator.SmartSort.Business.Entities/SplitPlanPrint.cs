﻿using System.Collections.Generic;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Business.Entities
{
    [XmlRoot("RoutePlan")]
    public class SplitPlanPrint
    {
        public string Terminal { get; set; }

        public string VersionNumber { get; set; }

        [XmlArray("Pudros")]
        [XmlArrayItem(typeof(SplitPlanPrintItem), ElementName = "PudroData")]
        public List<SplitPlanPrintItem> Pudros { get; set; }
    }
}