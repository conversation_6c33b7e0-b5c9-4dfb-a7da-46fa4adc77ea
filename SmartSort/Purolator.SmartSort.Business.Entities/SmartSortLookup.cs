﻿using System;
using System.Data;
using System.Configuration;
using System.Xml.Serialization;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Numerics;
using Purolator.SmartSort.Service.DataContracts;


namespace Purolator.SmartSort.Business.Entities
{
  
    [Serializable()]
    [XmlRoot("PINLookup")]
    public class SmartSortLookup
    {
        [XmlElement("TerminalID")]
        public String TerminalID { get; set; }

        [XmlElement("PIN")]
        public String PIN { get; set; }

        [XmlElement("CustomerName")]
        public String CustomerName { get; set; }

        [XmlElement("UnitSuiteNumber")]
        public String UnitSuiteNumber { get; set; }

        [XmlElement("StreetNumber")]
        public String StreetNumber { get; set; }

        [XmlElement("AddressLine1")]
        public String AddressLine1 { get; set; }

        [XmlElement("AddressLine2")]
        public String AddressLine2 { get; set; }

        [XmlElement("City")]
        public String City { get; set; }

        [XmlElement("Province")]
        public String Province { get; set; }

        [XmlElement("PostalCode")]
        public String PostalCode { get; set; }

        [XmlElement("DeliveryType")]
        public String DeliveryType { get; set; }
     
        public String Status { get; set; }
        //public Boolean Status { get; set; }
        public String ErrorMessage { get; set; }

    }   


}
