﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Business.Entities
{
    [XmlRoot(ElementName = "ManifestRow")]
    public class CourierManifestRow
    {

        [XmlElement("Stopid")]
        public int Stopid { get; set; }

        [XmlElement("StreetNumber")]
        public string StreetNumber { get; set; }

        [XmlElement("StreetNumberSuffix")]
        public string StreetNumberSuffix { get; set; }

        [XmlElement("StreetName")]
        public string StreetName { get; set; }

        [XmlElement("StreetType")]
        public string StreetType { get; set; }

        [XmlElement("StreetDirection")]
        public string StreetDirection { get; set; }

        [XmlElement("City")]
        public string City { get; set; }

        [XmlElement("PostalCode")]
        public string PostalCode { get; set; }

        [XmlElement("DeliverySequenceId")]
        public int DeliverySeqId { get; set; }

        [XmlArray("Packages")]
        [XmlArrayItem(typeof(CourierManifestPackage), ElementName = "Package")]
        public List<CourierManifestPackage> Packages { get; set; }
    }
}
