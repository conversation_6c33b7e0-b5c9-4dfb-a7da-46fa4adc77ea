﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Business.Entities
{
    public class Address
    {
        private string customerName;
        private string streetName;
        private string suite;
        private string floor;
        private string streetNumber;
        private string streetNumberSuffix;
        private string streetDirection;
        private string streetType;
        private string streetAddress2;
        private string streetAddress3;
        private string postalCode;
        private string city;
        private string provinceCode;

        private int addressSequenceCode;
        
        public string CustomerName
        {
            get { return customerName; }
            set { customerName = value; }
        }

        public string StreetName
        {
            get { return streetName; }
            set { streetName = value; }
        }

        public string Suite
        {
            get { return suite; }
            set { suite = value; }
        }

        public string Floor
        {
            get { return floor; }
            set { floor = value; }
        }

        public string StreetNumber
        {
            get { return streetNumber; }
            set { streetNumber = value; }
        }

        public string StreetNumberSuffix
        {
            get { return streetNumberSuffix; }
            set { streetNumberSuffix = value; }
        }

        public string StreetDirection
        {
            get { return streetDirection; }
            set { streetDirection = value; }
        }

        public string StreetType
        {
            get { return streetType; }
            set { streetType = value; }
        }

        public string PostalCode
        {
            get { return postalCode; }
            set { postalCode = value; }
        }

        public string StreetAddress2
        {
            get { return streetAddress2 ; }
            set { streetAddress2 = value; }
        }

        public string StreetAddress3
        {
            get { return streetAddress3; }
            set { streetAddress3 = value; }
        }

        public string City
        {
            get { return city; }
            set { city = value; }
        }

        public string ProvinceCode
        {
            get { return provinceCode; }
            set { provinceCode = value; }
        }

        public int AddressSequenceCode
        {
            get { return addressSequenceCode; }
            set { addressSequenceCode = value; }
        }

    }
}
