﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Business.Entities
{
    [XmlRoot(ElementName = "OFDEvent")]
    public class OFDEvent
    {
        [XmlElement("OFDQueueID")]
        public int OFDQueueID { get; set; }

        [XmlElement("PiecePin")]
        public string PiecePin { get; set; }

        [XmlElement("Terminal")]
        public string Terminal { get; set; }

        [XmlElement("Route")]
        public string Route { get; set; }

        [XmlElement("EventDateTime")]
        public string EventDateTime { get; set; }
    }
}
