﻿using System.Collections.Generic;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Business.Entities
{
    [XmlRoot("RoutePlan")]
    public class RoutePlanPrint
    {
        public string Terminal { get; set; }

        public string VersionNumber { get; set; }

        [XmlArray("Routes")]
        [XmlArrayItem(typeof(RoutePlanPrintItem), ElementName = "RouteData")]
        public List<RoutePlanPrintItem> Routes { get; set; }
    }
}