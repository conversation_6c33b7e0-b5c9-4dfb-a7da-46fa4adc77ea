﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Business.Entities
{
    public class LoadDeviationEvent
    {
        public Enums.RepositioningAction Action { get; set; }
        public string Terminal { get; set; }
        public string DeviceID { get; set; }
        public string PIN { get; set; }
        public string Route { get; set; }
        public string Shelf { get; set; }
        public DateTimeOffset ScanDateTime { get; set; }        
    }
}
