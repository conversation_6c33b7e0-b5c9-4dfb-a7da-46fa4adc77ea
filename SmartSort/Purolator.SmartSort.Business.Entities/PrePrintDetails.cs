﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;


namespace Purolator.SmartSort.Business.Entities
{
    [DataContract()]
    public class PrePrintDetails
    {
        [DataMember(Name = "PreprintStgID")]
        public int PrePrintStgId { get; set; }

        [DataMember(Name = "PreprintOrderID")]
        public int PreprintOrderID { get; set; }

        [DataMember(Name = "NumberOrdered")]
        public int NumberOrdered { get; set; }

        [DataMember(Name = "StartPin")]
        public string StartPin { get; set; }

        [DataMember(Name = "EndPin")]
        public string EndPin { get; set; }

        [DataMember(Name = "TransportMode")]
        public string TransportMode { get; set; }

        [DataMember(Name = "ServiceTime")]
        public string ServiceTime { get; set; }

        [DataMember(Name = "PackageType")]
        public string PackageType { get; set; }

        [DataMember(Name = "DangerousGoodsClass")]
        public string DangerousGoodsClass { get; set; }

        [DataMember(Name = "CustomerName")]
        public string CustomerName { get; set; }

        [DataMember(Name = "ReceiverAddress")]
        public string ReceiverAddress { get; set; }

        [DataMember(Name = "ReceiverUnitNumber")]
        public string ReceiverUnitNumber { get; set; }

        [field: NonSerializedAttribute()]
        public string Line1 { get; set; }

        [field: NonSerializedAttribute()]
        public string Line2 { get; set; }

        [DataMember(Name = "City")]
        public string City { get; set; }

        [DataMember(Name = "Province")]
        public string Province { get; set; }

        [DataMember(Name = "PostalCode")]
        public string PostalCode { get; set; }

        [DataMember(Name = "Country")]
        public string Country { get; set; }

        [DataMember(Name = "CreateDateTime")]
        public DateTime CreateDateTime { get; set; }

        [DataMember(Name = "ProcessedFlag")]
        public string ProcessedFlag { get; set; }

        [DataMember(Name = "ConsumptionCounter")]
        public int ConsumptionCounter { get; set; }

        [DataMember(Name = "AWSStreetNumber")]
        public string AWSStreetNumber { get; set; }

        [DataMember(Name = "AWSStreetNumSuf")]
        public string AWSStreetNumSuf { get; set; }

        [DataMember(Name = "AWSStreetName")]
        public string AWSStreetName { get; set; }

        [DataMember(Name = "AWSUnitNumber")]
        public string AWSUnitNumber { get; set; }

        [DataMember(Name = "AWSStreetType")]
        public string AWSStreetType { get; set; }

        [DataMember(Name = "AWSStreetDirection")]
        public string AWSStreetDirection { get; set; }

        [DataMember(Name = "AWSCity")]
        public string AWSCity { get; set; }

        [DataMember(Name = "AWSProvince")]
        public string AWSProvince { get; set; }

        [DataMember(Name = "AWSPostalCode")]
        public string AWSPostalCode { get; set; }

        [DataMember(Name = "AWSCompanyName")]
        public string AWSCompanyName { get; set; }

        [DataMember(Name = "DestinationTerminal")]
        public string DestinationTerminal { get; set; }

        [DataMember(Name = "PurolatorAVStatus")]
        public string PurolatorAVStatus { get; set; }

        [field: NonSerializedAttribute()]
        public bool AVSFinished { get; set; }
    }
}
