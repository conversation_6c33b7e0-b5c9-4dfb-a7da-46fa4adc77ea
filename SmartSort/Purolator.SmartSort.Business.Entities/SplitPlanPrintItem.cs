﻿using System;
using System.Collections.Generic;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Business.Entities
{
    public class SplitPlanPrintItem
    {
        public string Pudro { get; set; }

        [XmlArray("FS")]
        [XmlArrayItem(typeof(string), ElementName = "F")]
        public List<string> FSA { get; set; }

        [XmlArray("PS")]
        [XmlArrayItem(typeof(string), ElementName = "P")]
        public List<string> PostalCodes { get; set; }

        [XmlArray("CS")]
        [XmlArrayItem(typeof(string), ElementName = "C")]
        public List<string> Cities { get; set; }
    }
}
