﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Business.Entities
{
    public class RoutePrintWorkItem
    {
        // used to reset the queue
        public string ServerName { get; set; }

        public int RequestID {get; set;}

        public int RoutePlanVersionID {get; set;}
  
        public string RoutePlanPDF {get; set;}

        public string SplitPlanPDF { get; set; }

        public string Status { get; set; }

    }
}
