﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Purolator.SmartSort.Business.Entities
{    
    public class RouteDetailsAddressValidation
    {
        private String _route;
        private String _shelf;
        private String _truckShelfOverride;
        private String _deliverySeqID;
        private String _primarySort;
        private String _beltSide;
        private String _status;

        public String Route
        {
            get { return _route; }
            set { _route = value; }
        }

        public String Shelf
        {
            get { return _shelf; }
            set { _shelf = value; }
        }

        public String TruckShelfOverride
        {
            get { return _truckShelfOverride; }
            set { _truckShelfOverride = value; }
        }

        public String DeliverySeqID
        {
            get { return _deliverySeqID; }
            set { _deliverySeqID = value; }
        }

        public String PrimarySort
        {
            get { return _primarySort; }
            set { _primarySort = value; }
        }

        public String BeltSide
        {
            get { return _beltSide; }
            set { _beltSide = value; }
        }


        public String Status
        {
            get { return _status; }
            set { _status = value; }
        }    

        /*    public int OriginalShelf
        {
            get { return _originalShelf; }
            set { _originalShelf = value; }
        }
        
        public int OriginalTruckShelfOverride
        {
            get { return _originalTruckShelfOverride; }
            set { _originalTruckShelfOverride = value; }
        }

        public int DeliverySeqID
        {
            get { return _deliverySeqID; }
            set { _deliverySeqID = value; }
        }
        
        public int PrimarySort
        {
            get { return _primarySort; }
            set { _primarySort = value; }
        }

        public int BeltSide
        {
            get { return _beltSide; }
            set { _beltSide = value; }
        }            
         */
    }
}
