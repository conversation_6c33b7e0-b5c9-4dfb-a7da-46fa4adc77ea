﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using Microsoft.Practices.EnterpriseLibrary.Caching;
using Purolator.SmartSort.Business.Common;

namespace Purolator.SmartSort.Business.Entities
{
    public sealed class FunctionControlManager
    {
        public static readonly FunctionControlManager Instance = new FunctionControlManager();

        private FunctionControlFactory factory = null;

        private List<FunctionControl> functionControls;

        private ICacheManager cacheManager = CacheFactory.GetCacheManager("FunctionControlManager Cache Manager");
        private const string CACHE_KEY = "FunctionControls";
        private const string NULL_STRING = "NULL";

        private FunctionControlManager()
        {
            // Use the property to load the assembly into the current 
            // application domain.
            //Assembly assembly = Assembly.Load(Properties.Settings.Default.FunctionFactoryAssembly);

            //// Get the type to create
            //Type factoryType = assembly.GetType(Properties.Settings.Default.FunctionFactoryType);

            //factory = (FunctionControlFactory)Activator.CreateInstance(factoryType);
        }

        internal void LoadFunctionControls()
        {
            Hashtable myHashTable = new Hashtable();
            functionControls = factory.GetFunctionControlsFromRepository();
            foreach (FunctionControl control in functionControls)
            {
                myHashTable.Add(MakeHashKey(control), control);
            }

            //cacheManager.Add(CACHE_KEY, myHashTable, CacheItemPriority.Normal, new FunctionControlCacheRefreshAction(), new TimedFileDependency(ConfigurationManager.AppSettings[GlobalConstants.CACHE_DEPENDENCY_FILE].ToString()));
        }

        private string MakeHashKey(FunctionControl myControl)
        {
            return MakeHashKey(myControl.FunctionCode, myControl.UserType, myControl.WorkgroupId, myControl.UserRole);
        }

        private string MakeHashKey(string functionCode, string userType, int? workGroupId, string userRole)
        {
            string key = functionCode;
            key += "_" + (string.IsNullOrEmpty(userType) ? NULL_STRING : userType.ToUpper());
            key += "_" + ((workGroupId.HasValue && workGroupId.Value != 0) ? workGroupId.Value.ToString() : NULL_STRING);
            key += "_" + (string.IsNullOrEmpty(userRole) ? NULL_STRING : userRole.ToUpper());
            return key;
        }

        public List<FunctionControl> FunctionControls
        {
            get
            {
                if (functionControls == null || functionControls.Count == 0)
                    LoadFunctionControls();

                return functionControls;
            }
        }

        public FunctionControl GetFunctionControl(FunctionEnum functionEnum, string userType, int? workGroupId, string userRole)
        {
            FunctionControl retVal = null;
            string key = string.Empty;

            string functionCode = FunctionEnumToString(functionEnum);

            if (string.IsNullOrEmpty(functionCode))
            {
                //This should not happen
            }
            else
            {
                Hashtable myHashTable = null;
                if (!cacheManager.Contains(CACHE_KEY))
                {
                    LoadFunctionControls();
                    myHashTable = (Hashtable)cacheManager.GetData(CACHE_KEY);
                }
                else
                {
                    myHashTable = (Hashtable)cacheManager.GetData(CACHE_KEY);
                    if (myHashTable == null)
                    {
                        LoadFunctionControls();
                        myHashTable = (Hashtable)cacheManager.GetData(CACHE_KEY);
                    }
                }

                if (myHashTable != null)
                {

                    if (retVal == null && !string.IsNullOrEmpty(userRole) && workGroupId.HasValue)
                    {
                        key = MakeHashKey(functionCode, userType, workGroupId.Value, userRole);
                        if (myHashTable.ContainsKey(key))
                        {
                            retVal = (FunctionControl)myHashTable[key];
                        }
                    }

                    if (retVal == null && workGroupId.HasValue)
                    {
                        key = MakeHashKey(functionCode, userType, workGroupId.Value, null);
                        if (myHashTable.ContainsKey(key))
                        {
                            retVal = (FunctionControl)myHashTable[key];
                        }
                    }

                    if (retVal == null && !string.IsNullOrEmpty(userRole))
                    {
                        key = MakeHashKey(functionCode, userType, null, userRole);
                        if (myHashTable.ContainsKey(key))
                        {
                            retVal = (FunctionControl)myHashTable[key];
                        }
                    }

                    if (retVal == null && userType != null)
                    {
                        key = MakeHashKey(functionCode, userType, null, null);
                        if (myHashTable.ContainsKey(key))
                        {
                            retVal = (FunctionControl)myHashTable[key];
                        }
                    }

                    if (retVal == null && functionCode != null)
                    {
                        key = MakeHashKey(functionCode, null, null, null);
                        if (myHashTable.ContainsKey(key))
                        {
                            retVal = (FunctionControl)myHashTable[key];
                        }
                    }
                }
            }

            if (retVal == null)
            {
                string messageString = "FunctionControlManager.GetFunctionControl(): Function Not Found ";
                messageString += "Key: " + key;
                messageString += "Function: " + functionCode;
                messageString += "User Type: " + (string.IsNullOrEmpty(userType) ? NULL_STRING : userType.ToUpper());
                messageString += "Workgroup ID: " + ((workGroupId.HasValue && workGroupId.Value != 0) ? workGroupId.Value.ToString() : NULL_STRING);
                messageString += "User Role: " + (string.IsNullOrEmpty(userRole) ? NULL_STRING : userRole.ToUpper());
                Exception ex = new Exception(messageString);
                throw ex;
            }

            return retVal;
        }

        private static string FunctionEnumToString(FunctionEnum functionEnum)
        {
            return EnumUtils.stringValueOf(functionEnum);
        }
    }

    [Serializable]
    internal class FunctionControlCacheRefreshAction : ICacheItemRefreshAction
    {
        public void Refresh(string key, object expiredValue, CacheItemRemovedReason removalReason)
        {
            FunctionControlManager.Instance.LoadFunctionControls();
        }
    }
}
