﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;
using Purolator.SmartSort.Service.MessageContracts;
using System.ServiceModel.Activation;
using System.ServiceModel.Web;

namespace Purolator.SmartSort.Service.Implementation.SmartSortScan
{
    // NOTE: You can use the "Rename" command on the "Refactor" menu to change the interface name "ISmartSortScanService" in both code and config file together.    
    [ServiceContract]
    public interface ISmartSortScanService
    {

         
        [OperationContract]
        SmartSortScanResponse SmartSortScan(String scans);

        [OperationContract]
        bool IsServiceUp();

        [OperationContract]
        void flip(bool isRunning);
    }
}

