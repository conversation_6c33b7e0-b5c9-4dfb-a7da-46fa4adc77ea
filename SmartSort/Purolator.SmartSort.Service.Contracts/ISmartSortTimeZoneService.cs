﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.ServiceModel.Web;
using System.Text;
using Purolator.SmartSort.Service.MessageContracts;

namespace Purolator.SmartSort.Service.Implementation.SmartSortTimeZone
{
    // NOTE: You can use the "Rename" command on the "Refactor" menu to change the interface name "ISmartSortLookupService" in both code and config file together.
    [ServiceContract]
    public interface ISmartSortTimeZoneService
    {       
        [OperationContract]
        SmartSortGetTimeZoneSyncResponse GetTimeZoneSync(String terminal);        
    }
}
