﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using Purolator.SmartSort.Service.Contracts;
using Purolator.SmartSort.Service.MessageContracts;

namespace Purolator.SmartSort.Service.Contracts
{    
    [ServiceContract(Namespace = "http://www.purolator.com/smartsort")]
    public interface IShipmentEventService
    {
        [OperationContract]        
        ShipmentEventResponse LoadShipmentEvent(ShipmentEventRequest loadShipmentEventRequest);        
    }
}
