﻿using Purolator.SmartSort.Service.MessageContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;

namespace Purolator.SmartSort.Service.Contracts
{
    [ServiceContract(Namespace="http://www.purolator.com/smartsort")]
    
    public interface ICourierManifestService
    {
        /// <summary>
        /// Called at the time of Unloading the truck.
        /// </summary>
        /// <param name="unloadFromTruckRequest"></param>
        /// <returns></returns>
        [OperationContract]
        UnloadFromTruckResponse UnloadFromTruck(UnloadFromTruckRequest unloadFromTruckRequest);

        /// <summary>
        /// Called in case of reposition of the packages within truck.
        /// </summary>
        /// <param name="courierManifestRequest"></param>
        /// <returns></returns>
        [OperationContract]
        RepositionPackageResponse RepositionPackage(RepositionPackageRequest repositionPackageRequest);

        /// <summary>
        /// Called at the time of truck closing.
        /// </summary>
        /// <param name="closeTruckAndManifestedRequest"></param>
        /// <returns></returns>
        [OperationContract]
        CloseTruckResponse CloseTruck(CloseTruckRequest closeTruckRequest);

        /// <summary>
        /// Called when the package is couriered.
        /// </summary>
        /// <param name="closeTruckAndManifestedRequest"></param>
        /// <returns></returns>
        [OperationContract]
        CourierManifestResponse GetCourierManifest(CourierManifestRequest courierManifestRequest);

        [OperationContract]
        IsServiceUpResponse IsServiceUp();
    }
}
