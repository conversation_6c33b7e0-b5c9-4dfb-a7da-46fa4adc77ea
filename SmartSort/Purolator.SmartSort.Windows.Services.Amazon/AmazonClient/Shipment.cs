﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Windows.Services.Amazon
{
    class Shipment
    {        
        public string BillToAccountNumber {get; set;}
        public string Pickup_Company {get; set;}
        public string Pickup_Address1 {get; set;}
        public string Pickup_Address2 {get; set;}
        public string Pickup_Address3 {get; set;}
        public string Pickup_City {get; set;}
        public string Pickup_ProvState {get; set;}
        public string Pickup_PostalZip {get; set;}
        public string Pickup_Country {get; set;}
        public string Delivery_Company {get; set;}
        public string Delivery_Address1 {get; set;}
        public string Delivery_Address2 {get; set;}
        public string Delivery_Address3 {get; set;}
        public string Delivery_City {get; set;}
        public string Delivery_ProvState {get; set;} 
        public string Delivery_PostalZip {get; set;}
        public string Delivery_Country {get; set;}
        public string Delivery_Phone {get; set;}
        public string Delivery_Email {get; set;}
        public string ShipperReference {get; set;}
        public string CarrierReferenceNumber {get; set;}
        public string Notes {get; set;}
        public string ServiceCode {get; set;}
        public string RouteCode {get; set;}
        public ShipmentItem[] Items { get; set; }
    }
}
