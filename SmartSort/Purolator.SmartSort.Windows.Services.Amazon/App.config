﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="Purolator.SmartSort.Windows.Services.Amazon.AmazonSenderSettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
    </startup>
    <applicationSettings>
        <Purolator.SmartSort.Windows.Services.Amazon.AmazonSenderSettings>
            <setting name="FetchAmount" serializeAs="String">
                <value>100</value>
            </setting>
            <setting name="ProcessPeriodInSeconds" serializeAs="String">
                <value>30</value>
            </setting>
            <setting name="ConcurrentWorkers" serializeAs="String">
                <value>2</value>
            </setting>
            <setting name="URL" serializeAs="String">
                <value>https://qa.shiptrackapi.com</value>
            </setting>
        </Purolator.SmartSort.Windows.Services.Amazon.AmazonSenderSettings>
    </applicationSettings>
</configuration>