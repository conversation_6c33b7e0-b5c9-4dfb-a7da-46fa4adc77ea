﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.28307.572
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Source", "Source", "{27010071-73AD-4845-BF8A-56F8021998CB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{F986098F-B301-45B1-A9AC-6651A930B9C3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Deployment", "Deployment", "{AF283D3B-5A0E-4633-8132-D614E72F5D2E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Unit Test", "Unit Test", "{8BF20E42-C07E-45C4-B9ED-DA326B80355E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Executables", "Executables", "{2625E5BB-DDEC-4527-8F53-B0A9980D4503}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Business", "Business", "{7B3B279A-15EC-4FAC-99CC-74BCA120AAC4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Business Component", "Business Component", "{7AF1BF09-A112-4D13-92C6-B8103C869B4A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Business Entities", "Business Entities", "{04C25799-3835-4EC7-9772-6339E7E8ED39}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{0FB37A87-6537-4643-9A44-EB5F1CFF4992}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Data Access Layer", "Data Access Layer", "{5A6E9CBD-A35B-45EB-9C7B-998D36AA5DC3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service", "Service", "{57FC91C1-9E63-4A9C-AF33-AFB0D5452A1E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service Contracts", "Service Contracts", "{065587D7-513E-465D-9737-070492D9A072}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service Implementation", "Service Implementation", "{24252581-ACC5-472A-B730-ABFE8E3AFAD2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.Implementation.CourierManifest", "Purolator.SmartSort.Service.Implementation.CourierManifest\Purolator.SmartSort.Service.Implementation.CourierManifest.csproj", "{759632CE-7537-4246-B891-158142E736B1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Windows Services", "Windows Services", "{8942518B-37CA-4E44-81D3-03A5022BCC4E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Business.Entities", "Purolator.SmartSort.Business.Entities\Purolator.SmartSort.Business.Entities.csproj", "{862103A3-8DAB-46EE-8890-2F192A71ED66}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Business.Common", "Purolator.SmartSort.Business.Common\Purolator.SmartSort.Business.Common.csproj", "{7B3557D3-DC6A-484D-BBF3-E48B5832208E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.Contracts.Common", "Purolator.SmartSort.Service.Contracts.Common\Purolator.SmartSort.Service.Contracts.Common.csproj", "{60DB29E4-EEEB-4208-BB85-89C23BAC6A28}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Business.Components.Common", "Purolator.SmartSort.Business.Components.Common\Purolator.SmartSort.Business.Components.Common.csproj", "{CBDACD7A-4123-4188-AA08-A77CDAC1702F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Data.Access", "Purolator.SmartSort.Data.Access\Purolator.SmartSort.Data.Access.csproj", "{1FD9CDB9-E4B1-4AA5-AB52-2D3C6E03262D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{245429A6-B9DA-4957-9FBA-ED8F3507F9C0}"
	ProjectSection(SolutionItems) = preProject
		SmartSortRuleSet.ruleset = SmartSortRuleSet.ruleset
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.Contracts", "Purolator.SmartSort.Service.Contracts\Purolator.SmartSort.Service.Contracts.csproj", "{1AC49B5C-7F91-4E55-9701-68AB8AD35170}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Message Contracts", "Message Contracts", "{70DFD7D2-A8C6-48D3-959C-E90A99D60406}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.MessageContracts", "Purolator.Smartsort.Service.MessageContracts\Purolator.SmartSort.Service.MessageContracts.csproj", "{C89AFF74-2601-488A-AF90-B6B890DE9B8F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Data Contracts", "Data Contracts", "{4F07F4DC-B874-4FD7-8F41-8C9C65F8A746}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.DataContracts", "Purolator.SmartSort.Service.DataContracts\Purolator.SmartSort.Service.DataContracts.csproj", "{1807D0B9-D240-4ADC-A5B6-D8FA290F1171}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Enterprise Library", "Enterprise Library", "{2A2967CD-42D6-484B-9084-8381ED8FC64F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Interfaces", "Interfaces", "{29319D7E-C62E-4708-A52E-12D58D1E3F19}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Common", "Purolator.SmartSort.Common\Purolator.SmartSort.Common.csproj", "{93213BC1-5510-457F-B09B-94ACB33A232F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.Implementation.ShipmentEvent", "Purolator.SmartSort.Service.Implementation.ShipmentEvent\Purolator.SmartSort.Service.Implementation.ShipmentEvent.csproj", "{CD36F7DE-A423-4A5E-BD45-113D91B28911}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service Common", "Service Common", "{332C7795-A16A-49DB-BE91-4A1B6FBF0E8B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.Common", "Purolator.SmartSort.Service.Common\Purolator.SmartSort.Service.Common.csproj", "{57588481-3BFA-4A35-A1AB-09E202AB6DA6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebServices", "WebServices\WebServices.csproj", "{361C1D76-1D64-4486-AE6B-A1976AD0FA34}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WCFInterceptor", "WCFInterceptor", "{BBFC7A28-0C1C-4A5F-811B-FB80F192F7D1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.WSDLInterceptor", "Purolator.SmartSort.Service.WSDLInterceptor\Purolator.SmartSort.Service.WSDLInterceptor.csproj", "{72450B38-A6B8-41F9-BA92-FAFB870CF713}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Translators", "Translators", "{E6EE8A66-888F-498B-B6CB-AA50FD231BEA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.Translators", "Purolator.SmartSort.Service.Translators\Purolator.SmartSort.Service.Translators.csproj", "{0D2FCB2C-E293-4DEB-9C88-F2DD6F850B2A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Serializer", "Purolator.SmartSort.Serializer\Purolator.SmartSort.Serializer.csproj", "{96339C2A-F60D-4599-B2C2-BBA5813245E9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Channel Integration", "Channel Integration", "{A964C298-A340-4CF2-94D3-86A8279FB616}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.ChannelIntegration.Clients.AWS", "Purolator.SmartSort.ChannelIntegration.Clients.AWS\Purolator.SmartSort.ChannelIntegration.Clients.AWS.csproj", "{37718613-EC5C-4C0C-BC48-7626506BA000}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestSmartSortScanService", "TestSmartSortScanService\TestSmartSortScanService.csproj", "{9EBA319A-038D-4E2B-B838-D4D8704616C5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestLoadDeviation", "TestLoadDeviation\TestLoadDeviation.csproj", "{CB3E550F-1FBB-448A-8BE2-EED8FB5A2A1F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Windows.Services.AddressValidationService", "Purolator.SmartSort.Windows.Services.AddressValidationService\Purolator.SmartSort.Windows.Services.AddressValidationService.csproj", "{A4D64B2D-D8A0-4E24-928F-737DDA55D99F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Resources", "Purolator.SmartSort.Resources\Purolator.SmartSort.Resources.csproj", "{9952D96B-94CA-4A86-9742-ACB897367A31}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EventLogConfigurator", "EventLogConfigurator\EventLogConfigurator.csproj", "{C3E1F3FF-2B3B-4F12-9562-6CA67347A516}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Configuration", "Configuration", "{8F92A12F-0A56-442A-BC92-E02A8C786D1C}"
	ProjectSection(SolutionItems) = preProject
		Deployment\configuration\development.py = Deployment\configuration\development.py
		Deployment\configuration\integration.py = Deployment\configuration\integration.py
		Deployment\configuration\local.py = Deployment\configuration\local.py
		Deployment\configuration\production.py = Deployment\configuration\production.py
		Deployment\configuration\staging.py = Deployment\configuration\staging.py
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Templates", "Templates", "{4A6DAC53-62A4-40F8-B049-2C8C7E2BBF16}"
	ProjectSection(SolutionItems) = preProject
		Deployment\configuration\templates\AWSMunicipalityLoader.exe.config = Deployment\configuration\templates\AWSMunicipalityLoader.exe.config
		Deployment\configuration\templates\Purolator.SmartSort.Windows.Service.RoutePrinter.exe.config = Deployment\configuration\templates\Purolator.SmartSort.Windows.Service.RoutePrinter.exe.config
		Deployment\configuration\templates\Purolator.SmartSort.Windows.Services.AddressValidationService.exe.config = Deployment\configuration\templates\Purolator.SmartSort.Windows.Services.AddressValidationService.exe.config
		Deployment\configuration\templates\Purolator.SmartSort.Windows.Services.Amazon.exe.config = Deployment\configuration\templates\Purolator.SmartSort.Windows.Services.Amazon.exe.config
		Deployment\configuration\templates\Purolator.SmartSort.Windows.Services.EventSender.exe.config = Deployment\configuration\templates\Purolator.SmartSort.Windows.Services.EventSender.exe.config
		Deployment\configuration\templates\Purolator.SmartSort.Windows.Services.OFDSender.exe.config = Deployment\configuration\templates\Purolator.SmartSort.Windows.Services.OFDSender.exe.config
		Deployment\configuration\templates\web.config = Deployment\configuration\templates\web.config
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.Implementation.SmartSortScan", "Purolator.SmartSort.Service.Implementation.SmartSortScan\Purolator.SmartSort.Service.Implementation.SmartSortScan.csproj", "{BB815650-BE30-45D3-8EE8-61F94996D60D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Validation", "Validation", "{C102B11F-A557-4BB2-B6B1-37BFDA2752C4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DevTrends.WCFDataAnnotations", "DevTrends.WCFDataAnnotations\DevTrends.WCFDataAnnotations.csproj", "{5BD4EAA0-4782-4E75-AD6C-5753BA2D0A91}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Validation", "Validation", "{DAD7E7E9-993B-4068-9F78-504866B0CF3F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DevTrends.WCFDataAnnotations.UnitTests", "DevTrends.WCFDataAnnotations.UnitTests\DevTrends.WCFDataAnnotations.UnitTests.csproj", "{ADA5C939-0C93-4DE6-AC70-8227214C9878}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.Implementation.SmartSortLookup", "Purolator.SmartSort.Service.Implementation.SmartSortLookup\Purolator.SmartSort.Service.Implementation.SmartSortLookup.csproj", "{C9334151-3FA6-4A10-B92F-51AC1157D512}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Windows.Services.EventSender", "Purolator.SmartSort.Windows.Services.EventSender\Purolator.SmartSort.Windows.Services.EventSender.csproj", "{8FB84A5A-BD29-44C2-916E-0BDE19D11C12}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.ChannelIntegration.EnterpriseEvent", "Purolator.SmartSort.ChannelIntegration.EnterpriseEvent\Purolator.SmartSort.ChannelIntegration.EnterpriseEvent.csproj", "{ACD1BAED-49A9-4EEB-87EF-F56AD23BE739}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.Implementation.SmartSortTimeZone", "Purolator.SmartSort.Service.Implementation.SmartSortTimeZoneService\Purolator.SmartSort.Service.Implementation.SmartSortTimeZone.csproj", "{BD9D3564-9F51-4CC2-A55E-99B056F24C5D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Service.WCFMessageInspector", "Purolator.SmartSort.Service.WCFMessageInspector\Purolator.SmartSort.Service.WCFMessageInspector.csproj", "{D603BA92-87B3-485F-BB3C-35C9AA994A50}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Windows.Service.RoutePrinter", "Purolator.SmartSort.Windows.Service.RoutePrinter\Purolator.SmartSort.Windows.Service.RoutePrinter.csproj", "{D68E5C2B-A3C2-4172-B432-E03BBB220C05}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Windows.Services.OFDSender", "Purolator.SmartSort.Windows.Services.OFDSender\Purolator.SmartSort.Windows.Services.OFDSender.csproj", "{8E1C36FC-C719-4AAC-8AB0-BAA4D914D136}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.ChannelIntegration.TrackingHelper", "Purolator.SmartSort.ChannelIntegration.TrackingHelper\Purolator.SmartSort.ChannelIntegration.TrackingHelper.csproj", "{870172AC-371B-4FA0-81C3-3CC416BCC6E8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DB3FileConversion", "..\DB3FileConversion\FileConversion\DB3FileConversion.csproj", "{A70F7A0C-F5B8-4ADB-BC96-728815308F12}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AWSMunicipalityLoader", "..\AWSMunicipalities\AWSMunicipalityLoader\AWSMunicipalityLoader.csproj", "{6FEFB26D-171A-4974-92EA-1AD96A7700EF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Purolator.SmartSort.Windows.Services.Amazon", "Purolator.SmartSort.Windows.Services.Amazon\Purolator.SmartSort.Windows.Services.Amazon.csproj", "{7D21A05E-838F-4345-958C-844EF78A73FC}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{759632CE-7537-4246-B891-158142E736B1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{759632CE-7537-4246-B891-158142E736B1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{759632CE-7537-4246-B891-158142E736B1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{759632CE-7537-4246-B891-158142E736B1}.Release|Any CPU.Build.0 = Release|Any CPU
		{862103A3-8DAB-46EE-8890-2F192A71ED66}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{862103A3-8DAB-46EE-8890-2F192A71ED66}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{862103A3-8DAB-46EE-8890-2F192A71ED66}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{862103A3-8DAB-46EE-8890-2F192A71ED66}.Release|Any CPU.Build.0 = Release|Any CPU
		{7B3557D3-DC6A-484D-BBF3-E48B5832208E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7B3557D3-DC6A-484D-BBF3-E48B5832208E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7B3557D3-DC6A-484D-BBF3-E48B5832208E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7B3557D3-DC6A-484D-BBF3-E48B5832208E}.Release|Any CPU.Build.0 = Release|Any CPU
		{60DB29E4-EEEB-4208-BB85-89C23BAC6A28}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{60DB29E4-EEEB-4208-BB85-89C23BAC6A28}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{60DB29E4-EEEB-4208-BB85-89C23BAC6A28}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{60DB29E4-EEEB-4208-BB85-89C23BAC6A28}.Release|Any CPU.Build.0 = Release|Any CPU
		{CBDACD7A-4123-4188-AA08-A77CDAC1702F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CBDACD7A-4123-4188-AA08-A77CDAC1702F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CBDACD7A-4123-4188-AA08-A77CDAC1702F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CBDACD7A-4123-4188-AA08-A77CDAC1702F}.Release|Any CPU.Build.0 = Release|Any CPU
		{1FD9CDB9-E4B1-4AA5-AB52-2D3C6E03262D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1FD9CDB9-E4B1-4AA5-AB52-2D3C6E03262D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1FD9CDB9-E4B1-4AA5-AB52-2D3C6E03262D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1FD9CDB9-E4B1-4AA5-AB52-2D3C6E03262D}.Release|Any CPU.Build.0 = Release|Any CPU
		{1AC49B5C-7F91-4E55-9701-68AB8AD35170}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1AC49B5C-7F91-4E55-9701-68AB8AD35170}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1AC49B5C-7F91-4E55-9701-68AB8AD35170}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1AC49B5C-7F91-4E55-9701-68AB8AD35170}.Release|Any CPU.Build.0 = Release|Any CPU
		{C89AFF74-2601-488A-AF90-B6B890DE9B8F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C89AFF74-2601-488A-AF90-B6B890DE9B8F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C89AFF74-2601-488A-AF90-B6B890DE9B8F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C89AFF74-2601-488A-AF90-B6B890DE9B8F}.Release|Any CPU.Build.0 = Release|Any CPU
		{1807D0B9-D240-4ADC-A5B6-D8FA290F1171}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1807D0B9-D240-4ADC-A5B6-D8FA290F1171}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1807D0B9-D240-4ADC-A5B6-D8FA290F1171}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1807D0B9-D240-4ADC-A5B6-D8FA290F1171}.Release|Any CPU.Build.0 = Release|Any CPU
		{93213BC1-5510-457F-B09B-94ACB33A232F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{93213BC1-5510-457F-B09B-94ACB33A232F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{93213BC1-5510-457F-B09B-94ACB33A232F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{93213BC1-5510-457F-B09B-94ACB33A232F}.Release|Any CPU.Build.0 = Release|Any CPU
		{CD36F7DE-A423-4A5E-BD45-113D91B28911}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CD36F7DE-A423-4A5E-BD45-113D91B28911}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CD36F7DE-A423-4A5E-BD45-113D91B28911}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CD36F7DE-A423-4A5E-BD45-113D91B28911}.Release|Any CPU.Build.0 = Release|Any CPU
		{57588481-3BFA-4A35-A1AB-09E202AB6DA6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{57588481-3BFA-4A35-A1AB-09E202AB6DA6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{57588481-3BFA-4A35-A1AB-09E202AB6DA6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{57588481-3BFA-4A35-A1AB-09E202AB6DA6}.Release|Any CPU.Build.0 = Release|Any CPU
		{361C1D76-1D64-4486-AE6B-A1976AD0FA34}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{361C1D76-1D64-4486-AE6B-A1976AD0FA34}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{361C1D76-1D64-4486-AE6B-A1976AD0FA34}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{361C1D76-1D64-4486-AE6B-A1976AD0FA34}.Release|Any CPU.Build.0 = Release|Any CPU
		{72450B38-A6B8-41F9-BA92-FAFB870CF713}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{72450B38-A6B8-41F9-BA92-FAFB870CF713}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{72450B38-A6B8-41F9-BA92-FAFB870CF713}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{72450B38-A6B8-41F9-BA92-FAFB870CF713}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D2FCB2C-E293-4DEB-9C88-F2DD6F850B2A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D2FCB2C-E293-4DEB-9C88-F2DD6F850B2A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D2FCB2C-E293-4DEB-9C88-F2DD6F850B2A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D2FCB2C-E293-4DEB-9C88-F2DD6F850B2A}.Release|Any CPU.Build.0 = Release|Any CPU
		{96339C2A-F60D-4599-B2C2-BBA5813245E9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{96339C2A-F60D-4599-B2C2-BBA5813245E9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{96339C2A-F60D-4599-B2C2-BBA5813245E9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{96339C2A-F60D-4599-B2C2-BBA5813245E9}.Release|Any CPU.Build.0 = Release|Any CPU
		{37718613-EC5C-4C0C-BC48-7626506BA000}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{37718613-EC5C-4C0C-BC48-7626506BA000}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{37718613-EC5C-4C0C-BC48-7626506BA000}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{37718613-EC5C-4C0C-BC48-7626506BA000}.Release|Any CPU.Build.0 = Release|Any CPU
		{9EBA319A-038D-4E2B-B838-D4D8704616C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9EBA319A-038D-4E2B-B838-D4D8704616C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9EBA319A-038D-4E2B-B838-D4D8704616C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9EBA319A-038D-4E2B-B838-D4D8704616C5}.Release|Any CPU.Build.0 = Release|Any CPU
		{CB3E550F-1FBB-448A-8BE2-EED8FB5A2A1F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CB3E550F-1FBB-448A-8BE2-EED8FB5A2A1F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CB3E550F-1FBB-448A-8BE2-EED8FB5A2A1F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CB3E550F-1FBB-448A-8BE2-EED8FB5A2A1F}.Release|Any CPU.Build.0 = Release|Any CPU
		{A4D64B2D-D8A0-4E24-928F-737DDA55D99F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A4D64B2D-D8A0-4E24-928F-737DDA55D99F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A4D64B2D-D8A0-4E24-928F-737DDA55D99F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A4D64B2D-D8A0-4E24-928F-737DDA55D99F}.Release|Any CPU.Build.0 = Release|Any CPU
		{9952D96B-94CA-4A86-9742-ACB897367A31}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9952D96B-94CA-4A86-9742-ACB897367A31}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9952D96B-94CA-4A86-9742-ACB897367A31}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9952D96B-94CA-4A86-9742-ACB897367A31}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3E1F3FF-2B3B-4F12-9562-6CA67347A516}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3E1F3FF-2B3B-4F12-9562-6CA67347A516}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3E1F3FF-2B3B-4F12-9562-6CA67347A516}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3E1F3FF-2B3B-4F12-9562-6CA67347A516}.Release|Any CPU.Build.0 = Release|Any CPU
		{BB815650-BE30-45D3-8EE8-61F94996D60D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BB815650-BE30-45D3-8EE8-61F94996D60D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BB815650-BE30-45D3-8EE8-61F94996D60D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BB815650-BE30-45D3-8EE8-61F94996D60D}.Release|Any CPU.Build.0 = Release|Any CPU
		{5BD4EAA0-4782-4E75-AD6C-5753BA2D0A91}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5BD4EAA0-4782-4E75-AD6C-5753BA2D0A91}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5BD4EAA0-4782-4E75-AD6C-5753BA2D0A91}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5BD4EAA0-4782-4E75-AD6C-5753BA2D0A91}.Release|Any CPU.Build.0 = Release|Any CPU
		{ADA5C939-0C93-4DE6-AC70-8227214C9878}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ADA5C939-0C93-4DE6-AC70-8227214C9878}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ADA5C939-0C93-4DE6-AC70-8227214C9878}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ADA5C939-0C93-4DE6-AC70-8227214C9878}.Release|Any CPU.Build.0 = Release|Any CPU
		{C9334151-3FA6-4A10-B92F-51AC1157D512}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C9334151-3FA6-4A10-B92F-51AC1157D512}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C9334151-3FA6-4A10-B92F-51AC1157D512}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C9334151-3FA6-4A10-B92F-51AC1157D512}.Release|Any CPU.Build.0 = Release|Any CPU
		{8FB84A5A-BD29-44C2-916E-0BDE19D11C12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8FB84A5A-BD29-44C2-916E-0BDE19D11C12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8FB84A5A-BD29-44C2-916E-0BDE19D11C12}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8FB84A5A-BD29-44C2-916E-0BDE19D11C12}.Release|Any CPU.Build.0 = Release|Any CPU
		{ACD1BAED-49A9-4EEB-87EF-F56AD23BE739}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ACD1BAED-49A9-4EEB-87EF-F56AD23BE739}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ACD1BAED-49A9-4EEB-87EF-F56AD23BE739}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ACD1BAED-49A9-4EEB-87EF-F56AD23BE739}.Release|Any CPU.Build.0 = Release|Any CPU
		{BD9D3564-9F51-4CC2-A55E-99B056F24C5D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BD9D3564-9F51-4CC2-A55E-99B056F24C5D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BD9D3564-9F51-4CC2-A55E-99B056F24C5D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BD9D3564-9F51-4CC2-A55E-99B056F24C5D}.Release|Any CPU.Build.0 = Release|Any CPU
		{D603BA92-87B3-485F-BB3C-35C9AA994A50}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D603BA92-87B3-485F-BB3C-35C9AA994A50}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D603BA92-87B3-485F-BB3C-35C9AA994A50}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D603BA92-87B3-485F-BB3C-35C9AA994A50}.Release|Any CPU.Build.0 = Release|Any CPU
		{D68E5C2B-A3C2-4172-B432-E03BBB220C05}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D68E5C2B-A3C2-4172-B432-E03BBB220C05}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D68E5C2B-A3C2-4172-B432-E03BBB220C05}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D68E5C2B-A3C2-4172-B432-E03BBB220C05}.Release|Any CPU.Build.0 = Release|Any CPU
		{8E1C36FC-C719-4AAC-8AB0-BAA4D914D136}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8E1C36FC-C719-4AAC-8AB0-BAA4D914D136}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8E1C36FC-C719-4AAC-8AB0-BAA4D914D136}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8E1C36FC-C719-4AAC-8AB0-BAA4D914D136}.Release|Any CPU.Build.0 = Release|Any CPU
		{870172AC-371B-4FA0-81C3-3CC416BCC6E8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{870172AC-371B-4FA0-81C3-3CC416BCC6E8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{870172AC-371B-4FA0-81C3-3CC416BCC6E8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{870172AC-371B-4FA0-81C3-3CC416BCC6E8}.Release|Any CPU.Build.0 = Release|Any CPU
		{A70F7A0C-F5B8-4ADB-BC96-728815308F12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A70F7A0C-F5B8-4ADB-BC96-728815308F12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A70F7A0C-F5B8-4ADB-BC96-728815308F12}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A70F7A0C-F5B8-4ADB-BC96-728815308F12}.Release|Any CPU.Build.0 = Release|Any CPU
		{6FEFB26D-171A-4974-92EA-1AD96A7700EF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6FEFB26D-171A-4974-92EA-1AD96A7700EF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6FEFB26D-171A-4974-92EA-1AD96A7700EF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6FEFB26D-171A-4974-92EA-1AD96A7700EF}.Release|Any CPU.Build.0 = Release|Any CPU
		{7D21A05E-838F-4345-958C-844EF78A73FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7D21A05E-838F-4345-958C-844EF78A73FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7D21A05E-838F-4345-958C-844EF78A73FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7D21A05E-838F-4345-958C-844EF78A73FC}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F986098F-B301-45B1-A9AC-6651A930B9C3} = {27010071-73AD-4845-BF8A-56F8021998CB}
		{AF283D3B-5A0E-4633-8132-D614E72F5D2E} = {27010071-73AD-4845-BF8A-56F8021998CB}
		{8BF20E42-C07E-45C4-B9ED-DA326B80355E} = {27010071-73AD-4845-BF8A-56F8021998CB}
		{2625E5BB-DDEC-4527-8F53-B0A9980D4503} = {27010071-73AD-4845-BF8A-56F8021998CB}
		{7B3B279A-15EC-4FAC-99CC-74BCA120AAC4} = {27010071-73AD-4845-BF8A-56F8021998CB}
		{7AF1BF09-A112-4D13-92C6-B8103C869B4A} = {7B3B279A-15EC-4FAC-99CC-74BCA120AAC4}
		{04C25799-3835-4EC7-9772-6339E7E8ED39} = {7B3B279A-15EC-4FAC-99CC-74BCA120AAC4}
		{0FB37A87-6537-4643-9A44-EB5F1CFF4992} = {7B3B279A-15EC-4FAC-99CC-74BCA120AAC4}
		{5A6E9CBD-A35B-45EB-9C7B-998D36AA5DC3} = {27010071-73AD-4845-BF8A-56F8021998CB}
		{57FC91C1-9E63-4A9C-AF33-AFB0D5452A1E} = {7B3B279A-15EC-4FAC-99CC-74BCA120AAC4}
		{065587D7-513E-465D-9737-070492D9A072} = {57FC91C1-9E63-4A9C-AF33-AFB0D5452A1E}
		{24252581-ACC5-472A-B730-ABFE8E3AFAD2} = {57FC91C1-9E63-4A9C-AF33-AFB0D5452A1E}
		{759632CE-7537-4246-B891-158142E736B1} = {24252581-ACC5-472A-B730-ABFE8E3AFAD2}
		{8942518B-37CA-4E44-81D3-03A5022BCC4E} = {57FC91C1-9E63-4A9C-AF33-AFB0D5452A1E}
		{862103A3-8DAB-46EE-8890-2F192A71ED66} = {04C25799-3835-4EC7-9772-6339E7E8ED39}
		{7B3557D3-DC6A-484D-BBF3-E48B5832208E} = {0FB37A87-6537-4643-9A44-EB5F1CFF4992}
		{60DB29E4-EEEB-4208-BB85-89C23BAC6A28} = {065587D7-513E-465D-9737-070492D9A072}
		{CBDACD7A-4123-4188-AA08-A77CDAC1702F} = {7AF1BF09-A112-4D13-92C6-B8103C869B4A}
		{1FD9CDB9-E4B1-4AA5-AB52-2D3C6E03262D} = {5A6E9CBD-A35B-45EB-9C7B-998D36AA5DC3}
		{1AC49B5C-7F91-4E55-9701-68AB8AD35170} = {065587D7-513E-465D-9737-070492D9A072}
		{70DFD7D2-A8C6-48D3-959C-E90A99D60406} = {57FC91C1-9E63-4A9C-AF33-AFB0D5452A1E}
		{C89AFF74-2601-488A-AF90-B6B890DE9B8F} = {70DFD7D2-A8C6-48D3-959C-E90A99D60406}
		{4F07F4DC-B874-4FD7-8F41-8C9C65F8A746} = {57FC91C1-9E63-4A9C-AF33-AFB0D5452A1E}
		{1807D0B9-D240-4ADC-A5B6-D8FA290F1171} = {4F07F4DC-B874-4FD7-8F41-8C9C65F8A746}
		{2A2967CD-42D6-484B-9084-8381ED8FC64F} = {F986098F-B301-45B1-A9AC-6651A930B9C3}
		{29319D7E-C62E-4708-A52E-12D58D1E3F19} = {7B3B279A-15EC-4FAC-99CC-74BCA120AAC4}
		{93213BC1-5510-457F-B09B-94ACB33A232F} = {F986098F-B301-45B1-A9AC-6651A930B9C3}
		{CD36F7DE-A423-4A5E-BD45-113D91B28911} = {24252581-ACC5-472A-B730-ABFE8E3AFAD2}
		{332C7795-A16A-49DB-BE91-4A1B6FBF0E8B} = {57FC91C1-9E63-4A9C-AF33-AFB0D5452A1E}
		{57588481-3BFA-4A35-A1AB-09E202AB6DA6} = {332C7795-A16A-49DB-BE91-4A1B6FBF0E8B}
		{361C1D76-1D64-4486-AE6B-A1976AD0FA34} = {57FC91C1-9E63-4A9C-AF33-AFB0D5452A1E}
		{BBFC7A28-0C1C-4A5F-811B-FB80F192F7D1} = {57FC91C1-9E63-4A9C-AF33-AFB0D5452A1E}
		{72450B38-A6B8-41F9-BA92-FAFB870CF713} = {BBFC7A28-0C1C-4A5F-811B-FB80F192F7D1}
		{E6EE8A66-888F-498B-B6CB-AA50FD231BEA} = {57FC91C1-9E63-4A9C-AF33-AFB0D5452A1E}
		{0D2FCB2C-E293-4DEB-9C88-F2DD6F850B2A} = {E6EE8A66-888F-498B-B6CB-AA50FD231BEA}
		{96339C2A-F60D-4599-B2C2-BBA5813245E9} = {0FB37A87-6537-4643-9A44-EB5F1CFF4992}
		{A964C298-A340-4CF2-94D3-86A8279FB616} = {7B3B279A-15EC-4FAC-99CC-74BCA120AAC4}
		{37718613-EC5C-4C0C-BC48-7626506BA000} = {A964C298-A340-4CF2-94D3-86A8279FB616}
		{9EBA319A-038D-4E2B-B838-D4D8704616C5} = {8BF20E42-C07E-45C4-B9ED-DA326B80355E}
		{CB3E550F-1FBB-448A-8BE2-EED8FB5A2A1F} = {8BF20E42-C07E-45C4-B9ED-DA326B80355E}
		{A4D64B2D-D8A0-4E24-928F-737DDA55D99F} = {8942518B-37CA-4E44-81D3-03A5022BCC4E}
		{9952D96B-94CA-4A86-9742-ACB897367A31} = {27010071-73AD-4845-BF8A-56F8021998CB}
		{C3E1F3FF-2B3B-4F12-9562-6CA67347A516} = {27010071-73AD-4845-BF8A-56F8021998CB}
		{8F92A12F-0A56-442A-BC92-E02A8C786D1C} = {AF283D3B-5A0E-4633-8132-D614E72F5D2E}
		{4A6DAC53-62A4-40F8-B049-2C8C7E2BBF16} = {8F92A12F-0A56-442A-BC92-E02A8C786D1C}
		{BB815650-BE30-45D3-8EE8-61F94996D60D} = {24252581-ACC5-472A-B730-ABFE8E3AFAD2}
		{C102B11F-A557-4BB2-B6B1-37BFDA2752C4} = {F986098F-B301-45B1-A9AC-6651A930B9C3}
		{5BD4EAA0-4782-4E75-AD6C-5753BA2D0A91} = {C102B11F-A557-4BB2-B6B1-37BFDA2752C4}
		{DAD7E7E9-993B-4068-9F78-504866B0CF3F} = {8BF20E42-C07E-45C4-B9ED-DA326B80355E}
		{ADA5C939-0C93-4DE6-AC70-8227214C9878} = {DAD7E7E9-993B-4068-9F78-504866B0CF3F}
		{C9334151-3FA6-4A10-B92F-51AC1157D512} = {24252581-ACC5-472A-B730-ABFE8E3AFAD2}
		{8FB84A5A-BD29-44C2-916E-0BDE19D11C12} = {8942518B-37CA-4E44-81D3-03A5022BCC4E}
		{ACD1BAED-49A9-4EEB-87EF-F56AD23BE739} = {A964C298-A340-4CF2-94D3-86A8279FB616}
		{BD9D3564-9F51-4CC2-A55E-99B056F24C5D} = {24252581-ACC5-472A-B730-ABFE8E3AFAD2}
		{D603BA92-87B3-485F-BB3C-35C9AA994A50} = {BBFC7A28-0C1C-4A5F-811B-FB80F192F7D1}
		{D68E5C2B-A3C2-4172-B432-E03BBB220C05} = {8942518B-37CA-4E44-81D3-03A5022BCC4E}
		{8E1C36FC-C719-4AAC-8AB0-BAA4D914D136} = {8942518B-37CA-4E44-81D3-03A5022BCC4E}
		{870172AC-371B-4FA0-81C3-3CC416BCC6E8} = {A964C298-A340-4CF2-94D3-86A8279FB616}
		{7D21A05E-838F-4345-958C-844EF78A73FC} = {8942518B-37CA-4E44-81D3-03A5022BCC4E}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {458FAD47-5A47-4461-A65A-E4343B463C43}
	EndGlobalSection
EndGlobal
