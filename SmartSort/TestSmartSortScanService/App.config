<?xml version="1.0"?>
<configuration>
  <configSections>
    <section name="specFlow" type="TechTalk.SpecFlow.Configuration.ConfigurationSectionHandler, TechTalk.SpecFlow"/>
  </configSections>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5"/>
    </startup>


  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="Binding1"/>
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://inno-5cb1450s4q.innovapost.ca/SmartSort/SmartSortScanService.svc/smartsortscan" binding="basicHttpBinding" contract="SmartSortScanService.ISmartSortScanService" bindingConfiguration="Binding2" name="Purolator.SmartSort.Service.Implementation.SmartSortScanService.SmartSortScanService"/>
      <endpoint address="http://inno-5cb1450s4q.innovapost.ca/SmartSort/SmartSortScanService.svc/smartsortscanpost/New" binding="basicHttpBinding" contract="SmartSortScanService.ISmartSortScanService" bindingConfiguration="Binding1" name="Purolator.SmartSort.Service.Implementation.SmartSortScanService.SmartSortScanService"/>
      <endpoint address="http://inno-5cb1450s4q.innovapost.ca/SmartSort/SmartSortScanService.svc" binding="basicHttpBinding" contract="ISmartSortScanService" bindingConfiguration="Binding1" name="Purolator.SmartSort.Service.Implementation.SmartSortScanService.SmartSortScanService"/>
    </client>
  </system.serviceModel>
  
<specFlow>
    <!-- For additional details on SpecFlow configuration options see http://go.specflow.org/doc-config -->
  <!-- For additional details on SpecFlow configuration options see http://go.specflow.org/doc-config --><unitTestProvider name="NUnit"/></specFlow></configuration>
