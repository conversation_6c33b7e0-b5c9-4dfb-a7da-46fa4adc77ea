﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;


namespace TestSmartSortScanService
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }


        private void button1_Click_1(object sender, EventArgs e)
        {
            if (txtScanItems.Text != string.Empty)
            {

                // declare ascii encoding
                ASCIIEncoding encoding = new ASCIIEncoding();
                string postData = txtScanItems.Text.ToString();
                // convert xmlstring to byte using ascii encoding
                byte[] data = encoding.GetBytes(postData);
                // declare httpwebrequet wrt url defined above
                //HttpWebRequest webrequest = (HttpWebRequest)WebRequest.Create("http://inno-5cb1450s4q.innovapost.ca/SmartSort/SmartSortScanService.svc/smartsortscan");
                HttpWebRequest webrequest = (HttpWebRequest)WebRequest.Create("http://inno-5cb1450s4q.innovapost.ca/SmartSort/SmartSortScan.svc/smartsortscan");
                // set method as post
                webrequest.Method = "POST";
                // set content type
                webrequest.ContentType = "application/json";
                // set content length
                webrequest.ContentLength = data.Length;
                // get stream data out of webrequest object
                Stream newStream = webrequest.GetRequestStream();
                newStream.Write(data, 0, data.Length);
                newStream.Close();
                // declare & read response from service
                HttpWebResponse webresponse = (HttpWebResponse)webrequest.GetResponse();

                // set utf8 encoding
                Encoding enc = System.Text.Encoding.GetEncoding("utf-8");
                // read response stream from response object
                StreamReader loResponseStream = new StreamReader(webresponse.GetResponseStream(), enc);
                // read string from stream data
                String strResult = string.Empty;

                strResult = loResponseStream.ReadToEnd();
                // close the stream object
                loResponseStream.Close();
                // close the response object
                webresponse.Close();
 
                txtResult.Text = strResult;

            }
        }

        private void txtScanItems_TextChanged(object sender, EventArgs e)
        {

        }
    }
}
