﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.18408
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: System.Runtime.Serialization.ContractNamespaceAttribute("http://purolator.com/smartsort/datatypes/v1", ClrNamespace="purolator.com.smartsort.datatypes.v1")]

namespace Purolator.SmartSort.Service.MessageContracts
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SmartSortScanResponse", Namespace="http://schemas.datacontract.org/2004/07/Purolator.SmartSort.Service.MessageContra" +
        "cts")]
    public partial class SmartSortScanResponse : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private purolator.com.smartsort.datatypes.v1.SmartSortResponseError[] errorListField;
        
        private Purolator.SmartSort.Service.DataContracts.StatusCode statusCodeField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public purolator.com.smartsort.datatypes.v1.SmartSortResponseError[] errorList
        {
            get
            {
                return this.errorListField;
            }
            set
            {
                this.errorListField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public Purolator.SmartSort.Service.DataContracts.StatusCode statusCode
        {
            get
            {
                return this.statusCodeField;
            }
            set
            {
                this.statusCodeField = value;
            }
        }
    }
}
namespace purolator.com.smartsort.datatypes.v1
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SmartSortResponseError", Namespace="http://purolator.com/smartsort/datatypes/v1")]
    public partial class SmartSortResponseError : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string PinField;
        
        private string ErrorCodeField;
        
        private string ErrorMessageField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Pin
        {
            get
            {
                return this.PinField;
            }
            set
            {
                this.PinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public string ErrorCode
        {
            get
            {
                return this.ErrorCodeField;
            }
            set
            {
                this.ErrorCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=2)]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
    }
}
namespace Purolator.SmartSort.Service.DataContracts
{
    using System.Runtime.Serialization;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="StatusCode", Namespace="http://schemas.datacontract.org/2004/07/Purolator.SmartSort.Service.DataContracts" +
        "")]
    public enum StatusCode : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Error = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Success = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Partial = 2,
    }
}


[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.ServiceModel.ServiceContractAttribute(ConfigurationName="ISmartSortScanService")]
public interface ISmartSortScanService
{
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISmartSortScanService/SmartSortScan", ReplyAction="http://tempuri.org/ISmartSortScanService/SmartSortScanResponse")]
    Purolator.SmartSort.Service.MessageContracts.SmartSortScanResponse SmartSortScan(string scans);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISmartSortScanService/SmartSortScan", ReplyAction="http://tempuri.org/ISmartSortScanService/SmartSortScanResponse")]
    System.Threading.Tasks.Task<Purolator.SmartSort.Service.MessageContracts.SmartSortScanResponse> SmartSortScanAsync(string scans);
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
public interface ISmartSortScanServiceChannel : ISmartSortScanService, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
public partial class SmartSortScanServiceClient : System.ServiceModel.ClientBase<ISmartSortScanService>, ISmartSortScanService
{
    
    public SmartSortScanServiceClient()
    {
    }
    
    public SmartSortScanServiceClient(string endpointConfigurationName) : 
            base(endpointConfigurationName)
    {
    }
    
    public SmartSortScanServiceClient(string endpointConfigurationName, string remoteAddress) : 
            base(endpointConfigurationName, remoteAddress)
    {
    }
    
    public SmartSortScanServiceClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
            base(endpointConfigurationName, remoteAddress)
    {
    }
    
    public SmartSortScanServiceClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
            base(binding, remoteAddress)
    {
    }
    
    public Purolator.SmartSort.Service.MessageContracts.SmartSortScanResponse SmartSortScan(string scans)
    {
        return base.Channel.SmartSortScan(scans);
    }
    
    public System.Threading.Tasks.Task<Purolator.SmartSort.Service.MessageContracts.SmartSortScanResponse> SmartSortScanAsync(string scans)
    {
        return base.Channel.SmartSortScanAsync(scans);
    }
}


