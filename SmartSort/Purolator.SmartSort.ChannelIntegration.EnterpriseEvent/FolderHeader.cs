﻿using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class FolderHeader
    {
        [DataMember(Name = "busCd")]
        public string busCd { get; set; }

        [DataMember(Name = "lobCd")]
        public string lobCd { get; set; }

        [DataMember(Name = "termId")]
        public string termId { get; set; }

        [DataMember(Name = "srcSysCd")]
        public string srcSysCd { get; set; }

        [DataMember(Name = "srcSysVers")]
        public string srcSysVers { get; set; }

        [DataMember(Name = "srcSysRefCd")]
        public string srcSysRefCd { get; set; }

        [DataMember(Name = "devId")]
        public string devId { get; set; }

        [DataMember(Name = "usrId")]
        public string usrId { get; set; }

        [DataMember(Name = "rteId")]
        public string rteId { get; set; }
    }
}
