﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class Event
    {
        [DataMember(Name = "eventHdr")]
        public EventHeader eventHdr { get; set; }

        [DataMember(Name = "scanItms")]
        public List<ScanItem> scanItms { get; set; }

        [DataMember(Name = "loc")]
        public Location loc { get; set; }

        [DataMember(Name = "gps")]
        public Gps gps { get; set; }

        [DataMember(Name = "cntnr")]
        public Container cntnr { get; set; }

        [DataMember(Name = "eventImgs")]
        public List<EventImage> eventImgs { get; set; }

        [DataMember(Name = "sig")]
        public Signature sig { get; set; }

        [DataMember(Name = "mnfstDtls")]
        public ManifestDetails mnfstDtls { get; set; }

        [DataMember(Name = "pkpEventsDtls")]
        public PickupEventDetails pkpEventsDtls { get; set; }

        [DataMember(Name = "delEventDtls")]
        public DeliveryEventDetails delEventDtls { get; set; }

        [DataMember(Name = "comment")]
        public string comment { get; set; }

        [DataMember(Name = "addtnlEventAttrs")]
        public List<AddtnlEventAttr> addtnlEventAttrs { get; set; }
    }
}
