﻿using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class Barcode
    {
        [DataMember(Name = "captureMthdCd")]
        public string captureMthdCd { get; set; }

        [DataMember(Name = "brcdTypCd")]
        public string brcdTypCd { get; set; }

        [DataMember(Name = "brcdStrng")]
        public string brcdStrng { get; set; }
    }
}
