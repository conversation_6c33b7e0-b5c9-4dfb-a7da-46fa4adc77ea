﻿using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class Signature
    {
        [DataMember(Name = "sigCd")]
        public string sigCd { get; set; }

        [DataMember(Name = "sigNm")]
        public string sigNm { get; set; }

        [DataMember(Name = "imgCntnr")]
        public string imgCntnr { get; set; }

        [DataMember(Name = "imgFldr")]
        public string imgFldr { get; set; }

        [DataMember(Name = "imgObjNm")]
        public string imgObjNm { get; set; }
    }
}
