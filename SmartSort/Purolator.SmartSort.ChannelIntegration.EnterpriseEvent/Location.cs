﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class Location
    {
        [DataMember(Name = "locTypCd")]
        public string locTypCd { get; set; }

        [DataMember(Name = "locId")]
        public string locId { get; set; }

        [DataMember(Name = "custCntct")]
        public CustomerContact custCntct { get; set; }

        [DataMember(Name = "eventAddr")]
        public EventAddress eventAddr { get; set; }

        [DataMember(Name = "addtnlLocAttrs")]
        public List<AddtnlLocAttr> addtnlLocAttrs { get; set; }
    }
}
