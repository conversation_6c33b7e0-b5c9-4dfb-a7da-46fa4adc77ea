﻿using System.Collections.Generic;
using System.Runtime.Serialization;


namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class ScanItm
    {
        [DataMember(Name = "scanTms")]
        public string scanTms { get; set; }

        [DataMember(Name = "trkId")]
        public string trkId { get; set; }

        [DataMember(Name = "altRef")]
        public AlternateReference altRef { get; set; }

        [DataMember(Name = "brcdElmnts")]
        public BarcodeElements brcdElmnts { get; set; }

        [DataMember(Name = "brcd")]
        public Barcode brcd { get; set; }

        [DataMember(Name = "custRefs")]
        public List<CustomerReference> custRefs { get; set; }

        [DataMember(Name = "rwghDtls")]
        public ReweighDetails rwghDtls { get; set; }

        [DataMember(Name = "pkgImgs")]
        public List<PackageImage> pkgImgs { get; set; }

        [DataMember(Name = "addtnlItmAttrs")]
        public List<AddtnlItemAttr> addtnlItmAttrs { get; set; }
    }
}
