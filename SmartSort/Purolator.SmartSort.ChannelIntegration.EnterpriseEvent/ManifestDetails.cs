﻿using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class ManifestDetails
    {
        [DataMember(Name = "acctId")]
        public string acctId { get; set; }

        [DataMember(Name = "shpDt")]
        public string shpDt { get; set; }

        [DataMember(Name = "shpTrkId")]
        public string shpTrkId { get; set; }

        [DataMember(Name = "origCty")]
        public string origCty { get; set; }

        [DataMember(Name = "origPostCd")]
        public string origPostCd { get; set; }

        [DataMember(Name = "origCtryCd")]
        public string origCtryCd { get; set; }

        [DataMember(Name = "origTermId")]
        public string origTermId { get; set; }

        [DataMember(Name = "destCty")]
        public string destCty { get; set; }

        [DataMember(Name = "destRgnCd")]
        public string destRgnCd { get; set; }

        [DataMember(Name = "destPostCd")]
        public string destPostCd { get; set; }

        [DataMember(Name = "destCtryCd")]
        public string destCtryCd { get; set; }

        [DataMember(Name = "destTermId")]
        public string destTermId { get; set; }

        [DataMember(Name = "prodNbr")]
        public string prodNbr { get; set; }

        [DataMember(Name = "initEddDt")]
        public string initEddDt { get; set; }

        [DataMember(Name = "initTransDays")]
        public int initTransDays { get; set; }

        [DataMember(Name = "satPkpFlg")]
        public bool satPkpFlg { get; set; }

        [DataMember(Name = "satDelFlg")]
        public bool satDelFlg { get; set; }

        [DataMember(Name = "rtnFlg")]
        public bool rtnFlg { get; set; }
    }
}
