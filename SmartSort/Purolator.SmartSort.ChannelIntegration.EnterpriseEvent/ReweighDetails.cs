﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class ReweighDetails
    {
        [DataMember(Name = "classCd")]
        public string classCd { get; set; }

        [DataMember(Name = "captureMthdCd")]
        public string captureMthdCd { get; set; }

        [DataMember(Name = "captureModeCd")]
        public string captureModeCd { get; set; }

        [DataMember(Name = "len")]
        public decimal len { get; set; }

        [DataMember(Name = "wdth")]
        public decimal wdth { get; set; }

        [DataMember(Name = "hght")]
        public decimal hght { get; set; }

        [DataMember(Name = "lenUomCd")]
        public string lenUomCd { get; set; }

        [DataMember(Name = "wgt")]
        public decimal wgt { get; set; }

        [DataMember(Name = "wgtUomCd")]
        public string wgtUomCd { get; set; }

        [DataMember(Name = "dimStatCd")]
        public string dimStatCd { get; set; }

        [DataMember(Name = "wghtStatCd")]
        public string wghtStatCd { get; set; }

        [DataMember(Name = "svcs")]
        public List<Service> svcs { get; set; }
    }
}
