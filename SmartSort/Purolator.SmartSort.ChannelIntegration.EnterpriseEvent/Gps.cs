﻿using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class Gps
    {
        [DataMember(Name = "lat")]
        public decimal lat { get; set; }

        [DataMember(Name = "latDir")]
        public string latDir { get; set; }

        [DataMember(Name = "lng")]
        public string lng { get; set; }

        [DataMember(Name = "lngDir")]
        public string lngDir { get; set; }

        [DataMember(Name = "gpsTms")]
        public string gpsTms { get; set; }
    }
}
