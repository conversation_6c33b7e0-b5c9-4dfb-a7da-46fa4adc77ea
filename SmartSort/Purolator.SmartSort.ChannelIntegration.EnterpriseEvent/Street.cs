﻿using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class Street
    {
        [DataMember(Name = "nbr")]
        public string nbr { get; set; }

        [DataMember(Name = "suffix")]
        public string suffix { get; set; }

        [DataMember(Name = "name")]
        public string name { get; set; }

        [DataMember(Name = "type")]
        public string type { get; set; }

        [DataMember(Name = "dir")]
        public string dir { get; set; }
    }
}
