﻿using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class PackageImage
    {

        [DataMember(Name = "imgTypCd")]
        public string imgTypCd { get; set; }

        [DataMember(Name = "imgCntnr")]
        public string imgCntnr { get; set; }

        [DataMember(Name = "imgFldr")]
        public string imgFldr { get; set; }

        [DataMember(Name = "imgObjectNm")]
        public string imgObjectNm { get; set; }

        [DataMember(Name = "addtnlImgAttrs")]
        public List<AddtnlImageAttr> addtnlImgAttrs { get; set; }
    }
}
