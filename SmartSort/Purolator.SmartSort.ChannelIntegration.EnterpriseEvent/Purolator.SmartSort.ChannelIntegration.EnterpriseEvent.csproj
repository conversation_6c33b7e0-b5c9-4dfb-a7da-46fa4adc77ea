﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{ACD1BAED-49A9-4EEB-87EF-F56AD23BE739}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Purolator.SmartSort.ChannelIntegration.EnterpriseEvent</RootNamespace>
    <AssemblyName>Purolator.SmartSort.ChannelIntegration.EnterpriseEvent</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>..\SmartSortRuleSet.ruleset</CodeAnalysisRuleSet>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AWSSDK.Core, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWSSDK.Core.3.3.100.6\lib\net45\AWSSDK.Core.dll</HintPath>
    </Reference>
    <Reference Include="AWSSDK.SimpleNotificationService, Version=3.3.0.0, Culture=neutral, PublicKeyToken=885c28607f98e604, processorArchitecture=MSIL">
      <HintPath>..\packages\AWSSDK.SimpleNotificationService.3.3.100.6\lib\net45\AWSSDK.SimpleNotificationService.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AddtnlEventAttr.cs" />
    <Compile Include="AddtnlImageAttr.cs" />
    <Compile Include="AddtnlItemAttr.cs" />
    <Compile Include="AddtnlLocAttr.cs" />
    <Compile Include="AlternateReference.cs" />
    <Compile Include="Barcode.cs" />
    <Compile Include="BarcodeElements.cs" />
    <Compile Include="Container.cs" />
    <Compile Include="CustomerContact.cs" />
    <Compile Include="CustomerReference.cs" />
    <Compile Include="DeliveryEventDetails.cs" />
    <Compile Include="EnterpriseEventClient.cs" />
    <Compile Include="Event.cs" />
    <Compile Include="EventAddress.cs" />
    <Compile Include="EventFolder.cs" />
    <Compile Include="EventHeader.cs" />
    <Compile Include="EventImage.cs" />
    <Compile Include="EventMessage.cs" />
    <Compile Include="FolderHeader.cs" />
    <Compile Include="Gps.cs" />
    <Compile Include="Location.cs" />
    <Compile Include="ManifestDetails.cs" />
    <Compile Include="MessageHeader.cs" />
    <Compile Include="PackageImage.cs" />
    <Compile Include="PickupEventDetails.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="ReweighDetails.cs" />
    <Compile Include="ScanItem.cs" />
    <Compile Include="ScanItm.cs" />
    <Compile Include="Service.cs" />
    <Compile Include="Signature.cs" />
    <Compile Include="Street.cs" />
    <Compile Include="Translator.cs" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Purolator.SmartSort.Business.Entities\Purolator.SmartSort.Business.Entities.csproj">
      <Project>{862103a3-8dab-46ee-8890-2f192a71ed66}</Project>
      <Name>Purolator.SmartSort.Business.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Common\Purolator.SmartSort.Common.csproj">
      <Project>{93213bc1-5510-457f-b09b-94acb33a232f}</Project>
      <Name>Purolator.SmartSort.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\packages\AWSSDK.SimpleNotificationService.3.3.100.6\analyzers\dotnet\cs\AWSSDK.SimpleNotificationService.CodeAnalysis.dll" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>