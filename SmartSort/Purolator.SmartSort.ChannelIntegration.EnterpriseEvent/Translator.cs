﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class Translator
    {
        private static readonly string DF = "yyyy-MM-ddTHH:mm:ss.fffzzz";
        private static readonly string DFNOMILIS = "yyyy-MM-ddTHH:mm:sszzz";
        private static readonly string DFZ = "yyyy-MM-ddTHH:mm:ss.fffZ";
        private static readonly string DFNOMILISZ = "yyyy-MM-ddTHH:mm:ssZ";


        public static EventMessage TranslateEvent(Entities.EnterpriseEvent from)
        {
            var to = new EventMessage();

            to.msgHdr = new MessageHeader();
            to.msgHdr.id = from.EnterpriseEventID.ToString();
            to.msgHdr.vers = "1.0";
            to.msgHdr.pubTms = DateTime.Now.ToString(DF);

            to.eventFldr = new EventFolder();
            to.eventFldr.fldrHdr = new FolderHeader();
            to.eventFldr.fldrHdr.busCd = "PURO";
            to.eventFldr.fldrHdr.lobCd = "COURIER";
            to.eventFldr.fldrHdr.termId = from.Terminal;
            to.eventFldr.fldrHdr.srcSysCd = from.SS;
            to.eventFldr.fldrHdr.srcSysVers = "1.0";
            to.eventFldr.fldrHdr.srcSysRefCd = from.Source;
            if(from.EventExternalCode != 4010)
            {
                to.eventFldr.fldrHdr.devId = from.UserName;
            }
            else
            {
                to.eventFldr.fldrHdr.usrId = from.UserName;
            }

            to.eventFldr.fldrHdr.rteId = from.Route;

            to.eventFldr.events = new List<Event>();

            CultureInfo provider = CultureInfo.InvariantCulture;
            DateTimeOffset eventDateTime = new DateTimeOffset();

            bool isValidDate = DateTimeOffset.TryParseExact(from.EventDateTimeTZ, DF, provider, DateTimeStyles.AssumeLocal, out eventDateTime);

            if (!isValidDate)
            {
                isValidDate = DateTimeOffset.TryParseExact(from.EventDateTimeTZ, DFNOMILIS, provider, DateTimeStyles.AssumeLocal, out eventDateTime);
            }

            if (!isValidDate)
            {
                isValidDate = DateTimeOffset.TryParseExact(from.EventDateTimeTZ, DFZ, provider, DateTimeStyles.AssumeLocal, out eventDateTime);
            }

            if (!isValidDate)
            {
                isValidDate = DateTimeOffset.TryParseExact(from.EventDateTimeTZ, DFNOMILISZ, provider, DateTimeStyles.AssumeLocal, out eventDateTime);
            }

            string eventTMS = eventDateTime.ToString(DF);
            
            Event event1 = new Event();
            event1.eventHdr = new EventHeader();
            event1.eventHdr.eventTms = eventTMS;
            event1.eventHdr.eventCd = from.EventExternalCode.ToString();
            event1.eventHdr.eventRsnCd = getMappingCode(from.EventExternalCode, from.StatusReason);

            event1.scanItms = new List<ScanItem>();

            ScanItem scanItem = new ScanItem();
            scanItem.scanItm = new ScanItm();
            scanItem.scanItm.scanTms = eventTMS;
            scanItem.scanItm.trkId = from.PiecePin;

            scanItem.scanItm.brcd = new Barcode();

            if (!String.IsNullOrWhiteSpace(from.BarcodeType)
                && from.EventExternalCode != 4090
                && from.EventExternalCode != 4010)

            {
                scanItem.scanItm.brcd.captureMthdCd = "SS";
                switch(from.BarcodeType)
                {
                    case "01":
                        scanItem.scanItm.brcd.brcdTypCd = "1";
                        break;
                    case "02":
                        scanItem.scanItm.brcd.brcdTypCd = "2";
                        break;
                    case "03":
                        scanItem.scanItm.brcd.brcdTypCd = "3";
                        break;
                    case "04":
                        scanItem.scanItm.brcd.brcdTypCd = "4";
                        break;
                    case "05":
                        scanItem.scanItm.brcd.brcdTypCd = "5";
                        break;
                    case "06":
                        scanItem.scanItm.brcd.brcdTypCd = "6";
                        break;
                }                
            }
            if (from.EventExternalCode == 4010
                || from.EventExternalCode == 4030)
            {
                event1.loc = new Location();
                event1.loc.locTypCd = "ADDR_RTE_UPD";
                                
                if(from.EventExternalCode == 4010 && from.Source == "AutoTriage")
                {
                    event1.loc.locId = from.Source;
                }
                else
                {
                    event1.loc.locId = from.Terminal;
                }
                event1.loc.custCntct = new CustomerContact();
                event1.loc.custCntct.compNm = from.CustomerName;

                event1.loc.eventAddr = new EventAddress();
                event1.loc.eventAddr.addrTypCd = "DELIVERY_ADDR";
                event1.loc.eventAddr.street = new Street();
                event1.loc.eventAddr.street.nbr = from.StreetNumber;
                event1.loc.eventAddr.street.suffix = from.StreetNumSuf;
                event1.loc.eventAddr.street.name = from.StreetName;
                event1.loc.eventAddr.street.type = from.StreetType;
                event1.loc.eventAddr.street.dir = from.StreetDirection;
                event1.loc.eventAddr.ste = from.UnitNumber;
                event1.loc.eventAddr.muni = from.City;
                event1.loc.eventAddr.rgnCd = from.Province;
                event1.loc.eventAddr.postCd = from.PostalCode;

            }

            if(!String.IsNullOrWhiteSpace(from.Shelf))
            {
                event1.cntnr = new Container();
                event1.cntnr.cntnrTypCd = "SHELF";
                event1.cntnr.cntnrNbr = from.Shelf;
            }

            event1.scanItms.Add(scanItem);
            to.eventFldr.events.Add(event1);

            return to;
        }

        //private static DateTime getDate(string strDate)
        //{
        //    return DateTime.Parse(DateTime.Parse(strDate).ToString(DF));
        //}

        private static string getMappingCode(int eventCode, String sourceCode)
        {
            if(eventCode == 4010  || (eventCode == 4030 && sourceCode == "SRR"))
            {
                return sourceCode;
            }

            return null;
        }
    }
}
