﻿using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class EventAddress
    {
        [DataMember(Name = "addrTypCd")]
        public string addrTypCd { get; set; }

        [DataMember(Name = "consAddrLn1")]
        public string consAddrLn1 { get; set; }

        [DataMember(Name = "consAddrLn2")]
        public string consAddrLn2 { get; set; }

        [DataMember(Name = "street")]
        public Street street { get; set; }

        [DataMember(Name = "flr")]
        public string flr { get; set; }

        [DataMember(Name = "ste")]
        public string ste { get; set; }

        [DataMember(Name = "muni")]
        public string muni { get; set; }

        [DataMember(Name = "rgnCd")]
        public string rgnCd { get; set; }

        [DataMember(Name = "ctryCd")]
        public string ctryCd { get; set; }

        [DataMember(Name = "postCd")]
        public string postCd { get; set; }

        [DataMember(Name = "poBox")]
        public string poBox { get; set; }
    }
}
