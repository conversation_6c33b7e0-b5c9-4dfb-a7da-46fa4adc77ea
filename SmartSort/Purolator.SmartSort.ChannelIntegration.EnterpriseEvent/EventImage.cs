﻿using System.Runtime.Serialization;
using System.Collections.Generic;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class EventImage
    {
        [DataMember(Name = "imgTypCd")]
        public string imgTypCd { get; set; }

        [DataMember(Name = "imgCntnr")]
        public string imgCntnr { get; set; }

        [DataMember(Name = "imgFldr")]
        public string imgFldr { get; set; }

        [DataMember(Name = "imgObjNm")]
        public string imgObjNm { get; set; }

        [DataMember(Name = "addtnlImgAttrs")]
        public List<AddtnlImageAttr> addtnlImgAttrs { get; set; }
    }
}
