﻿using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class BarcodeElements
    {
        [DataMember(Name = "origPostCd")]
        public string origPostCd { get; set; }

        [DataMember(Name = "rcvrNm")]
        public string rcvrNm { get; set; }

        [DataMember(Name = "rcvrSteNbr")]
        public string rcvrSteNbr { get; set; }

        [DataMember(Name = "rcvrStNbr")]
        public string rcvrStNbr { get; set; }

        [DataMember(Name = "rcvrAddrLn1")]
        public string rcvrAddrLn1 { get; set; }

        [DataMember(Name = "rcvrAddrLn2")]
        public string rcvrAddrLn2 { get; set; }

        [DataMember(Name = "rcvrCty")]
        public string rcvrCty { get; set; }

        [DataMember(Name = "rcvrPostCd")]
        public string rcvrPostCd { get; set; }

        [DataMember(Name = "leadTrkId")]
        public string leadTrkId { get; set; }

        [DataMember(Name = "trnsprtMd")]
        public string trnsprtMd { get; set; }

        [DataMember(Name = "dlvryTime")]
        public string dlvryTime { get; set; }

        [DataMember(Name = "shpTyp")]
        public string shpTyp { get; set; }

        [DataMember(Name = "dlvryTyp")]
        public string dlvryTyp { get; set; }

        [DataMember(Name = "dvrsnCd")]
        public string dvrsnCd { get; set; }

        [DataMember(Name = "shpmnDt")]
        public string shpmnDt { get; set; }

        [DataMember(Name = "pieceNbr")]
        public string pieceNbr { get; set; }

        [DataMember(Name = "ttlPieceCnt")]
        public string ttlPieceCnt { get; set; }
    
        [DataMember(Name = "pieceWgt")]
        public string pieceWgt { get; set; }

        [DataMember(Name = "ttlShpWgt")]
        public string ttlShpWgt { get; set; }

        [DataMember(Name = "unicode")]
        public string unicode { get; set; }

        [DataMember(Name = "airportCd")]
        public string airportCd { get; set; }

        [DataMember(Name = "hndlngClsTyp")]
        public string hndlngClsTyp { get; set; }

        [DataMember(Name = "prodNbr")]
        public string prodNbr { get; set; }

        [DataMember(Name = "billingTyp")]
        public string billingTyp { get; set; }
    }
}
