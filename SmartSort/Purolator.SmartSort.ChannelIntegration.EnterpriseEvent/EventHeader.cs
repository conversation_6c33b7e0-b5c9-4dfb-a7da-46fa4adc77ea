﻿using System.Runtime.Serialization;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    class EventHeader
    {
        [DataMember(Name = "eventTms")]
        public string eventTms { get; set; }


        [DataMember(Name = "eventCd")]
        public string eventCd { get; set; }

        [DataMember(Name = "eventRsnCd")]
        public string eventRsnCd { get; set; }

        [DataMember(Name = "stopId")]
        public string stopId { get; set; }
    }
}
