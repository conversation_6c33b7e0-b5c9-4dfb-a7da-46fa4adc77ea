﻿using System;
using System.Configuration;
using Entities = Purolator.SmartSort.Business.Entities;
using Newtonsoft.Json;
using Amazon.SimpleNotificationService;
using Amazon.SimpleNotificationService.Model;
using Amazon;
using System.Net;

namespace Purolator.SmartSort.ChannelIntegration.EnterpriseEvent
{
    public class EnterpriseEventClient
    {
        public void SendEvent(Entities.EnterpriseEvent ev)
        {
            AppSettingsReader settingsReader = new AppSettingsReader();
            string AWS_KEY = (string)settingsReader.GetValue("AWS_KEY", typeof(String));
            string AWS_PASSWORD = (string)settingsReader.GetValue("AWS_PASSWORD", typeof(String));
            String topic = (string)settingsReader.GetValue("EVENTS_SNS_TOPIC", typeof(String));

            EventMessage request = Translator.TranslateEvent(ev);
            string json = JsonConvert.SerializeObject(request);
            Console.WriteLine(json);
            
            // send
            AmazonSimpleNotificationServiceClient client = new AmazonSimpleNotificationServiceClient(AWS_KEY, AWS_PASSWORD, RegionEndpoint.USEast1);

            PublishRequest publisReq = new PublishRequest
            {
                Message = json,                
                TopicArn = topic,
            };

            string eventType = string.Empty;

            switch(ev.EventExternalCode)
            {
                case 4000:
                case 4020:
                case 4050:
                case 4060:
                case 4070:
                case 4080:
                case 4090:
                    eventType = "SortEvent";
                    break;
                case 4010:
                    if(!string.IsNullOrEmpty(ev.StatusReason) 
                        && (ev.StatusReason == "SRR"
                        || ev.StatusReason == "VALNOMATCH"))
                    {
                        eventType = "SortEvent";
                    }
                    else
                    {
                        eventType = "SRREvent";
                    }
                    break;
                case 4030:
                    if (!string.IsNullOrEmpty(ev.StatusReason)
                        && ev.StatusReason == "SRR")
                    {
                        eventType = "SortEvent";
                    }
                    else
                    {
                        eventType = "SRREvent";
                    }
                    break;
            }
            publisReq.MessageAttributes["sourceEvent"] = new MessageAttributeValue { StringValue = eventType, DataType = "String" };

            PublishResponse result = client.Publish(publisReq);
            if(result.HttpStatusCode != HttpStatusCode.OK)
            {
                throw new Exception("Error sending, got HTTP " + result.HttpStatusCode);
            }
        }       
    }
}

