﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.Windows.Services.OFDSender
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        static void Main()
        {
            
            ServiceBase[] ServicesToRun;
            ServicesToRun = new ServiceBase[] 
            { 
                new OFDSender() 
            };
            ServiceBase.Run(ServicesToRun);
            

            /*
            string ConnectionInfo = OFDSenderSettings.Default.SSDB;
            var items = OFDSenderImplementation.GetWorkItems(ConnectionInfo, 20);
            foreach (var item in items)
            {
                Console.WriteLine(item);
            }
             * */

        }
    }
}
