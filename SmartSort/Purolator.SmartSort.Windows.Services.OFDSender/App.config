﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="loggingConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.LoggingSettings, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="true" />
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="Purolator.SmartSort.Windows.Services.OFDSender.OFDSenderSettings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <loggingConfiguration name="" tracingEnabled="true" defaultCategory="General">
    <listeners>
      <add name="SSDefaultEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSDefault" formatter="EventLogTextFormatter" log="SSDefault" machineName="." traceOutputOptions="None" filter="Warning" />
      <add name="SSWebPortalEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSWebPortal" formatter="EventLogTextFormatter" log="SSWebPortal" filter="Warning" />
      <add name="SSIncomingWSEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSIncomingWS" formatter="EventLogTextFormatter" log="SSIncomingWS" filter="Warning" />
      <add name="SSEventServiceEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSEventService" formatter="EventLogTextFormatter" log="SSEventService" filter="Warning" />
      <add name="SSAVServiceEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSAVService" formatter="EventLogTextFormatter" log="SSAVService" filter="Error" />
      <add name="SSScanSyncWSEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSScanSyncWS" formatter="EventLogTextFormatter" log="SSScanSyncWS" filter="Warning" />
      <add name="CourierManifestEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSCourierManifestW" formatter="EventLogTextFormatter" log="SSCourierManifestWS" filter="Warning" />
      <add name="DefaultTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\defaultTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
      <add name="WebPortalTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\webportalTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
      <add name="ShipmentIncomingTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\shipmentIncomingTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
      <add name="CourierManifestTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\courierManifestTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
      <add name="ScanLogSyncTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\scanLogSyncTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollSizeKB="10240" maxArchivedFiles="20" />
      <add name="EnterpriseEventServiceTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\enterpriseEventTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollSizeKB="10240" maxArchivedFiles="20" />
      <add name="AddressValidationServiceTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\addressValidationTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollSizeKB="10240" maxArchivedFiles="20" />
    </listeners>
    <formatters>
      <add type="Microsoft.Practices.EnterpriseLibrary.Logging.Formatters.TextFormatter, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" template="Timestamp: {timestamp}{newline}&#xA;Message: {message}{newline}&#xA;Category: {category}{newline}&#xA;Priority: {priority}{newline}&#xA;EventId: {eventid}{newline}&#xA;Severity: {severity}{newline}&#xA;Title:{title}{newline}&#xA;Machine: {localMachine}{newline}&#xA;App Domain: {localAppDomain}{newline}&#xA;ProcessId: {localProcessId}{newline}&#xA;Process Name: {localProcessName}{newline}&#xA;Thread Name: {threadName}{newline}&#xA;Win32 ThreadId:{win32ThreadId}{newline}&#xA;Extended Properties: {dictionary({key} - {value}{newline})}" name="EventLogTextFormatter" />
      <add type="Microsoft.Practices.EnterpriseLibrary.Logging.Formatters.TextFormatter, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" template="Timestamp: {timestamp}{newline}&#xA;Message: {message}{newline}&#xA;Category: {category}{newline}&#xA;Priority: {priority}{newline}&#xA;EventId: {eventid}{newline}&#xA;Severity: {severity}{newline}&#xA;Title:{title}{newline}&#xA;Machine: {localMachine}{newline}&#xA;App Domain: {localAppDomain}{newline}&#xA;ProcessId: {localProcessId}{newline}&#xA;Process Name: {localProcessName}{newline}&#xA;Thread Name: {threadName}{newline}&#xA;Win32 ThreadId:{win32ThreadId}{newline}&#xA;Extended Properties: {dictionary({key} - {value}{newline})}" name="DebugFormatter" />
    </formatters>
    <categorySources>
      <add switchValue="All" name="General">
        <listeners>
          <add name="SSDefaultEventLog" />
          <add name="DefaultTrace" />
        </listeners>
      </add>
      <add switchValue="All" name="WebPortal">
        <listeners>
          <add name="SSWebPortalEventLog" />
          <add name="WebPortalTrace" />
        </listeners>
      </add>
      <add switchValue="All" name="ShipmentIncoming">
        <listeners>
          <add name="SSIncomingWSEventLog" />
          <add name="ShipmentIncomingTrace" />
        </listeners>
      </add>
      <add switchValue="All" name="CourierManifest">
        <listeners>
          <add name="CourierManifestEventLog" />
          <add name="CourierManifestTrace" />
        </listeners>
      </add>
      <add switchValue="All" name="ScanLogSync">
        <listeners>
          <add name="SSScanSyncWSEventLog" />
          <add name="ScanLogSyncTrace" />
        </listeners>
      </add>
      <add switchValue="All" name="EnterpriseEvent">
        <listeners>
          <add name="SSEventServiceEventLog" />
          <add name="EnterpriseEventServiceTrace" />
        </listeners>
      </add>
      <add switchValue="All" name="AddressValidation">
        <listeners>
          <add name="SSAVServiceEventLog" />
          <add name="AddressValidationServiceTrace" />
        </listeners>
      </add>
    </categorySources>
    <specialSources>
      <allEvents switchValue="All" name="All Events" />
      <notProcessed switchValue="All" name="Unprocessed Category" />
      <errors switchValue="All" name="Logging Errors &amp; Warnings">
        <listeners>
          <add name="SSDefaultEventLog" />
        </listeners>
      </errors>
    </specialSources>
  </loggingConfiguration>
  <appSettings>
    <add key="DataPower_UserName" value="PURO_SMARTSORT_APP" />
    <add key="DataPower_Password" value="PURO_SMARTSORT-PERF" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
  </appSettings>
  <system.serviceModel>
    <client>
      <endpoint address="https://nonprod-soa-gw-01.cpggpc.ca/performance1/SortEvent" binding="wsHttpBinding" bindingConfiguration="DataPowerBinding" contract="SortEventProxy.SortEventPortType" name="DP_SortEvent" />
      <endpoint address="https://nonprod-soa-gw-01.cpggpc.ca/performance1/UpdateAddressEvent" binding="wsHttpBinding" bindingConfiguration="DataPowerBinding" contract="AddressEventProxy.UpdateAddressEventPortType" name="DP_AddressEvent" />
    </client>
    <bindings>
      <wsHttpBinding>
        <binding name="DataPowerBinding" bypassProxyOnLocal="false" transactionFlow="false" openTimeout="00:00:30" closeTimeout="00:00:30" sendTimeout="00:00:30" receiveTimeout="00:00:30" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxReceivedMessageSize="65536" messageEncoding="Text" textEncoding="utf-8" useDefaultWebProxy="true" allowCookies="false">
          <reliableSession ordered="true" inactivityTimeout="00:10:00" enabled="false" />
          <security mode="TransportWithMessageCredential">
            <transport clientCredentialType="None" proxyCredentialType="None" realm="" />
            <message clientCredentialType="UserName" negotiateServiceCredential="false" algorithmSuite="Default" establishSecurityContext="false" />
          </security>
        </binding>
      </wsHttpBinding>
    </bindings>
  </system.serviceModel>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
  </startup>
  <applicationSettings>
    <Purolator.SmartSort.Windows.Services.OFDSender.OFDSenderSettings>
      <setting name="FetchAmount" serializeAs="String">
        <value>100</value>
      </setting>
      <setting name="ProcessPeriodInSeconds" serializeAs="String">
        <value>30</value>
      </setting>
      <setting name="ConcurrentWorkers" serializeAs="String">
        <value>2</value>
      </setting>
    </Purolator.SmartSort.Windows.Services.OFDSender.OFDSenderSettings>
  </applicationSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.EnterpriseLibrary.Common" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
</configuration>