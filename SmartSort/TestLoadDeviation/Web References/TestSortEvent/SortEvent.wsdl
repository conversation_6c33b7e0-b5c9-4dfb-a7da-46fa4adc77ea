<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsp200607="http://www.w3.org/2006/07/ws-policy" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:ns0="http://www.purolator.com/ws/tracking/sortevent/soap/2015/05" xmlns:wsp200409="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap11="http://schemas.xmlsoap.org/wsdl/soap/" targetNamespace="http://www.purolator.com/ws/tracking/sortevent/soap/2015/05" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsp:Policy wsu:Id="policy0" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy">
    <wsp:ExactlyOne>
      <wsp:All>
        <dpe:summary xmlns:sp="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702" xmlns:dpe="http://www.datapower.com/extensions">
          <dppolicy:domain xmlns:dppolicy="http://www.datapower.com/policy">http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702
		</dppolicy:domain>
          <description>Implements WS Security Policy 1.1 - UsernameToken 1.0 and
			1.1 support</description>
        </dpe:summary>
        <sp:SupportingTokens xmlns:sp="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702">
          <sp:UsernameToken sp:IncludeToken="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702/IncludeToken/AlwaysToRecipient">
            <wsp:Policy>
              <sp:WssUsernameToken10 />
            </wsp:Policy>
          </sp:UsernameToken>
        </sp:SupportingTokens>
      </wsp:All>
      <wsp:All>
        <dpe:summary xmlns:sp="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702" xmlns:dpe="http://www.datapower.com/extensions">
          <dppolicy:domain xmlns:dppolicy="http://www.datapower.com/policy">http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702
		</dppolicy:domain>
          <description>Implements WS Security Policy 1.1 - UsernameToken 1.0 and
			1.1 support</description>
        </dpe:summary>
        <sp:SupportingTokens xmlns:sp="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702">
          <sp:UsernameToken sp:IncludeToken="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702/IncludeToken/AlwaysToRecipient">
            <wsp:Policy>
              <sp:WssUsernameToken11 />
            </wsp:Policy>
          </sp:UsernameToken>
        </sp:SupportingTokens>
      </wsp:All>
    </wsp:ExactlyOne>
  </wsp:Policy>
  <wsdl:types xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <xsd:schema>
      <xsd:import schemaLocation="SortEvent.xsd1.xsd" namespace="http://www.purolator.com/ws/tracking/sortevent/2015/05" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="SortEventRequest">
    <wsdl:part xmlns:xsns="http://www.purolator.com/ws/tracking/sortevent/2015/05" name="parameters" element="xsns:SortEventRequest" />
  </wsdl:message>
  <wsdl:message name="SortEventResponse">
    <wsdl:part xmlns:xsns="http://www.purolator.com/ws/tracking/sortevent/2015/05" name="parameters" element="xsns:SortEventResponse" />
  </wsdl:message>
  <wsdl:portType name="SortEventPortType">
    <wsdl:operation name="SortEvent">
      <wsdl:input name="SortEventRequest" message="ns0:SortEventRequest" />
      <wsdl:output name="SortEventResponse" message="ns0:SortEventResponse" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="SortEventSOAP1.2" type="ns0:SortEventPortType">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="SortEvent">
      <soap12:operation soapAction="SortEvent" style="document" />
      <wsdl:input name="SortEventRequest">
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output name="SortEventResponse">
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="SortEventService">
    <wsp:PolicyReference URI="#policy0" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" />
    <wsdl:port name="SortEventPort" binding="ns0:SortEventSOAP1.2">
      <soap12:address location="https://localhost:9090/SortEvent" />
    </wsdl:port>
    <wsdl:port name="SortEventPort.0" binding="ns0:SortEventSOAP1.2">
      <soap12:address location="https://localhost:9090/SortEvent" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>