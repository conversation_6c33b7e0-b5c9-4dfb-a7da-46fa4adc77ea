﻿using System;
using TechTalk.SpecFlow;

namespace TestLoadDeviation
{
    [Binding]
    public class LoadDeviationSteps
    {
        [Given(@"The package is assigned to a truck")]
        public void GivenThePackageIsAssignedToATruck()
        {
            ScenarioContext.Current.Pending();
        }
        
        [When(@"I call the unload package method")]
        public void WhenICallTheUnloadPackageMethod()
        {
            ScenarioContext.Current.Pending();
        }
        
        [When(@"I call the reposition package method")]
        public void WhenICallTheRepositionPackageMethod()
        {
            ScenarioContext.Current.Pending();
        }
        
        [Then(@"The package is not assigned to any route")]
        public void ThenThePackageIsNotAssignedToAnyRoute()
        {
            ScenarioContext.Current.Pending();
        }
        
        [Then(@"The package is re-assigned to the new route")]
        public void ThenThePackageIsRe_AssignedToTheNewRoute()
        {
            ScenarioContext.Current.Pending();
        }
    }
}
