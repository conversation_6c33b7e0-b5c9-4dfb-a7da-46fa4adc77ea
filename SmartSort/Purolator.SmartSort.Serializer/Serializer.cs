﻿using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Serializer
{
    internal class DeserializeData<T>
    {
        private string xmlData;
        protected T _data;

        public DeserializeData()
        {
            xmlData = String.Empty;
        }
        public string XmlData
        {
            get { return this.xmlData; }
            set { this.xmlData = value; }
        }
        public T Data
        {
            get { return this._data; }
            set { this._data = value; }
        }
        /// <summary>
        /// Deserialize Xml data string to T. T is the generic class
        /// </summary>
        /// <returns></returns>
        public T DeserializedData()
        {
            XmlSerializer serializer;
            XmlReader reader;
            try
            {
                serializer = new XmlSerializer(typeof(T));
                reader = XmlReader.Create(new StringReader(this.xmlData));
            }
            catch (Exception)
            { throw; }
            return (T)serializer.Deserialize(reader);
        }
    }
    /// <summary>
    /// T is any object type
    /// </summary>
    /// <typeparam name="T"></typeparam>
    internal class SerializeData<T>
    {
        protected T obj;
        public SerializeData()
        { }
        public T Data
        {
            get { return this.obj; }
            set { this.obj = value; }
        }

        public string SerializedData()
        {
            return SerializedData(null);
        }

        /// <summary>
        /// Serializes generic class T into a Xml data string. This is
        /// used after setting class properties then need to be saved in the
        /// corresponding data table.
        /// </summary>
        /// <returns></returns>
        public string SerializedData(XmlSerializerNamespaces ns)
        {
            StringBuilder sb = new StringBuilder();
            try
            {

                XmlSerializer serializer = new XmlSerializer(typeof(T));
                XmlWriterSettings ws = new XmlWriterSettings();
                ws.OmitXmlDeclaration = true;
                //ws.OmitXmlDeclaration = false;
                ws.CheckCharacters = false;
                ws.Indent = false;
                ws.CloseOutput = false;
                ws.Encoding = Encoding.Default;
                XmlWriter writer = XmlWriter.Create(sb, ws);
                if (ns != null)
                {
                    serializer.Serialize(writer, this.obj, ns);
                }
                else
                {
                    serializer.Serialize(writer, this.obj);
                }
                writer.Close();
            }
            catch (Exception)
            { throw; }
            return sb.ToString().Replace(@"=\", "=");
            //return sb.ToString();
        }
    }

    /// <summary>
    /// Use for serializing business objects to Xml -- Serializer.Serialize
    /// Use for deserializing Xml to business objects -- Serializer.Deserialize
    /// </summary>
    public class Serializer
    {
        public Serializer()
        { }
        /// <summary>
        /// Serialize obj in its current state using T.
        /// Return the Xml representation of the object for d/b update.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="obj"></param>
        /// <returns>string</returns>
        public static string Serialize<T>(T obj)
        {
            SerializeData<T> serializeData = new SerializeData<T>();
            serializeData.Data = obj;
            //return serializeData.SerializedData().Replace(@"=\", "=");
            return serializeData.SerializedData();
        }

        public static string Serialize<T>(T obj, XmlSerializerNamespaces ns)
        {
            SerializeData<T> serializeData = new SerializeData<T>();
            serializeData.Data = obj;
            //return serializeData.SerializedData().Replace(@"=\", "=");
            return serializeData.SerializedData();
        }
        /// <summary>
        /// Deserializes the generated xmlData using T
        /// Returns T object
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="xmlData"></param>
        /// <returns>T</returns>
        public static T Deserialize<T>(string xmlData)
        {
            DeserializeData<T> deserializeData = new DeserializeData<T>();
            deserializeData.XmlData = xmlData;
            return deserializeData.DeserializedData();
        }
    }
    /// <summary>
    /// This class will handle types that need to implement collection
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class DataCollection<T> : IEnumerable<T>
    {
        private List<T> coll = new List<T>();
        public DataCollection()
        { }
        public T this[T t]      //this is the Item property
        {
            get { return (T)t; }
            set { t = value; }
        }
        public void Add(T t)
        {
            this.coll.Add(t);
        }
        public int Count
        {
            get { return this.coll.Count; }
        }
        #region IEnumerable members
        IEnumerator IEnumerable.GetEnumerator()
        {
            return coll.GetEnumerator();
        }
        IEnumerator<T> IEnumerable<T>.GetEnumerator()
        {
            foreach (T t in this.coll)
                yield return t;
        }
        #endregion
    }
}
