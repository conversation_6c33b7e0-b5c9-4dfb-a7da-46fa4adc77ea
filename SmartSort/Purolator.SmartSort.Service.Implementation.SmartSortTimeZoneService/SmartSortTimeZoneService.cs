﻿    using System;
    using System.ServiceModel;
    using System.ServiceModel.Activation;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    //using Purolator.SmartSort.Business.Common.Exception;
    using System.ServiceModel.Web;
    using Purolator.SmartSort.Common;
    using Purolator.SmartSort.Service.MessageContracts;    
    using System.Xml.Linq;    
    using System.Data.Common;    
    using System.Configuration;
    using System.Data;
    using System.Reflection;
    using Entities = Purolator.SmartSort.Business.Entities;
    using Purolator.SmartSort.Data.Access;
    using System.ServiceModel.Description;
    using System.ServiceModel.Dispatcher;   
    using System.Runtime.Serialization.Json;
    using System.ServiceModel.Channels;
    using System.Net;
    using DevTrends.WCFDataAnnotations;
using Purolator.SmartSort.Resources;

    namespace Purolator.SmartSort.Service.Implementation.SmartSortTimeZone
    {
        // NOTE: You can use the "Rename" command on the "Refactor" menu to change the class name "SmartSortScanService" in code, svc and config file together.
        // NOTE: In order to launch WCF Test Client for testing this service, please select SmartSortScanService.svc or SmartSortScanService.svc.cs at the Solution Explorer and start debugging.
        //[ValidateDataAnnotationsBehavior]
        [AspNetCompatibilityRequirements(RequirementsMode = AspNetCompatibilityRequirementsMode.Allowed)]
        [ValidateDataAnnotationsBehavior]
        public class SmartSortTimeZoneService : ISmartSortTimeZoneService
        {
            bool isPinFound;
                      
            [WebGet(RequestFormat = WebMessageFormat.Json,
            ResponseFormat = WebMessageFormat.Json,
            BodyStyle = WebMessageBodyStyle.Bare,
            UriTemplate = "gettimezonesync/terminal={terminal}")]
            public SmartSortGetTimeZoneSyncResponse GetTimeZoneSync(String terminal)
            {
                SmartSortGetTimeZoneSyncResponse resp = new SmartSortGetTimeZoneSyncResponse();

                try
                {

                    resp.Terminal = terminal;

                    resp.Errors = new List<Contracts.Common.ResponseError>();

                    Entities.SmartSortLookup ssLookup = new Entities.SmartSortLookup();
                    Entities.SmartSortTimeZone ssTimeZone = new Entities.SmartSortTimeZone();
                                      
                    ssTimeZone.Terminal = terminal;
                    ssTimeZone.DateTime = DateTime.Now;

                    SmartSortTimeZoneRepository sstzRepo = new SmartSortTimeZoneRepository(Purolator.SmartSort.Common.Constants.DB_CONNECTION_NAME);

                    try
                    {
                        Entities.SmartSortTimeZone sstz = sstzRepo.GetTimeZoneSync(ssTimeZone);
                     
                        if (sstz != null)
                        {
                            if (sstz.TimeZoneOffset != null)
                            {
                                resp.TimeZoneOffset = sstz.TimeZoneOffset;                                
                            }
                        }
                        
                    }
                    catch (Exception ex)
                    {

                        Logger.Error("SmartSortTimeZoneService error", LogCategories.TIME_ZONE_SERVICE, ex);

                        Contracts.Common.ResponseError smartSortTimeZoneError = new Contracts.Common.ResponseError();
                        smartSortTimeZoneError.ErrorMessage = ErrorResources.E4001 + ' ' + ex.Message;
                        smartSortTimeZoneError.ErrorCode = 4001;
                        resp.Errors.Add(smartSortTimeZoneError);
                        return resp;
                    }

                    return resp;

                }
                catch (Exception ex)
                {
                    Logger.Error("SmartSortTimeZoneService error", LogCategories.TIME_ZONE_SERVICE, ex);

                    Contracts.Common.ResponseError smartSortTimeZoneError = new Contracts.Common.ResponseError();
                    smartSortTimeZoneError.ErrorMessage = ErrorResources.E4001 + ' ' + ex.Message;
                    smartSortTimeZoneError.ErrorCode = 4001;
                    resp.Errors.Add(smartSortTimeZoneError);
                    return resp;
                }

            }
        }

          
        public class WebHttpBehaviorEx : WebHttpBehavior
        {

            protected override void AddServerErrorHandlers(ServiceEndpoint endpoint, EndpointDispatcher endpointDispatcher)
            {

                // clear default erro handlers.

                endpointDispatcher.ChannelDispatcher.ErrorHandlers.Clear();

                // add our own error handler.

                endpointDispatcher.ChannelDispatcher.ErrorHandlers.Add(new ErrorHandlerEx());

            }

        }

        public class ErrorHandlerEx : IErrorHandler
        {

            public bool HandleError(Exception error)
            {

                return true;

            }

            public void ProvideFault(Exception error, System.ServiceModel.Channels.MessageVersion version, ref System.ServiceModel.Channels.Message fault)
            {

                // extract the our FaultContract object from the exception object.
                var detail = error.GetType().GetProperty("Message").GetGetMethod().Invoke(error, null);

                Contracts.Common.ResponseError smartSortTimeZoneError = new Contracts.Common.ResponseError();
                smartSortTimeZoneError.ErrorMessage = ErrorResources.E4000 + ':' + error.Message;
                smartSortTimeZoneError.ErrorCode = 4000;

                Logger.Error("SmartSortTimeZoneService error", LogCategories.TIME_ZONE_SERVICE, error);

                // create a fault message containing our FaultContract object
                fault = System.ServiceModel.Channels.Message.CreateMessage(version, "", smartSortTimeZoneError, new DataContractJsonSerializer(smartSortTimeZoneError.GetType()));

                // tell WCF to use JSON encoding rather than default XML
                var wbf = new WebBodyFormatMessageProperty(WebContentFormat.Json);
                fault.Properties.Add(WebBodyFormatMessageProperty.Name, wbf);

                var rmp = new HttpResponseMessageProperty();

                rmp.Headers[HttpResponseHeader.ContentType] = "application/json";
                rmp.Headers["jsonerror"] = "true";

                rmp.StatusCode = System.Net.HttpStatusCode.BadRequest;

                // put appropraite description here..
                rmp.StatusDescription = "See fault object for more information.";

                fault.Properties.Add(HttpResponseMessageProperty.Name, rmp);
            }

        }

        public class MyFactory : WebServiceHostFactory
        {

            public override ServiceHostBase CreateServiceHost(string constructorString, Uri[] baseAddresses)
            {

                var sh = new ServiceHost(typeof(SmartSortTimeZoneService), baseAddresses);

                sh.Description.Endpoints[0].Behaviors.Add(new WebHttpBehaviorEx());

                return sh;

            }

            protected override ServiceHost CreateServiceHost(Type serviceType, Uri[] baseAddresses)
            {

                return base.CreateServiceHost(serviceType, baseAddresses);

            }


        }

  
}
