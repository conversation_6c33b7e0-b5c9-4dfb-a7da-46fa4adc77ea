﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Security.Permissions;
using System.Diagnostics;
using Microsoft.Win32;

namespace EventLogConfigurator
{
    public class EventLogConfigurator
    {
        // Event logs
        private const string EVENT_LOG_DEFAULT = "SSDefault";
        private const string EVENT_LOG_WEB_PORTAL = "SSWebPortal";
        private const string EVENT_LOG_SS_EVENT_INCOMING = "SSIncomingWS";
        private const string EVENT_LOG_SSEVENT_SERVICE = "SSEventService";
        private const string EVENT_LOG_ADDRESS_VALIDATION_SERVICE = "SSAVService";
        private const string COURIER_MANIFEST_SERVICE = "SSCourierManifestWS";
        private const string SCAN_LOG_SERVICE = "SSScanSyncWS";
        private const string PRINT_LOG_SERVICE = "SSPrintService";

        public static string[] EVENT_LOGS = 
        {
            EVENT_LOG_DEFAULT,
            EVENT_LOG_WEB_PORTAL,
            EVENT_LOG_SS_EVENT_INCOMING,
            EVENT_LOG_SSEVENT_SERVICE,
            EVENT_LOG_ADDRESS_VALIDATION_SERVICE,
            COURIER_MANIFEST_SERVICE,
            SCAN_LOG_SERVICE,
            PRINT_LOG_SERVICE
        };

        static void Main(string[] args)
        {
            foreach (string cat in EVENT_LOGS)
            {
                if (!EventLog.Exists(cat))
                {
                    EventLog.CreateEventSource(cat, cat);
                    Console.WriteLine("Creating Event Source for " + cat);
                }
                else
                {
                    Console.WriteLine("Already exist " + cat);
                }
            }            
        }
    }
}

