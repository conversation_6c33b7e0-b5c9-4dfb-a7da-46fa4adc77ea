from distutils.core import setup
import py2exe


py2exe_options = {
    'py2exe': {
        'compressed': 1,
        'optimize': 2,
        'ascii': 1,
        'bundle_files': 1,
        'dist_dir': 'setup',
        'packages': ['encodings'],
        'includes': ['pkg_resources.extern.six',
                     'pkg_resources.extern.six.moves'],
        'excludes': [
            'pywin',
            'pywin.debugger',
            'pywin.debugger.dbgcon',
            'pywin.dialogs',
            'pywin.dialogs.list',
            'Tkconstants',
            'Tkinter',
            'tcl',
        ],
        'dll_excludes': ['w9xpopen.exe', 'MSVCR71.dll']
    }
}


setup(console=['postbuild.py'])
