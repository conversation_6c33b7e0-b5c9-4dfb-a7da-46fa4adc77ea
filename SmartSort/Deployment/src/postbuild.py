# -*- coding: utf-8 -*-
from jinja2 import Environment, PackageLoader, StrictUndefined, Template, DebugUndefined
import os
import sys
import importlib
import argparse
from datetime import datetime

import jinja2


@jinja2.contextfunction
def get_context(c):
    return c


parser = argparse.ArgumentParser()
parser.add_argument("-template", help="The template file to use")
parser.add_argument(
    "-config", help="The config file to generate relative to current path")
parser.add_argument(
    "-env", help="The environment name")


args = parser.parse_args()

# if hasattr(sys, 'frozen'):
#    basis = sys.executable
# else:
#    basis = sys.argv[0]

# current_path = os.path.split(basis)[0]
current_path = os.getcwd()
template_file = args.template or 'web.config'
file_name = args.config or "web.config"

environment = 'local'
if args.env:
    environment = args.env

print "Current path=%s" % current_path
print "Template file=%s" % template_file
print "Config file=%s" % file_name

sys.path.append(current_path)

env = Environment(autoescape=False, loader=PackageLoader('configuration', 'templates'),
                  variable_start_string='#{', variable_end_string='}', undefined=DebugUndefined)
template = env.get_template(template_file)


config = __import__("configuration.%s" %
                    environment, globals(), locals(), ['PUBLIC', 'SECRET'], -1)

with open(file_name, "w") as index_file:
    template.globals['context'] = get_context
    template.globals['callable'] = callable
    config.PUBLIC['__CURRENT_TIME__'] = datetime.now()
    step1 = template.render(config.PUBLIC)
    # Do not ignore errors on last step, report missing properties
    last_step = Template(step1)
    last_step.environment.undefined = StrictUndefined
    last_step.globals['context'] = get_context
    last_step.globals['callable'] = callable
    output = last_step.render(config.SECRET)

    # jinja returns unicode - so `output` needs to be encoded to a bytestring
    # before writing it to a file
    index_file.write(output.encode('utf-8'))
