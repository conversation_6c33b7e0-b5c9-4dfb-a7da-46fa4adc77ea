# -*- coding: utf-8 -*-

PUBLIC = {
    # APP specific (not shared with web)
    'DP_SORT_ENDPOINT'           : 'https://soa-gw-01.cpggpc.ca/SortEvent',
    'DP_ADDRUPDT_ENDPOINT'       : 'https://soa-gw-01.cpggpc.ca/UpdateAddressEvent',                                        
    'DP_AWS_ENDPOINT'            : 'https://soa-gw-01.cpggpc.ca/AddressingWebService/V3/Service.svc',
    'IL_TRACKINGHELPER_ENDPOINT' : 'http://eshipservices.cpggpc.ca/TrackingHelperService/TrackingHelperService.asmx',
    'DPEM_USERNAME'              : 'PURO_SMARTSORT_APP',
    'DPAWS_USERNAME'             : 'PURO_SMARTSORT_APP',
    'IL_USERNAME'                : 'CPG-GPC\\1092-SVC-ILSDSV2-PRD',                  
    'WS_CONCURRENT'              : '5',
	'DEBUG'                      : 'false',
    'HTTP_WSDL'                  : 'false',
    'UNC_PATH'                   : '\\\\*************\\PROD_SMARTSORT_FTP\\PDF',
    'UNC_USER'                   : '1075-SVC-ssadacc-PRD',
    'UNC_DOMAIN'                 : 'CPG-GPC',
    'PICOM_URL'                  : 'https://pd-am.shiptrackapi.com/apiv2/shipment/Create',
	'PREPRINT_SQS_URL'           : 'https://sqs.us-east-1.amazonaws.com/459078032059/infohub-events-prod-master-PrePrint',
	'AWS_KEY'                    : 'AKIAWVYZO3K5VJIBH6OD',
	'EVENTS_SNS_TOPIC'           : 'arn:aws:sns:us-east-1:459078032059:infohub-events-prod-ScanEventsIntake'
}

SECRET = {
    # shared with web app
    'SSDB'           : '###',
    'DPEM_PASSWORD'  : '###',
    'DPAWS_PASSWORD' : '###',
    'IL_PASSWORD'    : '###',
    'UNC_PASSWORD'   : '###',
    'PICOM_APIKEY'   : '###',
	'AWS_PASSWORD'   : '###'
}
