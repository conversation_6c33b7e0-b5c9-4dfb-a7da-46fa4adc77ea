﻿<?xml version="1.0"?>
<!-- 
 !!! DEVELOPER WARNING !!!
 THIS FILE IS AUTOGENERATED ON EVERY BUILD
 IF YOU WANT TO DO CHANGES THE TEMPLATE LOCATION IS: /Deployment/configuration/templates/web.config
 THE TOKENIZED ENVIRONMENT SPECIFIC PROPERTY FILES ARE IN: /Deployment/configuration/
 GENERATED AT: #{ __CURRENT_TIME__ }
-->
<configuration>
	<configSections>
		<section name="loggingConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.LoggingSettings, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="true" />
		<section name="dataConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings, Microsoft.Practices.EnterpriseLibrary.Data" />
		<section name="validation" type="Microsoft.Practices.EnterpriseLibrary.Validation.Configuration.ValidationSettings, Microsoft.Practices.EnterpriseLibrary.Validation" />
	</configSections>
	<!--
    For a description of web.config changes for .NET 4.5 see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.5" />
      </system.Web>
  -->
	<loggingConfiguration name="" tracingEnabled="true" defaultCategory="General">
		<listeners>
			<add name="SSDefaultEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSDefault" formatter="EventLogTextFormatter" log="SSDefault" machineName="." traceOutputOptions="None" filter="Warning" />
			<add name="SSWebPortalEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSWebPortal" formatter="EventLogTextFormatter" log="SSWebPortal" filter="Warning" />
			<add name="SSIncomingWSEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSIncomingWS" formatter="EventLogTextFormatter" log="SSIncomingWS" filter="Warning" />
			<add name="SSEventServiceEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSEventService" formatter="EventLogTextFormatter" log="SSEventService" filter="Warning" />
			<add name="SSAVServiceEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSAVService" formatter="EventLogTextFormatter" log="SSAVService" filter="Error" />
			<add name="SSScanSyncWSEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSScanSyncWS" formatter="EventLogTextFormatter" log="SSScanSyncWS" filter="Warning" />
			<add name="CourierManifestEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSCourierManifestWS" formatter="EventLogTextFormatter" log="SSCourierManifestWS" filter="Warning" />
			<add name="DefaultTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\defaultTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="WebPortalTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\webportalTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="ShipmentIncomingTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\shipmentIncomingTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="CourierManifestTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\courierManifestTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="ScanLogSyncTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\scanLogSyncTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="EnterpriseEventServiceTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\enterpriseEventTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="AddressValidationServiceTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\addressValidationTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollSizeKB="10240" maxArchivedFiles="20" />
		</listeners>
		<formatters>
			<add type="Microsoft.Practices.EnterpriseLibrary.Logging.Formatters.TextFormatter, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" template="Timestamp: {timestamp}{newline}&#xA;Message: {message}{newline}&#xA;Category: {category}{newline}&#xA;Priority: {priority}{newline}&#xA;EventId: {eventid}{newline}&#xA;Severity: {severity}{newline}&#xA;Title:{title}{newline}&#xA;Machine: {localMachine}{newline}&#xA;App Domain: {localAppDomain}{newline}&#xA;ProcessId: {localProcessId}{newline}&#xA;Process Name: {localProcessName}{newline}&#xA;Thread Name: {threadName}{newline}&#xA;Win32 ThreadId:{win32ThreadId}{newline}&#xA;Extended Properties: {dictionary({key} - {value}{newline})}" name="EventLogTextFormatter" />
			<add type="Microsoft.Practices.EnterpriseLibrary.Logging.Formatters.TextFormatter, Microsoft.Practices.EnterpriseLibrary.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" template="Timestamp: {timestamp}{newline}&#xA;Message: {message}{newline}&#xA;Category: {category}{newline}&#xA;Priority: {priority}{newline}&#xA;EventId: {eventid}{newline}&#xA;Severity: {severity}{newline}&#xA;Title:{title}{newline}&#xA;Machine: {localMachine}{newline}&#xA;App Domain: {localAppDomain}{newline}&#xA;ProcessId: {localProcessId}{newline}&#xA;Process Name: {localProcessName}{newline}&#xA;Thread Name: {threadName}{newline}&#xA;Win32 ThreadId:{win32ThreadId}{newline}&#xA;Extended Properties: {dictionary({key} - {value}{newline})}" name="DebugFormatter" />
		</formatters>
		<categorySources>
			<add switchValue="All" name="General">
				<listeners>
					<add name="SSDefaultEventLog" />
					<add name="DefaultTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="WebPortal">
				<listeners>
					<add name="SSWebPortalEventLog" />
					<add name="WebPortalTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="ShipmentIncoming">
				<listeners>
					<add name="SSIncomingWSEventLog" />
					<add name="ShipmentIncomingTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="CourierManifest">
				<listeners>
					<add name="CourierManifestEventLog" />
					<add name="CourierManifestTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="ScanLogSync">
				<listeners>
					<add name="SSScanSyncWSEventLog" />
					<add name="ScanLogSyncTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="EnterpriseEvent">
				<listeners>
					<add name="SSEventServiceEventLog" />
					<add name="EnterpriseEventServiceTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="AddressValidation">
				<listeners>
					<add name="SSAVServiceEventLog" />
					<add name="AddressValidationServiceTrace" />
				</listeners>
			</add>
		</categorySources>
		<specialSources>
			<allEvents switchValue="All" name="All Events" />
			<notProcessed switchValue="All" name="Unprocessed Category" />
			<errors switchValue="All" name="Logging Errors &amp; Warnings">
				<listeners>
					<add name="SSDefaultEventLog" />
				</listeners>
			</errors>
		</specialSources>
	</loggingConfiguration>
	<appSettings>
		<add key="aspnet:UseTaskFriendlySynchronizationContext" value="true" />
		<add key="DataPower_UserName" value="#{DPAWS_USERNAME}" />
		<add key="DataPower_Password" value="#{DPAWS_PASSWORD}" />
		<add key="LOGCATEGORY" value="General" />
		<add key="SmartSortScanLog_ConcurrentWorkers" value="10" />
	</appSettings>
	<system.web>
		<compilation debug="#{DEBUG}" targetFramework="4.5" />
		<httpRuntime targetFramework="4.5" />
	</system.web>

	<system.serviceModel>

		<client>
			<endpoint address="#{DP_AWS_ENDPOINT}" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IAddressLookup" contract="AWSServiceProxy.IAddressLookup" name="DP_IAddressLookup" />
		</client>


		<bindings>

			<!-- Customizations for REST service -->
			<webHttpBinding>
				<!-- Limits set to 10 MB (specified value in bytes) -->
				<binding name="Purolator.SmartSort.Service.Implementation.SmartSortScan.SmartSortScanServiceBinding" maxReceivedMessageSize="1048576000" maxBufferPoolSize="1048576000" maxBufferSize="1048576000" closeTimeout="00:03:00" openTimeout="00:03:00" receiveTimeout="00:10:00" sendTimeout="00:03:00">
					<readerQuotas maxDepth="32" maxStringContentLength="104857600" maxArrayLength="1048576000" maxBytesPerRead="1048576000" />
					<security mode="TransportCredentialOnly">
						<transport clientCredentialType="InheritedFromHost" />
					</security>
				</binding>

				<!-- Limits set to 10 MB (specified value in bytes) -->
				<binding name="Purolator.SmartSort.Service.Implementation.SmartSortLookup.SmartSortLookupServiceBinding" maxReceivedMessageSize="1048576000" maxBufferPoolSize="1048576000" maxBufferSize="1048576000" closeTimeout="00:03:00" openTimeout="00:03:00" receiveTimeout="00:10:00" sendTimeout="00:03:00">
					<readerQuotas maxDepth="32" maxStringContentLength="104857600" maxArrayLength="1048576000" maxBytesPerRead="1048576000" />
					<security mode="TransportCredentialOnly">
						<transport clientCredentialType="InheritedFromHost" />
					</security>
				</binding>

				<!-- Limits set to 10 MB (specified value in bytes) -->
				<binding name="Purolator.SmartSort.Service.Implementation.SmartSortTimeZone.SmartSortTimeZoneServiceBinding" maxReceivedMessageSize="1048576000" maxBufferPoolSize="1048576000" maxBufferSize="1048576000" closeTimeout="00:03:00" openTimeout="00:03:00" receiveTimeout="00:10:00" sendTimeout="00:03:00">
					<readerQuotas maxDepth="32" maxStringContentLength="104857600" maxArrayLength="1048576000" maxBytesPerRead="1048576000" />
					<security mode="TransportCredentialOnly">
						<transport clientCredentialType="InheritedFromHost" />
					</security>
				</binding>
			</webHttpBinding>

			<basicHttpBinding>
				<binding name="BasicHttpBinding_IAddressLookup" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="524288" maxBufferSize="65536" maxReceivedMessageSize="65536" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true" messageEncoding="Text">
					<readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384" />
					<security mode="TransportWithMessageCredential">
						<message clientCredentialType="UserName" algorithmSuite="Default" />
					</security>
				</binding>
				<binding name="BasicHttpEndpointBinding">
					<security mode="TransportCredentialOnly">
						<transport clientCredentialType="InheritedFromHost" />
					</security>
				</binding>
			</basicHttpBinding>


		</bindings>
		<services>
			<service name="Purolator.SmartSort.Service.Implementation.SmartSortScan.SmartSortScanService" behaviorConfiguration="Purolator.SmartSort.Service.Implementation.SmartSortScan.SmartSortScanServiceBehavior">
				<endpoint address="" binding="webHttpBinding" contract="Purolator.SmartSort.Service.Implementation.SmartSortScan.ISmartSortScanService" bindingConfiguration="Purolator.SmartSort.Service.Implementation.SmartSortScan.SmartSortScanServiceBinding" behaviorConfiguration="webHttpBehaviour" />
			</service>


			<service name="Purolator.SmartSort.Service.Implementation.SmartSortLookup.SmartSortLookupService" behaviorConfiguration="Purolator.SmartSort.Service.Implementation.SmartSortLookup.SmartSortLookupServiceBehavior">
				<endpoint address="" binding="webHttpBinding" contract="Purolator.SmartSort.Service.Implementation.SmartSortLookup.ISmartSortLookupService" bindingConfiguration="Purolator.SmartSort.Service.Implementation.SmartSortLookup.SmartSortLookupServiceBinding" behaviorConfiguration="webHttpBehaviour" />
			</service>


			<service name="Purolator.SmartSort.Service.Implementation.SmartSortTimeZone.SmartSortTimeZoneService" behaviorConfiguration="Purolator.SmartSort.Service.Implementation.SmartSortTimeZone.SmartSortTimeZoneServiceBehavior">
				<endpoint address="" binding="webHttpBinding" contract="Purolator.SmartSort.Service.Implementation.SmartSortTimeZone.ISmartSortTimeZoneService" bindingConfiguration="Purolator.SmartSort.Service.Implementation.SmartSortTimeZone.SmartSortTimeZoneServiceBinding" behaviorConfiguration="webHttpBehaviour" />
			</service>


			<service behaviorConfiguration="Purolator.SmartSort.Service.Implementation.ShipmentEvent.ShipmentEventServiceBehavior" name="Purolator.SmartSort.Service.Implementation.ShipmentEvent.ShipmentEventService">
				<endpoint address="" binding="basicHttpBinding" bindingConfiguration="BasicHttpEndpointBinding" contract="Purolator.SmartSort.Service.Contracts.IShipmentEventService" />
			</service>

			<service behaviorConfiguration="Purolator.SmartSort.Service.Implementation.CourierManifest.CourierManifestServiceBehavior" name="Purolator.SmartSort.Service.Implementation.CourierManifest.CourierManifestService">
				<endpoint address="" binding="basicHttpBinding" bindingConfiguration="BasicHttpEndpointBinding" contract="Purolator.SmartSort.Service.Contracts.ICourierManifestService" />
			</service>

		</services>
		<behaviors>
			<endpointBehaviors>
				<behavior name="webHttpBehaviour">
					<webHttp />
				</behavior>
			</endpointBehaviors>

			<serviceBehaviors>

				<behavior name="Purolator.SmartSort.Service.Implementation.SmartSortScan.SmartSortScanServiceBehavior">
					<serviceMetadata httpGetEnabled="true" httpsGetEnabled="true" />
					<serviceDebug includeExceptionDetailInFaults="#{DEBUG}" />
				</behavior>

				<behavior name="Purolator.SmartSort.Service.Implementation.SmartSortLookup.SmartSortLookupServiceBehavior">
					<serviceMetadata httpGetEnabled="true" httpsGetEnabled="true" />
					<serviceDebug includeExceptionDetailInFaults="#{DEBUG}" />
				</behavior>

				<behavior name="Purolator.SmartSort.Service.Implementation.SmartSortTimeZone.SmartSortTimeZoneServiceBehavior">
					<serviceMetadata httpGetEnabled="true" httpsGetEnabled="true" />
					<serviceDebug includeExceptionDetailInFaults="#{DEBUG}" />
				</behavior>

				<behavior name="Purolator.SmartSort.Service.Implementation.ShipmentEvent.ShipmentEventServiceBehavior">
					<serviceMetadata httpGetEnabled="true" httpsGetEnabled="true" />
					<serviceDebug includeExceptionDetailInFaults="#{DEBUG}" />
				</behavior>

				<behavior name="Purolator.SmartSort.Service.Implementation.CourierManifest.CourierManifestServiceBehavior">
					<serviceMetadata httpGetEnabled="true" httpsGetEnabled="true" />
					<serviceDebug includeExceptionDetailInFaults="#{DEBUG}" />
				</behavior>
			</serviceBehaviors>
		</behaviors>
		<protocolMapping>
			<add binding="basicHttpsBinding" scheme="https" />
		</protocolMapping>
		<serviceHostingEnvironment aspNetCompatibilityEnabled="true" multipleSiteBindingsEnabled="true" />
	</system.serviceModel>
	<system.webServer>

		<modules runAllManagedModulesForAllRequests="true">
			<add name="WsdlInterceptionHttpModule" type="Purolator.SmartSort.Service.WSDLInterceptor.WsdlInterceptionHttpModule" />
		</modules>
		<!--
        To browse web app root directory during debugging, set the value below to true.
        Set to false before deployment to avoid disclosing web app folder information.
      -->
		<directoryBrowse enabled="true" />
	</system.webServer>
	<dataConfiguration defaultDatabase="SSDB" />
	<connectionStrings>
		<add name="SSDB" connectionString="#{SSDB}" providerName="System.Data.SqlClient"/>
	</connectionStrings>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Practices.EnterpriseLibrary.Common" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Practices.Unity" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.1.505.0" newVersion="2.1.505.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
</configuration>
