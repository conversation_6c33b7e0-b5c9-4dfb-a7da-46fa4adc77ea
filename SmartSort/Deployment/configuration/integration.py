# -*- coding: utf-8 -*-

PUBLIC = {
    # APP specific (not shared with web)
    'DP_SORT_ENDPOINT'           : 'https://nonprod-soa-gw-01.cpggpc.ca/performance1/SortEvent',
    'DP_ADDRUPDT_ENDPOINT'       : 'https://nonprod-soa-gw-01.cpggpc.ca/performance1/UpdateAddressEvent',
    'DP_AWS_ENDPOINT'            : 'https://nonprod-soa-gw-01.cpggpc.ca/performance1/AddressingWebService/V3/Service.svc',
    'IL_TRACKINGHELPER_ENDPOINT' : 'http://**************/TrackingHelperService/TrackingHelperService.asmx',
    'DPEM_USERNAME'              : 'PURO_SMARTSORT_APP',
    'DPAWS_USERNAME'             : 'SMARTSORT_APP',
    'IL_USERNAME'                : 'CPG-GPC\\1092-SVC-ILWEBU-DEV',
    'WS_CONCURRENT'              : '2',
    'DEBUG'                      : 'true',
    'HTTP_WSDL'                  : 'true',
    'UNC_PATH'                   : '\\\\Inno-5cg5250xlr\\pdf',
    'UNC_USER'                   : 'test',
    'UNC_DOMAIN'                 : 'INNO-5CG5250XLR',
    'PICOM_URL'                  : 'https://pd-am-stg.shiptrackapi.com/apiv2/shipment/Create',
	'PREPRINT_SQS_URL'           : 'https://sqs.us-east-1.amazonaws.com/853215655166/infohub-events-test-master-PrePrint',
	'AWS_KEY'                    : 'AKIA4NJ45WT7PJZWYZKI',
	'EVENTS_SNS_TOPIC'           : 'arn:aws:sns:us-east-1:853215655166:infohub-events-test-ScanEventsIntake'
}

SECRET = {
    # shared with web app
    'SSDB'           : '###',
    'DPEM_PASSWORD'  : '###',
    'DPAWS_PASSWORD' : '###',
    'IL_PASSWORD'    : '###',
    'UNC_PASSWORD'   : '###',
    'PICOM_APIKEY'   : '###',
	'AWS_PASSWORD'   : '###'
}
