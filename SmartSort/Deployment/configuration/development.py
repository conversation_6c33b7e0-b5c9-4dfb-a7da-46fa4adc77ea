# -*- coding: utf-8 -*-

PUBLIC = {
    # APP specific (not shared with web)
    'DP_SORT_ENDPOINT'           : 'https://nonprod-soa-gw-01.cpggpc.ca/performance1/SortEvent',
    'DP_ADDRUPDT_ENDPOINT'       : 'https://nonprod-soa-gw-01.cpggpc.ca/performance1/UpdateAddressEvent',
    'DP_AWS_ENDPOINT'            : 'https://nonprod-soa-gw-01.cpggpc.ca/performance1/AddressingWebService/V3/Service.svc',
    'IL_TRACKINGHELPER_ENDPOINT' : 'http://**************/TrackingHelperService/TrackingHelperService.asmx',
    'DPEM_USERNAME'              : 'PURO_SMARTSORT_APP',
    'DPAWS_USERNAME'             : 'SMARTSORT_APP',
    'IL_USERNAME'                : 'CPG-GPC\\1092-SVC-ILWEBU-DEV',
    'WS_CONCURRENT'              : '2',
    'DEBUG'                      : 'true',
    'HTTP_WSDL'                  : 'true',
    'UNC_PATH'                   : '\\\\Inno-5cg5250xlr\\pdf',
    'UNC_USER'                   : 'test',
    'UNC_DOMAIN'                 : 'INNO-5CG5250XLR',
    'PICOM_URL'                  : 'https://pd-am-stg.shiptrackapi.com/apiv2/shipment/Create',
	'PREPRINT_SQS_URL'           : 'https://sqs.us-east-1.amazonaws.com/853215655166/infohub-events-dev-master-PrePrint',
	'AWS_KEY'                    : '********************',
	'EVENTS_SNS_TOPIC'           : 'arn:aws:sns:us-east-1:853215655166:infohub-events-dev-ScanEventsIntake'
}

SECRET = {
    # shared with web app
    'SSDB'           : 'Data Source= W-00821-D2C\SmartSort,14330;Initial Catalog= SSBE;User ID= smartsortapp;Password=***********;Pooling=true;Max Pool Size=100;Min Pool Size=0;Connection Lifetime=60',
    'DPEM_PASSWORD'  : 'PURO_SMARTSORT-PERF',
    'DPAWS_PASSWORD' : 'SMARTSORT-PERF',
    'IL_PASSWORD'    : 'Resolve456#',
    'UNC_PASSWORD'   : 'Welcome123',
    'PICOM_APIKEY'   : '858f331d-79fb-4927-8cdd-c00b23ab373a',
	'AWS_PASSWORD'   : 'O6i3GXHCYDcN5KJeGtCwHPPw/6M9HP6zaW+P6SvG'
}
