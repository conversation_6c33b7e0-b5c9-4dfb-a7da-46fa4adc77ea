# -*- coding: utf-8 -*-

PUBLIC = {
    # APP specific (not shared with web)
    'DP_SORT_ENDPOINT'           : 'https://nonprod-soa-gw-01.cpggpc.ca/staging1/SortEvent',
    'DP_ADDRUPDT_ENDPOINT'       : 'https://nonprod-soa-gw-01.cpggpc.ca/staging1/UpdateAddressEvent',
    'DP_AWS_ENDPOINT'            : 'https://nonprod-soa-gw-01.cpggpc.ca/staging1/AddressingWebService/V3/Service.svc',
    'IL_TRACKINGHELPER_ENDPOINT' : 'http://**************/TrackingHelperService/TrackingHelperService.asmx',
    'DPEM_USERNAME'              : 'PURO_SMARTSORT_APP',
    'DPAWS_USERNAME'             : 'SMARTSORT_APP',
    'IL_USERNAME'                : 'CPG-GPC\\1092-SVC-ILWEBU-DEV',
    'WS_CONCURRENT'              : '2',
    'DEBUG'                      : 'true',
    'HTTP_WSDL'                  : 'false',
    'UNC_PATH'                   : '\\\\IP-DM-00011-F5B\\CERT_SMARTSORT_FTP\\PDF',
    'UNC_USER'                   : 'test',
    'UNC_DOMAIN'                 : 'CPG-GPC',
    'PICOM_URL'                  : 'https://pd-am-stg.shiptrackapi.com/apiv2/shipment/Create',
	'PREPRINT_SQS_URL'           : 'https://sqs.us-east-1.amazonaws.com/344822470078/infohub-events-stg-master-PrePrint',
	'AWS_KEY'                    : 'AKIAVASIFKW7AC242K3L',
	'EVENTS_SNS_TOPIC'           : 'arn:aws:sns:us-east-1:344822470078:infohub-events-stg-ScanEventsIntake'
}

SECRET = {
    # shared with web app
    'SSDB'           : '###',
    'DPEM_PASSWORD'  : '###',
    'DPAWS_PASSWORD' : '###',
    'IL_PASSWORD'    : '###',
    'UNC_PASSWORD'   : '###',
    'PICOM_APIKEY'   : '###',
	'AWS_PASSWORD'   : '###'
}
