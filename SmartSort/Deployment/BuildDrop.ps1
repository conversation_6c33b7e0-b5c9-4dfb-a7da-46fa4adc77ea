new-item .\BuildDrop -type directory

new-item .\BuildDrop\AWSMunicipalitiesLoader -type directory

new-item .\BuildDrop\Services\CourierManifest\bin -type directory
new-item .\BuildDrop\Services\RestServices -type directory
new-item .\BuildDrop\Services\Root -type directory
new-item .\BuildDrop\Services\ShipmentEvent -type directory
new-item .\BuildDrop\Services\WebServices -type directory

new-item .\BuildDrop\WindowsServices\EMEventSender -type directory
new-item .\BuildDrop\WindowsServices\Amazon -type directory
new-item .\BuildDrop\WindowsServices\AWSValidationService -type directory
new-item .\BuildDrop\WindowsServices\OFDSender -type directory
new-item .\BuildDrop\WindowsServices\RoutePrintingService -type directory

copy-item SmartSort\WebServices\bin\development.ps1 .\BuildDrop\smartsort-dev.ps1
copy-item SmartSort\WebServices\bin\integration.ps1 .\BuildDrop\smartsort-ipt.ps1
copy-item SmartSort\WebServices\bin\staging.ps1 .\BuildDrop\smartsort-stg.ps1
copy-item SmartSort\WebServices\bin\production.ps1 .\BuildDrop\smartsort-prd.ps1

copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\AWSMunicipalityLoader.exe .\BuildDrop\AWSMunicipalitiesLoader
copy-item SmartSort\Deployment\configuration\templates\AWSMunicipalityLoader.exe.config .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Microsoft.Practices.EnterpriseLibrary.Common.dll .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Microsoft.Practices.EnterpriseLibrary.Common.xml .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.dll .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Microsoft.Practices.EnterpriseLibrary.Data.xml .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Microsoft.Practices.EnterpriseLibrary.Logging.dll .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Microsoft.Practices.EnterpriseLibrary.Logging.xml .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Purolator.SmartSort.Business.Common.dll .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Purolator.SmartSort.Business.Entities.dll .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Purolator.SmartSort.ChannelIntegration.Clients.AWS.dll .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Purolator.SmartSort.Common.dll .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Purolator.SmartSort.Data.Access.dll .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Purolator.SmartSort.Serializer.dll .\BuildDrop\AWSMunicipalitiesLoader
copy-item AWSMunicipalities\AWSMunicipalityLoader\bin\Release\Purolator.SmartSort.Service.DataContracts.dll .\BuildDrop\AWSMunicipalitiesLoader

copy-item SmartSort\WebServices\bin\* .\BuildDrop\Services\CourierManifest\bin -recurse -exclude "*.config"
copy-item SmartSort\WebServices\CourierManifest.svc .\BuildDrop\Services\CourierManifest
copy-item SmartSort\Deployment\configuration\templates\web.config .\BuildDrop\Services\CourierManifest

copy-item SmartSort\WebServices\bin\* .\BuildDrop\Services\RestServices\bin -recurse -exclude "*.config"
copy-item SmartSort\WebServices\SmartSortLookupService.svc .\BuildDrop\Services\RestServices
copy-item SmartSort\WebServices\SmartSortScanService.svc .\BuildDrop\Services\RestServices
copy-item SmartSort\WebServices\SmartSortTimeZoneService.svc .\BuildDrop\Services\RestServices
copy-item SmartSort\Deployment\configuration\templates\web.config .\BuildDrop\Services\RestServices

copy-item SmartSort\WebServices\bin\* .\BuildDrop\Services\ShipmentEvent\bin -recurse -exclude "*.config"
copy-item SmartSort\WebServices\ShipmentEvent.svc .\BuildDrop\Services\ShipmentEvent
copy-item SmartSort\Deployment\configuration\templates\web.config .\BuildDrop\Services\ShipmentEvent

copy-item SmartSort\WebServices\bin\* .\BuildDrop\Services\WebServices\bin -recurse -exclude "*.config"
copy-item SmartSort\WebServices\ShipmentEvent.svc .\BuildDrop\Services\WebServices
copy-item SmartSort\WebServices\SmartSortLookupService.svc .\BuildDrop\Services\WebServices
copy-item SmartSort\WebServices\SmartSortScanService.svc .\BuildDrop\Services\WebServices
copy-item SmartSort\WebServices\SmartSortTimeZoneService.svc .\BuildDrop\Services\WebServices
copy-item SmartSort\Deployment\configuration\templates\web.config .\BuildDrop\Services\WebServices
copy-item SmartSort\WebServices\packages.config .\BuildDrop\Services\WebServices
copy-item SmartSort\WebServices\index.html .\BuildDrop\Services\WebServices
copy-item SmartSort\WebServices\empty.txt .\BuildDrop\Services\WebServices
copy-item SmartSort\WebServices\CourierManifest.svc .\BuildDrop\Services\WebServices

copy-item SmartSort\WebServices\index.html .\BuildDrop\Services\Root

copy-item SmartSort\Purolator.SmartSort.Windows.Services.AddressValidationService\bin\Release\Purolator.SmartSort.Windows.Services.AddressValidationService.exe .\BuildDrop\WindowsServices\AWSValidationService
copy-item SmartSort\Deployment\configuration\templates\Purolator.SmartSort.Windows.Services.AddressValidationService.exe.config .\BuildDrop\WindowsServices\AWSValidationService
copy-item SmartSort\Purolator.SmartSort.Windows.Services.AddressValidationService\bin\Release\*.dll .\BuildDrop\WindowsServices\AWSValidationService

copy-item SmartSort\Purolator.SmartSort.Windows.Services.Amazon\bin\Release\Purolator.SmartSort.Windows.Services.Amazon.exe .\BuildDrop\WindowsServices\Amazon
copy-item SmartSort\Deployment\configuration\templates\Purolator.SmartSort.Windows.Services.Amazon.exe.config .\BuildDrop\WindowsServices\Amazon
copy-item SmartSort\Purolator.SmartSort.Windows.Services.Amazon\bin\Release\*.dll .\BuildDrop\WindowsServices\Amazon

copy-item SmartSort\Purolator.SmartSort.Windows.Services.EventSender\bin\Release\Purolator.SmartSort.Windows.Services.EventSender.exe .\BuildDrop\WindowsServices\EMEventSender
copy-item SmartSort\Deployment\configuration\templates\Purolator.SmartSort.Windows.Services.EventSender.exe.config .\BuildDrop\WindowsServices\EMEventSender
copy-item SmartSort\Purolator.SmartSort.Windows.Services.EventSender\bin\Release\*.dll .\BuildDrop\WindowsServices\EMEventSender

copy-item SmartSort\Purolator.SmartSort.Windows.Services.OFDSender\bin\Release\Purolator.SmartSort.Windows.Services.OFDSender.exe .\BuildDrop\WindowsServices\OFDSender
copy-item SmartSort\Deployment\configuration\templates\Purolator.SmartSort.Windows.Services.OFDSender.exe.config .\BuildDrop\WindowsServices\OFDSender
copy-item SmartSort\Purolator.SmartSort.Windows.Services.OFDSender\bin\Release\*.dll .\BuildDrop\WindowsServices\OFDSender

copy-item SmartSort\Purolator.SmartSort.Windows.Service.RoutePrinter\bin\Release\Purolator.SmartSort.Windows.Service.RoutePrinter.exe .\BuildDrop\WindowsServices\RoutePrintingService
copy-item SmartSort\Deployment\configuration\templates\Purolator.SmartSort.Windows.Service.RoutePrinter.exe.config .\BuildDrop\WindowsServices\RoutePrintingService
copy-item SmartSort\Purolator.SmartSort.Windows.Service.RoutePrinter\bin\Release\*.dll .\BuildDrop\WindowsServices\RoutePrintingService

