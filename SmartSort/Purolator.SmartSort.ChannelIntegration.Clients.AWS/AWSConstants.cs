﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace Purolator.SmartSort.ChannelIntegration.Clients.AWS
{
    public class AWSConstants
    {
         public const string SPACE = " ";
         public const string FRENCH = "F";

         public const string ODD_STREET_NUMBERS = "ODD";
         public const string EVEN_STREET_NUMBERS = "EVEN";

         public const string GENERAL_DELIVERY_STRING = "GD";
         public const string PO_BOX_STRING = "PO BOX";
         public const string RURAL_ROUTE_STRING = "RR";
        
    }

    public enum AWSErrorEnum
    {
        [DescriptionAttribute("DetailedValidationIncompleteInput")] DetailedValidationIncompleteInput,
        [DescriptionAttribute("DetailedValidationInvalid")] DetailedValidationInvalid,
        [DescriptionAttribute("DetailedValidationNotCanadian")] DetailedValidationNotCanadian,
        [DescriptionAttribute("DetailedValidationUnparseable")] DetailedValidationUnparseable,
        [DescriptionAttribute("DetailedValidationMunicipalityNotFound")] DetailedValidationMunicipalityNotFound,
        [DescriptionAttribute("DetailedValidationPOBoxInvalid")] DetailedValidationPOBoxInvalid,
        [DescriptionAttribute("DetailedValidationPOBoxMissing")] DetailedValidationPOBoxMissing,
        [DescriptionAttribute("DetailedValidationPOBoxNotFound")] DetailedValidationPOBoxNotFound,
        [DescriptionAttribute("DetailedValidationProvinceInvalid")] DetailedValidationProvinceInvalid,
        [DescriptionAttribute("DetailedValidationRouteNumberInvalid")] DetailedValidationRouteNumberInvalid,
        [DescriptionAttribute("DetailedValidationRouteNumberMissing")] DetailedValidationRouteNumberMissing,
        [DescriptionAttribute("DetailedValidationRouteNumberNotFound")] DetailedValidationRouteNumberNotFound,
        [DescriptionAttribute("DetailedValidationStreetDirectionInvalid")] DetailedValidationStreetDirectionInvalid,
        [DescriptionAttribute("DetailedValidationStreetDirectionMissing")] DetailedValidationStreetDirectionMissing,
        [DescriptionAttribute("DetailedValidationStreetNotFound")] DetailedValidationStreetNotFound,
        [DescriptionAttribute("DetailedValidationStreetNumberInvalid")] DetailedValidationStreetNumberInvalid,
        [DescriptionAttribute("DetailedValidationStreetNumberNotFound")] DetailedValidationStreetNumberNotFound,
        [DescriptionAttribute("DetailedValidationStreetNumberSuffixMissing")] DetailedValidationStreetNumberSuffixMissing,
        [DescriptionAttribute("DetailedValidationStreetNumberSuffixNotFound")] DetailedValidationStreetNumberSuffixNotFound,
        [DescriptionAttribute("DetailedValidationStreetTypeInvalid")] DetailedValidationStreetTypeInvalid,
        [DescriptionAttribute("DetailedValidationStreetTypeMissing")] DetailedValidationStreetTypeMissing,
        [DescriptionAttribute("DetailedValidationSuiteInvalid")] DetailedValidationSuiteInvalid,
        [DescriptionAttribute("DetailedValidationSuiteMissing")] DetailedValidationSuiteMissing,
        [DescriptionAttribute("DetailedValidationSuiteNotFound")] DetailedValidationSuiteNotFound,
        [DescriptionAttribute("DetailedValidationStreetMissing")] DetailedValidationStreetMissing,
        [DescriptionAttribute("DetailedValidationMunicipalityMissing")] DetailedValidationMunicipalityMissing,
        [DescriptionAttribute("DetailedValidationProvinceMissing")] DetailedValidationProvinceMissing,
        [DescriptionAttribute("DetailedValidationPostalCodeInvalid")] DetailedValidationPostalCodeInvalid,
        [DescriptionAttribute("DetailedValidationPostalCodeMissing")] DetailedValidationPostalCodeMissing,
        [DescriptionAttribute("DetailedValidationInvalidInputNumber")] DetailedValidationInvalidInputNumber,
        [DescriptionAttribute("DetailedValidationGDNotFound")] DetailedValidationGDNotFound,
        [DescriptionAttribute("DetailedValidationGDMissing")] DetailedValidationGDMissing,
        [DescriptionAttribute("DetailedValidationMunicipalityAlternateUsed")] DetailedValidationMunicipalityAlternateUsed,
        [DescriptionAttribute("DetailedValidationMunicipalityInvalid")] DetailedValidationMunicipalityInvalid,
        [DescriptionAttribute("DetailedValidationDeliveryInstallationRemoved")] DetailedValidationDeliveryInstallationRemoved,
        [DescriptionAttribute("DetailedValidationMunicipalityAdded")] DetailedValidationMunicipalityAdded,
        [DescriptionAttribute("DetailedValidationPostalCodeAdded")] DetailedValidationPostalCodeAdded,
        [DescriptionAttribute("DetailedValidationPostalCodeChanged")] DetailedValidationPostalCodeChanged,
        [DescriptionAttribute("DetailedValidationProvinceAdded")] DetailedValidationProvinceAdded,
        [DescriptionAttribute("DetailedValidationProvinceChanged")] DetailedValidationProvinceChanged,
        [DescriptionAttribute("DetailedValidationStreetAdded")] DetailedValidationStreetAdded,
        [DescriptionAttribute("DetailedValidationStreetDirectionAdded")] DetailedValidationStreetDirectionAdded,
        [DescriptionAttribute("DetailedValidationStreetDirectionChanged")] DetailedValidationStreetDirectionChanged,
        [DescriptionAttribute("DetailedValidationStreetDirectionRemoved")] DetailedValidationStreetDirectionRemoved,
        [DescriptionAttribute("DetailedValidationStreetNumberAdded")] DetailedValidationStreetNumberAdded,
        [DescriptionAttribute("DetailedValidationStreetNumberChanged")] DetailedValidationStreetNumberChanged,
        [DescriptionAttribute("DetailedValidationStreetNumberRemoved")] DetailedValidationStreetNumberRemoved,
        [DescriptionAttribute("DetailedValidationStreetNumberSuffixAdded")] DetailedValidationStreetNumberSuffixAdded,
        [DescriptionAttribute("DetailedValidationStreetNumberSuffixChanged")] DetailedValidationStreetNumberSuffixChanged,
        [DescriptionAttribute("DetailedValidationStreetNumberSuffixInvalid")] DetailedValidationStreetNumberSuffixInvalid,
        [DescriptionAttribute("DetailedValidationStreetNumberSuffixRemoved")] DetailedValidationStreetNumberSuffixRemoved,
        [DescriptionAttribute("DetailedValidationStreetTypeAdded")] DetailedValidationStreetTypeAdded,
        [DescriptionAttribute("DetailedValidationStreetTypeChanged")] DetailedValidationStreetTypeChanged,
        [DescriptionAttribute("DetailedValidationStreetTypeRemoved")] DetailedValidationStreetTypeRemoved,
        [DescriptionAttribute("DetailedValidationSuiteAdded")] DetailedValidationSuiteAdded,
        [DescriptionAttribute("DetailedValidationSuiteChanged")] DetailedValidationSuiteChanged,
        [DescriptionAttribute("DetailedValidationStreetAlternateUsed")] DetailedValidationStreetAlternateUsed,
        [DescriptionAttribute("DetailedValidationStreetInvalid")] DetailedValidationStreetInvalid,
        [DescriptionAttribute("DetailedValidationStreetRemoved")] DetailedValidationStreetRemoved,
        [DescriptionAttribute("DetailedValidationDeliveryInstallationChanged")] DetailedValidationDeliveryInstallationChanged,
        [DescriptionAttribute("DetailedValidationDeliveryInstallationInvalid")] DetailedValidationDeliveryInstallationInvalid,
        [DescriptionAttribute("DetailedValidationPOBoxAdded")] DetailedValidationPOBoxAdded,
        [DescriptionAttribute("DetailedValidationPOBoxChanged")] DetailedValidationPOBoxChanged,
        [DescriptionAttribute("DetailedValidationRuralAdded")] DetailedValidationRuralAdded,
        [DescriptionAttribute("DetailedValidationRuralChanged")] DetailedValidationRuralChanged,
        [DescriptionAttribute("DetailedValidationRuralRemoved")] DetailedValidationRuralRemoved,
        [DescriptionAttribute("DetailedValidationGDAddrRemoved")] DetailedValidationGDAddrRemoved,
        [DescriptionAttribute("DetailedValidationPOBoxAddrRemoved")] DetailedValidationPOBoxAddrRemoved,
        [DescriptionAttribute("DetailedValidationRuralAddrRemoved")] DetailedValidationRuralAddrRemoved,
        [DescriptionAttribute("DetailedValidationStreetAddrRemoved")] DetailedValidationStreetAddrRemoved,
        [DescriptionAttribute("DetailedValidationStreetWithPOBoxAddrRemoved")] DetailedValidationStreetWithPOBoxAddrRemoved,
        [DescriptionAttribute("DetailedValidationGDAdded")] DetailedValidationGDAdded,
        [DescriptionAttribute("DetailedValidationGDChanged")] DetailedValidationGDChanged,
        [DescriptionAttribute("DetailedValidationGDRemoved")] DetailedValidationGDRemoved,
        [DescriptionAttribute("DetailedValidationSuiteRemoved")] DetailedValidationSuiteRemoved,
        [DescriptionAttribute("DetailedValidationPOBoxRemoved")] DetailedValidationPOBoxRemoved
    }

    public enum AWSStatusCode
    {        
        Valid,
        Invalid,
        ValidRangeOnly,
        Unparsable,
        IncompleteInput,
        NotCanadian
    }
}
