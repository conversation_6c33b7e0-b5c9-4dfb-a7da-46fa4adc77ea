﻿using System;
using System.Net;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Threading.Tasks;
using System.Configuration;
using System.ServiceModel.Channels;
using System.Xml.Serialization;
using Purolator.SmartSort.Common;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.ChannelIntegration.Clients.AWS
{
    public class AWS
    {
        private AWSServiceProxy.AddressLookupClient GetAWSProxy()
        {
            AWSServiceProxy.AddressLookupClient awsProxy = new AWSServiceProxy.AddressLookupClient("DP_IAddressLookup");
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;//force tls1.2
            AppSettingsReader settingsReader = new AppSettingsReader();
            string DATAPOWER_USERNAME = (string)settingsReader.GetValue("DataPower_UserName", typeof(String));
            string DATAPOWER_PASSWORD = (string)settingsReader.GetValue("DataPower_Password", typeof(String));

            awsProxy.ClientCredentials.UserName.UserName = DATAPOWER_USERNAME;
            awsProxy.ClientCredentials.UserName.Password = DATAPOWER_PASSWORD;

            BindingElementCollection elements = awsProxy.Endpoint.Binding.CreateBindingElements();
            elements.Find<SecurityBindingElement>().IncludeTimestamp = false;
            awsProxy.Endpoint.Binding = new CustomBinding(elements);

            return awsProxy;
        }


        public BOResult<ValidateAddressDetailsResult> ValidateAddress(Address address)
        {
            BOResult<ValidateAddressDetailsResult> response = new BOResult<ValidateAddressDetailsResult>();
            try
            {
                BOResult<AWSServiceProxy.Address> parseAndValidateResult = ValidateAWSAddress(address);
                if (parseAndValidateResult.IsSuccess)
                {
                    if (parseAndValidateResult.ReturnObject != null)
                    {
                        response.IsSuccess = true;

                        //Get AWS Parseable Status
                        bool awsParseableStatus = AWSTranslate.DetermineAWSParseable(parseAndValidateResult.ReturnObject);

                        //Get AWS Validation Status
                        bool awsValidationStatus = AWSTranslate.DetermineAWSValidationStatus(parseAndValidateResult.ReturnObject);

                        //Set Validation Status
                        bool localValidationStatus = awsValidationStatus;

                        //Default Alternate Address Flag
                        bool localAlternateAddressInd = false;

                        //If AWS Status is Valid, Determine Alternate Address Status
                        if (awsValidationStatus)
                        {
                            //Determine Corrected Address Status
                            if (parseAndValidateResult.ReturnObject != null)
                            {
                                //Result must be valid and contain a Corrected Address
                                bool alternativeAddressFlag = AWSTranslate.DetermineAWSAlternativeAddressInd(parseAndValidateResult.ReturnObject);
                                if (alternativeAddressFlag)
                                {
                                    //Valid AWS Alternate Address Offerered
                                    localAlternateAddressInd = true;
                                }
                            }
                        }

                        //Translate AWS Corrected Address
                        SuggestedAddress convertedAWSCorrectedAddress = AWSTranslate.ConvertAWSCorrectedAddressToSuggestedAddress(parseAndValidateResult.ReturnObject, address);

                        //Create Return Object for Response
                        ValidateAddressDetailsResult validateAddressDetailsResult = new ValidateAddressDetailsResult();

                        // set the AWS original response
                        validateAddressDetailsResult.AWSStatusCode = (AWSStatusCode) Enum.Parse(typeof(AWSStatusCode), parseAndValidateResult.ReturnObject.StatusCode.ToString());

                        //Set Address Parseable Status
                        validateAddressDetailsResult.IsParseable = awsParseableStatus;

                        //Set Address Validation Status
                        validateAddressDetailsResult.IsValid = localValidationStatus;

                        //Set Alternate Address Flag
                        validateAddressDetailsResult.AlternativeAddressInd = localAlternateAddressInd;

                        //AWS Matching Suggested Address. Set Object even if address is NOT valid.  GUI needs AWS to tell us the address Type.
                        validateAddressDetailsResult.MatchingSuggestedAddress = convertedAWSCorrectedAddress;

                        //Assign Address Details Result Object to the Response
                        response.ReturnObject = validateAddressDetailsResult;

                        //Add Translated AWS Errors to Response
                        Purolator.SmartSort.Business.Common.Exception.SmartSortError[] translatedErrors = AWSTranslate.TranslateAWSErrors(  address,
                                                                                                                                            parseAndValidateResult.ReturnObject,
                                                                                                                                            awsValidationStatus );
                        response.AddErrors(translatedErrors);
                        if (!awsValidationStatus && response.GetErrors().Count == 0)
                        {
                            //Just in case there are no AWS Warning/Error Codes translated into ESO Errors, and the Adderss is Invalid.
                            response.AddError(new Purolator.SmartSort.Business.Common.Exception.SmartSortError(string.Format("No Error Specified by AWS.ParseAndValidateAddress2() for: {0}", AWSTranslate.BuildCompleteAWSAddress(address))));
                        }
                        
                    }
                    else
                    {
                        //Failed Call To AWS Validation
                        response.IsSuccess = false;
                        ValidateAddressDetailsResult failedResult = new ValidateAddressDetailsResult();
                        failedResult.IsParseable = false;
                        failedResult.IsValid = false;
                        failedResult.AlternativeAddressInd = false;
                        failedResult.MatchingSuggestedAddress = null;
                        response.ReturnObject = failedResult;
                        response.AddErrors(parseAndValidateResult.GetErrors());
                    }
                }

            }
            catch (Exception ex)
            {
                Logger.Error("Error in AWS().ValidateAddress()", LogCategories.AWS, ex);
                bool rethrow = true;
                if (rethrow)
                    throw;
            }
            return response;
        }


        /// <summary>
        /// ValidateAWSAddress
        /// </summary>
        /// <param name="criteria">Address</param>
        /// <returns>Returns BOResult containing one AWSServiceProxy.FormattedAddress</returns>
        private BOResult<AWSServiceProxy.Address> ValidateAWSAddress(Address address)
        {
            BOResult<AWSServiceProxy.Address> result = new BOResult<AWSServiceProxy.Address>();
            try
            {
                if (address != null)
                {
                    try
                    {
                        AWSServiceProxy.AddressLookupClient awsProxy = GetAWSProxy();

                        //Initialize Address For Request
                        AWSServiceProxy.AddressInput oneAddressRequest = new AWSServiceProxy.AddressInput();
                        oneAddressRequest.MunicipalityName = address.City;
                        oneAddressRequest.ProvinceCode = address.ProvinceCode;
                        oneAddressRequest.PostalCode = address.PostalCode;
                        oneAddressRequest.AddressLine1 = AWSTranslate.BuildAWSAddressLine1(address);
                        oneAddressRequest.AddressLine2 = AWSTranslate.BuildAWSAddressLine2(address);

                        /*
                        XmlSerializer serializer = new XmlSerializer(typeof(AWSServiceProxy.AddressInput));
                        using (StringWriter textWriter = new StringWriter())
                        {
                            serializer.Serialize(textWriter, oneAddressRequest);
                            Logger.Debug("AWS address request=" + textWriter.ToString(), LogCategories.AWS);                            
                        }
                        */
                        

                        //One Address Request
                        AWSServiceProxy.AddressInput[] requestArray = new AWSServiceProxy.AddressInput[1];
                        requestArray[0] = oneAddressRequest;

                        //Call AWS
                        AWSServiceProxy.Address[] response = awsProxy.ValidateAddress2(requestArray, true);

                        if (response != null && response.Length == 1)
                        {
                            AWSServiceProxy.Address addressResponse = response[0];
                            result.IsSuccess = true;
                            result.ReturnObject = addressResponse;
                        }
                        else
                        {
                            //Unexpected Response Array Size
                            result.IsSuccess = false;
                            result.ReturnObject = null;
                            Logger.Error("Error in AWS().ValidateAWSAddress(): Unexpected Response Array Size", LogCategories.AWS, null );
                        }
                    }
                    catch (Exception ex)
                    {
                        //Do Not Throw Errors For AWS Ecceptions here. Log Error and Continue
                        Logger.Error("Error in AWS().ValidateAWSAddress()", LogCategories.AWS, ex);
                        bool rethrow = true;
                        if (rethrow)
                            throw;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error in AWS().ValidateAWSAddress()", LogCategories.AWS, ex);
                bool rethrow = true;
                if (rethrow)
                    throw;
            }
            return result;
        }

        /// <summary>
        /// Get Postal Code Details
        /// </summary>
        /// <param name="criteria">Criteria contains Postal Code</param>
        /// <returns>Returns BOResult containing AWSSuggestedAddressResult</returns>
        public BOResult<GetSuggestedAddressesResult> GetPostalCodeDetails(string postalCode)
        {
            BOResult<GetSuggestedAddressesResult> result = new BOResult<GetSuggestedAddressesResult>();

            try
            {
                AWSServiceProxy.AddressLookupClient awsProxy = GetAWSProxy();

                string textToLog = string.Format("Postal Code: {0}", postalCode);
                try
                {
                    // Call the AWS Service - GetPostalCodeDetails
                    //if (AWSLogEntry())
                    //{
                        //Only Log Entry For AWS Request, when AWS Logging Function Control is ENABLED
                    //    Helper.LogAWSServicesData("AWS - GetPostalCodeDetails - Request", awsProxy.Endpoint, textToLog);
                    //}

                    string businessName = null;         //Never Supply Business Name for Validation.
                    bool suppressFrenchAccents = true;  //Suppress French Accents in Results from AWS.

                    //Call AWS
                    AWSServiceProxy.PostalCode proxyPostalCode = awsProxy.GetPostalCodeDetails(postalCode, businessName, suppressFrenchAccents);

                    // Call the AWS Service - GetPostalCodeDetails
                    //if (AWSLogEntry())
                    //{
                        //Only Log Entry For AWS Response, when AWS Logging Function Control is ENABLED
                    //Helper.LogAWSServicesData("AWS - GetPostalCodeDetails - Response", awsProxy.Endpoint, proxyPostalCode);
                    //}

                    //Translate AWS AWS PostalCode Return Object into our Suggested Addresses.
                    List<SuggestedAddress> addressList = AWSTranslate.PostalCodeProxy(proxyPostalCode);

                    //Build a GetSuggestedAddressesResult Return Object and set the Cache Flag to true.
                    GetSuggestedAddressesResult returnObject = new GetSuggestedAddressesResult(addressList, true);
                    result.ReturnObject = returnObject;
                    result.IsSuccess = true;
                }
                catch (Exception ex)
                {
                    Logger.Error("Error in AWS().GetPostalCodeDetails()", LogCategories.AWS, ex);
                    bool rethrow = true;
                    if (rethrow)
                        throw;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error in AWS().GetPostalCodeDetails()", LogCategories.AWS, ex);
                bool rethrow = true;
                if (rethrow)
                    throw;
            }
            return result;
        }


        public String GetAlternateMunicipalities()
        {
            try
            {
                AWSServiceProxy.AddressLookupClient awsProxy = GetAWSProxy();
                AWSServiceProxy.MunicipalityNames cities =  awsProxy.GetMunicipalityList(null, null, true);
                MunicipalityNames result = new MunicipalityNames(cities);
                XmlSerializer serializer = new XmlSerializer(typeof(MunicipalityNames));
                XmlSerializerNamespaces ns = new XmlSerializerNamespaces();
                ns.Add("", "");

                StringBuilder stb = new StringBuilder();

                using(StringWriter writer = new StringWriter(stb))
                {
                    serializer.Serialize(writer, result, ns);
                }

                return stb.ToString();
            }
            catch (Exception ex)
            {
                Logger.Error("Error in AWS().GetAlternateMunicipalities()", LogCategories.AWS, ex);
                bool rethrow = true;
                if (rethrow)
                    throw;
            }

            return String.Empty;
        }


    }
}
