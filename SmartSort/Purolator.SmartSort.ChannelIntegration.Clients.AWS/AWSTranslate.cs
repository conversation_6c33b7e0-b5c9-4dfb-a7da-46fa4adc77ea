﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Purolator.SmartSort.Business.Entities;

using Purolator.SmartSort.Common;

namespace Purolator.SmartSort.ChannelIntegration.Clients.AWS
{
    public static class AWSTranslate
    {
        private const string SPACE = " ";
        private const string FRENCH = "F";

        private const string ODD_STREET_NUMBERS = "ODD";
        private const string EVEN_STREET_NUMBERS = "EVEN";

        private const string GENERAL_DELIVERY_STRING = "GD";
        private const string PO_BOX_STRING = "PO BOX";
        private const string RURAL_ROUTE_STRING = "RR";


        public static bool DetermineAWSParseable(AWSServiceProxy.Address proxyAddressResult)
        {
            bool retVal = true;
            if (proxyAddressResult != null)
            {
                switch (proxyAddressResult.StatusCode)
                {
                    case AWSServiceProxy.StatusCode.IncompleteInput:
                        break;
                    case AWSServiceProxy.StatusCode.Invalid:
                        break;
                    case AWSServiceProxy.StatusCode.NotCanadian:
                        break;
                    case AWSServiceProxy.StatusCode.Unparsable:
                        retVal = false;
                        break;
                    case AWSServiceProxy.StatusCode.Valid:
                        break;
                    case AWSServiceProxy.StatusCode.ValidRangeOnly:
                        break;
                }
            }
            return retVal;
        }

        public static bool DetermineAWSValidationStatus(AWSServiceProxy.Address proxyAddressResult)
        {
            bool retVal = true;
            if (proxyAddressResult != null)
            {
                switch (proxyAddressResult.StatusCode)
                {
                    case AWSServiceProxy.StatusCode.IncompleteInput:
                        retVal = false;
                        break;
                    case AWSServiceProxy.StatusCode.Invalid:
                        retVal = false;
                        break;
                    case AWSServiceProxy.StatusCode.NotCanadian:
                        retVal = false;
                        break;
                    case AWSServiceProxy.StatusCode.Unparsable:
                        retVal = false;
                        break;
                    case AWSServiceProxy.StatusCode.Valid:
                        break;
                    case AWSServiceProxy.StatusCode.ValidRangeOnly:
                        break;
                }
            }
            return retVal;
        }

        public static bool DetermineAWSAlternativeAddressInd(AWSServiceProxy.Address proxyAddressResult)
        {
            bool retVal = false;
            if (proxyAddressResult != null)
            {
                AWSServiceProxy.WarningReasonCode[] warningReasonCodeArray = AWSTranslate.TranslateWarningReasonCodeToArray(proxyAddressResult.WarningReasonCodes);
                foreach (AWSServiceProxy.WarningReasonCode warningReasonCode in warningReasonCodeArray)
                {
                    switch (warningReasonCode)
                    {
                        case AWSServiceProxy.WarningReasonCode.BusinessNameAdded:
                            break;
                        case AWSServiceProxy.WarningReasonCode.BusinessNameChanged:
                            break;
                        case AWSServiceProxy.WarningReasonCode.BusinessNameRemoved:
                            break;
                        case AWSServiceProxy.WarningReasonCode.AlternateMunicipalityUsed:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.AlternateStreetUsed:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.DeliveryInstallationChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.DeliveryInstallationRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.FuzzyBusinessNameUsed:
                            break;
                        case AWSServiceProxy.WarningReasonCode.FuzzyMunicipalityUsed:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.FuzzyStreetUsed:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.MunicipalityAdded:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.PostalCodeAdded:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.PostalCodeChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.ProvinceAdded:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.ProvinceChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetAdded:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.SuiteAdded:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.SuiteChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.SuiteRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetDirectionAdded:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetDirectionChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetDirectionRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetNumberAdded:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetNumberChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetNumberRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetNumberSuffixAdded:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetNumberSuffixChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetNumberSuffixRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetTypeAdded:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetTypeChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetTypeRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.MunicipalityChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.POBoxAdded:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.POBoxChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.POBoxRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.RuralRouteAdded:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.RuralRouteChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.RuralRouteRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.GDAddressRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.POBoxAddressRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.RuralAddressRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetAddressRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetWithPOBoxAddressRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.GDAdded:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.GDChanged:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.GDRemoved:
                            retVal = true;
                            break;
                        case AWSServiceProxy.WarningReasonCode.InvalidCharacterRemoved:
                            retVal = true;
                            break;
                    }
                }

                if (!retVal)
                {
                    AWSServiceProxy.InfoReasonCode[] infoReasonCodeArray = AWSTranslate.TranslateInfoReasonCodeToArray(proxyAddressResult.InfoReasonCodes);
                    foreach (AWSServiceProxy.InfoReasonCode infoReasonCode in infoReasonCodeArray)
                    {
                        switch (infoReasonCode)
                        {
                            case AWSServiceProxy.InfoReasonCode.DeliveryInstallationAdded:
                                retVal = true;
                                break;
                        }
                    }
                }
            }
            return retVal;
        }

        public static AWSServiceProxy.InfoReasonCode[] TranslateInfoReasonCodeToArray(AWSServiceProxy.InfoReasonCode code)
        {
            List<AWSServiceProxy.InfoReasonCode> retVal = new List<AWSServiceProxy.InfoReasonCode>();
            if (code.HasFlag(AWSServiceProxy.InfoReasonCode.BuildingFound))
            {
                retVal.Add(AWSServiceProxy.InfoReasonCode.BuildingFound);
            }
            if (code.HasFlag(AWSServiceProxy.InfoReasonCode.BusinessNameFound))
            {
                retVal.Add(AWSServiceProxy.InfoReasonCode.BusinessNameFound);
            }
            if (code.HasFlag(AWSServiceProxy.InfoReasonCode.DeliveryInstallationAdded))
            {
                retVal.Add(AWSServiceProxy.InfoReasonCode.DeliveryInstallationAdded);
            }
            if (code.HasFlag(AWSServiceProxy.InfoReasonCode.FarmFound))
            {
                retVal.Add(AWSServiceProxy.InfoReasonCode.FarmFound);
            }
            if (code.HasFlag(AWSServiceProxy.InfoReasonCode.GovtBuildingFound))
            {
                retVal.Add(AWSServiceProxy.InfoReasonCode.GovtBuildingFound);
            }
            if (code.HasFlag(AWSServiceProxy.InfoReasonCode.LargeVolumeReceiverFound))
            {
                retVal.Add(AWSServiceProxy.InfoReasonCode.LargeVolumeReceiverFound);
            }
            if (code.HasFlag(AWSServiceProxy.InfoReasonCode.MultipleMatchesFound))
            {
                retVal.Add(AWSServiceProxy.InfoReasonCode.MultipleMatchesFound);
            }
            if (code.HasFlag(AWSServiceProxy.InfoReasonCode.StreetAddressMissing))
            {
                retVal.Add(AWSServiceProxy.InfoReasonCode.StreetAddressMissing);
            }
            return retVal.ToArray();
        }

        public static AWSServiceProxy.ErrorReasonCode[] TranslateErrorReasonCodeToArray(AWSServiceProxy.ErrorReasonCode code)
        {
            List<AWSServiceProxy.ErrorReasonCode> retVal = new List<AWSServiceProxy.ErrorReasonCode>();
            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.InvalidInputNumber))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.InvalidInputNumber);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.MunicipalityMissing))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.MunicipalityMissing);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.MunicipalityNotFound))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.MunicipalityNotFound);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.POBoxInvalid))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.POBoxInvalid);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.POBoxMissing))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.POBoxMissing);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.POBoxNotFound))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.POBoxNotFound);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.ProvinceInvalid))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.ProvinceInvalid);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.RuralRouteInvalid))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.RuralRouteInvalid);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.RuralRouteMissing))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.RuralRouteMissing);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.RuralRouteNotFound))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.RuralRouteNotFound);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.StreetDirectionInvalid))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.StreetDirectionInvalid);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.StreetDirectionMissing))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.StreetDirectionMissing);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.StreetNotFound))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.StreetNotFound);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.StreetNumberInvalid))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.StreetNumberInvalid);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.StreetNumberNotFound))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.StreetNumberNotFound);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.StreetNumberSuffixMissing))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.StreetNumberSuffixMissing);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.StreetNumberSuffixNotFound))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.StreetNumberSuffixNotFound);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.StreetTypeInvalid))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.StreetTypeInvalid);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.StreetTypeMissing))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.StreetTypeMissing);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.SuiteInvalid))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.SuiteInvalid);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.SuiteMissing))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.SuiteMissing);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.SuiteNotFound))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.SuiteNotFound);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.StreetMissing))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.StreetMissing);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.ProvinceMissing))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.ProvinceMissing);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.PostalCodeInvalid))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.PostalCodeInvalid);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.PostalCodeMissing))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.PostalCodeMissing);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.GDMissing))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.GDMissing);
            }

            if (code.HasFlag(AWSServiceProxy.ErrorReasonCode.GDNotFound))
            {
                retVal.Add(AWSServiceProxy.ErrorReasonCode.GDNotFound);
            }

            return retVal.ToArray();
        }

        public static AWSServiceProxy.WarningReasonCode[] TranslateWarningReasonCodeToArray(AWSServiceProxy.WarningReasonCode code)
        {
            List<AWSServiceProxy.WarningReasonCode> retVal = new List<AWSServiceProxy.WarningReasonCode>();

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.SuiteAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.SuiteAdded);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.SuiteChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.SuiteChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.AlternateMunicipalityUsed))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.AlternateMunicipalityUsed);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.AlternateStreetUsed))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.AlternateStreetUsed);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.BusinessNameAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.BusinessNameAdded);
            }


            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.BusinessNameChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.BusinessNameChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.BusinessNameRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.BusinessNameRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.DeliveryInstallationChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.DeliveryInstallationChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.DeliveryInstallationRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.DeliveryInstallationRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.FuzzyBusinessNameUsed))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.FuzzyBusinessNameUsed);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.FuzzyMunicipalityUsed))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.FuzzyMunicipalityUsed);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.FuzzyStreetUsed))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.FuzzyStreetUsed);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.MunicipalityAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.MunicipalityAdded);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.MunicipalityChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.MunicipalityChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.PostalCodeAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.PostalCodeAdded);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.PostalCodeChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.PostalCodeChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.ProvinceAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.ProvinceAdded);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.ProvinceChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.ProvinceChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetAdded);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetDirectionAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetDirectionAdded);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetDirectionChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetDirectionChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetDirectionRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetDirectionRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetNumberAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetNumberAdded);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetNumberChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetNumberChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetNumberRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetNumberRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetNumberSuffixAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetNumberSuffixAdded);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetNumberSuffixChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetNumberSuffixChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetNumberSuffixRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetNumberSuffixRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetTypeAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetTypeAdded);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetTypeChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetTypeChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetTypeRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetTypeRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.SuiteRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.SuiteRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.POBoxAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.POBoxAdded);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.POBoxChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.POBoxChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.POBoxRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.POBoxRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.RuralRouteAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.RuralRouteAdded);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.RuralRouteChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.RuralRouteChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.RuralRouteRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.RuralRouteRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.GDAddressRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.GDAddressRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.POBoxAddressRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.POBoxAddressRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.RuralAddressRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.RuralAddressRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetAddressRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetAddressRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.StreetWithPOBoxAddressRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.StreetWithPOBoxAddressRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.GDAdded))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.GDAdded);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.GDChanged))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.GDChanged);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.GDRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.GDRemoved);
            }

            if (code.HasFlag(AWSServiceProxy.WarningReasonCode.InvalidCharacterRemoved))
            {
                retVal.Add(AWSServiceProxy.WarningReasonCode.InvalidCharacterRemoved);
            }

            return retVal.ToArray();
        }


        public static List<SuggestedAddress> PostalCodeProxy(AWSServiceProxy.PostalCode postalCodeProxyObj)
        {
            List<SuggestedAddress> retVal = new List<SuggestedAddress>();
            if (postalCodeProxyObj != null)
            {
                if (postalCodeProxyObj.Municipality != null && postalCodeProxyObj.Municipality.Length > 0)
                {
                    foreach (AWSServiceProxy.Municipality municipalityObj in postalCodeProxyObj.Municipality)
                    {
                        if (municipalityObj.Streets != null && municipalityObj.Streets.Length > 0)
                        {
                            retVal.AddRange(AWSTranslate.Streets(postalCodeProxyObj, municipalityObj));
                        }
                        if (municipalityObj.GeneralDeliveries != null && municipalityObj.GeneralDeliveries.Length > 0)
                        {
                            retVal.AddRange(AWSTranslate.GeneralDeliveries(postalCodeProxyObj, municipalityObj));
                        }
                        if (municipalityObj.POBoxes != null)
                        {
                            retVal.AddRange(AWSTranslate.PoBoxes(postalCodeProxyObj, municipalityObj));
                        }
                        if (municipalityObj.RuralRoutes != null)
                        {
                            retVal.AddRange(AWSTranslate.RuralRoutes(postalCodeProxyObj, municipalityObj));
                        }
                    }
                }
            }
            return retVal;
        }

        private static SuggestedAddress InitializeSuggestedAddress(SuggestedAddress.AddressTypes addressType, AWSServiceProxy.PostalCode postalCodeObj, AWSServiceProxy.Municipality municipalityObj)
        {
            SuggestedAddress retVal = new SuggestedAddress();
            retVal.AddressType = addressType;
            retVal.ProvinceCode = postalCodeObj.ProvinceCode;
            retVal.PostalCode = postalCodeObj.Value;
            retVal.City = Helpers.RemoveDiacritics(municipalityObj.Name); //Remove Accents for AWS City Names
            retVal.StreetNumber = string.Empty;
            retVal.ToStreetNumber = string.Empty;
            retVal.FromStreetNumber = string.Empty;
            retVal.StreetNumberSequenceType = SuggestedAddress.StreetNumberSequence.All;
            retVal.FromStreetNumberSuffix = string.Empty;
            retVal.ToStreetNumberSuffix = string.Empty;
            retVal.StreetName = string.Empty;
            retVal.StreetType = string.Empty;
            retVal.FromSuiteNumber = string.Empty;
            retVal.ToSuiteNumber = string.Empty;
            retVal.Floor = string.Empty;
            retVal.Suite = string.Empty;
            retVal.StreetDirection = string.Empty;
            retVal.StreetNumberSuffix = string.Empty;
            retVal.StreetAddress2 = string.Empty;
            retVal.StreetAddress3 = string.Empty;
            return retVal;
        }

        private static List<SuggestedAddress> PoBoxes(AWSServiceProxy.PostalCode postalCodeObj, AWSServiceProxy.Municipality municipalityObj)
        {
            List<SuggestedAddress> retVal = new List<SuggestedAddress>();
            if (municipalityObj != null)
            {
                if (municipalityObj.POBoxes != null)
                {
                    //Init Base PO Box Address
                    SuggestedAddress basePoBoxAddress = InitializeSuggestedAddress(SuggestedAddress.AddressTypes.PostOfficeBox, postalCodeObj, municipalityObj);
                    basePoBoxAddress.StreetAddress2 = FormatInstallationInfo(municipalityObj.Name, municipalityObj.POBoxes.InstallationArea, municipalityObj.POBoxes.InstallationTypeCode, municipalityObj.POBoxes.InstallationName);

                    //PO Box Number(s)
                    if (municipalityObj.POBoxes.Number != null && municipalityObj.POBoxes.Number.Length > 0)
                    {
                        foreach (AWSServiceProxy.POBoxNumber onePOBoxNumber in municipalityObj.POBoxes.Number)
                        {
                            retVal.Add(POBoxNumber(basePoBoxAddress, onePOBoxNumber));
                        }
                    }

                    //POBox Number Ranges
                    if (municipalityObj.POBoxes.NumberRange != null && municipalityObj.POBoxes.NumberRange.Length > 0)
                    {
                        foreach (AWSServiceProxy.POBoxNumberRange onePOBoxNumberRange in municipalityObj.POBoxes.NumberRange)
                        {
                            retVal.AddRange(AWSTranslate.POBoxNumberRange(basePoBoxAddress, onePOBoxNumberRange));
                        }
                    }
                }
            }
            return retVal;
        }

        private static SuggestedAddress POBoxNumber(SuggestedAddress basePoBoxAddres, AWSServiceProxy.POBoxNumber poBoxNumber)
        {

            SuggestedAddress retVal = null;
            if (basePoBoxAddres != null)
            {
                if (poBoxNumber != null)
                {
                    retVal = OnePoBox(basePoBoxAddres, poBoxNumber.Value);
                }
            }
            return retVal;
        }

        private static List<SuggestedAddress> POBoxNumberRange(SuggestedAddress basePOBoxAddress, AWSServiceProxy.POBoxNumberRange poBoxNumberRange)
        {
            List<SuggestedAddress> retVal = new List<SuggestedAddress>();
            if (basePOBoxAddress != null)
            {
                if (poBoxNumberRange != null)
                {
                    int startBox = poBoxNumberRange.From;
                    int endBox = poBoxNumberRange.To;
                    for (int i = startBox; i <= endBox; i++)
                    {
                        retVal.Add(OnePoBox(basePOBoxAddress, i));
                    }
                }
            }
            return retVal;
        }

        private static SuggestedAddress OnePoBox(SuggestedAddress basePOBoxAddress, int poBoxNumber)
        {
            SuggestedAddress retVal = null;
            if (basePOBoxAddress != null)
            {
                if (poBoxNumber > 0)
                {
                    retVal = basePOBoxAddress.Clone();

                    //PO BOX Street Name
                    retVal.StreetName = FormatPOBoxStreetName(poBoxNumber);
                }
            }
            return retVal;
        }

        private static List<SuggestedAddress> RuralRoutes(AWSServiceProxy.PostalCode postalCodeObj, AWSServiceProxy.Municipality municipalityObj)
        {
            List<SuggestedAddress> retVal = new List<SuggestedAddress>();
            if (municipalityObj != null)
            {
                if (municipalityObj.RuralRoutes != null)
                {
                    SuggestedAddress baseRuralRouteAddress = InitializeSuggestedAddress(SuggestedAddress.AddressTypes.RuralRoute, postalCodeObj, municipalityObj);
                    baseRuralRouteAddress.StreetAddress2 = FormatInstallationInfo(municipalityObj.Name, municipalityObj.RuralRoutes.InstallationArea, municipalityObj.RuralRoutes.InstallationTypeCode, municipalityObj.RuralRoutes.InstallationName);

                    foreach (AWSServiceProxy.RuralRoute oneRuralRoute in municipalityObj.RuralRoutes.Route)
                    {
                        retVal.Add(RuralRoute(baseRuralRouteAddress, oneRuralRoute));
                    }
                }
            }
            return retVal;
        }

        private static SuggestedAddress RuralRoute(SuggestedAddress baseRuralRouteAddress, AWSServiceProxy.RuralRoute ruralRoute)
        {
            SuggestedAddress retVal = null;
            if (baseRuralRouteAddress != null)
            {
                if (ruralRoute != null)
                {
                    retVal = baseRuralRouteAddress.Clone();
                    //Set Rural Route Street Name
                    retVal.StreetName = FormatRuralRouteStreetName(ruralRoute.DeliveryMode, ruralRoute.DeliveryNumber);
                }
            }
            return retVal;
        }

        private static List<SuggestedAddress> GeneralDeliveries(AWSServiceProxy.PostalCode postalCodeObj, AWSServiceProxy.Municipality municipalityObj)
        {
            List<SuggestedAddress> retVal = new List<SuggestedAddress>();
            if (municipalityObj != null)
            {
                if (municipalityObj.GeneralDeliveries != null && municipalityObj.GeneralDeliveries.Length > 0)
                {
                    foreach (AWSServiceProxy.GeneralDelivery oneGeneralDelivery in municipalityObj.GeneralDeliveries)
                    {
                        retVal.Add(GeneralDelivery(municipalityObj.Name, postalCodeObj, municipalityObj, oneGeneralDelivery));
                    }
                }
            }
            return retVal;
        }

        private static SuggestedAddress GeneralDelivery(string municipalityName,
                                                        AWSServiceProxy.PostalCode postalCodeObj, AWSServiceProxy.Municipality municipalityObj, AWSServiceProxy.GeneralDelivery generalDelivery)
        {
            SuggestedAddress retVal = null;
            if (generalDelivery != null)
            {
                retVal = InitializeSuggestedAddress(SuggestedAddress.AddressTypes.GeneralDelivery, postalCodeObj, municipalityObj);

                //Street Name
                retVal.StreetName = FormatGeneralDeliveryStreetName(generalDelivery.DeliveryMode);

                //Address2
                retVal.StreetAddress2 = FormatGeneralDeliveryInstallationInfo(municipalityName, generalDelivery);
            }
            return retVal;
        }

        private static List<SuggestedAddress> Streets(AWSServiceProxy.PostalCode postalCodeObj, AWSServiceProxy.Municipality municipalityObj)
        {
            List<SuggestedAddress> retVal = new List<SuggestedAddress>();
            if (municipalityObj != null)
            {
                if (municipalityObj.Streets != null && municipalityObj.Streets.Length > 0)
                {
                    foreach (AWSServiceProxy.Street oneStreet in municipalityObj.Streets)
                    {
                        retVal.AddRange(Street(postalCodeObj, municipalityObj, oneStreet));
                    }
                }
            }
            return retVal;
        }

        private static List<SuggestedAddress> Street(AWSServiceProxy.PostalCode postalCodeObj, AWSServiceProxy.Municipality municipalityObj, AWSServiceProxy.Street streetObj)
        {
            List<SuggestedAddress> retVal = new List<SuggestedAddress>();
            if (streetObj != null)
            {
                //Street Numbers
                if (streetObj.Number != null && streetObj.Number.Length > 0)
                {
                    foreach (AWSServiceProxy.StreetNumber oneStreetNumber in streetObj.Number)
                    {
                        retVal.AddRange(StreetNumber(postalCodeObj, municipalityObj, streetObj, oneStreetNumber));
                    }
                }

                //Street Ranges
                if (streetObj.NumberRange != null && streetObj.NumberRange.Length > 0)
                {
                    foreach (AWSServiceProxy.StreetNumberRange oneStreetNumberRange in streetObj.NumberRange)
                    {
                        retVal.Add(StreetNumberRange(postalCodeObj, municipalityObj, streetObj, oneStreetNumberRange));
                    }
                }
            }
            return retVal;
        }


        private static List<SuggestedAddress> StreetNumber(AWSServiceProxy.PostalCode postalCodeObj,
                                                                                AWSServiceProxy.Municipality municipalityObj,
                                                                                AWSServiceProxy.Street streetObj,
                                                                                AWSServiceProxy.StreetNumber streetNumberObj)
        {
            List<SuggestedAddress> retVal = new List<SuggestedAddress>();
            if (streetNumberObj != null)
            {
                SuggestedAddress streetNumberAddress = InitializeSuggestedAddress(SuggestedAddress.AddressTypes.Street, postalCodeObj, municipalityObj);

                if (Helpers.IsInteger(streetNumberObj.Number))
                {
                    if (Helpers.GetInteger(streetNumberObj.Number) > 0)
                    {
                        streetNumberAddress.FromStreetNumber = Helpers.NullToEmptyString(streetNumberObj.Number);
                        streetNumberAddress.ToStreetNumber = Helpers.NullToEmptyString(streetNumberObj.Number);
                        streetNumberAddress.StreetNumber = Helpers.NullToEmptyString(streetNumberObj.Number);
                    }
                }

                streetNumberAddress.StreetNumberSuffix = Helpers.NullToEmptyString(streetNumberObj.Suffix);
                streetNumberAddress.StreetName = Helpers.NullToEmptyString(streetObj.Name);
                streetNumberAddress.StreetType = Helpers.RemoveDiacritics(Helpers.NullToEmptyString(streetObj.Type));
                streetNumberAddress.StreetDirection = Helpers.NullToEmptyString(streetObj.Direction);

                //Suites
                if (streetNumberObj.Suites != null && streetNumberObj.Suites.Length > 0)
                {
                    foreach (AWSServiceProxy.Suite oneSuiteObj in streetNumberObj.Suites)
                    {
                        retVal.Add(BuildOneSuite(streetNumberAddress, oneSuiteObj.Value, null, null));
                    }
                }
                //Suite Ranges
                if (streetNumberObj.SuiteRanges != null && streetNumberObj.SuiteRanges.Length > 0)
                {
                    foreach (AWSServiceProxy.SuiteRange oneSuiteRangeObj in streetNumberObj.SuiteRanges)
                    {
                        retVal.AddRange(SuiteRange(streetNumberAddress, oneSuiteRangeObj));
                    }
                }

                //If No Suites...Add Base Address
                if (retVal.Count == 0)
                {
                    //Add the Main Address to Result
                    retVal.Add(streetNumberAddress);
                }
            }
            return retVal;
        }

        private static List<SuggestedAddress> SuiteRange( SuggestedAddress streetNumberAddress, AWSServiceProxy.SuiteRange suiteRangeObj)
        {
            List<SuggestedAddress> retVal = new List<SuggestedAddress>();
            if (suiteRangeObj != null)
            {
                retVal.Add(BuildOneSuite(streetNumberAddress, null, suiteRangeObj.From, suiteRangeObj.To));
            }
            return retVal;
        }


        private static SuggestedAddress BuildOneSuite( SuggestedAddress streetNumberAddress,
                                                                            string suiteNumber,
                                                                            string fromSuiteNumber,
                                                                            string toSuiteNumber)
        {
            SuggestedAddress retVal = null;
            if (suiteNumber.ValueExists() || fromSuiteNumber.ValueExists() || toSuiteNumber.ValueExists())
            {
                retVal = streetNumberAddress.Clone();
                if (suiteNumber.ValueExists())
                {
                    retVal.FromSuiteNumber = Helpers.NullToEmptyString(suiteNumber);
                    retVal.ToSuiteNumber = Helpers.NullToEmptyString(suiteNumber);
                    retVal.Suite = Helpers.NullToEmptyString(suiteNumber);
                }
                else
                {
                    retVal.FromSuiteNumber = Helpers.NullToEmptyString(fromSuiteNumber);
                    retVal.ToSuiteNumber = Helpers.NullToEmptyString(toSuiteNumber);
                    if (Helpers.StringCompare(retVal.FromSuiteNumber, retVal.ToSuiteNumber, true))
                    {
                        retVal.Suite = Helpers.NullToEmptyString(retVal.FromSuiteNumber);
                    }
                }
            }
            return retVal;
        }

        private static SuggestedAddress StreetNumberRange(AWSServiceProxy.PostalCode postalCodeObj,
                                                                             AWSServiceProxy.Municipality municipalityObj,
                                                                             AWSServiceProxy.Street streetObj,
                                                                             AWSServiceProxy.StreetNumberRange streetNumberRangeObj)
        {
            SuggestedAddress retVal = null;
            if (streetNumberRangeObj != null)
            {
                retVal = InitializeSuggestedAddress(SuggestedAddress.AddressTypes.Street, postalCodeObj, municipalityObj);

                if (streetNumberRangeObj.NumberFrom > 0)
                {
                    retVal.FromStreetNumber = streetNumberRangeObj.NumberFrom.ToString();
                }

                if (streetNumberRangeObj.NumberTo > 0)
                {
                    retVal.ToStreetNumber = streetNumberRangeObj.NumberTo.ToString();
                }

                if (streetNumberRangeObj.NumberFrom == streetNumberRangeObj.NumberTo)
                {
                    if (streetNumberRangeObj.NumberFrom > 0)
                    {
                        retVal.StreetNumber = streetNumberRangeObj.NumberFrom.ToString();
                    }
                }

                if (Helpers.StringCompare(streetNumberRangeObj.Type, ODD_STREET_NUMBERS, true))
                {
                    retVal.StreetNumberSequenceType = SuggestedAddress.StreetNumberSequence.Odd;
                }
                else if (Helpers.StringCompare(streetNumberRangeObj.Type, EVEN_STREET_NUMBERS, true))
                {
                    retVal.StreetNumberSequenceType = SuggestedAddress.StreetNumberSequence.Even;
                }
                else
                {
                    retVal.StreetNumberSequenceType = SuggestedAddress.StreetNumberSequence.All;
                }

                retVal.FromStreetNumberSuffix = Helpers.NullToEmptyString(streetNumberRangeObj.SuffixFrom);
                retVal.ToStreetNumberSuffix = Helpers.NullToEmptyString(streetNumberRangeObj.SuffixTo);
                if (Helpers.StringCompare(streetNumberRangeObj.SuffixFrom, streetNumberRangeObj.SuffixTo, true))
                {
                    retVal.StreetNumberSuffix = Helpers.NullToEmptyString(streetNumberRangeObj.SuffixFrom);
                }
                else
                {
                    retVal.StreetNumberSuffix = string.Empty;
                }

                retVal.StreetName = Helpers.NullToEmptyString(streetObj.Name);
                retVal.StreetType = Helpers.RemoveDiacritics(Helpers.NullToEmptyString(streetObj.Type));
                retVal.StreetDirection = Helpers.NullToEmptyString(streetObj.Direction);
            }
            return retVal;
        }

        public static string FormatPOBoxStreetName(int poBoxNumber)
        {
            string retVal = string.Empty;
            if (poBoxNumber > 0)
            {
                retVal = string.Format("{0} {1}", PO_BOX_STRING, poBoxNumber.ToString());
            }
            else
            {
                retVal = PO_BOX_STRING;
            }
            return retVal;
        }

        public static string FormatPOBoxStreetNameForSorting(int poBoxNumber)
        {
            //This Method is only used to Fix Sorting Of Po Boxes
            string retVal = string.Empty;
            if (poBoxNumber > 0)
            {
                retVal = string.Format("{0} {1}", PO_BOX_STRING, poBoxNumber.ToString().PadLeft(6, '0'));
            }
            else
            {
                retVal = PO_BOX_STRING;
            }
            return retVal;
        }

        public static string FormatRuralRouteStreetName(string deliveryMode, int deliveryNumber)
        {
            string retVal = string.Empty;
            if (deliveryNumber > 0)
            {
                if (deliveryMode.ValueExists())
                {
                    retVal = string.Format("{0} {1}", deliveryMode, deliveryNumber.ToString());
                }
                else
                {
                    retVal = string.Format("{0} {1}", RURAL_ROUTE_STRING, deliveryNumber.ToString());
                }
            }
            else
            {
                retVal = RURAL_ROUTE_STRING;
            }
            return retVal;
        }

        public static string FormatRuralRouteStreetNameForSorting(string deliveryMode, int deliveryNumber)
        {
            //This Method is only used to Fix Sorting Of RR
            string retVal = string.Empty;
            if (deliveryNumber > 0)
            {
                if (deliveryMode.ValueExists())
                {
                    retVal = string.Format("{0} {1}", deliveryMode, deliveryNumber.ToString().PadLeft(6, '0'));
                }
                else
                {
                    retVal = string.Format("{0} {1}", RURAL_ROUTE_STRING, deliveryNumber.ToString().PadLeft(6, '0'));
                }
            }
            else
            {
                retVal = RURAL_ROUTE_STRING;
            }
            return retVal;
        }

        public static string FormatGeneralDeliveryStreetName(string deliveryMode)
        {
            string retVal = string.Empty;
            if (deliveryMode.ValueExists())
            {
                retVal = deliveryMode.Trim();
            }
            else
            {
                if (retVal.Length > 0) retVal += SPACE;
                retVal += GENERAL_DELIVERY_STRING;
            }
            return retVal;
        }

        private static string FormatGeneralDeliveryInstallationInfo(    string municipalityName,
                                                                        AWSServiceProxy.GeneralDelivery generalDelivery)
        {
            if (generalDelivery != null)
            {
                return FormatInstallationInfo(  municipalityName,
                                                generalDelivery.InstallationArea,
                                                generalDelivery.InstallationTypeCode,
                                                generalDelivery.InstallationName);
            }
            else
                return string.Empty;
        }

        public static string FormatGeneralDeliveryInstallationInfo(string municipalityName,
                                                                    AWSServiceProxy.GeneralDeliveryAddressType generalDelivery)
        {
            if (generalDelivery != null)
            {
                return FormatInstallationInfo   (municipalityName,
                                                generalDelivery.InstallationArea,
                                                generalDelivery.InstallationTypeCode,
                                                generalDelivery.InstallationName);
            }
            else
                return string.Empty;
        }

        public static string FormatPoBoxWithStreetInstallationInfo( string municipalityName,
                                                                    AWSServiceProxy.POBoxWithStreetAddressType poBoxWithStreetAddress)
        {
            if (poBoxWithStreetAddress != null)
            {
                return FormatInstallationInfo(  municipalityName,
                                                poBoxWithStreetAddress.InstallationArea,
                                                poBoxWithStreetAddress.InstallationTypeCode,
                                                poBoxWithStreetAddress.InstallationName);
            }
            else
                return string.Empty;
        }

        public static string FormatPoBoxInstallationInfo(string municipalityName, AWSServiceProxy.POBoxAddressType poBoxAddress)
        {
            if (poBoxAddress != null)
            {
                return FormatInstallationInfo(  municipalityName,
                                                poBoxAddress.InstallationArea,
                                                poBoxAddress.InstallationTypeCode,
                                                poBoxAddress.InstallationName);
            }
            else
                return string.Empty;
        }

        public static string FormatRuralRouteInstallationInfo(string municipalityName, AWSServiceProxy.RuralRouteAddressType ruralRouteAddress)
        {
            if (ruralRouteAddress != null)
            {
                return FormatInstallationInfo(  municipalityName,
                                                ruralRouteAddress.InstallationArea,
                                                ruralRouteAddress.InstallationTypeCode,
                                                ruralRouteAddress.InstallationName);
            }
            else
                return string.Empty;
        }

        private static string FormatInstallationInfo(string municipalityName, string installationArea, string installationTypeCode, string installationName)
        {
            string retVal = string.Empty;
            if (installationArea.ValueExists())
            {
                if (Helpers.StringCompare(municipalityName, installationArea, true))
                {
                    //Installation Area Matches Municipality, do not add installation area
                }
                else
                {
                    if (retVal.Length > 0) retVal += SPACE;
                    retVal += installationArea.Trim();
                }
            }

            if (installationTypeCode.ValueExists() && installationName.ValueExists())
            {
                if (retVal.Length > 0) retVal += SPACE;
                retVal += installationTypeCode.Trim();
                if (retVal.Length > 0) retVal += SPACE;
                retVal += installationName.Trim();
            }
            return retVal;
        }

        public static SuggestedAddress ConvertAWSCorrectedAddressToSuggestedAddress(AWSServiceProxy.Address proxyAddressResult, Address addressBase)
        {
            SuggestedAddress correctedAddress = null;

            if (proxyAddressResult != null)
            {
                correctedAddress = new SuggestedAddress();
                correctedAddress.PostalCode = Helpers.StripPostalZipCode(proxyAddressResult.PostalCode);
                correctedAddress.City = Helpers.RemoveDiacritics(proxyAddressResult.MunicipalityName);  //Remove Accents from AWS City Names
                if (proxyAddressResult.ProvinceCode.HasValue)
                {
                    correctedAddress.ProvinceCode = proxyAddressResult.ProvinceCode.ToString();
                }
                else
                {
                    correctedAddress.ProvinceCode = addressBase.ProvinceCode;
                }

                //Set Address Type From Result
                if (proxyAddressResult.AddressType.Equals(AWSServiceProxy.AddressRecordType.GeneralDelivery))
                {
                    correctedAddress.AddressType = SuggestedAddress.AddressTypes.GeneralDelivery;
                }
                else if (proxyAddressResult.AddressType.Equals(AWSServiceProxy.AddressRecordType.POBox))
                {
                    correctedAddress.AddressType = SuggestedAddress.AddressTypes.PostOfficeBox;
                }
                else if (proxyAddressResult.AddressType.Equals(AWSServiceProxy.AddressRecordType.RuralRoute))
                {
                    correctedAddress.AddressType = SuggestedAddress.AddressTypes.RuralRoute;
                }
                else if (proxyAddressResult.AddressType.Equals(AWSServiceProxy.AddressRecordType.Street))
                {
                    correctedAddress.AddressType = SuggestedAddress.AddressTypes.Street;
                }
                else if (proxyAddressResult.AddressType.Equals(AWSServiceProxy.AddressRecordType.POBoxWithStreet))
                {
                    if (CheckResultContainsStreetAddressMissingInfoCode(proxyAddressResult))
                    {
                        //If Warning Code "StreetAddressMissing" is returned for a POBoxWithStreet Address, then we treat corrected Address as PO BOX A.
                        correctedAddress.AddressType = SuggestedAddress.AddressTypes.PostOfficeBox;
                    }
                    else
                    {
                        //If Warning Code does not contain "StreetAddressMissing" for a POBoxWithStreet Address, then we treat corrected Address as Street Type.
                        correctedAddress.AddressType = SuggestedAddress.AddressTypes.Street;
                    }
                }
                else
                {
                    correctedAddress.AddressType = SuggestedAddress.AddressTypes.Unknown;
                }

                //Cast Object
                if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.FormattedGeneralDeliveryAddress))
                {
                    AWSServiceProxy.FormattedGeneralDeliveryAddress gdAddress = (AWSServiceProxy.FormattedGeneralDeliveryAddress)proxyAddressResult;
                    if (gdAddress.GeneralDelivery != null)
                    {
                        //Street Name
                        correctedAddress.StreetName = AWSTranslate.FormatGeneralDeliveryStreetName(gdAddress.GeneralDelivery.DeliveryTypeCode);
                        //Address2
                        string lAddress2 = string.Empty;
                        if (gdAddress.GeneralDelivery != null)
                        {
                            lAddress2 = AWSTranslate.FormatGeneralDeliveryInstallationInfo(proxyAddressResult.MunicipalityName, gdAddress.GeneralDelivery);
                        }
                        correctedAddress.StreetAddress2 = lAddress2;
                    }
                }
                else if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.FormattedPOBoxAddress))
                {
                    AWSServiceProxy.FormattedPOBoxAddress poBoxAddress = (AWSServiceProxy.FormattedPOBoxAddress)proxyAddressResult;
                    if (poBoxAddress.POBox != null)
                    {
                        //Street Name
                        correctedAddress.StreetName = AWSTranslate.FormatPOBoxStreetName(poBoxAddress.POBox.Number);
                        //Address2
                        string lAddress2 = string.Empty;
                        if (poBoxAddress.POBox != null)
                        {
                            lAddress2 = AWSTranslate.FormatPoBoxInstallationInfo(proxyAddressResult.MunicipalityName, poBoxAddress.POBox);
                        }
                        correctedAddress.StreetAddress2 = lAddress2;
                    }
                }
                else if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.FormattedRuralRouteAddress))
                {
                    AWSServiceProxy.FormattedRuralRouteAddress rrAddress = (AWSServiceProxy.FormattedRuralRouteAddress)proxyAddressResult;
                    if (rrAddress.Rural != null)
                    {
                        //Street Name
                        correctedAddress.StreetName = AWSTranslate.FormatRuralRouteStreetName(rrAddress.Rural.RouteTypeCode, rrAddress.Rural.RouteNumber);
                        //Address2
                        string lAddress2 = string.Empty;
                        if (rrAddress.Rural != null)
                        {
                            lAddress2 = AWSTranslate.FormatRuralRouteInstallationInfo(proxyAddressResult.MunicipalityName, rrAddress.Rural);
                        }
                        correctedAddress.StreetAddress2 = lAddress2;
                    }
                }
                else if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.FormattedStreetAddress))
                {
                    AWSServiceProxy.FormattedStreetAddress formattedStreetAddress = (AWSServiceProxy.FormattedStreetAddress)proxyAddressResult;
                    if (formattedStreetAddress.Street != null)
                    {
                        if (formattedStreetAddress.Street.Number > 0)
                        {
                            correctedAddress.StreetNumber = formattedStreetAddress.Street.Number.ToString();
                        }
                        correctedAddress.StreetName = formattedStreetAddress.Street.Name;
                        correctedAddress.StreetType = Helpers.RemoveDiacritics(formattedStreetAddress.Street.Type);
                        correctedAddress.StreetNumberSuffix = formattedStreetAddress.Street.NumberSuffix;
                        correctedAddress.Suite = formattedStreetAddress.Street.Suite;
                        correctedAddress.StreetDirection = formattedStreetAddress.Street.Direction;
                        if (formattedStreetAddress.Street.Number > 0)
                        {
                            correctedAddress.FromStreetNumber = formattedStreetAddress.Street.Number.ToString();
                        }
                        if (formattedStreetAddress.Street.Number > 0)
                        {
                            correctedAddress.ToStreetNumber = formattedStreetAddress.Street.Number.ToString();
                        }
                        correctedAddress.FromStreetNumberSuffix = formattedStreetAddress.Street.NumberSuffix;
                        correctedAddress.ToStreetNumberSuffix = formattedStreetAddress.Street.NumberSuffix;
                        correctedAddress.FromSuiteNumber = formattedStreetAddress.Street.Suite;
                        correctedAddress.ToSuiteNumber = formattedStreetAddress.Street.Suite;
                    }
                }
                else if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.FormattedPOBoxWithStreetAddress))
                {
                    AWSServiceProxy.FormattedPOBoxWithStreetAddress formattedPOBoxWithStreetAddress = (AWSServiceProxy.FormattedPOBoxWithStreetAddress)proxyAddressResult;
                    if (formattedPOBoxWithStreetAddress.POBoxWithStreetAddress != null)
                    {
                        //PO Box With Street Address - Street Address
                        if (formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.StreetNumber > 0)
                        {
                            correctedAddress.StreetNumber = formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.StreetNumber.ToString();
                        }
                        correctedAddress.StreetNumberSuffix = formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.NumberSuffix;
                        correctedAddress.StreetType = Helpers.RemoveDiacritics(formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.Type);
                        correctedAddress.StreetDirection = formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.Direction;
                        correctedAddress.StreetName = formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.Name;
                        correctedAddress.Suite = formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.Suite;

                        if (formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.StreetNumber > 0)
                        {
                            correctedAddress.FromStreetNumber = formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.StreetNumber.ToString();
                        }
                        if (formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.StreetNumber > 0)
                        {
                            correctedAddress.ToStreetNumber = formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.StreetNumber.ToString();
                        }
                        correctedAddress.FromStreetNumberSuffix = formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.NumberSuffix;
                        correctedAddress.ToStreetNumberSuffix = formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.NumberSuffix;
                        correctedAddress.FromSuiteNumber = formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.Suite;
                        correctedAddress.ToSuiteNumber = formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.Suite;

                        //PO Box With Street Address - PO Box Number
                        correctedAddress.StreetAddress2 = AWSTranslate.FormatPOBoxStreetName(formattedPOBoxWithStreetAddress.POBoxWithStreetAddress.PONumber);

                        //PO Box With Street Address - PO Box Line 2
                        string lAddress3 = string.Empty;
                        if (formattedPOBoxWithStreetAddress.POBoxWithStreetAddress != null)
                        {
                            lAddress3 = AWSTranslate.FormatPoBoxWithStreetInstallationInfo(proxyAddressResult.MunicipalityName, formattedPOBoxWithStreetAddress.POBoxWithStreetAddress);
                        }
                        correctedAddress.StreetAddress3 = lAddress3;

                        //If StreetName is Empty, pull Address Lines Up.
                        if (correctedAddress.StreetName.ValueEmpty())
                        {
                            if (correctedAddress.StreetAddress2.ValueExists())
                            {
                                //Use Address2
                                correctedAddress.StreetName = correctedAddress.StreetAddress2;
                                correctedAddress.StreetAddress2 = correctedAddress.StreetAddress3;
                                correctedAddress.StreetAddress3 = string.Empty;
                            }
                            else if (correctedAddress.StreetAddress3.ValueExists())
                            {
                                //Use Address3
                                correctedAddress.StreetName = correctedAddress.StreetAddress3;
                                correctedAddress.StreetAddress2 = string.Empty;
                                correctedAddress.StreetAddress3 = string.Empty;
                            }
                        }

                    }
                }
                else if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.Address))
                {
                    AWSServiceProxy.Address address = (AWSServiceProxy.Address)proxyAddressResult;
                }
                else if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.FormattedInvalidAddress))
                {
                    AWSServiceProxy.FormattedInvalidAddress formattedInvalidAddress = (AWSServiceProxy.FormattedInvalidAddress)proxyAddressResult;
                }
                else
                {
                    string errorMessage = string.Format("AWS Manager.ConvertAWSCorrectedAddressToSuggestedAddress(): Unknown Object Type is {0}", proxyAddressResult.GetType());
                    Logger.Error(errorMessage, LogCategories.AWS, null);

                }
            }
            return correctedAddress;
        }


        private static bool CheckResultContainsStreetAddressMissingInfoCode(AWSServiceProxy.Address proxyAddressResult)
        {
            bool retVal = false;
            if (proxyAddressResult.InfoReasonCodes.HasFlag(AWSServiceProxy.InfoReasonCode.StreetAddressMissing))
            {
                retVal = true;
            }
            return retVal;
        }



        public static Purolator.SmartSort.Business.Common.Exception.SmartSortError[] TranslateAWSErrors(    Address address,
                                                                                                            AWSServiceProxy.Address proxyAddressResult,
                                                                                                            bool matchingSuggestedAddressValid )
        {
            Dictionary<AWSErrorEnum, Purolator.SmartSort.Business.Common.Exception.SmartSortError> errors = new Dictionary<AWSErrorEnum, Purolator.SmartSort.Business.Common.Exception.SmartSortError>();

            if (proxyAddressResult != null)
            {
                //Check Status
                switch (proxyAddressResult.StatusCode)
                {
                    case AWSServiceProxy.StatusCode.IncompleteInput:
                        AddAWSError(errors, AWSErrorEnum.DetailedValidationIncompleteInput);
                        break;
                    case AWSServiceProxy.StatusCode.Invalid:
                        AddAWSError(errors, AWSErrorEnum.DetailedValidationInvalid);
                        break;
                    case AWSServiceProxy.StatusCode.NotCanadian:
                        AddAWSError(errors, AWSErrorEnum.DetailedValidationNotCanadian);
                        break;
                    case AWSServiceProxy.StatusCode.Unparsable:
                        AddAWSError(errors, AWSErrorEnum.DetailedValidationUnparseable);
                        break;
                    case AWSServiceProxy.StatusCode.Valid:
                        //No Error
                        break;
                    case AWSServiceProxy.StatusCode.ValidRangeOnly:
                        //No Error
                        break;
                    default:
                        //No Error
                        break;
                }

                //Error Reason Codes
                AWSServiceProxy.ErrorReasonCode[] errorReasonCodeArray = AWSTranslate.TranslateErrorReasonCodeToArray(proxyAddressResult.ErrorReasonCodes);
                foreach (AWSServiceProxy.ErrorReasonCode errorReasonCode in errorReasonCodeArray)
                {
                    //Determine ESO Errors From AWS ERRORS
                    switch (errorReasonCode)
                    {
                        case AWSServiceProxy.ErrorReasonCode.MunicipalityNotFound:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationMunicipalityNotFound);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.POBoxInvalid:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationPOBoxInvalid);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.POBoxMissing:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationPOBoxMissing);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.POBoxNotFound:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationPOBoxNotFound);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.ProvinceInvalid:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationProvinceInvalid);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.RuralRouteInvalid:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationRouteNumberInvalid);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.RuralRouteMissing:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationRouteNumberMissing);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.RuralRouteNotFound:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationRouteNumberNotFound);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.StreetDirectionInvalid:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetDirectionInvalid);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.StreetDirectionMissing:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetDirectionMissing);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.StreetNotFound:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNotFound);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.StreetNumberInvalid:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberInvalid);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.StreetNumberNotFound:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberNotFound);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.StreetNumberSuffixMissing:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberSuffixMissing);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.StreetNumberSuffixNotFound:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberSuffixNotFound);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.StreetTypeInvalid:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetTypeInvalid);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.StreetTypeMissing:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetTypeMissing);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.SuiteInvalid:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationSuiteInvalid);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.SuiteMissing:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationSuiteMissing);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.SuiteNotFound:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationSuiteNotFound);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.StreetMissing:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetMissing);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.MunicipalityMissing:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationMunicipalityMissing);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.ProvinceMissing:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationProvinceMissing);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.PostalCodeInvalid:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationPostalCodeInvalid);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.PostalCodeMissing:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationPostalCodeMissing);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.InvalidInputNumber:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationInvalidInputNumber);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.GDNotFound:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationGDNotFound);
                            break;
                        case AWSServiceProxy.ErrorReasonCode.GDMissing:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationGDMissing);
                            break;
                    }
                }

                //Determine ESO Errors From AWS Warnings
                AWSServiceProxy.WarningReasonCode[] warningReasonCodeArray = AWSTranslate.TranslateWarningReasonCodeToArray(proxyAddressResult.WarningReasonCodes);
                foreach (AWSServiceProxy.WarningReasonCode warningReasonCode in warningReasonCodeArray)
                {
                    switch (warningReasonCode)
                    {
                        case AWSServiceProxy.WarningReasonCode.AlternateMunicipalityUsed:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationMunicipalityAlternateUsed);
                            break;
                        case AWSServiceProxy.WarningReasonCode.BusinessNameAdded:
                            break;
                        case AWSServiceProxy.WarningReasonCode.BusinessNameChanged:
                            break;
                        case AWSServiceProxy.WarningReasonCode.BusinessNameRemoved:
                            break;
                        case AWSServiceProxy.WarningReasonCode.DeliveryInstallationRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.FuzzyBusinessNameUsed:
                            break;
                        case AWSServiceProxy.WarningReasonCode.FuzzyMunicipalityUsed:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationMunicipalityAlternateUsed);
                            break;
                        case AWSServiceProxy.WarningReasonCode.MunicipalityAdded:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationMunicipalityAdded);
                            break;
                        case AWSServiceProxy.WarningReasonCode.MunicipalityChanged:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationMunicipalityAlternateUsed );
                            break;
                        case AWSServiceProxy.WarningReasonCode.PostalCodeAdded:
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationPostalCodeAdded);
                            break;
                        case AWSServiceProxy.WarningReasonCode.PostalCodeChanged:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationPostalCodeChanged);
                            break;
                        case AWSServiceProxy.WarningReasonCode.ProvinceAdded:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationProvinceAdded);
                            break;
                        case AWSServiceProxy.WarningReasonCode.ProvinceChanged:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationProvinceChanged);
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetAdded:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetAdded);
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetMissing);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetDirectionAdded:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetDirectionAdded );
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetDirectionMissing );
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetDirectionChanged:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetDirectionChanged );
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetDirectionInvalid );
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetDirectionRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetDirectionRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetNumberAdded:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberAdded );
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberInvalid);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetNumberChanged:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum. DetailedValidationStreetNumberChanged);
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberInvalid );
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetNumberRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetNumberSuffixAdded:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberSuffixAdded );
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberSuffixMissing);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetNumberSuffixChanged:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberSuffixChanged);
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberSuffixInvalid);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetNumberSuffixRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNumberSuffixRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetTypeAdded:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetTypeAdded);
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetTypeMissing);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetTypeChanged:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetTypeChanged);
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetTypeInvalid);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetTypeRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetTypeRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.SuiteAdded:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationSuiteAdded );
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationSuiteMissing);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.SuiteChanged:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationSuiteChanged );
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationSuiteInvalid);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.SuiteRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationSuiteRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.FuzzyStreetUsed:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetAlternateUsed );
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetNotFound);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetChanged:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetAlternateUsed );
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetInvalid);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.AlternateStreetUsed:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetAlternateUsed);
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetInvalid);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetRemoved:
                            if (matchingSuggestedAddressValid)
                            {
                                    AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetRemoved);
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetInvalid);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.DeliveryInstallationChanged:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationChanged);
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationInvalid);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.POBoxAdded:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationPOBoxAdded);
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationPOBoxMissing);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.POBoxChanged:
                            if (matchingSuggestedAddressValid)
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationPOBoxChanged);
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationPOBoxInvalid);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.POBoxRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationPOBoxRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.RuralRouteAdded:
                            if (matchingSuggestedAddressValid)
                            {
                                if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.FormattedRuralRouteAddress))
                                {
                                    AWSServiceProxy.FormattedRuralRouteAddress formattedRuralRouteAddress = (AWSServiceProxy.FormattedRuralRouteAddress)proxyAddressResult;
                                    if (formattedRuralRouteAddress != null && formattedRuralRouteAddress.Rural != null)
                                    {
                                        AddAWSError(errors, AWSErrorEnum.DetailedValidationRuralAdded);
                                    }
                                    else
                                    {
                                        AddAWSError(errors, AWSErrorEnum.DetailedValidationRouteNumberMissing);
                                    }
                                }
                                else
                                {
                                    AddAWSError(errors, AWSErrorEnum.DetailedValidationRouteNumberMissing);
                                }
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationRouteNumberMissing);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.RuralRouteChanged:
                            if (matchingSuggestedAddressValid)
                            {
                                if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.FormattedRuralRouteAddress))
                                {
                                    AWSServiceProxy.FormattedRuralRouteAddress formattedRuralRouteAddress = (AWSServiceProxy.FormattedRuralRouteAddress)proxyAddressResult;
                                    if (formattedRuralRouteAddress != null && formattedRuralRouteAddress.Rural != null)
                                    {
                                        AddAWSError(errors, AWSErrorEnum.DetailedValidationRuralChanged);
                                    }
                                    else
                                    {
                                        AddAWSError(errors, AWSErrorEnum.DetailedValidationRouteNumberInvalid);
                                    }
                                }
                                else
                                {
                                    AddAWSError(errors, AWSErrorEnum.DetailedValidationRouteNumberInvalid);
                                }
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationRouteNumberInvalid);
                            }
                            break;
                        case AWSServiceProxy.WarningReasonCode.RuralRouteRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationRuralRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.GDAddressRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationGDAddrRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.POBoxAddressRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationPOBoxAddrRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.RuralAddressRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationRuralAddrRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetAddressRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetAddrRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.StreetWithPOBoxAddressRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationStreetWithPOBoxAddrRemoved);
                            break;
                        case AWSServiceProxy.WarningReasonCode.GDAdded:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationGDAdded);
                            break;
                        case AWSServiceProxy.WarningReasonCode.GDChanged:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationGDChanged);
                            break;
                        case AWSServiceProxy.WarningReasonCode.GDRemoved:
                            AddAWSError(errors, AWSErrorEnum.DetailedValidationGDRemoved);
                            break;
                    }
                }

                //Determine ESO Errors From AWS Info Reason Codes
                AWSServiceProxy.InfoReasonCode[] infoReasonCodeArray = AWSTranslate.TranslateInfoReasonCodeToArray(proxyAddressResult.InfoReasonCodes);
                foreach (AWSServiceProxy.InfoReasonCode infoReasonCode in infoReasonCodeArray)
                {
                    switch (infoReasonCode)
                    {
                        case AWSServiceProxy.InfoReasonCode.DeliveryInstallationAdded:
                            if (matchingSuggestedAddressValid)
                            {
                                if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.FormattedPOBoxAddress))
                                {
                                    AWSServiceProxy.FormattedPOBoxAddress formattedPOBoxAddress = (AWSServiceProxy.FormattedPOBoxAddress)proxyAddressResult;
                                    if (formattedPOBoxAddress != null)
                                    {
                                        AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationChanged);
                                    }
                                    else
                                    {
                                        AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationInvalid);
                                    }
                                }
                                else if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.FormattedGeneralDeliveryAddress))
                                {
                                    AWSServiceProxy.FormattedGeneralDeliveryAddress formattedGeneralDeliveryAddress = (AWSServiceProxy.FormattedGeneralDeliveryAddress)proxyAddressResult;
                                    if (formattedGeneralDeliveryAddress != null && formattedGeneralDeliveryAddress.GeneralDelivery != null)
                                    {
                                        AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationChanged);
                                    }
                                    else
                                    {
                                        AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationInvalid);
                                    }
                                }
                                else if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.FormattedRuralRouteAddress))
                                {
                                    AWSServiceProxy.FormattedRuralRouteAddress formattedRuralRouteAddress = (AWSServiceProxy.FormattedRuralRouteAddress)proxyAddressResult;
                                    if (formattedRuralRouteAddress != null && formattedRuralRouteAddress.Rural != null)
                                    {
                                        AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationChanged);
                                    }
                                    else
                                    {
                                        AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationInvalid);
                                    }
                                }
                                else if (proxyAddressResult.GetType() == typeof(AWSServiceProxy.FormattedPOBoxWithStreetAddress))
                                {
                                    AWSServiceProxy.FormattedPOBoxWithStreetAddress formattedPOBoxWithStreetAddress = (AWSServiceProxy.FormattedPOBoxWithStreetAddress)proxyAddressResult;
                                    if (formattedPOBoxWithStreetAddress != null && formattedPOBoxWithStreetAddress.POBoxWithStreetAddress != null)
                                    {
                                        AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationChanged);
                                    }
                                    else
                                    {
                                        AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationInvalid);
                                    }
                                }
                                else
                                {
                                    AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationInvalid);
                                }
                            }
                            else
                            {
                                AddAWSError(errors, AWSErrorEnum.DetailedValidationDeliveryInstallationInvalid);
                            }
                            break;
                    }
                }
            }

            //Convert Dictionary To Array
            Purolator.SmartSort.Business.Common.Exception.SmartSortError[] errorArray = new Purolator.SmartSort.Business.Common.Exception.SmartSortError[errors.Values.Count];
            if (errors.Values.Count > 0)
            {
                errors.Values.CopyTo(errorArray, 0);
            }
            return errorArray;
        }

        private static void AddAWSError(System.Collections.Generic.Dictionary<AWSErrorEnum, Purolator.SmartSort.Business.Common.Exception.SmartSortError> errorList, AWSErrorEnum errorEnum)
        {
            if (errorList == null) errorList = new Dictionary<AWSErrorEnum, Purolator.SmartSort.Business.Common.Exception.SmartSortError>();
            if (!errorList.ContainsKey(errorEnum))
            {
                Purolator.SmartSort.Business.Common.Exception.SmartSortError newError = new Business.Common.Exception.SmartSortError(errorEnum.ToString());
                errorList.Add(errorEnum, newError);
            }
        }
        
        public static string BuildAWSAddressLine1(Address address)
        {
            string retVal = string.Empty;

            if (address.StreetNumber.ValueExists() && address.StreetNumberSuffix.ValueExists())
            {
                if (address.StreetNumberSuffix.Trim().Length == 1)
                {
                    retVal = string.Format("{0}{1}", address.StreetNumber.Trim(), address.StreetNumberSuffix.Trim());
                }
                else
                {
                    retVal = string.Format("{0} {1}", address.StreetNumber.Trim(), address.StreetNumberSuffix.Trim());
                }
            }
            else
            {
                if (address.StreetNumber.ValueExists())
                {
                    if (retVal.Length > 0) retVal += " ";
                    retVal += address.StreetNumber.Trim();
                }
                else if (address.StreetNumberSuffix.ValueExists())
                {
                    if (retVal.Length > 0) retVal += " ";
                    retVal += address.StreetNumberSuffix.Trim();
                }
            }

            if (address.StreetName.ValueExists())
            {
                if (retVal.Length > 0) retVal += " ";
                retVal += address.StreetName.Trim();
            }

            if (address.StreetType.ValueExists())
            {
                if (retVal.Length > 0) retVal += " ";
                retVal += address.StreetType.Trim();
            }

            if (address.StreetDirection.ValueExists())
            {
                if (retVal.Length > 0) retVal += " ";
                retVal += address.StreetDirection.Trim();
            }

            //Floor Not Validated By AWS

            if (address.Suite.ValueExists())
            {
                if (retVal.Length > 0) retVal += " ";
                retVal += string.Format("SUITE# {0}", address.Suite.Trim());
            }

            return retVal;
        }

        public static string BuildAWSAddressLine2(Address address)
        {
            string retVal = string.Empty;
            if (address != null)
            {
                if (address.StreetAddress2.ValueExists())
                {
                    if (retVal.Length > 0) retVal += " ";
                    retVal += address.StreetAddress2.Trim();
                }
                if (address.StreetAddress3.ValueExists())
                {
                    if (retVal.Length > 0) retVal += " ";
                    retVal += address.StreetAddress3.Trim();
                }
            }
            return string.Empty;
        }

        public static string BuildCompleteAWSAddress(Address address)
        {
            string retVal = string.Empty;
            if (retVal.Length > 0 && BuildAWSAddressLine1(address).Length > 0) retVal += " ";
            retVal += BuildAWSAddressLine1(address);

            if (retVal.Length > 0 && BuildAWSAddressLine2(address).Length > 0) retVal += " ";
            retVal += BuildAWSAddressLine2(address);

            if (retVal.Length > 0 && address.City.ValueExists()) retVal += ", ";
            retVal += address.City;

            if (retVal.Length > 0 && address.ProvinceCode.ValueExists()) retVal += " ";
            retVal += address.ProvinceCode;

            if (retVal.Length > 0 && address.PostalCode.ValueExists()) retVal += ", ";
            retVal += address.PostalCode;

            return retVal;
        }
    }

}
