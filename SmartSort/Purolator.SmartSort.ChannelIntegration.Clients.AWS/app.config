﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.serviceModel>
    <client>
      <endpoint address="https://qa.soa-gw.mailposte.ca:5015/AddressingWebService/V3/Service.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IAddressLookup1" contract="AWSServiceProxy.IAddressLookup" name="BasicHttpBinding_IAddressLookup" />
    </client>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_IAddressLookup1" closeTimeout="00:01:00" openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00" allowCookies="false" bypassProxyOnLocal="false" maxBufferPoolSize="524288" maxReceivedMessageSize="65536" useDefaultWebProxy="true" />
        <binding name="BasicHttpBinding_IAddressLookup">
          <security mode="Transport" />
        </binding>
      </basicHttpBinding>
    </bindings>
  </system.serviceModel>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.EnterpriseLibrary.Common" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>  