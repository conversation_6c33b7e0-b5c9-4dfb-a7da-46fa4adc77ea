﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.ChannelIntegration.Clients.AWS
{
    public class MunicipalityNames
    {
                        
        public List<Province> Provinces { get; set; }

        public MunicipalityNames()
        {
        }

        public MunicipalityNames(AWSServiceProxy.MunicipalityNames awsResult)
        {
            Provinces = new List<Province>();

            foreach (var province in awsResult.Provinces)
            {                
                Provinces.Add(new Province(province));
            }   
        }        
    }


    public class Province
    {
        public string Code { get; set; }
        public List<MunicipalityName> MunicipalityNames { get; set; }

        public Province()
        {
        }

        public Province(AWSServiceProxy.Province province)
        {
            this.Code = province.Code;
            MunicipalityNames = new List<MunicipalityName>();
            foreach (var city in province.MunicipalityNames)
            {
                MunicipalityNames.Add(new MunicipalityName(city));
            }
        }
    }


    public class MunicipalityName
    {
         public string Value { get; set; }
         public List<AlternateMunicipalityName> AlternateMunicipalityNames { get; set; }

         public MunicipalityName()
         {
         }

        public MunicipalityName(AWSServiceProxy.MunicipalityName munName)
        {
            Value = munName.Value;
            if (munName.AlternateMunicipalityNames != null)
            {
                AlternateMunicipalityNames = new List<AlternateMunicipalityName>();
                foreach (var city in munName.AlternateMunicipalityNames)
                {
                    AlternateMunicipalityNames.Add(new AlternateMunicipalityName(city));
                }
            }
        }
    }

    public class AlternateMunicipalityName
    {
         public string Value { get; set; }
         public bool IsValid { get; set; }

         public AlternateMunicipalityName()
         {
         }

        public AlternateMunicipalityName(AWSServiceProxy.AlternateMunicipalityName altName)
        {
            Value = altName.Value;
            IsValid = altName.IsValid;
        }

    }

}
