﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.ChannelIntegration.Clients.AWS
{
    public class GetSuggestedAddressesResult
    {
        private List<SuggestedAddress> _suggestedAddresses = null;
        private bool _cacheDataInd = false;

        public List<SuggestedAddress> SuggestedAddresses
        {
            get { return _suggestedAddresses; }
            set { _suggestedAddresses = value; }
        }

        public bool CacheDataInd
        {
            get { return _cacheDataInd; }
            set { _cacheDataInd = value; }
        }

        public GetSuggestedAddressesResult(List<SuggestedAddress> suggestedAddressList, bool CacheDataInd)
        {
            _suggestedAddresses = suggestedAddressList;
            _cacheDataInd = CacheDataInd;
        }

        public GetSuggestedAddressesResult()
        {
        }
    }
}
