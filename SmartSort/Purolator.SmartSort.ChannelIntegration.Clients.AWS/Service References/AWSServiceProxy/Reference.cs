﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.34209
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy {
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class CustomFaultMsg : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string myCustomErrMsgField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string MyCustomErrMsg {
            get {
                return this.myCustomErrMsgField;
            }
            set {
                this.myCustomErrMsgField = value;
                this.RaisePropertyChanged("MyCustomErrMsg");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/cmb/2015/01")]
    public partial class CMBInfoType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string siteField;
        
        private string moduleField;
        
        private string compartmentField;
        
        private string locationDescriptionEnField;
        
        private string locationDescriptionFrField;
        
        private string implementationStateField;
        
        private System.Nullable<System.DateTime> implementationDateField;
        
        private double latitudeField;
        
        private double longitudeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Site {
            get {
                return this.siteField;
            }
            set {
                this.siteField = value;
                this.RaisePropertyChanged("Site");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Module {
            get {
                return this.moduleField;
            }
            set {
                this.moduleField = value;
                this.RaisePropertyChanged("Module");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string Compartment {
            get {
                return this.compartmentField;
            }
            set {
                this.compartmentField = value;
                this.RaisePropertyChanged("Compartment");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string LocationDescriptionEn {
            get {
                return this.locationDescriptionEnField;
            }
            set {
                this.locationDescriptionEnField = value;
                this.RaisePropertyChanged("LocationDescriptionEn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string LocationDescriptionFr {
            get {
                return this.locationDescriptionFrField;
            }
            set {
                this.locationDescriptionFrField = value;
                this.RaisePropertyChanged("LocationDescriptionFr");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string ImplementationState {
            get {
                return this.implementationStateField;
            }
            set {
                this.implementationStateField = value;
                this.RaisePropertyChanged("ImplementationState");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="date", IsNullable=true, Order=6)]
        public System.Nullable<System.DateTime> ImplementationDate {
            get {
                return this.implementationDateField;
            }
            set {
                this.implementationDateField = value;
                this.RaisePropertyChanged("ImplementationDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public double Latitude {
            get {
                return this.latitudeField;
            }
            set {
                this.latitudeField = value;
                this.RaisePropertyChanged("Latitude");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public double Longitude {
            get {
                return this.longitudeField;
            }
            set {
                this.longitudeField = value;
                this.RaisePropertyChanged("Longitude");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class POBoxAddressType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private int numberField;
        
        private string installationTypeCodeField;
        
        private string installationNameField;
        
        private string installationAreaField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public int Number {
            get {
                return this.numberField;
            }
            set {
                this.numberField = value;
                this.RaisePropertyChanged("Number");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string InstallationTypeCode {
            get {
                return this.installationTypeCodeField;
            }
            set {
                this.installationTypeCodeField = value;
                this.RaisePropertyChanged("InstallationTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string InstallationName {
            get {
                return this.installationNameField;
            }
            set {
                this.installationNameField = value;
                this.RaisePropertyChanged("InstallationName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string InstallationArea {
            get {
                return this.installationAreaField;
            }
            set {
                this.installationAreaField = value;
                this.RaisePropertyChanged("InstallationArea");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class GeneralDeliveryAddressType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string deliveryTypeCodeField;
        
        private string installationTypeCodeField;
        
        private string installationNameField;
        
        private string installationAreaField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string DeliveryTypeCode {
            get {
                return this.deliveryTypeCodeField;
            }
            set {
                this.deliveryTypeCodeField = value;
                this.RaisePropertyChanged("DeliveryTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string InstallationTypeCode {
            get {
                return this.installationTypeCodeField;
            }
            set {
                this.installationTypeCodeField = value;
                this.RaisePropertyChanged("InstallationTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string InstallationName {
            get {
                return this.installationNameField;
            }
            set {
                this.installationNameField = value;
                this.RaisePropertyChanged("InstallationName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string InstallationArea {
            get {
                return this.installationAreaField;
            }
            set {
                this.installationAreaField = value;
                this.RaisePropertyChanged("InstallationArea");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class RuralRouteAddressType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string routeTypeCodeField;
        
        private int routeNumberField;
        
        private string installationTypeCodeField;
        
        private string installationNameField;
        
        private string installationAreaField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string RouteTypeCode {
            get {
                return this.routeTypeCodeField;
            }
            set {
                this.routeTypeCodeField = value;
                this.RaisePropertyChanged("RouteTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int RouteNumber {
            get {
                return this.routeNumberField;
            }
            set {
                this.routeNumberField = value;
                this.RaisePropertyChanged("RouteNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string InstallationTypeCode {
            get {
                return this.installationTypeCodeField;
            }
            set {
                this.installationTypeCodeField = value;
                this.RaisePropertyChanged("InstallationTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string InstallationName {
            get {
                return this.installationNameField;
            }
            set {
                this.installationNameField = value;
                this.RaisePropertyChanged("InstallationName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string InstallationArea {
            get {
                return this.installationAreaField;
            }
            set {
                this.installationAreaField = value;
                this.RaisePropertyChanged("InstallationArea");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class StreetAddressType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string suiteField;
        
        private string nameField;
        
        private int numberField;
        
        private string numberSuffixField;
        
        private string typeField;
        
        private string directionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Suite {
            get {
                return this.suiteField;
            }
            set {
                this.suiteField = value;
                this.RaisePropertyChanged("Suite");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public int Number {
            get {
                return this.numberField;
            }
            set {
                this.numberField = value;
                this.RaisePropertyChanged("Number");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string NumberSuffix {
            get {
                return this.numberSuffixField;
            }
            set {
                this.numberSuffixField = value;
                this.RaisePropertyChanged("NumberSuffix");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string Type {
            get {
                return this.typeField;
            }
            set {
                this.typeField = value;
                this.RaisePropertyChanged("Type");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string Direction {
            get {
                return this.directionField;
            }
            set {
                this.directionField = value;
                this.RaisePropertyChanged("Direction");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class POBoxWithStreetAddressType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string suiteField;
        
        private string nameField;
        
        private int streetNumberField;
        
        private string numberSuffixField;
        
        private string typeField;
        
        private string directionField;
        
        private int pONumberField;
        
        private string installationTypeCodeField;
        
        private string installationNameField;
        
        private string installationAreaField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Suite {
            get {
                return this.suiteField;
            }
            set {
                this.suiteField = value;
                this.RaisePropertyChanged("Suite");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public int StreetNumber {
            get {
                return this.streetNumberField;
            }
            set {
                this.streetNumberField = value;
                this.RaisePropertyChanged("StreetNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string NumberSuffix {
            get {
                return this.numberSuffixField;
            }
            set {
                this.numberSuffixField = value;
                this.RaisePropertyChanged("NumberSuffix");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string Type {
            get {
                return this.typeField;
            }
            set {
                this.typeField = value;
                this.RaisePropertyChanged("Type");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string Direction {
            get {
                return this.directionField;
            }
            set {
                this.directionField = value;
                this.RaisePropertyChanged("Direction");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public int PONumber {
            get {
                return this.pONumberField;
            }
            set {
                this.pONumberField = value;
                this.RaisePropertyChanged("PONumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string InstallationTypeCode {
            get {
                return this.installationTypeCodeField;
            }
            set {
                this.installationTypeCodeField = value;
                this.RaisePropertyChanged("InstallationTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string InstallationName {
            get {
                return this.installationNameField;
            }
            set {
                this.installationNameField = value;
                this.RaisePropertyChanged("InstallationName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string InstallationArea {
            get {
                return this.installationAreaField;
            }
            set {
                this.installationAreaField = value;
                this.RaisePropertyChanged("InstallationArea");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class InvalidAddressType : object, System.ComponentModel.INotifyPropertyChanged {
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class FormattedAddressLines : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string addresseeField;
        
        private string line1Field;
        
        private string line2Field;
        
        private string line3Field;
        
        private string langField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Addressee {
            get {
                return this.addresseeField;
            }
            set {
                this.addresseeField = value;
                this.RaisePropertyChanged("Addressee");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Line1 {
            get {
                return this.line1Field;
            }
            set {
                this.line1Field = value;
                this.RaisePropertyChanged("Line1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string Line2 {
            get {
                return this.line2Field;
            }
            set {
                this.line2Field = value;
                this.RaisePropertyChanged("Line2");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string Line3 {
            get {
                return this.line3Field;
            }
            set {
                this.line3Field = value;
                this.RaisePropertyChanged("Line3");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(Form=System.Xml.Schema.XmlSchemaForm.Qualified, Namespace="http://www.w3.org/XML/1998/namespace")]
        public string lang {
            get {
                return this.langField;
            }
            set {
                this.langField = value;
                this.RaisePropertyChanged("lang");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FormattedPOBoxAddress))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FormattedGeneralDeliveryAddress))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FormattedRuralRouteAddress))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FormattedStreetAddress))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FormattedPOBoxWithStreetAddress))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(FormattedInvalidAddress))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class Address : object, System.ComponentModel.INotifyPropertyChanged {
        
        private StatusCode statusCodeField;
        
        private int confidenceLevelField;
        
        private InfoReasonCode infoReasonCodesField;
        
        private WarningReasonCode warningReasonCodesField;
        
        private ErrorReasonCode errorReasonCodesField;
        
        private System.Nullable<AddressRecordType> addressTypeField;
        
        private string municipalityNameField;
        
        private System.Nullable<ProvinceCodeType1> provinceCodeField;
        
        private string postalCodeField;
        
        private System.Nullable<decimal> latitudeField;
        
        private System.Nullable<decimal> longitudeField;
        
        private FormattedAddressLines[] formattedField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public StatusCode StatusCode {
            get {
                return this.statusCodeField;
            }
            set {
                this.statusCodeField = value;
                this.RaisePropertyChanged("StatusCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int ConfidenceLevel {
            get {
                return this.confidenceLevelField;
            }
            set {
                this.confidenceLevelField = value;
                this.RaisePropertyChanged("ConfidenceLevel");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public InfoReasonCode InfoReasonCodes {
            get {
                return this.infoReasonCodesField;
            }
            set {
                this.infoReasonCodesField = value;
                this.RaisePropertyChanged("InfoReasonCodes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public WarningReasonCode WarningReasonCodes {
            get {
                return this.warningReasonCodesField;
            }
            set {
                this.warningReasonCodesField = value;
                this.RaisePropertyChanged("WarningReasonCodes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public ErrorReasonCode ErrorReasonCodes {
            get {
                return this.errorReasonCodesField;
            }
            set {
                this.errorReasonCodesField = value;
                this.RaisePropertyChanged("ErrorReasonCodes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
        public System.Nullable<AddressRecordType> AddressType {
            get {
                return this.addressTypeField;
            }
            set {
                this.addressTypeField = value;
                this.RaisePropertyChanged("AddressType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string MunicipalityName {
            get {
                return this.municipalityNameField;
            }
            set {
                this.municipalityNameField = value;
                this.RaisePropertyChanged("MunicipalityName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
        public System.Nullable<ProvinceCodeType1> ProvinceCode {
            get {
                return this.provinceCodeField;
            }
            set {
                this.provinceCodeField = value;
                this.RaisePropertyChanged("ProvinceCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string PostalCode {
            get {
                return this.postalCodeField;
            }
            set {
                this.postalCodeField = value;
                this.RaisePropertyChanged("PostalCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
        public System.Nullable<decimal> Latitude {
            get {
                return this.latitudeField;
            }
            set {
                this.latitudeField = value;
                this.RaisePropertyChanged("Latitude");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
        public System.Nullable<decimal> Longitude {
            get {
                return this.longitudeField;
            }
            set {
                this.longitudeField = value;
                this.RaisePropertyChanged("Longitude");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Formatted", Order=11)]
        public FormattedAddressLines[] Formatted {
            get {
                return this.formattedField;
            }
            set {
                this.formattedField = value;
                this.RaisePropertyChanged("Formatted");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public enum StatusCode {
        
        /// <remarks/>
        Valid,
        
        /// <remarks/>
        Invalid,
        
        /// <remarks/>
        ValidRangeOnly,
        
        /// <remarks/>
        Unparsable,
        
        /// <remarks/>
        IncompleteInput,
        
        /// <remarks/>
        NotCanadian,
    }
    
    /// <remarks/>
    [System.FlagsAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public enum InfoReasonCode {
        
        /// <remarks/>
        BuildingFound = 1,
        
        /// <remarks/>
        LargeVolumeReceiverFound = 2,
        
        /// <remarks/>
        GovtBuildingFound = 4,
        
        /// <remarks/>
        BusinessNameFound = 8,
        
        /// <remarks/>
        FarmFound = 16,
        
        /// <remarks/>
        DeliveryInstallationAdded = 32,
        
        /// <remarks/>
        StreetAddressMissing = 64,
        
        /// <remarks/>
        MultipleMatchesFound = 128,
    }
    
    /// <remarks/>
    [System.FlagsAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public enum WarningReasonCode : long {
        
        /// <remarks/>
        SuiteAdded = 1,
        
        /// <remarks/>
        SuiteChanged = 2,
        
        /// <remarks/>
        SuiteRemoved = 4,
        
        /// <remarks/>
        StreetNumberAdded = 8,
        
        /// <remarks/>
        StreetNumberChanged = 16,
        
        /// <remarks/>
        StreetNumberRemoved = 32,
        
        /// <remarks/>
        StreetNumberSuffixAdded = 64,
        
        /// <remarks/>
        StreetNumberSuffixChanged = 128,
        
        /// <remarks/>
        StreetNumberSuffixRemoved = 256,
        
        /// <remarks/>
        StreetAdded = 512,
        
        /// <remarks/>
        StreetChanged = 1024,
        
        /// <remarks/>
        StreetRemoved = 2048,
        
        /// <remarks/>
        FuzzyStreetUsed = 4096,
        
        /// <remarks/>
        AlternateStreetUsed = 8192,
        
        /// <remarks/>
        StreetTypeAdded = 16384,
        
        /// <remarks/>
        StreetTypeChanged = 32768,
        
        /// <remarks/>
        StreetTypeRemoved = 65536,
        
        /// <remarks/>
        StreetDirectionAdded = 131072,
        
        /// <remarks/>
        StreetDirectionChanged = 262144,
        
        /// <remarks/>
        StreetDirectionRemoved = 524288,
        
        /// <remarks/>
        MunicipalityAdded = 1048576,
        
        /// <remarks/>
        MunicipalityChanged = 2097152,
        
        /// <remarks/>
        FuzzyMunicipalityUsed = 4194304,
        
        /// <remarks/>
        AlternateMunicipalityUsed = 8388608,
        
        /// <remarks/>
        ProvinceAdded = 16777216,
        
        /// <remarks/>
        ProvinceChanged = 33554432,
        
        /// <remarks/>
        PostalCodeAdded = 67108864,
        
        /// <remarks/>
        PostalCodeChanged = 134217728,
        
        /// <remarks/>
        DeliveryInstallationChanged = 268435456,
        
        /// <remarks/>
        DeliveryInstallationRemoved = 536870912,
        
        /// <remarks/>
        BusinessNameAdded = 1073741824,
        
        /// <remarks/>
        BusinessNameChanged = 2147483648,
        
        /// <remarks/>
        BusinessNameRemoved = 4294967296,
        
        /// <remarks/>
        FuzzyBusinessNameUsed = 8589934592,
        
        /// <remarks/>
        POBoxAdded = ***********,
        
        /// <remarks/>
        POBoxChanged = ***********,
        
        /// <remarks/>
        POBoxRemoved = ***********,
        
        /// <remarks/>
        RuralRouteAdded = 137438953472,
        
        /// <remarks/>
        RuralRouteChanged = 274877906944,
        
        /// <remarks/>
        RuralRouteRemoved = 549755813888,
        
        /// <remarks/>
        GDAdded = 1099511627776,
        
        /// <remarks/>
        GDChanged = 2199023255552,
        
        /// <remarks/>
        GDRemoved = 4398046511104,
        
        /// <remarks/>
        StreetAddressRemoved = 8796093022208,
        
        /// <remarks/>
        POBoxAddressRemoved = 17592186044416,
        
        /// <remarks/>
        RuralAddressRemoved = 35184372088832,
        
        /// <remarks/>
        GDAddressRemoved = 70368744177664,
        
        /// <remarks/>
        StreetWithPOBoxAddressRemoved = 140737488355328,
        
        /// <remarks/>
        InvalidCharacterRemoved = 281474976710656,
    }
    
    /// <remarks/>
    [System.FlagsAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public enum ErrorReasonCode {
        
        /// <remarks/>
        SuiteNotFound = 1,
        
        /// <remarks/>
        SuiteMissing = 2,
        
        /// <remarks/>
        SuiteInvalid = 4,
        
        /// <remarks/>
        StreetNumberNotFound = 8,
        
        /// <remarks/>
        StreetNumberInvalid = 16,
        
        /// <remarks/>
        StreetNumberSuffixNotFound = 32,
        
        /// <remarks/>
        StreetNumberSuffixMissing = 64,
        
        /// <remarks/>
        StreetNotFound = 128,
        
        /// <remarks/>
        StreetMissing = 256,
        
        /// <remarks/>
        StreetDirectionMissing = 512,
        
        /// <remarks/>
        StreetDirectionInvalid = 1024,
        
        /// <remarks/>
        StreetTypeMissing = 2048,
        
        /// <remarks/>
        StreetTypeInvalid = 4096,
        
        /// <remarks/>
        MunicipalityNotFound = 8192,
        
        /// <remarks/>
        MunicipalityMissing = 16384,
        
        /// <remarks/>
        ProvinceInvalid = 32768,
        
        /// <remarks/>
        ProvinceMissing = 65536,
        
        /// <remarks/>
        POBoxNotFound = 131072,
        
        /// <remarks/>
        POBoxMissing = 262144,
        
        /// <remarks/>
        POBoxInvalid = 524288,
        
        /// <remarks/>
        RuralRouteNotFound = 1048576,
        
        /// <remarks/>
        RuralRouteMissing = 2097152,
        
        /// <remarks/>
        RuralRouteInvalid = 4194304,
        
        /// <remarks/>
        PostalCodeInvalid = 8388608,
        
        /// <remarks/>
        PostalCodeMissing = 16777216,
        
        /// <remarks/>
        InvalidInputNumber = 33554432,
        
        /// <remarks/>
        GDNotFound = 67108864,
        
        /// <remarks/>
        GDMissing = 134217728,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public enum AddressRecordType {
        
        /// <remarks/>
        Street,
        
        /// <remarks/>
        POBox,
        
        /// <remarks/>
        RuralRoute,
        
        /// <remarks/>
        GeneralDelivery,
        
        /// <remarks/>
        POBoxWithStreet,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="ProvinceCodeType", Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public enum ProvinceCodeType1 {
        
        /// <remarks/>
        AB,
        
        /// <remarks/>
        BC,
        
        /// <remarks/>
        MB,
        
        /// <remarks/>
        NB,
        
        /// <remarks/>
        NL,
        
        /// <remarks/>
        NS,
        
        /// <remarks/>
        NT,
        
        /// <remarks/>
        NU,
        
        /// <remarks/>
        ON,
        
        /// <remarks/>
        PE,
        
        /// <remarks/>
        QC,
        
        /// <remarks/>
        SK,
        
        /// <remarks/>
        YT,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class FormattedPOBoxAddress : Address {
        
        private POBoxAddressType pOBoxField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public POBoxAddressType POBox {
            get {
                return this.pOBoxField;
            }
            set {
                this.pOBoxField = value;
                this.RaisePropertyChanged("POBox");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class FormattedGeneralDeliveryAddress : Address {
        
        private GeneralDeliveryAddressType generalDeliveryField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public GeneralDeliveryAddressType GeneralDelivery {
            get {
                return this.generalDeliveryField;
            }
            set {
                this.generalDeliveryField = value;
                this.RaisePropertyChanged("GeneralDelivery");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class FormattedRuralRouteAddress : Address {
        
        private RuralRouteAddressType ruralField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public RuralRouteAddressType Rural {
            get {
                return this.ruralField;
            }
            set {
                this.ruralField = value;
                this.RaisePropertyChanged("Rural");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class FormattedStreetAddress : Address {
        
        private StreetAddressType streetField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public StreetAddressType Street {
            get {
                return this.streetField;
            }
            set {
                this.streetField = value;
                this.RaisePropertyChanged("Street");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class FormattedPOBoxWithStreetAddress : Address {
        
        private POBoxWithStreetAddressType pOBoxWithStreetAddressField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public POBoxWithStreetAddressType POBoxWithStreetAddress {
            get {
                return this.pOBoxWithStreetAddressField;
            }
            set {
                this.pOBoxWithStreetAddressField = value;
                this.RaisePropertyChanged("POBoxWithStreetAddress");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class FormattedInvalidAddress : Address {
        
        private InvalidAddressType invalidField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public InvalidAddressType Invalid {
            get {
                return this.invalidField;
            }
            set {
                this.invalidField = value;
                this.RaisePropertyChanged("Invalid");
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/cmb/2015/01")]
    public partial class CMBDetailsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string addressValidationStatusField;
        
        private Address addressField;
        
        private string depotSiteIDField;
        
        private string depotSiteNameField;
        
        private string deliveryModeNumberField;
        
        private string deliveryModeTypeField;
        
        private string deliveryModeDescriptionEnField;
        
        private string deliveryModeDescriptionFrField;
        
        private string transferPointTypeField;
        
        private string transferPointDescriptionEnField;
        
        private string transferPointDescriptionFrField;
        
        private bool consumerChoiceFlagField;
        
        private CMBInfoType cMBInfoField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string AddressValidationStatus {
            get {
                return this.addressValidationStatusField;
            }
            set {
                this.addressValidationStatusField = value;
                this.RaisePropertyChanged("AddressValidationStatus");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Address Address {
            get {
                return this.addressField;
            }
            set {
                this.addressField = value;
                this.RaisePropertyChanged("Address");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string DepotSiteID {
            get {
                return this.depotSiteIDField;
            }
            set {
                this.depotSiteIDField = value;
                this.RaisePropertyChanged("DepotSiteID");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string DepotSiteName {
            get {
                return this.depotSiteNameField;
            }
            set {
                this.depotSiteNameField = value;
                this.RaisePropertyChanged("DepotSiteName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string DeliveryModeNumber {
            get {
                return this.deliveryModeNumberField;
            }
            set {
                this.deliveryModeNumberField = value;
                this.RaisePropertyChanged("DeliveryModeNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string DeliveryModeType {
            get {
                return this.deliveryModeTypeField;
            }
            set {
                this.deliveryModeTypeField = value;
                this.RaisePropertyChanged("DeliveryModeType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string DeliveryModeDescriptionEn {
            get {
                return this.deliveryModeDescriptionEnField;
            }
            set {
                this.deliveryModeDescriptionEnField = value;
                this.RaisePropertyChanged("DeliveryModeDescriptionEn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=7)]
        public string DeliveryModeDescriptionFr {
            get {
                return this.deliveryModeDescriptionFrField;
            }
            set {
                this.deliveryModeDescriptionFrField = value;
                this.RaisePropertyChanged("DeliveryModeDescriptionFr");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=8)]
        public string TransferPointType {
            get {
                return this.transferPointTypeField;
            }
            set {
                this.transferPointTypeField = value;
                this.RaisePropertyChanged("TransferPointType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=9)]
        public string TransferPointDescriptionEn {
            get {
                return this.transferPointDescriptionEnField;
            }
            set {
                this.transferPointDescriptionEnField = value;
                this.RaisePropertyChanged("TransferPointDescriptionEn");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=10)]
        public string TransferPointDescriptionFr {
            get {
                return this.transferPointDescriptionFrField;
            }
            set {
                this.transferPointDescriptionFrField = value;
                this.RaisePropertyChanged("TransferPointDescriptionFr");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=11)]
        public bool ConsumerChoiceFlag {
            get {
                return this.consumerChoiceFlagField;
            }
            set {
                this.consumerChoiceFlagField = value;
                this.RaisePropertyChanged("ConsumerChoiceFlag");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=12)]
        public CMBInfoType CMBInfo {
            get {
                return this.cMBInfoField;
            }
            set {
                this.cMBInfoField = value;
                this.RaisePropertyChanged("CMBInfo");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://aws.canadapost.ca/2015/01", ConfigurationName="AWSServiceProxy.IAddressLookup")]
    public interface IAddressLookup {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetCMBDetails", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CustomFaultMsg), Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetCMBDetails", Name="CustomFaultMsg", Namespace="http://www.canadapost.ca/ws/address/2015/01")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CMBDetailsType GetCMBDetails(string AddressLine1, string MunicipalityName, Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.ProvinceCodeType ProvinceCode, string PostalCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetCMBDetails", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CMBDetailsType> GetCMBDetailsAsync(string AddressLine1, string MunicipalityName, Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.ProvinceCodeType ProvinceCode, string PostalCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetFSADetails", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CustomFaultMsg), Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetFSADetails", Name="CustomFaultMsg", Namespace="http://www.canadapost.ca/ws/address/2015/01")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.FSADetailsType GetFSADetails(string PostalCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetFSADetails", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.FSADetailsType> GetFSADetailsAsync(string PostalCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetMunicipalityList", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.MunicipalityNames GetMunicipalityList(string ProvinceCode, string MunicipalitySearchCriteria, bool SuppressAccent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetMunicipalityList", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.MunicipalityNames> GetMunicipalityListAsync(string ProvinceCode, string MunicipalitySearchCriteria, bool SuppressAccent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetPostalCodeDetails", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CustomFaultMsg), Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetPostalCodeDetails", Name="CustomFaultMsg", Namespace="http://www.canadapost.ca/ws/address/2015/01")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetPostalCodeDetailsResponse GetPostalCodeDetails(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetPostalCodeDetailsRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetPostalCodeDetails", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetPostalCodeDetailsResponse> GetPostalCodeDetailsAsync(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetPostalCodeDetailsRequest request);
        
        // CODEGEN: Parameter 'GetStreetTypeListResult' requires additional schema information that cannot be captured using the parameter mode. The specific attribute is 'System.Xml.Serialization.XmlArrayItemAttribute'.
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetStreetTypeList", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListResponse GetStreetTypeList(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/GetStreetTypeList", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListResponse> GetStreetTypeListAsync(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListRequest request);
        
        // CODEGEN: Parameter 'LookupGeneralDeliveryAddressResult' requires additional schema information that cannot be captured using the parameter mode. The specific attribute is 'System.Xml.Serialization.XmlArrayAttribute'.
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupGeneralDeliveryAddress", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CustomFaultMsg), Action="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupGeneralDeliveryAddress", Name="CustomFaultMsg", Namespace="http://www.canadapost.ca/ws/address/2015/01")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressResponse LookupGeneralDeliveryAddress(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupGeneralDeliveryAddress", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressResponse> LookupGeneralDeliveryAddressAsync(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressRequest request);
        
        // CODEGEN: Parameter 'LookupPOBoxAddressResult' requires additional schema information that cannot be captured using the parameter mode. The specific attribute is 'System.Xml.Serialization.XmlArrayAttribute'.
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupPOBoxAddress", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CustomFaultMsg), Action="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupPOBoxAddress", Name="CustomFaultMsg", Namespace="http://www.canadapost.ca/ws/address/2015/01")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressResponse LookupPOBoxAddress(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupPOBoxAddress", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressResponse> LookupPOBoxAddressAsync(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressRequest request);
        
        // CODEGEN: Parameter 'LookupRuralAddressResult' requires additional schema information that cannot be captured using the parameter mode. The specific attribute is 'System.Xml.Serialization.XmlArrayAttribute'.
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupRuralAddress", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CustomFaultMsg), Action="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupRuralAddress", Name="CustomFaultMsg", Namespace="http://www.canadapost.ca/ws/address/2015/01")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressResponse LookupRuralAddress(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupRuralAddress", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressResponse> LookupRuralAddressAsync(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressRequest request);
        
        // CODEGEN: Parameter 'LookupStreetAddressResult' requires additional schema information that cannot be captured using the parameter mode. The specific attribute is 'System.Xml.Serialization.XmlArrayAttribute'.
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupStreetAddress", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CustomFaultMsg), Action="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupStreetAddress", Name="CustomFaultMsg", Namespace="http://www.canadapost.ca/ws/address/2015/01")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressResponse LookupStreetAddress(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupStreetAddress", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressResponse> LookupStreetAddressAsync(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateAddress1", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CustomFaultMsg), Action="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateAddress1", Name="CustomFaultMsg", Namespace="http://www.canadapost.ca/ws/address/2015/01")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="Addresses")]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[] ValidateAddress1(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.ParseAndValidateAddress1Request[] CollectionRequest, bool SuppressAccent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateAddress1", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="Addresses")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[]> ValidateAddress1Async(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.ParseAndValidateAddress1Request[] CollectionRequest, bool SuppressAccent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateAddress2", ReplyAction="*")]
        [System.ServiceModel.FaultContractAttribute(typeof(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CustomFaultMsg), Action="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateAddress2", Name="CustomFaultMsg", Namespace="http://www.canadapost.ca/ws/address/2015/01")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="Addresses")]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[] ValidateAddress2(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.AddressInput[] ValidateAddress2Request, bool SuppressAccent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateAddress2", ReplyAction="*")]
        [return: System.ServiceModel.MessageParameterAttribute(Name="Addresses")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[]> ValidateAddress2Async(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.AddressInput[] ValidateAddress2Request, bool SuppressAccent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidatePOBoxAddress", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[] ValidatePOBoxAddress(string BusinessName, int POBoxNumber, string MunicipalityName, string ProvinceCode, string PostalCode, bool SuppressAccent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidatePOBoxAddress", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[]> ValidatePOBoxAddressAsync(string BusinessName, int POBoxNumber, string MunicipalityName, string ProvinceCode, string PostalCode, bool SuppressAccent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateRuralAddress", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[] ValidateRuralAddress(string BusinessName, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string ServiceType, int ServiceNumber, string MunicipalityName, string ProvinceCode, string PostalCode, bool SuppressAccent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateRuralAddress", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[]> ValidateRuralAddressAsync(string BusinessName, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string ServiceType, int ServiceNumber, string MunicipalityName, string ProvinceCode, string PostalCode, bool SuppressAccent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateStreetAddress", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[] ValidateStreetAddress(string BusinessName, string BuildingName, string SuiteNumber, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string MunicipalityName, string ProvinceCode, string PostalCode, bool SuppressAccent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateStreetAddress", ReplyAction="*")]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[]> ValidateStreetAddressAsync(string BusinessName, string BuildingName, string SuiteNumber, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string MunicipalityName, string ProvinceCode, string PostalCode, bool SuppressAccent);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public enum ProvinceCodeType {
        
        /// <remarks/>
        AB,
        
        /// <remarks/>
        BC,
        
        /// <remarks/>
        MB,
        
        /// <remarks/>
        NB,
        
        /// <remarks/>
        NL,
        
        /// <remarks/>
        NS,
        
        /// <remarks/>
        NT,
        
        /// <remarks/>
        NU,
        
        /// <remarks/>
        ON,
        
        /// <remarks/>
        PE,
        
        /// <remarks/>
        QC,
        
        /// <remarks/>
        SK,
        
        /// <remarks/>
        YT,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class FSADetailsType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string fSAField;
        
        private System.Nullable<System.DateTime> cMBImplementationDateField;
        
        private string cMBImplementationStateField;
        
        private FSAStatus fSAStatusField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string FSA {
            get {
                return this.fSAField;
            }
            set {
                this.fSAField = value;
                this.RaisePropertyChanged("FSA");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="date", IsNullable=true, Order=1)]
        public System.Nullable<System.DateTime> CMBImplementationDate {
            get {
                return this.cMBImplementationDateField;
            }
            set {
                this.cMBImplementationDateField = value;
                this.RaisePropertyChanged("CMBImplementationDate");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string CMBImplementationState {
            get {
                return this.cMBImplementationStateField;
            }
            set {
                this.cMBImplementationStateField = value;
                this.RaisePropertyChanged("CMBImplementationState");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public FSAStatus FSAStatus {
            get {
                return this.fSAStatusField;
            }
            set {
                this.fSAStatusField = value;
                this.RaisePropertyChanged("FSAStatus");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public enum FSAStatus {
        
        /// <remarks/>
        Unknown,
        
        /// <remarks/>
        Invalid,
        
        /// <remarks/>
        OK,
        
        /// <remarks/>
        NotFound,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class MunicipalityNames : object, System.ComponentModel.INotifyPropertyChanged {
        
        private Province[] provincesField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=0)]
        public Province[] Provinces {
            get {
                return this.provincesField;
            }
            set {
                this.provincesField = value;
                this.RaisePropertyChanged("Provinces");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class Province : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string codeField;
        
        private MunicipalityName[] municipalityNamesField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Code {
            get {
                return this.codeField;
            }
            set {
                this.codeField = value;
                this.RaisePropertyChanged("Code");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        public MunicipalityName[] MunicipalityNames {
            get {
                return this.municipalityNamesField;
            }
            set {
                this.municipalityNamesField = value;
                this.RaisePropertyChanged("MunicipalityNames");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class MunicipalityName : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string valueField;
        
        private AlternateMunicipalityName[] alternateMunicipalityNamesField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        public AlternateMunicipalityName[] AlternateMunicipalityNames {
            get {
                return this.alternateMunicipalityNamesField;
            }
            set {
                this.alternateMunicipalityNamesField = value;
                this.RaisePropertyChanged("AlternateMunicipalityNames");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class AlternateMunicipalityName : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string valueField;
        
        private bool isValidField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public bool IsValid {
            get {
                return this.isValidField;
            }
            set {
                this.isValidField = value;
                this.RaisePropertyChanged("IsValid");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class PostalCode : object, System.ComponentModel.INotifyPropertyChanged {
        
        private PostalCodeType typeField;
        
        private string valueField;
        
        private string provinceCodeField;
        
        private Municipality[] municipalityField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public PostalCodeType Type {
            get {
                return this.typeField;
            }
            set {
                this.typeField = value;
                this.RaisePropertyChanged("Type");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string ProvinceCode {
            get {
                return this.provinceCodeField;
            }
            set {
                this.provinceCodeField = value;
                this.RaisePropertyChanged("ProvinceCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Municipality", Order=3)]
        public Municipality[] Municipality {
            get {
                return this.municipalityField;
            }
            set {
                this.municipalityField = value;
                this.RaisePropertyChanged("Municipality");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.FlagsAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public enum PostalCodeType {
        
        /// <remarks/>
        Civic = 1,
        
        /// <remarks/>
        Building = 2,
        
        /// <remarks/>
        GD = 4,
        
        /// <remarks/>
        Route = 8,
        
        /// <remarks/>
        Unique = 16,
        
        /// <remarks/>
        POBox = 32,
        
        /// <remarks/>
        DeliveryInstallation = 64,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class Municipality : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string nameField;
        
        private Street[] streetsField;
        
        private RuralRoutes ruralRoutesField;
        
        private POBoxes pOBoxesField;
        
        private GeneralDelivery[] generalDeliveriesField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=1)]
        public Street[] Streets {
            get {
                return this.streetsField;
            }
            set {
                this.streetsField = value;
                this.RaisePropertyChanged("Streets");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public RuralRoutes RuralRoutes {
            get {
                return this.ruralRoutesField;
            }
            set {
                this.ruralRoutesField = value;
                this.RaisePropertyChanged("RuralRoutes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public POBoxes POBoxes {
            get {
                return this.pOBoxesField;
            }
            set {
                this.pOBoxesField = value;
                this.RaisePropertyChanged("POBoxes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("GeneralDeliveries", Order=4)]
        public GeneralDelivery[] GeneralDeliveries {
            get {
                return this.generalDeliveriesField;
            }
            set {
                this.generalDeliveriesField = value;
                this.RaisePropertyChanged("GeneralDeliveries");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class Street : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string nameField;
        
        private string typeField;
        
        private string directionField;
        
        private StreetNumberRange[] numberRangeField;
        
        private StreetNumber[] numberField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Type {
            get {
                return this.typeField;
            }
            set {
                this.typeField = value;
                this.RaisePropertyChanged("Type");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string Direction {
            get {
                return this.directionField;
            }
            set {
                this.directionField = value;
                this.RaisePropertyChanged("Direction");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("NumberRange", Order=3)]
        public StreetNumberRange[] NumberRange {
            get {
                return this.numberRangeField;
            }
            set {
                this.numberRangeField = value;
                this.RaisePropertyChanged("NumberRange");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Number", Order=4)]
        public StreetNumber[] Number {
            get {
                return this.numberField;
            }
            set {
                this.numberField = value;
                this.RaisePropertyChanged("Number");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class StreetNumberRange : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string fromField;
        
        private string toField;
        
        private string typeField;
        
        private int numberFromField;
        
        private int numberToField;
        
        private string suffixFromField;
        
        private string suffixToField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string From {
            get {
                return this.fromField;
            }
            set {
                this.fromField = value;
                this.RaisePropertyChanged("From");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string To {
            get {
                return this.toField;
            }
            set {
                this.toField = value;
                this.RaisePropertyChanged("To");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string Type {
            get {
                return this.typeField;
            }
            set {
                this.typeField = value;
                this.RaisePropertyChanged("Type");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public int NumberFrom {
            get {
                return this.numberFromField;
            }
            set {
                this.numberFromField = value;
                this.RaisePropertyChanged("NumberFrom");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public int NumberTo {
            get {
                return this.numberToField;
            }
            set {
                this.numberToField = value;
                this.RaisePropertyChanged("NumberTo");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string SuffixFrom {
            get {
                return this.suffixFromField;
            }
            set {
                this.suffixFromField = value;
                this.RaisePropertyChanged("SuffixFrom");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public string SuffixTo {
            get {
                return this.suffixToField;
            }
            set {
                this.suffixToField = value;
                this.RaisePropertyChanged("SuffixTo");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class StreetNumber : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string nameField;
        
        private string valueField;
        
        private string numberField;
        
        private string suffixField;
        
        private SuiteRange[] suiteRangesField;
        
        private Suite[] suitesField;
        
        private Address1 occupantDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string Number {
            get {
                return this.numberField;
            }
            set {
                this.numberField = value;
                this.RaisePropertyChanged("Number");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string Suffix {
            get {
                return this.suffixField;
            }
            set {
                this.suffixField = value;
                this.RaisePropertyChanged("Suffix");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("SuiteRanges", Order=4)]
        public SuiteRange[] SuiteRanges {
            get {
                return this.suiteRangesField;
            }
            set {
                this.suiteRangesField = value;
                this.RaisePropertyChanged("SuiteRanges");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Suites", Order=5)]
        public Suite[] Suites {
            get {
                return this.suitesField;
            }
            set {
                this.suitesField = value;
                this.RaisePropertyChanged("Suites");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=6)]
        public Address1 OccupantDetails {
            get {
                return this.occupantDetailsField;
            }
            set {
                this.occupantDetailsField = value;
                this.RaisePropertyChanged("OccupantDetails");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class SuiteRange : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string fromField;
        
        private string toField;
        
        private string nameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string From {
            get {
                return this.fromField;
            }
            set {
                this.fromField = value;
                this.RaisePropertyChanged("From");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string To {
            get {
                return this.toField;
            }
            set {
                this.toField = value;
                this.RaisePropertyChanged("To");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class Suite : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string valueField;
        
        private string nameField;
        
        private Address1 occupantDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public Address1 OccupantDetails {
            get {
                return this.occupantDetailsField;
            }
            set {
                this.occupantDetailsField = value;
                this.RaisePropertyChanged("OccupantDetails");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="Address", Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class Address1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string primaryTypeCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string PrimaryTypeCode {
            get {
                return this.primaryTypeCodeField;
            }
            set {
                this.primaryTypeCodeField = value;
                this.RaisePropertyChanged("PrimaryTypeCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class RuralRoutes : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string installationTypeCodeField;
        
        private string installationNameField;
        
        private string installationAreaField;
        
        private RuralRoute[] routeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string InstallationTypeCode {
            get {
                return this.installationTypeCodeField;
            }
            set {
                this.installationTypeCodeField = value;
                this.RaisePropertyChanged("InstallationTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string InstallationName {
            get {
                return this.installationNameField;
            }
            set {
                this.installationNameField = value;
                this.RaisePropertyChanged("InstallationName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string InstallationArea {
            get {
                return this.installationAreaField;
            }
            set {
                this.installationAreaField = value;
                this.RaisePropertyChanged("InstallationArea");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Route", Order=3)]
        public RuralRoute[] Route {
            get {
                return this.routeField;
            }
            set {
                this.routeField = value;
                this.RaisePropertyChanged("Route");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class RuralRoute : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string valueField;
        
        private string deliveryModeField;
        
        private int deliveryNumberField;
        
        private Address1[] addressCollectionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string DeliveryMode {
            get {
                return this.deliveryModeField;
            }
            set {
                this.deliveryModeField = value;
                this.RaisePropertyChanged("DeliveryMode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public int DeliveryNumber {
            get {
                return this.deliveryNumberField;
            }
            set {
                this.deliveryNumberField = value;
                this.RaisePropertyChanged("DeliveryNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AddressCollection", Order=3)]
        public Address1[] AddressCollection {
            get {
                return this.addressCollectionField;
            }
            set {
                this.addressCollectionField = value;
                this.RaisePropertyChanged("AddressCollection");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class POBoxes : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string installationTypeCodeField;
        
        private string installationNameField;
        
        private string installationAreaField;
        
        private POBoxNumberRange[] numberRangeField;
        
        private POBoxNumber[] numberField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string InstallationTypeCode {
            get {
                return this.installationTypeCodeField;
            }
            set {
                this.installationTypeCodeField = value;
                this.RaisePropertyChanged("InstallationTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string InstallationName {
            get {
                return this.installationNameField;
            }
            set {
                this.installationNameField = value;
                this.RaisePropertyChanged("InstallationName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string InstallationArea {
            get {
                return this.installationAreaField;
            }
            set {
                this.installationAreaField = value;
                this.RaisePropertyChanged("InstallationArea");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("NumberRange", Order=3)]
        public POBoxNumberRange[] NumberRange {
            get {
                return this.numberRangeField;
            }
            set {
                this.numberRangeField = value;
                this.RaisePropertyChanged("NumberRange");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("Number", Order=4)]
        public POBoxNumber[] Number {
            get {
                return this.numberField;
            }
            set {
                this.numberField = value;
                this.RaisePropertyChanged("Number");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class POBoxNumberRange : object, System.ComponentModel.INotifyPropertyChanged {
        
        private int fromField;
        
        private int toField;
        
        private Address1 occupantDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public int From {
            get {
                return this.fromField;
            }
            set {
                this.fromField = value;
                this.RaisePropertyChanged("From");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int To {
            get {
                return this.toField;
            }
            set {
                this.toField = value;
                this.RaisePropertyChanged("To");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public Address1 OccupantDetails {
            get {
                return this.occupantDetailsField;
            }
            set {
                this.occupantDetailsField = value;
                this.RaisePropertyChanged("OccupantDetails");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class POBoxNumber : object, System.ComponentModel.INotifyPropertyChanged {
        
        private int valueField;
        
        private Address1 occupantDetailsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public int Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public Address1 OccupantDetails {
            get {
                return this.occupantDetailsField;
            }
            set {
                this.occupantDetailsField = value;
                this.RaisePropertyChanged("OccupantDetails");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class GeneralDelivery : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string deliveryModeField;
        
        private string installationTypeCodeField;
        
        private string installationNameField;
        
        private string installationAreaField;
        
        private Address1[] addressCollectionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string DeliveryMode {
            get {
                return this.deliveryModeField;
            }
            set {
                this.deliveryModeField = value;
                this.RaisePropertyChanged("DeliveryMode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string InstallationTypeCode {
            get {
                return this.installationTypeCodeField;
            }
            set {
                this.installationTypeCodeField = value;
                this.RaisePropertyChanged("InstallationTypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string InstallationName {
            get {
                return this.installationNameField;
            }
            set {
                this.installationNameField = value;
                this.RaisePropertyChanged("InstallationName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string InstallationArea {
            get {
                return this.installationAreaField;
            }
            set {
                this.installationAreaField = value;
                this.RaisePropertyChanged("InstallationArea");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("AddressCollection", Order=4)]
        public Address1[] AddressCollection {
            get {
                return this.addressCollectionField;
            }
            set {
                this.addressCollectionField = value;
                this.RaisePropertyChanged("AddressCollection");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetPostalCodeDetails", WrapperNamespace="http://aws.canadapost.ca/2015/01", IsWrapped=true)]
    public partial class GetPostalCodeDetailsRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=0)]
        public string PostalCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=1)]
        public string BusinessName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=2)]
        public bool SuppressAccent;
        
        public GetPostalCodeDetailsRequest() {
        }
        
        public GetPostalCodeDetailsRequest(string PostalCode, string BusinessName, bool SuppressAccent) {
            this.PostalCode = PostalCode;
            this.BusinessName = BusinessName;
            this.SuppressAccent = SuppressAccent;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetPostalCodeDetailsResponse", WrapperNamespace="http://aws.canadapost.ca/2015/01", IsWrapped=true)]
    public partial class GetPostalCodeDetailsResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=0)]
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode PostalCode;
        
        public GetPostalCodeDetailsResponse() {
        }
        
        public GetPostalCodeDetailsResponse(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode PostalCode) {
            this.PostalCode = PostalCode;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class StreetTypesLocalized : object, System.ComponentModel.INotifyPropertyChanged {
        
        private StreetType[] streetTypeField;
        
        private string langField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute("StreetType", Order=0)]
        public StreetType[] StreetType {
            get {
                return this.streetTypeField;
            }
            set {
                this.streetTypeField = value;
                this.RaisePropertyChanged("StreetType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAttributeAttribute(Form=System.Xml.Schema.XmlSchemaForm.Qualified, Namespace="http://www.w3.org/XML/1998/namespace")]
        public string lang {
            get {
                return this.langField;
            }
            set {
                this.langField = value;
                this.RaisePropertyChanged("lang");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class StreetType : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string codeField;
        
        private string descriptionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Code {
            get {
                return this.codeField;
            }
            set {
                this.codeField = value;
                this.RaisePropertyChanged("Code");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
                this.RaisePropertyChanged("Description");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetStreetTypeList", WrapperNamespace="http://aws.canadapost.ca/2015/01", IsWrapped=true)]
    public partial class GetStreetTypeListRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=0)]
        public bool SuppressAccent;
        
        public GetStreetTypeListRequest() {
        }
        
        public GetStreetTypeListRequest(bool SuppressAccent) {
            this.SuppressAccent = SuppressAccent;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetStreetTypeListResponse", WrapperNamespace="http://aws.canadapost.ca/2015/01", IsWrapped=true)]
    public partial class GetStreetTypeListResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute("StreetTypes", Namespace="http://www.canadapost.ca/ws/address/2015/01", IsNullable=false)]
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.StreetTypesLocalized[] GetStreetTypeListResult;
        
        public GetStreetTypeListResponse() {
        }
        
        public GetStreetTypeListResponse(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.StreetTypesLocalized[] GetStreetTypeListResult) {
            this.GetStreetTypeListResult = GetStreetTypeListResult;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="PostalCode", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class PostalCode1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private Municipality1[] municipalitiesField;
        
        private string provinceCodeField;
        
        private PostalCodeType1 typeField;
        
        private bool typeFieldSpecified;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
        public Municipality1[] Municipalities {
            get {
                return this.municipalitiesField;
            }
            set {
                this.municipalitiesField = value;
                this.RaisePropertyChanged("Municipalities");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string ProvinceCode {
            get {
                return this.provinceCodeField;
            }
            set {
                this.provinceCodeField = value;
                this.RaisePropertyChanged("ProvinceCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public PostalCodeType1 Type {
            get {
                return this.typeField;
            }
            set {
                this.typeField = value;
                this.RaisePropertyChanged("Type");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool TypeSpecified {
            get {
                return this.typeFieldSpecified;
            }
            set {
                this.typeFieldSpecified = value;
                this.RaisePropertyChanged("TypeSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="Municipality", Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class Municipality1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private GeneralDelivery1[] generalDeliveriesField;
        
        private string nameField;
        
        private string oldNameField;
        
        private POBoxes1 pOBoxesField;
        
        private RuralRoutes1 ruralRoutesField;
        
        private Street1[] streetsField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=0)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
        public GeneralDelivery1[] GeneralDeliveries {
            get {
                return this.generalDeliveriesField;
            }
            set {
                this.generalDeliveriesField = value;
                this.RaisePropertyChanged("GeneralDeliveries");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string OldName {
            get {
                return this.oldNameField;
            }
            set {
                this.oldNameField = value;
                this.RaisePropertyChanged("OldName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public POBoxes1 POBoxes {
            get {
                return this.pOBoxesField;
            }
            set {
                this.pOBoxesField = value;
                this.RaisePropertyChanged("POBoxes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
        public RuralRoutes1 RuralRoutes {
            get {
                return this.ruralRoutesField;
            }
            set {
                this.ruralRoutesField = value;
                this.RaisePropertyChanged("RuralRoutes");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=5)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
        public Street1[] Streets {
            get {
                return this.streetsField;
            }
            set {
                this.streetsField = value;
                this.RaisePropertyChanged("Streets");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="GeneralDelivery", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class GeneralDelivery1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private Address2[] addressCollectionField;
        
        private string dIAreaNameField;
        
        private string dIQualifierField;
        
        private string dITypeField;
        
        private string deliveryModeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=0)]
        public Address2[] AddressCollection {
            get {
                return this.addressCollectionField;
            }
            set {
                this.addressCollectionField = value;
                this.RaisePropertyChanged("AddressCollection");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string DIAreaName {
            get {
                return this.dIAreaNameField;
            }
            set {
                this.dIAreaNameField = value;
                this.RaisePropertyChanged("DIAreaName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string DIQualifier {
            get {
                return this.dIQualifierField;
            }
            set {
                this.dIQualifierField = value;
                this.RaisePropertyChanged("DIQualifier");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public string DIType {
            get {
                return this.dITypeField;
            }
            set {
                this.dITypeField = value;
                this.RaisePropertyChanged("DIType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
        public string DeliveryMode {
            get {
                return this.deliveryModeField;
            }
            set {
                this.deliveryModeField = value;
                this.RaisePropertyChanged("DeliveryMode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="Address", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class Address2 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string primaryTypeCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string PrimaryTypeCode {
            get {
                return this.primaryTypeCodeField;
            }
            set {
                this.primaryTypeCodeField = value;
                this.RaisePropertyChanged("PrimaryTypeCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="POBoxes", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class POBoxes1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string dIAreaNameField;
        
        private string dIQualifierField;
        
        private string dITypeField;
        
        private POBoxNumberRange1[] pOBoxNumberRangesField;
        
        private POBoxNumber1[] pOBoxNumbersField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string DIAreaName {
            get {
                return this.dIAreaNameField;
            }
            set {
                this.dIAreaNameField = value;
                this.RaisePropertyChanged("DIAreaName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string DIQualifier {
            get {
                return this.dIQualifierField;
            }
            set {
                this.dIQualifierField = value;
                this.RaisePropertyChanged("DIQualifier");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string DIType {
            get {
                return this.dITypeField;
            }
            set {
                this.dITypeField = value;
                this.RaisePropertyChanged("DIType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=3)]
        public POBoxNumberRange1[] POBoxNumberRanges {
            get {
                return this.pOBoxNumberRangesField;
            }
            set {
                this.pOBoxNumberRangesField = value;
                this.RaisePropertyChanged("POBoxNumberRanges");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=4)]
        public POBoxNumber1[] POBoxNumbers {
            get {
                return this.pOBoxNumbersField;
            }
            set {
                this.pOBoxNumbersField = value;
                this.RaisePropertyChanged("POBoxNumbers");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="POBoxNumberRange", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class POBoxNumberRange1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private int fromField;
        
        private bool fromFieldSpecified;
        
        private Address2 occupantDetailsField;
        
        private int toField;
        
        private bool toFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public int From {
            get {
                return this.fromField;
            }
            set {
                this.fromField = value;
                this.RaisePropertyChanged("From");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool FromSpecified {
            get {
                return this.fromFieldSpecified;
            }
            set {
                this.fromFieldSpecified = value;
                this.RaisePropertyChanged("FromSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public Address2 OccupantDetails {
            get {
                return this.occupantDetailsField;
            }
            set {
                this.occupantDetailsField = value;
                this.RaisePropertyChanged("OccupantDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public int To {
            get {
                return this.toField;
            }
            set {
                this.toField = value;
                this.RaisePropertyChanged("To");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ToSpecified {
            get {
                return this.toFieldSpecified;
            }
            set {
                this.toFieldSpecified = value;
                this.RaisePropertyChanged("ToSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="POBoxNumber", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class POBoxNumber1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private Address2 occupantDetailsField;
        
        private int valueField;
        
        private bool valueFieldSpecified;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public Address2 OccupantDetails {
            get {
                return this.occupantDetailsField;
            }
            set {
                this.occupantDetailsField = value;
                this.RaisePropertyChanged("OccupantDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool ValueSpecified {
            get {
                return this.valueFieldSpecified;
            }
            set {
                this.valueFieldSpecified = value;
                this.RaisePropertyChanged("ValueSpecified");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="RuralRoutes", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class RuralRoutes1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string dIAreaNameField;
        
        private string dIQualifierField;
        
        private string dITypeField;
        
        private RuralRoute1[] routesField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string DIAreaName {
            get {
                return this.dIAreaNameField;
            }
            set {
                this.dIAreaNameField = value;
                this.RaisePropertyChanged("DIAreaName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string DIQualifier {
            get {
                return this.dIQualifierField;
            }
            set {
                this.dIQualifierField = value;
                this.RaisePropertyChanged("DIQualifier");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string DIType {
            get {
                return this.dITypeField;
            }
            set {
                this.dITypeField = value;
                this.RaisePropertyChanged("DIType");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=3)]
        public RuralRoute1[] Routes {
            get {
                return this.routesField;
            }
            set {
                this.routesField = value;
                this.RaisePropertyChanged("Routes");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="RuralRoute", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class RuralRoute1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private Address2[] addressCollectionField;
        
        private string deliveryModeField;
        
        private int deliveryNumberField;
        
        private bool deliveryNumberFieldSpecified;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=0)]
        public Address2[] AddressCollection {
            get {
                return this.addressCollectionField;
            }
            set {
                this.addressCollectionField = value;
                this.RaisePropertyChanged("AddressCollection");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string DeliveryMode {
            get {
                return this.deliveryModeField;
            }
            set {
                this.deliveryModeField = value;
                this.RaisePropertyChanged("DeliveryMode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public int DeliveryNumber {
            get {
                return this.deliveryNumberField;
            }
            set {
                this.deliveryNumberField = value;
                this.RaisePropertyChanged("DeliveryNumber");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool DeliveryNumberSpecified {
            get {
                return this.deliveryNumberFieldSpecified;
            }
            set {
                this.deliveryNumberFieldSpecified = value;
                this.RaisePropertyChanged("DeliveryNumberSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="Street", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class Street1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string directionCodeField;
        
        private string nameField;
        
        private StreetNumberRange1[] streetNumberRangesField;
        
        private StreetNumber1[] streetNumbersField;
        
        private string typeCodeField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string DirectionCode {
            get {
                return this.directionCodeField;
            }
            set {
                this.directionCodeField = value;
                this.RaisePropertyChanged("DirectionCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=2)]
        public StreetNumberRange1[] StreetNumberRanges {
            get {
                return this.streetNumberRangesField;
            }
            set {
                this.streetNumberRangesField = value;
                this.RaisePropertyChanged("StreetNumberRanges");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=3)]
        public StreetNumber1[] StreetNumbers {
            get {
                return this.streetNumbersField;
            }
            set {
                this.streetNumbersField = value;
                this.RaisePropertyChanged("StreetNumbers");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
        public string TypeCode {
            get {
                return this.typeCodeField;
            }
            set {
                this.typeCodeField = value;
                this.RaisePropertyChanged("TypeCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="StreetNumberRange", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class StreetNumberRange1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string fromField;
        
        private int numberFromField;
        
        private bool numberFromFieldSpecified;
        
        private int numberToField;
        
        private bool numberToFieldSpecified;
        
        private string suffixFromField;
        
        private string suffixToField;
        
        private string toField;
        
        private string typeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string From {
            get {
                return this.fromField;
            }
            set {
                this.fromField = value;
                this.RaisePropertyChanged("From");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public int NumberFrom {
            get {
                return this.numberFromField;
            }
            set {
                this.numberFromField = value;
                this.RaisePropertyChanged("NumberFrom");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool NumberFromSpecified {
            get {
                return this.numberFromFieldSpecified;
            }
            set {
                this.numberFromFieldSpecified = value;
                this.RaisePropertyChanged("NumberFromSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public int NumberTo {
            get {
                return this.numberToField;
            }
            set {
                this.numberToField = value;
                this.RaisePropertyChanged("NumberTo");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlIgnoreAttribute()]
        public bool NumberToSpecified {
            get {
                return this.numberToFieldSpecified;
            }
            set {
                this.numberToFieldSpecified = value;
                this.RaisePropertyChanged("NumberToSpecified");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public string SuffixFrom {
            get {
                return this.suffixFromField;
            }
            set {
                this.suffixFromField = value;
                this.RaisePropertyChanged("SuffixFrom");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
        public string SuffixTo {
            get {
                return this.suffixToField;
            }
            set {
                this.suffixToField = value;
                this.RaisePropertyChanged("SuffixTo");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
        public string To {
            get {
                return this.toField;
            }
            set {
                this.toField = value;
                this.RaisePropertyChanged("To");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
        public string Type {
            get {
                return this.typeField;
            }
            set {
                this.typeField = value;
                this.RaisePropertyChanged("Type");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="StreetNumber", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class StreetNumber1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string nameField;
        
        private string numberField;
        
        private Address2 occupantDetailsField;
        
        private string suffixField;
        
        private SuiteRange1[] suiteRangesField;
        
        private Suite1[] suitesField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string Number {
            get {
                return this.numberField;
            }
            set {
                this.numberField = value;
                this.RaisePropertyChanged("Number");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public Address2 OccupantDetails {
            get {
                return this.occupantDetailsField;
            }
            set {
                this.occupantDetailsField = value;
                this.RaisePropertyChanged("OccupantDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
        public string Suffix {
            get {
                return this.suffixField;
            }
            set {
                this.suffixField = value;
                this.RaisePropertyChanged("Suffix");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=4)]
        public SuiteRange1[] SuiteRanges {
            get {
                return this.suiteRangesField;
            }
            set {
                this.suiteRangesField = value;
                this.RaisePropertyChanged("SuiteRanges");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=5)]
        public Suite1[] Suites {
            get {
                return this.suitesField;
            }
            set {
                this.suitesField = value;
                this.RaisePropertyChanged("Suites");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="SuiteRange", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class SuiteRange1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string fromField;
        
        private string nameField;
        
        private string toField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string From {
            get {
                return this.fromField;
            }
            set {
                this.fromField = value;
                this.RaisePropertyChanged("From");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string To {
            get {
                return this.toField;
            }
            set {
                this.toField = value;
                this.RaisePropertyChanged("To");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="Suite", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
    public partial class Suite1 : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string nameField;
        
        private Address2 occupantDetailsField;
        
        private string valueField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
                this.RaisePropertyChanged("Name");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
        public Address2 OccupantDetails {
            get {
                return this.occupantDetailsField;
            }
            set {
                this.occupantDetailsField = value;
                this.RaisePropertyChanged("OccupantDetails");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
                this.RaisePropertyChanged("Value");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.FlagsAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(TypeName="PostalCodeType", Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects." +
        "Enums")]
    public enum PostalCodeType1 {
        
        /// <remarks/>
        Civic = 1,
        
        /// <remarks/>
        Building = 2,
        
        /// <remarks/>
        GD = 4,
        
        /// <remarks/>
        Route = 8,
        
        /// <remarks/>
        Unique = 16,
        
        /// <remarks/>
        POBox = 32,
        
        /// <remarks/>
        DeliveryInstallation = 64,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="LookupGeneralDeliveryAddress", WrapperNamespace="http://aws.canadapost.ca/2015/01", IsWrapped=true)]
    public partial class LookupGeneralDeliveryAddressRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string BusinessName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=1)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string MunicipalityName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=2)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string ProvinceCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=3)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string PostalCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=4)]
        public int MaxRecordsReturned;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=5)]
        public bool SuppressAccent;
        
        public LookupGeneralDeliveryAddressRequest() {
        }
        
        public LookupGeneralDeliveryAddressRequest(string BusinessName, string MunicipalityName, string ProvinceCode, string PostalCode, int MaxRecordsReturned, bool SuppressAccent) {
            this.BusinessName = BusinessName;
            this.MunicipalityName = MunicipalityName;
            this.ProvinceCode = ProvinceCode;
            this.PostalCode = PostalCode;
            this.MaxRecordsReturned = MaxRecordsReturned;
            this.SuppressAccent = SuppressAccent;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="LookupGeneralDeliveryAddressResponse", WrapperNamespace="http://aws.canadapost.ca/2015/01", IsWrapped=true)]
    public partial class LookupGeneralDeliveryAddressResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=0)]
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1[] LookupGeneralDeliveryAddressResult;
        
        public LookupGeneralDeliveryAddressResponse() {
        }
        
        public LookupGeneralDeliveryAddressResponse(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1[] LookupGeneralDeliveryAddressResult) {
            this.LookupGeneralDeliveryAddressResult = LookupGeneralDeliveryAddressResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="LookupPOBoxAddress", WrapperNamespace="http://aws.canadapost.ca/2015/01", IsWrapped=true)]
    public partial class LookupPOBoxAddressRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string BusinessName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=1)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> POBoxNumber;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=2)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string MunicipalityName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=3)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string ProvinceCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=4)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string PostalCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=5)]
        public int MaxRecordsReturned;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=6)]
        public bool SuppressAccent;
        
        public LookupPOBoxAddressRequest() {
        }
        
        public LookupPOBoxAddressRequest(string BusinessName, System.Nullable<int> POBoxNumber, string MunicipalityName, string ProvinceCode, string PostalCode, int MaxRecordsReturned, bool SuppressAccent) {
            this.BusinessName = BusinessName;
            this.POBoxNumber = POBoxNumber;
            this.MunicipalityName = MunicipalityName;
            this.ProvinceCode = ProvinceCode;
            this.PostalCode = PostalCode;
            this.MaxRecordsReturned = MaxRecordsReturned;
            this.SuppressAccent = SuppressAccent;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="LookupPOBoxAddressResponse", WrapperNamespace="http://aws.canadapost.ca/2015/01", IsWrapped=true)]
    public partial class LookupPOBoxAddressResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=0)]
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1[] LookupPOBoxAddressResult;
        
        public LookupPOBoxAddressResponse() {
        }
        
        public LookupPOBoxAddressResponse(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1[] LookupPOBoxAddressResult) {
            this.LookupPOBoxAddressResult = LookupPOBoxAddressResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="LookupRuralAddress", WrapperNamespace="http://aws.canadapost.ca/2015/01", IsWrapped=true)]
    public partial class LookupRuralAddressRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string BusinessName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=1)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string SuiteNumber;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=2)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string StreetNumber;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=3)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string StreetNumberSuffix;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=4)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string StreetName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=5)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string StreetTypeCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=6)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string StreetDirectionCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=7)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string ServiceType;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=8)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> ServiceNumber;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=9)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string MunicipalityName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=10)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string ProvinceCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=11)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string PostalCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=12)]
        public int MaxRecordsReturned;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=13)]
        public bool SuppressAccent;
        
        public LookupRuralAddressRequest() {
        }
        
        public LookupRuralAddressRequest(string BusinessName, string SuiteNumber, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string ServiceType, System.Nullable<int> ServiceNumber, string MunicipalityName, string ProvinceCode, string PostalCode, int MaxRecordsReturned, bool SuppressAccent) {
            this.BusinessName = BusinessName;
            this.SuiteNumber = SuiteNumber;
            this.StreetNumber = StreetNumber;
            this.StreetNumberSuffix = StreetNumberSuffix;
            this.StreetName = StreetName;
            this.StreetTypeCode = StreetTypeCode;
            this.StreetDirectionCode = StreetDirectionCode;
            this.ServiceType = ServiceType;
            this.ServiceNumber = ServiceNumber;
            this.MunicipalityName = MunicipalityName;
            this.ProvinceCode = ProvinceCode;
            this.PostalCode = PostalCode;
            this.MaxRecordsReturned = MaxRecordsReturned;
            this.SuppressAccent = SuppressAccent;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="LookupRuralAddressResponse", WrapperNamespace="http://aws.canadapost.ca/2015/01", IsWrapped=true)]
    public partial class LookupRuralAddressResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=0)]
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1[] LookupRuralAddressResult;
        
        public LookupRuralAddressResponse() {
        }
        
        public LookupRuralAddressResponse(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1[] LookupRuralAddressResult) {
            this.LookupRuralAddressResult = LookupRuralAddressResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="LookupStreetAddress", WrapperNamespace="http://aws.canadapost.ca/2015/01", IsWrapped=true)]
    public partial class LookupStreetAddressRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string BusinessName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=1)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string BuildingName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=2)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string SuiteNumber;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=3)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string StreetNumber;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=4)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string StreetNumberSuffix;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=5)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string StreetName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=6)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string StreetTypeCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=7)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string StreetDirectionCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=8)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string MunicipalityName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=9)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string ProvinceCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=10)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public string PostalCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=11)]
        public int MaxRecordsReturned;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=12)]
        public bool SuppressAccent;
        
        public LookupStreetAddressRequest() {
        }
        
        public LookupStreetAddressRequest(string BusinessName, string BuildingName, string SuiteNumber, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string MunicipalityName, string ProvinceCode, string PostalCode, int MaxRecordsReturned, bool SuppressAccent) {
            this.BusinessName = BusinessName;
            this.BuildingName = BuildingName;
            this.SuiteNumber = SuiteNumber;
            this.StreetNumber = StreetNumber;
            this.StreetNumberSuffix = StreetNumberSuffix;
            this.StreetName = StreetName;
            this.StreetTypeCode = StreetTypeCode;
            this.StreetDirectionCode = StreetDirectionCode;
            this.MunicipalityName = MunicipalityName;
            this.ProvinceCode = ProvinceCode;
            this.PostalCode = PostalCode;
            this.MaxRecordsReturned = MaxRecordsReturned;
            this.SuppressAccent = SuppressAccent;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="LookupStreetAddressResponse", WrapperNamespace="http://aws.canadapost.ca/2015/01", IsWrapped=true)]
    public partial class LookupStreetAddressResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://aws.canadapost.ca/2015/01", Order=0)]
        [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true)]
        [System.Xml.Serialization.XmlArrayItemAttribute(Namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects")]
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1[] LookupStreetAddressResult;
        
        public LookupStreetAddressResponse() {
        }
        
        public LookupStreetAddressResponse(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1[] LookupStreetAddressResult) {
            this.LookupStreetAddressResult = LookupStreetAddressResult;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://aws.canadapost.ca/2015/01")]
    public partial class ParseAndValidateAddress1Request : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string businessNameField;
        
        private string addressLine1Field;
        
        private string addressLine2Field;
        
        private string addressLine3Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string BusinessName {
            get {
                return this.businessNameField;
            }
            set {
                this.businessNameField = value;
                this.RaisePropertyChanged("BusinessName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string AddressLine1 {
            get {
                return this.addressLine1Field;
            }
            set {
                this.addressLine1Field = value;
                this.RaisePropertyChanged("AddressLine1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string AddressLine2 {
            get {
                return this.addressLine2Field;
            }
            set {
                this.addressLine2Field = value;
                this.RaisePropertyChanged("AddressLine2");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string AddressLine3 {
            get {
                return this.addressLine3Field;
            }
            set {
                this.addressLine3Field = value;
                this.RaisePropertyChanged("AddressLine3");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.0.30319.34234")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.canadapost.ca/ws/address/2015/01")]
    public partial class AddressInput : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string addresseeField;
        
        private string addressLine1Field;
        
        private string addressLine2Field;
        
        private string municipalityNameField;
        
        private string provinceCodeField;
        
        private string postalCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public string Addressee {
            get {
                return this.addresseeField;
            }
            set {
                this.addresseeField = value;
                this.RaisePropertyChanged("Addressee");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string AddressLine1 {
            get {
                return this.addressLine1Field;
            }
            set {
                this.addressLine1Field = value;
                this.RaisePropertyChanged("AddressLine1");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=2)]
        public string AddressLine2 {
            get {
                return this.addressLine2Field;
            }
            set {
                this.addressLine2Field = value;
                this.RaisePropertyChanged("AddressLine2");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=3)]
        public string MunicipalityName {
            get {
                return this.municipalityNameField;
            }
            set {
                this.municipalityNameField = value;
                this.RaisePropertyChanged("MunicipalityName");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=4)]
        public string ProvinceCode {
            get {
                return this.provinceCodeField;
            }
            set {
                this.provinceCodeField = value;
                this.RaisePropertyChanged("ProvinceCode");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=5)]
        public string PostalCode {
            get {
                return this.postalCodeField;
            }
            set {
                this.postalCodeField = value;
                this.RaisePropertyChanged("PostalCode");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface IAddressLookupChannel : Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class AddressLookupClient : System.ServiceModel.ClientBase<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup>, Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup {
        
        public AddressLookupClient() {
        }
        
        public AddressLookupClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public AddressLookupClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public AddressLookupClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public AddressLookupClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CMBDetailsType GetCMBDetails(string AddressLine1, string MunicipalityName, Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.ProvinceCodeType ProvinceCode, string PostalCode) {
            return base.Channel.GetCMBDetails(AddressLine1, MunicipalityName, ProvinceCode, PostalCode);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CMBDetailsType> GetCMBDetailsAsync(string AddressLine1, string MunicipalityName, Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.ProvinceCodeType ProvinceCode, string PostalCode) {
            return base.Channel.GetCMBDetailsAsync(AddressLine1, MunicipalityName, ProvinceCode, PostalCode);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.FSADetailsType GetFSADetails(string PostalCode) {
            return base.Channel.GetFSADetails(PostalCode);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.FSADetailsType> GetFSADetailsAsync(string PostalCode) {
            return base.Channel.GetFSADetailsAsync(PostalCode);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.MunicipalityNames GetMunicipalityList(string ProvinceCode, string MunicipalitySearchCriteria, bool SuppressAccent) {
            return base.Channel.GetMunicipalityList(ProvinceCode, MunicipalitySearchCriteria, SuppressAccent);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.MunicipalityNames> GetMunicipalityListAsync(string ProvinceCode, string MunicipalitySearchCriteria, bool SuppressAccent) {
            return base.Channel.GetMunicipalityListAsync(ProvinceCode, MunicipalitySearchCriteria, SuppressAccent);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetPostalCodeDetailsResponse Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup.GetPostalCodeDetails(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetPostalCodeDetailsRequest request) {
            return base.Channel.GetPostalCodeDetails(request);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode GetPostalCodeDetails(string PostalCode, string BusinessName, bool SuppressAccent) {
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetPostalCodeDetailsRequest inValue = new Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetPostalCodeDetailsRequest();
            inValue.PostalCode = PostalCode;
            inValue.BusinessName = BusinessName;
            inValue.SuppressAccent = SuppressAccent;
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetPostalCodeDetailsResponse retVal = ((Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup)(this)).GetPostalCodeDetails(inValue);
            return retVal.PostalCode;
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetPostalCodeDetailsResponse> GetPostalCodeDetailsAsync(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetPostalCodeDetailsRequest request) {
            return base.Channel.GetPostalCodeDetailsAsync(request);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListResponse Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup.GetStreetTypeList(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListRequest request) {
            return base.Channel.GetStreetTypeList(request);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.StreetTypesLocalized[] GetStreetTypeList(bool SuppressAccent) {
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListRequest inValue = new Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListRequest();
            inValue.SuppressAccent = SuppressAccent;
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListResponse retVal = ((Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup)(this)).GetStreetTypeList(inValue);
            return retVal.GetStreetTypeListResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListResponse> Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup.GetStreetTypeListAsync(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListRequest request) {
            return base.Channel.GetStreetTypeListAsync(request);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListResponse> GetStreetTypeListAsync(bool SuppressAccent) {
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListRequest inValue = new Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListRequest();
            inValue.SuppressAccent = SuppressAccent;
            return ((Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup)(this)).GetStreetTypeListAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressResponse Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup.LookupGeneralDeliveryAddress(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressRequest request) {
            return base.Channel.LookupGeneralDeliveryAddress(request);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1[] LookupGeneralDeliveryAddress(string BusinessName, string MunicipalityName, string ProvinceCode, string PostalCode, int MaxRecordsReturned, bool SuppressAccent) {
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressRequest inValue = new Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressRequest();
            inValue.BusinessName = BusinessName;
            inValue.MunicipalityName = MunicipalityName;
            inValue.ProvinceCode = ProvinceCode;
            inValue.PostalCode = PostalCode;
            inValue.MaxRecordsReturned = MaxRecordsReturned;
            inValue.SuppressAccent = SuppressAccent;
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressResponse retVal = ((Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup)(this)).LookupGeneralDeliveryAddress(inValue);
            return retVal.LookupGeneralDeliveryAddressResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressResponse> Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup.LookupGeneralDeliveryAddressAsync(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressRequest request) {
            return base.Channel.LookupGeneralDeliveryAddressAsync(request);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressResponse> LookupGeneralDeliveryAddressAsync(string BusinessName, string MunicipalityName, string ProvinceCode, string PostalCode, int MaxRecordsReturned, bool SuppressAccent) {
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressRequest inValue = new Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressRequest();
            inValue.BusinessName = BusinessName;
            inValue.MunicipalityName = MunicipalityName;
            inValue.ProvinceCode = ProvinceCode;
            inValue.PostalCode = PostalCode;
            inValue.MaxRecordsReturned = MaxRecordsReturned;
            inValue.SuppressAccent = SuppressAccent;
            return ((Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup)(this)).LookupGeneralDeliveryAddressAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressResponse Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup.LookupPOBoxAddress(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressRequest request) {
            return base.Channel.LookupPOBoxAddress(request);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1[] LookupPOBoxAddress(string BusinessName, System.Nullable<int> POBoxNumber, string MunicipalityName, string ProvinceCode, string PostalCode, int MaxRecordsReturned, bool SuppressAccent) {
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressRequest inValue = new Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressRequest();
            inValue.BusinessName = BusinessName;
            inValue.POBoxNumber = POBoxNumber;
            inValue.MunicipalityName = MunicipalityName;
            inValue.ProvinceCode = ProvinceCode;
            inValue.PostalCode = PostalCode;
            inValue.MaxRecordsReturned = MaxRecordsReturned;
            inValue.SuppressAccent = SuppressAccent;
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressResponse retVal = ((Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup)(this)).LookupPOBoxAddress(inValue);
            return retVal.LookupPOBoxAddressResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressResponse> Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup.LookupPOBoxAddressAsync(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressRequest request) {
            return base.Channel.LookupPOBoxAddressAsync(request);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressResponse> LookupPOBoxAddressAsync(string BusinessName, System.Nullable<int> POBoxNumber, string MunicipalityName, string ProvinceCode, string PostalCode, int MaxRecordsReturned, bool SuppressAccent) {
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressRequest inValue = new Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressRequest();
            inValue.BusinessName = BusinessName;
            inValue.POBoxNumber = POBoxNumber;
            inValue.MunicipalityName = MunicipalityName;
            inValue.ProvinceCode = ProvinceCode;
            inValue.PostalCode = PostalCode;
            inValue.MaxRecordsReturned = MaxRecordsReturned;
            inValue.SuppressAccent = SuppressAccent;
            return ((Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup)(this)).LookupPOBoxAddressAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressResponse Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup.LookupRuralAddress(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressRequest request) {
            return base.Channel.LookupRuralAddress(request);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1[] LookupRuralAddress(string BusinessName, string SuiteNumber, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string ServiceType, System.Nullable<int> ServiceNumber, string MunicipalityName, string ProvinceCode, string PostalCode, int MaxRecordsReturned, bool SuppressAccent) {
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressRequest inValue = new Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressRequest();
            inValue.BusinessName = BusinessName;
            inValue.SuiteNumber = SuiteNumber;
            inValue.StreetNumber = StreetNumber;
            inValue.StreetNumberSuffix = StreetNumberSuffix;
            inValue.StreetName = StreetName;
            inValue.StreetTypeCode = StreetTypeCode;
            inValue.StreetDirectionCode = StreetDirectionCode;
            inValue.ServiceType = ServiceType;
            inValue.ServiceNumber = ServiceNumber;
            inValue.MunicipalityName = MunicipalityName;
            inValue.ProvinceCode = ProvinceCode;
            inValue.PostalCode = PostalCode;
            inValue.MaxRecordsReturned = MaxRecordsReturned;
            inValue.SuppressAccent = SuppressAccent;
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressResponse retVal = ((Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup)(this)).LookupRuralAddress(inValue);
            return retVal.LookupRuralAddressResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressResponse> Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup.LookupRuralAddressAsync(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressRequest request) {
            return base.Channel.LookupRuralAddressAsync(request);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressResponse> LookupRuralAddressAsync(string BusinessName, string SuiteNumber, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string ServiceType, System.Nullable<int> ServiceNumber, string MunicipalityName, string ProvinceCode, string PostalCode, int MaxRecordsReturned, bool SuppressAccent) {
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressRequest inValue = new Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressRequest();
            inValue.BusinessName = BusinessName;
            inValue.SuiteNumber = SuiteNumber;
            inValue.StreetNumber = StreetNumber;
            inValue.StreetNumberSuffix = StreetNumberSuffix;
            inValue.StreetName = StreetName;
            inValue.StreetTypeCode = StreetTypeCode;
            inValue.StreetDirectionCode = StreetDirectionCode;
            inValue.ServiceType = ServiceType;
            inValue.ServiceNumber = ServiceNumber;
            inValue.MunicipalityName = MunicipalityName;
            inValue.ProvinceCode = ProvinceCode;
            inValue.PostalCode = PostalCode;
            inValue.MaxRecordsReturned = MaxRecordsReturned;
            inValue.SuppressAccent = SuppressAccent;
            return ((Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup)(this)).LookupRuralAddressAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressResponse Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup.LookupStreetAddress(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressRequest request) {
            return base.Channel.LookupStreetAddress(request);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1[] LookupStreetAddress(string BusinessName, string BuildingName, string SuiteNumber, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string MunicipalityName, string ProvinceCode, string PostalCode, int MaxRecordsReturned, bool SuppressAccent) {
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressRequest inValue = new Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressRequest();
            inValue.BusinessName = BusinessName;
            inValue.BuildingName = BuildingName;
            inValue.SuiteNumber = SuiteNumber;
            inValue.StreetNumber = StreetNumber;
            inValue.StreetNumberSuffix = StreetNumberSuffix;
            inValue.StreetName = StreetName;
            inValue.StreetTypeCode = StreetTypeCode;
            inValue.StreetDirectionCode = StreetDirectionCode;
            inValue.MunicipalityName = MunicipalityName;
            inValue.ProvinceCode = ProvinceCode;
            inValue.PostalCode = PostalCode;
            inValue.MaxRecordsReturned = MaxRecordsReturned;
            inValue.SuppressAccent = SuppressAccent;
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressResponse retVal = ((Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup)(this)).LookupStreetAddress(inValue);
            return retVal.LookupStreetAddressResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressResponse> Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup.LookupStreetAddressAsync(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressRequest request) {
            return base.Channel.LookupStreetAddressAsync(request);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressResponse> LookupStreetAddressAsync(string BusinessName, string BuildingName, string SuiteNumber, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string MunicipalityName, string ProvinceCode, string PostalCode, int MaxRecordsReturned, bool SuppressAccent) {
            Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressRequest inValue = new Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressRequest();
            inValue.BusinessName = BusinessName;
            inValue.BuildingName = BuildingName;
            inValue.SuiteNumber = SuiteNumber;
            inValue.StreetNumber = StreetNumber;
            inValue.StreetNumberSuffix = StreetNumberSuffix;
            inValue.StreetName = StreetName;
            inValue.StreetTypeCode = StreetTypeCode;
            inValue.StreetDirectionCode = StreetDirectionCode;
            inValue.MunicipalityName = MunicipalityName;
            inValue.ProvinceCode = ProvinceCode;
            inValue.PostalCode = PostalCode;
            inValue.MaxRecordsReturned = MaxRecordsReturned;
            inValue.SuppressAccent = SuppressAccent;
            return ((Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.IAddressLookup)(this)).LookupStreetAddressAsync(inValue);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[] ValidateAddress1(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.ParseAndValidateAddress1Request[] CollectionRequest, bool SuppressAccent) {
            return base.Channel.ValidateAddress1(CollectionRequest, SuppressAccent);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[]> ValidateAddress1Async(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.ParseAndValidateAddress1Request[] CollectionRequest, bool SuppressAccent) {
            return base.Channel.ValidateAddress1Async(CollectionRequest, SuppressAccent);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[] ValidateAddress2(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.AddressInput[] ValidateAddress2Request, bool SuppressAccent) {
            return base.Channel.ValidateAddress2(ValidateAddress2Request, SuppressAccent);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[]> ValidateAddress2Async(Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.AddressInput[] ValidateAddress2Request, bool SuppressAccent) {
            return base.Channel.ValidateAddress2Async(ValidateAddress2Request, SuppressAccent);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[] ValidatePOBoxAddress(string BusinessName, int POBoxNumber, string MunicipalityName, string ProvinceCode, string PostalCode, bool SuppressAccent) {
            return base.Channel.ValidatePOBoxAddress(BusinessName, POBoxNumber, MunicipalityName, ProvinceCode, PostalCode, SuppressAccent);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[]> ValidatePOBoxAddressAsync(string BusinessName, int POBoxNumber, string MunicipalityName, string ProvinceCode, string PostalCode, bool SuppressAccent) {
            return base.Channel.ValidatePOBoxAddressAsync(BusinessName, POBoxNumber, MunicipalityName, ProvinceCode, PostalCode, SuppressAccent);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[] ValidateRuralAddress(string BusinessName, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string ServiceType, int ServiceNumber, string MunicipalityName, string ProvinceCode, string PostalCode, bool SuppressAccent) {
            return base.Channel.ValidateRuralAddress(BusinessName, StreetNumber, StreetNumberSuffix, StreetName, StreetTypeCode, StreetDirectionCode, ServiceType, ServiceNumber, MunicipalityName, ProvinceCode, PostalCode, SuppressAccent);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[]> ValidateRuralAddressAsync(string BusinessName, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string ServiceType, int ServiceNumber, string MunicipalityName, string ProvinceCode, string PostalCode, bool SuppressAccent) {
            return base.Channel.ValidateRuralAddressAsync(BusinessName, StreetNumber, StreetNumberSuffix, StreetName, StreetTypeCode, StreetDirectionCode, ServiceType, ServiceNumber, MunicipalityName, ProvinceCode, PostalCode, SuppressAccent);
        }
        
        public Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[] ValidateStreetAddress(string BusinessName, string BuildingName, string SuiteNumber, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string MunicipalityName, string ProvinceCode, string PostalCode, bool SuppressAccent) {
            return base.Channel.ValidateStreetAddress(BusinessName, BuildingName, SuiteNumber, StreetNumber, StreetNumberSuffix, StreetName, StreetTypeCode, StreetDirectionCode, MunicipalityName, ProvinceCode, PostalCode, SuppressAccent);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address[]> ValidateStreetAddressAsync(string BusinessName, string BuildingName, string SuiteNumber, string StreetNumber, string StreetNumberSuffix, string StreetName, string StreetTypeCode, string StreetDirectionCode, string MunicipalityName, string ProvinceCode, string PostalCode, bool SuppressAccent) {
            return base.Channel.ValidateStreetAddressAsync(BusinessName, BuildingName, SuiteNumber, StreetNumber, StreetNumberSuffix, StreetName, StreetTypeCode, StreetDirectionCode, MunicipalityName, ProvinceCode, PostalCode, SuppressAccent);
        }
    }
}
