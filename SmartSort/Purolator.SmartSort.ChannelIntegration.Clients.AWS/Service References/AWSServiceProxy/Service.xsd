<?xml version="1.0" encoding="utf-8"?>
<xsd:schema xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:import schemaLocation="Service.svc.xsd2.xsd" namespace="http://www.canadapost.ca/ws/address/2015/01" />
  <xsd:import schemaLocation="Service.svc.xsd5.xsd" namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects.Enums" />
  <xsd:complexType name="ArrayOfPostalCode">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="PostalCode" nillable="true" type="tns:PostalCode" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ArrayOfPostalCode" nillable="true" type="tns:ArrayOfPostalCode" />
  <xsd:complexType name="PostalCode">
    <xsd:sequence>
      <xsd:element xmlns:q41="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" name="Municipalities" nillable="true" type="q41:ArrayOfMunicipality" />
      <xsd:element minOccurs="0" name="ProvinceCode" nillable="true" type="xsd:string" />
      <xsd:element xmlns:q42="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects.Enums" minOccurs="0" name="Type" type="q42:PostalCodeType" />
      <xsd:element minOccurs="0" name="Value" nillable="true" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="PostalCode" nillable="true" type="tns:PostalCode" />
  <xsd:complexType name="ArrayOfGeneralDelivery">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="GeneralDelivery" nillable="true" type="tns:GeneralDelivery" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ArrayOfGeneralDelivery" nillable="true" type="tns:ArrayOfGeneralDelivery" />
  <xsd:complexType name="GeneralDelivery">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="AddressCollection" nillable="true" type="tns:AddressCollection" />
      <xsd:element minOccurs="0" name="DIAreaName" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="DIQualifier" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="DIType" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="DeliveryMode" nillable="true" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="GeneralDelivery" nillable="true" type="tns:GeneralDelivery" />
  <xsd:complexType name="AddressCollection">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="Address" nillable="true" type="tns:Address" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="AddressCollection" nillable="true" type="tns:AddressCollection" />
  <xsd:complexType name="Address">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="PrimaryTypeCode" nillable="true" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="Address" nillable="true" type="tns:Address" />
  <xsd:complexType name="POBoxes">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="DIAreaName" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="DIQualifier" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="DIType" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="POBoxNumberRanges" nillable="true" type="tns:ArrayOfPOBoxNumberRange" />
      <xsd:element minOccurs="0" name="POBoxNumbers" nillable="true" type="tns:ArrayOfPOBoxNumber" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="POBoxes" nillable="true" type="tns:POBoxes" />
  <xsd:complexType name="ArrayOfPOBoxNumberRange">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="POBoxNumberRange" nillable="true" type="tns:POBoxNumberRange" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ArrayOfPOBoxNumberRange" nillable="true" type="tns:ArrayOfPOBoxNumberRange" />
  <xsd:complexType name="POBoxNumberRange">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="From" type="xsd:int" />
      <xsd:element minOccurs="0" name="OccupantDetails" nillable="true" type="tns:Address" />
      <xsd:element minOccurs="0" name="To" type="xsd:int" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="POBoxNumberRange" nillable="true" type="tns:POBoxNumberRange" />
  <xsd:complexType name="ArrayOfPOBoxNumber">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="POBoxNumber" nillable="true" type="tns:POBoxNumber" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ArrayOfPOBoxNumber" nillable="true" type="tns:ArrayOfPOBoxNumber" />
  <xsd:complexType name="POBoxNumber">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="OccupantDetails" nillable="true" type="tns:Address" />
      <xsd:element minOccurs="0" name="Value" type="xsd:int" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="POBoxNumber" nillable="true" type="tns:POBoxNumber" />
  <xsd:complexType name="RuralRoutes">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="DIAreaName" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="DIQualifier" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="DIType" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="Routes" nillable="true" type="tns:ArrayOfRuralRoute" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="RuralRoutes" nillable="true" type="tns:RuralRoutes" />
  <xsd:complexType name="ArrayOfRuralRoute">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="RuralRoute" nillable="true" type="tns:RuralRoute" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ArrayOfRuralRoute" nillable="true" type="tns:ArrayOfRuralRoute" />
  <xsd:complexType name="RuralRoute">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="AddressCollection" nillable="true" type="tns:AddressCollection" />
      <xsd:element minOccurs="0" name="DeliveryMode" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="DeliveryNumber" type="xsd:int" />
      <xsd:element minOccurs="0" name="Value" nillable="true" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="RuralRoute" nillable="true" type="tns:RuralRoute" />
  <xsd:complexType name="ArrayOfStreet">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="Street" nillable="true" type="tns:Street" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ArrayOfStreet" nillable="true" type="tns:ArrayOfStreet" />
  <xsd:complexType name="Street">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="DirectionCode" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="Name" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="StreetNumberRanges" nillable="true" type="tns:ArrayOfStreetNumberRange" />
      <xsd:element minOccurs="0" name="StreetNumbers" nillable="true" type="tns:ArrayOfStreetNumber" />
      <xsd:element minOccurs="0" name="TypeCode" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="Value" nillable="true" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="Street" nillable="true" type="tns:Street" />
  <xsd:complexType name="ArrayOfStreetNumberRange">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="StreetNumberRange" nillable="true" type="tns:StreetNumberRange" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ArrayOfStreetNumberRange" nillable="true" type="tns:ArrayOfStreetNumberRange" />
  <xsd:complexType name="StreetNumberRange">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="From" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="NumberFrom" type="xsd:int" />
      <xsd:element minOccurs="0" name="NumberTo" type="xsd:int" />
      <xsd:element minOccurs="0" name="SuffixFrom" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="SuffixTo" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="To" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="Type" nillable="true" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="StreetNumberRange" nillable="true" type="tns:StreetNumberRange" />
  <xsd:complexType name="ArrayOfStreetNumber">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="StreetNumber" nillable="true" type="tns:StreetNumber" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ArrayOfStreetNumber" nillable="true" type="tns:ArrayOfStreetNumber" />
  <xsd:complexType name="StreetNumber">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="Name" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="Number" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="OccupantDetails" nillable="true" type="tns:Address" />
      <xsd:element minOccurs="0" name="Suffix" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="SuiteRanges" nillable="true" type="tns:ArrayOfSuiteRange" />
      <xsd:element minOccurs="0" name="Suites" nillable="true" type="tns:ArrayOfSuite" />
      <xsd:element minOccurs="0" name="Value" nillable="true" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="StreetNumber" nillable="true" type="tns:StreetNumber" />
  <xsd:complexType name="ArrayOfSuiteRange">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="SuiteRange" nillable="true" type="tns:SuiteRange" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ArrayOfSuiteRange" nillable="true" type="tns:ArrayOfSuiteRange" />
  <xsd:complexType name="SuiteRange">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="From" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="Name" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="To" nillable="true" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="SuiteRange" nillable="true" type="tns:SuiteRange" />
  <xsd:complexType name="ArrayOfSuite">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="Suite" nillable="true" type="tns:Suite" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ArrayOfSuite" nillable="true" type="tns:ArrayOfSuite" />
  <xsd:complexType name="Suite">
    <xsd:sequence>
      <xsd:element minOccurs="0" name="Name" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="OccupantDetails" nillable="true" type="tns:Address" />
      <xsd:element minOccurs="0" name="Value" nillable="true" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="Suite" nillable="true" type="tns:Suite" />
</xsd:schema>