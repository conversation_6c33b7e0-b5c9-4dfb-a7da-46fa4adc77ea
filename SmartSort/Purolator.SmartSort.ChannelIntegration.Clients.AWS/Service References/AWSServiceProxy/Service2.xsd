<?xml version="1.0" encoding="utf-8"?>
<xsd:schema xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://aws.canadapost.ca/2015/01" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" elementFormDefault="qualified" targetNamespace="http://www.canadapost.ca/ws/address/cmb/2015/01" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:import schemaLocation="Service.svc.xsd2.xsd" namespace="http://www.canadapost.ca/ws/address/2015/01" />
  <xsd:complexType name="CMBDetailsType">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="AddressValidationStatus" type="xsd:string" />
      <xsd:element xmlns:q43="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="1" name="Address" type="q43:Address" />
      <xsd:element minOccurs="0" maxOccurs="1" name="DepotSiteID" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="DepotSiteName" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="DeliveryModeNumber" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="DeliveryModeType" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="DeliveryModeDescriptionEn" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="DeliveryModeDescriptionFr" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="TransferPointType" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="TransferPointDescriptionEn" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="TransferPointDescriptionFr" type="xsd:string" />
      <xsd:element minOccurs="1" maxOccurs="1" name="ConsumerChoiceFlag" type="xsd:boolean" />
      <xsd:element xmlns:q44="http://www.canadapost.ca/ws/address/cmb/2015/01" minOccurs="0" maxOccurs="1" name="CMBInfo" type="q44:CMBInfoType" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CMBInfoType">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Site" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Module" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Compartment" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="LocationDescriptionEn" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="LocationDescriptionFr" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="ImplementationState" type="xsd:string" />
      <xsd:element minOccurs="1" maxOccurs="1" name="ImplementationDate" nillable="true" type="xsd:date" />
      <xsd:element minOccurs="1" maxOccurs="1" name="Latitude" type="xsd:double" />
      <xsd:element minOccurs="1" maxOccurs="1" name="Longitude" type="xsd:double" />
    </xsd:sequence>
  </xsd:complexType>
</xsd:schema>