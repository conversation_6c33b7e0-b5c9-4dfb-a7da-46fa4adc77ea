<?xml version="1.0" encoding="utf-8"?>
<xsd:schema xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects.Enums" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects.Enums" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:simpleType name="PostalCodeType">
    <xsd:list>
      <xsd:simpleType>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="Civic" />
          <xsd:enumeration value="Building" />
          <xsd:enumeration value="GD" />
          <xsd:enumeration value="Route" />
          <xsd:enumeration value="Unique" />
          <xsd:enumeration value="POBox" />
          <xsd:enumeration value="DeliveryInstallation" />
        </xsd:restriction>
      </xsd:simpleType>
    </xsd:list>
  </xsd:simpleType>
  <xsd:element name="PostalCodeType" nillable="true" type="tns:PostalCodeType" />
</xsd:schema>