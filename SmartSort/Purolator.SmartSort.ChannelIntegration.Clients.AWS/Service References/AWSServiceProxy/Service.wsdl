<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsp200607="http://www.w3.org/2006/07/ws-policy" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:ns0="http://aws.canadapost.ca/2015/01" xmlns:wsp200409="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap11="http://schemas.xmlsoap.org/wsdl/soap/" targetNamespace="http://aws.canadapost.ca/2015/01" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <xsd:schema>
      <xsd:import schemaLocation="Service.svc.xsd1.xsd" namespace="http://aws.canadapost.ca/2015/01" />
      <xsd:import schemaLocation="Service.svc.xsd2.xsd" namespace="http://www.canadapost.ca/ws/address/2015/01" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IAddressLookup_GetCMBDetails_CustomFaultMsgFault_FaultMessage">
    <wsdl:part xmlns:xsns="http://www.canadapost.ca/ws/address/2015/01" name="detail" element="xsns:CustomFaultMsg" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_GetCMBDetails_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:GetCMBDetails" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_GetCMBDetails_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:GetCMBDetailsResponse" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_GetFSADetails_CustomFaultMsgFault_FaultMessage">
    <wsdl:part xmlns:xsns="http://www.canadapost.ca/ws/address/2015/01" name="detail" element="xsns:CustomFaultMsg" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_GetFSADetails_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:GetFSADetails" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_GetFSADetails_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:GetFSADetailsResponse" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_GetMunicipalityList_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:GetMunicipalityList" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_GetMunicipalityList_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:GetMunicipalityListResponse" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_GetPostalCodeDetails_CustomFaultMsgFault_FaultMessage">
    <wsdl:part xmlns:xsns="http://www.canadapost.ca/ws/address/2015/01" name="detail" element="xsns:CustomFaultMsg" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_GetPostalCodeDetails_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:GetPostalCodeDetails" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_GetPostalCodeDetails_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:GetPostalCodeDetailsResponse" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_GetStreetTypeList_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:GetStreetTypeList" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_GetStreetTypeList_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:GetStreetTypeListResponse" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_LookupGeneralDeliveryAddress_CustomFaultMsgFault_FaultMessage">
    <wsdl:part xmlns:xsns="http://www.canadapost.ca/ws/address/2015/01" name="detail" element="xsns:CustomFaultMsg" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_LookupGeneralDeliveryAddress_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:LookupGeneralDeliveryAddress" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_LookupGeneralDeliveryAddress_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:LookupGeneralDeliveryAddressResponse" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_LookupPOBoxAddress_CustomFaultMsgFault_FaultMessage">
    <wsdl:part xmlns:xsns="http://www.canadapost.ca/ws/address/2015/01" name="detail" element="xsns:CustomFaultMsg" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_LookupPOBoxAddress_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:LookupPOBoxAddress" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_LookupPOBoxAddress_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:LookupPOBoxAddressResponse" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_LookupRuralAddress_CustomFaultMsgFault_FaultMessage">
    <wsdl:part xmlns:xsns="http://www.canadapost.ca/ws/address/2015/01" name="detail" element="xsns:CustomFaultMsg" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_LookupRuralAddress_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:LookupRuralAddress" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_LookupRuralAddress_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:LookupRuralAddressResponse" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_LookupStreetAddress_CustomFaultMsgFault_FaultMessage">
    <wsdl:part xmlns:xsns="http://www.canadapost.ca/ws/address/2015/01" name="detail" element="xsns:CustomFaultMsg" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_LookupStreetAddress_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:LookupStreetAddress" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_LookupStreetAddress_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:LookupStreetAddressResponse" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_ValidateAddress1_CustomFaultMsgFault_FaultMessage">
    <wsdl:part xmlns:xsns="http://www.canadapost.ca/ws/address/2015/01" name="detail" element="xsns:CustomFaultMsg" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_ValidateAddress1_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:ValidateAddress1" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_ValidateAddress1_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:ValidateAddress1Response" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_ValidateAddress2_CustomFaultMsgFault_FaultMessage">
    <wsdl:part xmlns:xsns="http://www.canadapost.ca/ws/address/2015/01" name="detail" element="xsns:CustomFaultMsg" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_ValidateAddress2_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:ValidateAddress2" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_ValidateAddress2_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:ValidateAddress2Response" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_ValidatePOBoxAddress_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:ValidatePOBoxAddress" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_ValidatePOBoxAddress_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:ValidatePOBoxAddressResponse" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_ValidateRuralAddress_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:ValidateRuralAddress" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_ValidateRuralAddress_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:ValidateRuralAddressResponse" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_ValidateStreetAddress_InputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:ValidateStreetAddress" />
  </wsdl:message>
  <wsdl:message name="IAddressLookup_ValidateStreetAddress_OutputMessage">
    <wsdl:part xmlns:xsns="http://aws.canadapost.ca/2015/01" name="parameters" element="xsns:ValidateStreetAddressResponse" />
  </wsdl:message>
  <wsdl:portType name="IAddressLookup">
    <wsdl:operation name="GetCMBDetails">
      <wsdl:input name="GetCMBDetailsRequest" message="ns0:IAddressLookup_GetCMBDetails_InputMessage" />
      <wsdl:output name="GetCMBDetailsResponse" message="ns0:IAddressLookup_GetCMBDetails_OutputMessage" />
      <wsdl:fault name="CustomFaultMsgFault" message="ns0:IAddressLookup_GetCMBDetails_CustomFaultMsgFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetFSADetails">
      <wsdl:input name="GetFSADetailsRequest" message="ns0:IAddressLookup_GetFSADetails_InputMessage" />
      <wsdl:output name="GetFSADetailsResponse" message="ns0:IAddressLookup_GetFSADetails_OutputMessage" />
      <wsdl:fault name="CustomFaultMsgFault" message="ns0:IAddressLookup_GetFSADetails_CustomFaultMsgFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetMunicipalityList">
      <wsdl:input name="GetMunicipalityListRequest" message="ns0:IAddressLookup_GetMunicipalityList_InputMessage" />
      <wsdl:output name="GetMunicipalityListResponse" message="ns0:IAddressLookup_GetMunicipalityList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetPostalCodeDetails">
      <wsdl:input name="GetPostalCodeDetailsRequest" message="ns0:IAddressLookup_GetPostalCodeDetails_InputMessage" />
      <wsdl:output name="GetPostalCodeDetailsResponse" message="ns0:IAddressLookup_GetPostalCodeDetails_OutputMessage" />
      <wsdl:fault name="CustomFaultMsgFault" message="ns0:IAddressLookup_GetPostalCodeDetails_CustomFaultMsgFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetStreetTypeList">
      <wsdl:input name="GetStreetTypeListRequest" message="ns0:IAddressLookup_GetStreetTypeList_InputMessage" />
      <wsdl:output name="GetStreetTypeListResponse" message="ns0:IAddressLookup_GetStreetTypeList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LookupGeneralDeliveryAddress">
      <wsdl:input name="LookupGeneralDeliveryAddressRequest" message="ns0:IAddressLookup_LookupGeneralDeliveryAddress_InputMessage" />
      <wsdl:output name="LookupGeneralDeliveryAddressResponse" message="ns0:IAddressLookup_LookupGeneralDeliveryAddress_OutputMessage" />
      <wsdl:fault name="CustomFaultMsgFault" message="ns0:IAddressLookup_LookupGeneralDeliveryAddress_CustomFaultMsgFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="LookupPOBoxAddress">
      <wsdl:input name="LookupPOBoxAddressRequest" message="ns0:IAddressLookup_LookupPOBoxAddress_InputMessage" />
      <wsdl:output name="LookupPOBoxAddressResponse" message="ns0:IAddressLookup_LookupPOBoxAddress_OutputMessage" />
      <wsdl:fault name="CustomFaultMsgFault" message="ns0:IAddressLookup_LookupPOBoxAddress_CustomFaultMsgFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="LookupRuralAddress">
      <wsdl:input name="LookupRuralAddressRequest" message="ns0:IAddressLookup_LookupRuralAddress_InputMessage" />
      <wsdl:output name="LookupRuralAddressResponse" message="ns0:IAddressLookup_LookupRuralAddress_OutputMessage" />
      <wsdl:fault name="CustomFaultMsgFault" message="ns0:IAddressLookup_LookupRuralAddress_CustomFaultMsgFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="LookupStreetAddress">
      <wsdl:input name="LookupStreetAddressRequest" message="ns0:IAddressLookup_LookupStreetAddress_InputMessage" />
      <wsdl:output name="LookupStreetAddressResponse" message="ns0:IAddressLookup_LookupStreetAddress_OutputMessage" />
      <wsdl:fault name="CustomFaultMsgFault" message="ns0:IAddressLookup_LookupStreetAddress_CustomFaultMsgFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="ValidateAddress1">
      <wsdl:input name="ValidateAddress1Request" message="ns0:IAddressLookup_ValidateAddress1_InputMessage" />
      <wsdl:output name="ValidateAddress1Response" message="ns0:IAddressLookup_ValidateAddress1_OutputMessage" />
      <wsdl:fault name="CustomFaultMsgFault" message="ns0:IAddressLookup_ValidateAddress1_CustomFaultMsgFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="ValidateAddress2">
      <wsdl:input name="ValidateAddress2Request" message="ns0:IAddressLookup_ValidateAddress2_InputMessage" />
      <wsdl:output name="ValidateAddress2Response" message="ns0:IAddressLookup_ValidateAddress2_OutputMessage" />
      <wsdl:fault name="CustomFaultMsgFault" message="ns0:IAddressLookup_ValidateAddress2_CustomFaultMsgFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="ValidatePOBoxAddress">
      <wsdl:input name="ValidatePOBoxAddressRequest" message="ns0:IAddressLookup_ValidatePOBoxAddress_InputMessage" />
      <wsdl:output name="ValidatePOBoxAddressResponse" message="ns0:IAddressLookup_ValidatePOBoxAddress_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ValidateRuralAddress">
      <wsdl:input name="ValidateRuralAddressRequest" message="ns0:IAddressLookup_ValidateRuralAddress_InputMessage" />
      <wsdl:output name="ValidateRuralAddressResponse" message="ns0:IAddressLookup_ValidateRuralAddress_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ValidateStreetAddress">
      <wsdl:input name="ValidateStreetAddressRequest" message="ns0:IAddressLookup_ValidateStreetAddress_InputMessage" />
      <wsdl:output name="ValidateStreetAddressResponse" message="ns0:IAddressLookup_ValidateStreetAddress_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IAddressLookup" type="ns0:IAddressLookup">
    <soap11:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="GetCMBDetails">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/GetCMBDetails" style="document" />
      <wsdl:input name="GetCMBDetailsRequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="GetCMBDetailsResponse">
        <soap11:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="CustomFaultMsgFault">
        <soap11:fault use="literal" name="CustomFaultMsgFault" namespace="" />
      </wsdl:fault>
    </wsdl:operation>
    <wsdl:operation name="GetFSADetails">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/GetFSADetails" style="document" />
      <wsdl:input name="GetFSADetailsRequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="GetFSADetailsResponse">
        <soap11:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="CustomFaultMsgFault">
        <soap11:fault use="literal" name="CustomFaultMsgFault" namespace="" />
      </wsdl:fault>
    </wsdl:operation>
    <wsdl:operation name="GetMunicipalityList">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/GetMunicipalityList" style="document" />
      <wsdl:input name="GetMunicipalityListRequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="GetMunicipalityListResponse">
        <soap11:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPostalCodeDetails">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/GetPostalCodeDetails" style="document" />
      <wsdl:input name="GetPostalCodeDetailsRequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="GetPostalCodeDetailsResponse">
        <soap11:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="CustomFaultMsgFault">
        <soap11:fault use="literal" name="CustomFaultMsgFault" namespace="" />
      </wsdl:fault>
    </wsdl:operation>
    <wsdl:operation name="GetStreetTypeList">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/GetStreetTypeList" style="document" />
      <wsdl:input name="GetStreetTypeListRequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="GetStreetTypeListResponse">
        <soap11:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LookupGeneralDeliveryAddress">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupGeneralDeliveryAddress" style="document" />
      <wsdl:input name="LookupGeneralDeliveryAddressRequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="LookupGeneralDeliveryAddressResponse">
        <soap11:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="CustomFaultMsgFault">
        <soap11:fault use="literal" name="CustomFaultMsgFault" namespace="" />
      </wsdl:fault>
    </wsdl:operation>
    <wsdl:operation name="LookupPOBoxAddress">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupPOBoxAddress" style="document" />
      <wsdl:input name="LookupPOBoxAddressRequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="LookupPOBoxAddressResponse">
        <soap11:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="CustomFaultMsgFault">
        <soap11:fault use="literal" name="CustomFaultMsgFault" namespace="" />
      </wsdl:fault>
    </wsdl:operation>
    <wsdl:operation name="LookupRuralAddress">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupRuralAddress" style="document" />
      <wsdl:input name="LookupRuralAddressRequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="LookupRuralAddressResponse">
        <soap11:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="CustomFaultMsgFault">
        <soap11:fault use="literal" name="CustomFaultMsgFault" namespace="" />
      </wsdl:fault>
    </wsdl:operation>
    <wsdl:operation name="LookupStreetAddress">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/LookupStreetAddress" style="document" />
      <wsdl:input name="LookupStreetAddressRequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="LookupStreetAddressResponse">
        <soap11:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="CustomFaultMsgFault">
        <soap11:fault use="literal" name="CustomFaultMsgFault" namespace="" />
      </wsdl:fault>
    </wsdl:operation>
    <wsdl:operation name="ValidateAddress1">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateAddress1" style="document" />
      <wsdl:input name="ValidateAddress1Request">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ValidateAddress1Response">
        <soap11:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="CustomFaultMsgFault">
        <soap11:fault use="literal" name="CustomFaultMsgFault" namespace="" />
      </wsdl:fault>
    </wsdl:operation>
    <wsdl:operation name="ValidateAddress2">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateAddress2" style="document" />
      <wsdl:input name="ValidateAddress2Request">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ValidateAddress2Response">
        <soap11:body use="literal" />
      </wsdl:output>
      <wsdl:fault name="CustomFaultMsgFault">
        <soap11:fault use="literal" name="CustomFaultMsgFault" namespace="" />
      </wsdl:fault>
    </wsdl:operation>
    <wsdl:operation name="ValidatePOBoxAddress">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidatePOBoxAddress" style="document" />
      <wsdl:input name="ValidatePOBoxAddressRequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ValidatePOBoxAddressResponse">
        <soap11:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidateRuralAddress">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateRuralAddress" style="document" />
      <wsdl:input name="ValidateRuralAddressRequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ValidateRuralAddressResponse">
        <soap11:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidateStreetAddress">
      <soap11:operation soapAction="http://aws.canadapost.ca/2015/01/IAddressLookup/ValidateStreetAddress" style="document" />
      <wsdl:input name="ValidateStreetAddressRequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ValidateStreetAddressResponse">
        <soap11:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="AddressLookupService">
    <wsdl:port name="BasicHttpBinding_IAddressLookup" binding="ns0:BasicHttpBinding_IAddressLookup">
      <soap11:address location="https://localhost:9090/AddressingWebService/V3/Service.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>