<?xml version="1.0" encoding="utf-8"?>
<xsd:schema xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://aws.canadapost.ca/2015/01" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" elementFormDefault="qualified" targetNamespace="http://aws.canadapost.ca/2015/01" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:import schemaLocation="Service.svc.xsd2.xsd" namespace="http://www.canadapost.ca/ws/address/2015/01" />
  <xsd:import schemaLocation="Service.svc.xsd4.xsd" namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects" />
  <xsd:import schemaLocation="Service.svc.xsd6.xsd" namespace="http://www.canadapost.ca/ws/address/cmb/2015/01" />
  <xsd:element name="ValidateStreetAddress">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="BusinessName" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="BuildingName" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="SuiteNumber" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="StreetNumber" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="StreetNumberSuffix" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="StreetName" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="StreetTypeCode" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="StreetDirectionCode" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="MunicipalityName" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="ProvinceCode" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="PostalCode" type="xsd:string" />
        <xsd:element minOccurs="1" maxOccurs="1" name="SuppressAccent" type="xsd:boolean" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ValidateStreetAddressResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="ValidateStreetAddressResult" type="tns:ArrayOfAddress" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ArrayOfAddress">
    <xsd:sequence>
      <xsd:element xmlns:q1="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="unbounded" name="Address" nillable="true" type="q1:Address" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ValidatePOBoxAddress">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="BusinessName" type="xsd:string" />
        <xsd:element minOccurs="1" maxOccurs="1" name="POBoxNumber" type="xsd:int" />
        <xsd:element minOccurs="0" maxOccurs="1" name="MunicipalityName" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="ProvinceCode" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="PostalCode" type="xsd:string" />
        <xsd:element minOccurs="1" maxOccurs="1" name="SuppressAccent" type="xsd:boolean" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ValidatePOBoxAddressResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="ValidatePOBoxAddressResult" type="tns:ArrayOfAddress" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ValidateRuralAddress">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="BusinessName" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="StreetNumber" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="StreetNumberSuffix" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="StreetName" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="StreetTypeCode" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="StreetDirectionCode" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="ServiceType" type="xsd:string" />
        <xsd:element minOccurs="1" maxOccurs="1" name="ServiceNumber" type="xsd:int" />
        <xsd:element minOccurs="0" maxOccurs="1" name="MunicipalityName" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="ProvinceCode" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="PostalCode" type="xsd:string" />
        <xsd:element minOccurs="1" maxOccurs="1" name="SuppressAccent" type="xsd:boolean" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ValidateRuralAddressResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="ValidateRuralAddressResult" type="tns:ArrayOfAddress" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ValidateAddress1">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="CollectionRequest" type="tns:ArrayOfParseAndValidateAddress1Request" />
        <xsd:element minOccurs="1" maxOccurs="1" name="SuppressAccent" type="xsd:boolean" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ArrayOfParseAndValidateAddress1Request">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="ParseAndValidateAddress1Request" nillable="true" type="tns:ParseAndValidateAddress1Request" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ParseAndValidateAddress1Request">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="BusinessName" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="AddressLine1" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="AddressLine2" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="AddressLine3" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ValidateAddress1Response">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="Addresses" type="tns:ArrayOfAddress" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="ValidateAddress2">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="ValidateAddress2Request" type="tns:ArrayOfAddressInput" />
        <xsd:element minOccurs="1" maxOccurs="1" name="SuppressAccent" type="xsd:boolean" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="ArrayOfAddressInput">
    <xsd:sequence>
      <xsd:element xmlns:q2="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="unbounded" name="AddressInput" nillable="true" type="q2:AddressInput" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="ValidateAddress2Response">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="Addresses" type="tns:ArrayOfAddress" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetPostalCodeDetails">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="PostalCode" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="BusinessName" type="xsd:string" />
        <xsd:element minOccurs="1" maxOccurs="1" name="SuppressAccent" type="xsd:boolean" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetPostalCodeDetailsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="PostalCode" type="tns:PostalCode" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="PostalCode">
    <xsd:sequence>
      <xsd:element minOccurs="1" maxOccurs="1" name="Type" type="tns:PostalCodeType" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Value" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="ProvinceCode" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="Municipality" type="tns:Municipality" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="PostalCodeType">
    <xsd:list>
      <xsd:simpleType>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="Civic" />
          <xsd:enumeration value="Building" />
          <xsd:enumeration value="GD" />
          <xsd:enumeration value="Route" />
          <xsd:enumeration value="Unique" />
          <xsd:enumeration value="POBox" />
          <xsd:enumeration value="DeliveryInstallation" />
        </xsd:restriction>
      </xsd:simpleType>
    </xsd:list>
  </xsd:simpleType>
  <xsd:complexType name="Municipality">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Name" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Streets" type="tns:ArrayOfStreet" />
      <xsd:element minOccurs="0" maxOccurs="1" name="RuralRoutes" type="tns:RuralRoutes" />
      <xsd:element minOccurs="0" maxOccurs="1" name="POBoxes" type="tns:POBoxes" />
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="GeneralDeliveries" type="tns:GeneralDelivery" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ArrayOfStreet">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="Street" nillable="true" type="tns:Street" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Street">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Name" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Type" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Direction" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="NumberRange" type="tns:StreetNumberRange" />
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="Number" type="tns:StreetNumber" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="StreetNumberRange">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="From" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="To" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Type" type="xsd:string" />
      <xsd:element minOccurs="1" maxOccurs="1" name="NumberFrom" type="xsd:int" />
      <xsd:element minOccurs="1" maxOccurs="1" name="NumberTo" type="xsd:int" />
      <xsd:element minOccurs="0" maxOccurs="1" name="SuffixFrom" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="SuffixTo" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="StreetNumber">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Name" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Value" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Number" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Suffix" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="SuiteRanges" type="tns:SuiteRange" />
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="Suites" type="tns:Suite" />
      <xsd:element minOccurs="0" maxOccurs="1" name="OccupantDetails" type="tns:Address" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SuiteRange">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="From" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="To" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Name" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Suite">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Value" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Name" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="OccupantDetails" type="tns:Address" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Address">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="PrimaryTypeCode" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RuralRoutes">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationTypeCode" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationName" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationArea" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="Route" type="tns:RuralRoute" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RuralRoute">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Value" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="DeliveryMode" type="xsd:string" />
      <xsd:element minOccurs="1" maxOccurs="1" name="DeliveryNumber" type="xsd:int" />
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="AddressCollection" type="tns:Address" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="POBoxes">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationTypeCode" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationName" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationArea" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="NumberRange" type="tns:POBoxNumberRange" />
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="Number" type="tns:POBoxNumber" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="POBoxNumberRange">
    <xsd:sequence>
      <xsd:element minOccurs="1" maxOccurs="1" name="From" type="xsd:int" />
      <xsd:element minOccurs="1" maxOccurs="1" name="To" type="xsd:int" />
      <xsd:element minOccurs="0" maxOccurs="1" name="OccupantDetails" type="tns:Address" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="POBoxNumber">
    <xsd:sequence>
      <xsd:element minOccurs="1" maxOccurs="1" name="Value" type="xsd:int" />
      <xsd:element minOccurs="0" maxOccurs="1" name="OccupantDetails" type="tns:Address" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GeneralDelivery">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="DeliveryMode" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationTypeCode" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationName" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationArea" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="unbounded" name="AddressCollection" type="tns:Address" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="LookupStreetAddress">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" name="BusinessName" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="BuildingName" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="SuiteNumber" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="StreetNumber" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="StreetNumberSuffix" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="StreetName" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="StreetTypeCode" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="StreetDirectionCode" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="MunicipalityName" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="ProvinceCode" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="PostalCode" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="MaxRecordsReturned" type="xsd:int" />
        <xsd:element minOccurs="0" name="SuppressAccent" type="xsd:boolean" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="LookupStreetAddressResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element xmlns:q3="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects" minOccurs="0" name="LookupStreetAddressResult" nillable="true" type="q3:ArrayOfPostalCode" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="LookupPOBoxAddress">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" name="BusinessName" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="POBoxNumber" nillable="true" type="xsd:int" />
        <xsd:element minOccurs="0" name="MunicipalityName" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="ProvinceCode" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="PostalCode" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="MaxRecordsReturned" type="xsd:int" />
        <xsd:element minOccurs="0" name="SuppressAccent" type="xsd:boolean" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="LookupPOBoxAddressResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element xmlns:q4="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects" minOccurs="0" name="LookupPOBoxAddressResult" nillable="true" type="q4:ArrayOfPostalCode" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="LookupRuralAddress">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" name="BusinessName" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="SuiteNumber" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="StreetNumber" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="StreetNumberSuffix" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="StreetName" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="StreetTypeCode" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="StreetDirectionCode" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="ServiceType" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="ServiceNumber" nillable="true" type="xsd:int" />
        <xsd:element minOccurs="0" name="MunicipalityName" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="ProvinceCode" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="PostalCode" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="MaxRecordsReturned" type="xsd:int" />
        <xsd:element minOccurs="0" name="SuppressAccent" type="xsd:boolean" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="LookupRuralAddressResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element xmlns:q5="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects" minOccurs="0" name="LookupRuralAddressResult" nillable="true" type="q5:ArrayOfPostalCode" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="LookupGeneralDeliveryAddress">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" name="BusinessName" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="MunicipalityName" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="ProvinceCode" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="PostalCode" nillable="true" type="xsd:string" />
        <xsd:element minOccurs="0" name="MaxRecordsReturned" type="xsd:int" />
        <xsd:element minOccurs="0" name="SuppressAccent" type="xsd:boolean" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="LookupGeneralDeliveryAddressResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element xmlns:q6="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects" minOccurs="0" name="LookupGeneralDeliveryAddressResult" nillable="true" type="q6:ArrayOfPostalCode" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetFSADetails">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="PostalCode" type="xsd:string" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetFSADetailsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element xmlns:q7="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="1" name="GetFSADetailsResult" type="q7:FSADetailsType" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetCMBDetails">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="AddressLine1" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="MunicipalityName" type="xsd:string" />
        <xsd:element minOccurs="1" maxOccurs="1" name="ProvinceCode" type="tns:ProvinceCodeType" />
        <xsd:element minOccurs="0" maxOccurs="1" name="PostalCode" type="xsd:string" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:simpleType name="ProvinceCodeType">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="AB" />
      <xsd:enumeration value="BC" />
      <xsd:enumeration value="MB" />
      <xsd:enumeration value="NB" />
      <xsd:enumeration value="NL" />
      <xsd:enumeration value="NS" />
      <xsd:enumeration value="NT" />
      <xsd:enumeration value="NU" />
      <xsd:enumeration value="ON" />
      <xsd:enumeration value="PE" />
      <xsd:enumeration value="QC" />
      <xsd:enumeration value="SK" />
      <xsd:enumeration value="YT" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:element name="GetCMBDetailsResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element xmlns:q8="http://www.canadapost.ca/ws/address/cmb/2015/01" minOccurs="0" maxOccurs="1" name="GetCMBDetailsResult" type="q8:CMBDetailsType" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetMunicipalityList">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="0" maxOccurs="1" name="ProvinceCode" type="xsd:string" />
        <xsd:element minOccurs="0" maxOccurs="1" name="MunicipalitySearchCriteria" type="xsd:string" />
        <xsd:element minOccurs="1" maxOccurs="1" name="SuppressAccent" type="xsd:boolean" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetMunicipalityListResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element xmlns:q9="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="1" name="GetMunicipalityListResult" type="q9:MunicipalityNames" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetStreetTypeList">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element minOccurs="1" maxOccurs="1" name="SuppressAccent" type="xsd:boolean" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="GetStreetTypeListResponse">
    <xsd:complexType>
      <xsd:sequence>
        <xsd:element xmlns:q10="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="1" name="GetStreetTypeListResult" type="q10:StreetTypeLists" />
      </xsd:sequence>
    </xsd:complexType>
  </xsd:element>
</xsd:schema>