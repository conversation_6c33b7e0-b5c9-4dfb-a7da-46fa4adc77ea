<?xml version="1.0" encoding="utf-8"?>
<ReferenceGroup xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ID="28043733-b58f-4dec-9275-e45534f73167" xmlns="urn:schemas-microsoft-com:xml-wcfservicemap">
  <ClientOptions>
    <GenerateAsynchronousMethods>false</GenerateAsynchronousMethods>
    <GenerateTaskBasedAsynchronousMethod>true</GenerateTaskBasedAsynchronousMethod>
    <EnableDataBinding>true</EnableDataBinding>
    <ExcludedTypes />
    <ImportXmlTypes>false</ImportXmlTypes>
    <GenerateInternalTypes>false</GenerateInternalTypes>
    <GenerateMessageContracts>false</GenerateMessageContracts>
    <NamespaceMappings />
    <CollectionMappings />
    <GenerateSerializableTypes>true</GenerateSerializableTypes>
    <Serializer>Auto</Serializer>
    <UseSerializerForFaults>true</UseSerializerForFaults>
    <ReferenceAllAssemblies>true</ReferenceAllAssemblies>
    <ReferencedAssemblies />
    <ReferencedDataContractTypes />
    <ServiceContractMappings />
  </ClientOptions>
  <MetadataSources>
    <MetadataSource Address="https://qa.soa-gw.mailposte.ca:5015/AddressingWebService/V3/Service.svc?wsdl" Protocol="http" SourceId="1" />
  </MetadataSources>
  <Metadata>
    <MetadataFile FileName="Service.xsd" MetadataType="Schema" ID="7e01ed36-df3e-4dda-be9f-35dea7de7863" SourceId="1" SourceUrl="https://qa.soa-gw.mailposte.ca:5015/AddressingWebService/V3/Service.svc.xsd4.xsd" />
    <MetadataFile FileName="Service3.xsd" MetadataType="Schema" ID="b6c29034-2797-4f93-8c1a-1526a3e88a28" SourceId="1" SourceUrl="https://qa.soa-gw.mailposte.ca:5015/AddressingWebService/V3/Service.svc.xsd1.xsd" />
    <MetadataFile FileName="Service.wsdl" MetadataType="Wsdl" ID="62113930-3dd7-4c07-a27a-4a4758a2cd6c" SourceId="1" SourceUrl="https://qa.soa-gw.mailposte.ca:5015/AddressingWebService/V3/Service.svc?wsdl" />
    <MetadataFile FileName="Service1.xsd" MetadataType="Schema" ID="cabeb513-9448-47b4-a2cf-e66ea389be7f" SourceId="1" SourceUrl="https://qa.soa-gw.mailposte.ca:5015/AddressingWebService/V3/Service.svc.xsd2.xsd" />
    <MetadataFile FileName="Service4.xsd" MetadataType="Schema" ID="40d7b8b1-18db-4808-bf37-8cbccefb617e" SourceId="1" SourceUrl="https://qa.soa-gw.mailposte.ca:5015/AddressingWebService/V3/Service.svc.xsd5.xsd" />
    <MetadataFile FileName="Service2.xsd" MetadataType="Schema" ID="e2426467-4659-444f-aa49-9db72fbe371b" SourceId="1" SourceUrl="https://qa.soa-gw.mailposte.ca:5015/AddressingWebService/V3/Service.svc.xsd6.xsd" />
  </Metadata>
  <Extensions>
    <ExtensionFile FileName="configuration91.svcinfo" Name="configuration91.svcinfo" />
    <ExtensionFile FileName="configuration.svcinfo" Name="configuration.svcinfo" />
  </Extensions>
</ReferenceGroup>