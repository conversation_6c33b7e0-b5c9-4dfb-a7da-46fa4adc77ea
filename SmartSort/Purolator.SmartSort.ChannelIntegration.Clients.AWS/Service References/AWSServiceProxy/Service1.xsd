<?xml version="1.0" encoding="utf-8"?>
<xsd:schema xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://aws.canadapost.ca/2015/01" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" elementFormDefault="qualified" targetNamespace="http://www.canadapost.ca/ws/address/2015/01" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
  <xsd:import schemaLocation="Service.svc.xsd4.xsd" namespace="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects" />
  <xsd:complexType name="Address">
    <xsd:sequence>
      <xsd:element xmlns:q11="http://www.canadapost.ca/ws/address/2015/01" minOccurs="1" maxOccurs="1" name="StatusCode" type="q11:StatusCode" />
      <xsd:element minOccurs="1" maxOccurs="1" name="ConfidenceLevel" type="xsd:int" />
      <xsd:element xmlns:q12="http://www.canadapost.ca/ws/address/2015/01" minOccurs="1" maxOccurs="1" name="InfoReasonCodes" type="q12:InfoReasonCode" />
      <xsd:element xmlns:q13="http://www.canadapost.ca/ws/address/2015/01" minOccurs="1" maxOccurs="1" name="WarningReasonCodes" type="q13:WarningReasonCode" />
      <xsd:element xmlns:q14="http://www.canadapost.ca/ws/address/2015/01" minOccurs="1" maxOccurs="1" name="ErrorReasonCodes" type="q14:ErrorReasonCode" />
      <xsd:element xmlns:q15="http://www.canadapost.ca/ws/address/2015/01" minOccurs="1" maxOccurs="1" name="AddressType" nillable="true" type="q15:AddressRecordType" />
      <xsd:element minOccurs="0" maxOccurs="1" name="MunicipalityName" type="xsd:string" />
      <xsd:element xmlns:q16="http://www.canadapost.ca/ws/address/2015/01" minOccurs="1" maxOccurs="1" name="ProvinceCode" nillable="true" type="q16:ProvinceCodeType" />
      <xsd:element minOccurs="0" maxOccurs="1" name="PostalCode" type="xsd:string" />
      <xsd:element minOccurs="1" maxOccurs="1" name="Latitude" nillable="true" type="xsd:decimal" />
      <xsd:element minOccurs="1" maxOccurs="1" name="Longitude" nillable="true" type="xsd:decimal" />
      <xsd:element xmlns:q17="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="unbounded" name="Formatted" type="q17:FormattedAddressLines" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="StatusCode">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="Valid" />
      <xsd:enumeration value="Invalid" />
      <xsd:enumeration value="ValidRangeOnly" />
      <xsd:enumeration value="Unparsable" />
      <xsd:enumeration value="IncompleteInput" />
      <xsd:enumeration value="NotCanadian" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="InfoReasonCode">
    <xsd:list>
      <xsd:simpleType>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="BuildingFound" />
          <xsd:enumeration value="LargeVolumeReceiverFound" />
          <xsd:enumeration value="GovtBuildingFound" />
          <xsd:enumeration value="BusinessNameFound" />
          <xsd:enumeration value="FarmFound" />
          <xsd:enumeration value="DeliveryInstallationAdded" />
          <xsd:enumeration value="StreetAddressMissing" />
          <xsd:enumeration value="MultipleMatchesFound" />
        </xsd:restriction>
      </xsd:simpleType>
    </xsd:list>
  </xsd:simpleType>
  <xsd:simpleType name="WarningReasonCode">
    <xsd:list>
      <xsd:simpleType>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="SuiteAdded" />
          <xsd:enumeration value="SuiteChanged" />
          <xsd:enumeration value="SuiteRemoved" />
          <xsd:enumeration value="StreetNumberAdded" />
          <xsd:enumeration value="StreetNumberChanged" />
          <xsd:enumeration value="StreetNumberRemoved" />
          <xsd:enumeration value="StreetNumberSuffixAdded" />
          <xsd:enumeration value="StreetNumberSuffixChanged" />
          <xsd:enumeration value="StreetNumberSuffixRemoved" />
          <xsd:enumeration value="StreetAdded" />
          <xsd:enumeration value="StreetChanged" />
          <xsd:enumeration value="StreetRemoved" />
          <xsd:enumeration value="FuzzyStreetUsed" />
          <xsd:enumeration value="AlternateStreetUsed" />
          <xsd:enumeration value="StreetTypeAdded" />
          <xsd:enumeration value="StreetTypeChanged" />
          <xsd:enumeration value="StreetTypeRemoved" />
          <xsd:enumeration value="StreetDirectionAdded" />
          <xsd:enumeration value="StreetDirectionChanged" />
          <xsd:enumeration value="StreetDirectionRemoved" />
          <xsd:enumeration value="MunicipalityAdded" />
          <xsd:enumeration value="MunicipalityChanged" />
          <xsd:enumeration value="FuzzyMunicipalityUsed" />
          <xsd:enumeration value="AlternateMunicipalityUsed" />
          <xsd:enumeration value="ProvinceAdded" />
          <xsd:enumeration value="ProvinceChanged" />
          <xsd:enumeration value="PostalCodeAdded" />
          <xsd:enumeration value="PostalCodeChanged" />
          <xsd:enumeration value="DeliveryInstallationChanged" />
          <xsd:enumeration value="DeliveryInstallationRemoved" />
          <xsd:enumeration value="BusinessNameAdded" />
          <xsd:enumeration value="BusinessNameChanged" />
          <xsd:enumeration value="BusinessNameRemoved" />
          <xsd:enumeration value="FuzzyBusinessNameUsed" />
          <xsd:enumeration value="POBoxAdded" />
          <xsd:enumeration value="POBoxChanged" />
          <xsd:enumeration value="POBoxRemoved" />
          <xsd:enumeration value="RuralRouteAdded" />
          <xsd:enumeration value="RuralRouteChanged" />
          <xsd:enumeration value="RuralRouteRemoved" />
          <xsd:enumeration value="GDAdded" />
          <xsd:enumeration value="GDChanged" />
          <xsd:enumeration value="GDRemoved" />
          <xsd:enumeration value="StreetAddressRemoved" />
          <xsd:enumeration value="POBoxAddressRemoved" />
          <xsd:enumeration value="RuralAddressRemoved" />
          <xsd:enumeration value="GDAddressRemoved" />
          <xsd:enumeration value="StreetWithPOBoxAddressRemoved" />
          <xsd:enumeration value="InvalidCharacterRemoved" />
        </xsd:restriction>
      </xsd:simpleType>
    </xsd:list>
  </xsd:simpleType>
  <xsd:simpleType name="ErrorReasonCode">
    <xsd:list>
      <xsd:simpleType>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="SuiteNotFound" />
          <xsd:enumeration value="SuiteMissing" />
          <xsd:enumeration value="SuiteInvalid" />
          <xsd:enumeration value="StreetNumberNotFound" />
          <xsd:enumeration value="StreetNumberInvalid" />
          <xsd:enumeration value="StreetNumberSuffixNotFound" />
          <xsd:enumeration value="StreetNumberSuffixMissing" />
          <xsd:enumeration value="StreetNotFound" />
          <xsd:enumeration value="StreetMissing" />
          <xsd:enumeration value="StreetDirectionMissing" />
          <xsd:enumeration value="StreetDirectionInvalid" />
          <xsd:enumeration value="StreetTypeMissing" />
          <xsd:enumeration value="StreetTypeInvalid" />
          <xsd:enumeration value="MunicipalityNotFound" />
          <xsd:enumeration value="MunicipalityMissing" />
          <xsd:enumeration value="ProvinceInvalid" />
          <xsd:enumeration value="ProvinceMissing" />
          <xsd:enumeration value="POBoxNotFound" />
          <xsd:enumeration value="POBoxMissing" />
          <xsd:enumeration value="POBoxInvalid" />
          <xsd:enumeration value="RuralRouteNotFound" />
          <xsd:enumeration value="RuralRouteMissing" />
          <xsd:enumeration value="RuralRouteInvalid" />
          <xsd:enumeration value="PostalCodeInvalid" />
          <xsd:enumeration value="PostalCodeMissing" />
          <xsd:enumeration value="InvalidInputNumber" />
          <xsd:enumeration value="GDNotFound" />
          <xsd:enumeration value="GDMissing" />
        </xsd:restriction>
      </xsd:simpleType>
    </xsd:list>
  </xsd:simpleType>
  <xsd:simpleType name="AddressRecordType">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="Street" />
      <xsd:enumeration value="POBox" />
      <xsd:enumeration value="RuralRoute" />
      <xsd:enumeration value="GeneralDelivery" />
      <xsd:enumeration value="POBoxWithStreet" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:simpleType name="ProvinceCodeType">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="AB" />
      <xsd:enumeration value="BC" />
      <xsd:enumeration value="MB" />
      <xsd:enumeration value="NB" />
      <xsd:enumeration value="NL" />
      <xsd:enumeration value="NS" />
      <xsd:enumeration value="NT" />
      <xsd:enumeration value="NU" />
      <xsd:enumeration value="ON" />
      <xsd:enumeration value="PE" />
      <xsd:enumeration value="QC" />
      <xsd:enumeration value="SK" />
      <xsd:enumeration value="YT" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="FormattedAddressLines">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Addressee" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Line1" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Line2" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Line3" type="xsd:string" />
    </xsd:sequence>
    <xsd:attribute ref="xml:lang" use="optional" />
  </xsd:complexType>
  <xsd:complexType name="FormattedInvalidAddress">
    <xsd:complexContent mixed="false">
      <xsd:extension xmlns:q18="http://www.canadapost.ca/ws/address/2015/01" base="q18:Address">
        <xsd:sequence>
          <xsd:element minOccurs="0" maxOccurs="1" name="Invalid" type="q18:InvalidAddressType" />
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="InvalidAddressType" />
  <xsd:complexType name="FormattedPOBoxWithStreetAddress">
    <xsd:complexContent mixed="false">
      <xsd:extension xmlns:q19="http://www.canadapost.ca/ws/address/2015/01" base="q19:Address">
        <xsd:sequence>
          <xsd:element minOccurs="0" maxOccurs="1" name="POBoxWithStreetAddress" type="q19:POBoxWithStreetAddressType" />
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="POBoxWithStreetAddressType">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Suite" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Name" type="xsd:string" />
      <xsd:element minOccurs="1" maxOccurs="1" name="StreetNumber" type="xsd:int" />
      <xsd:element minOccurs="0" maxOccurs="1" name="NumberSuffix" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Type" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Direction" type="xsd:string" />
      <xsd:element minOccurs="1" maxOccurs="1" name="PONumber" type="xsd:int" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationTypeCode" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationName" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationArea" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="FormattedStreetAddress">
    <xsd:complexContent mixed="false">
      <xsd:extension xmlns:q20="http://www.canadapost.ca/ws/address/2015/01" base="q20:Address">
        <xsd:sequence>
          <xsd:element minOccurs="0" maxOccurs="1" name="Street" type="q20:StreetAddressType" />
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="StreetAddressType">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Suite" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Name" type="xsd:string" />
      <xsd:element minOccurs="1" maxOccurs="1" name="Number" type="xsd:int" />
      <xsd:element minOccurs="0" maxOccurs="1" name="NumberSuffix" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Type" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Direction" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="FormattedRuralRouteAddress">
    <xsd:complexContent mixed="false">
      <xsd:extension xmlns:q21="http://www.canadapost.ca/ws/address/2015/01" base="q21:Address">
        <xsd:sequence>
          <xsd:element minOccurs="0" maxOccurs="1" name="Rural" type="q21:RuralRouteAddressType" />
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="RuralRouteAddressType">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="RouteTypeCode" type="xsd:string" />
      <xsd:element minOccurs="1" maxOccurs="1" name="RouteNumber" type="xsd:int" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationTypeCode" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationName" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationArea" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="FormattedGeneralDeliveryAddress">
    <xsd:complexContent mixed="false">
      <xsd:extension xmlns:q22="http://www.canadapost.ca/ws/address/2015/01" base="q22:Address">
        <xsd:sequence>
          <xsd:element minOccurs="0" maxOccurs="1" name="GeneralDelivery" type="q22:GeneralDeliveryAddressType" />
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="GeneralDeliveryAddressType">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="DeliveryTypeCode" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationTypeCode" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationName" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationArea" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="FormattedPOBoxAddress">
    <xsd:complexContent mixed="false">
      <xsd:extension xmlns:q23="http://www.canadapost.ca/ws/address/2015/01" base="q23:Address">
        <xsd:sequence>
          <xsd:element minOccurs="0" maxOccurs="1" name="POBox" type="q23:POBoxAddressType" />
        </xsd:sequence>
      </xsd:extension>
    </xsd:complexContent>
  </xsd:complexType>
  <xsd:complexType name="POBoxAddressType">
    <xsd:sequence>
      <xsd:element minOccurs="1" maxOccurs="1" name="Number" type="xsd:int" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationTypeCode" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationName" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="InstallationArea" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CustomFaultMsg">
    <xsd:annotation>
      <xsd:appinfo>
        <IsValueType xmlns="http://schemas.microsoft.com/2003/10/Serialization/">true</IsValueType>
      </xsd:appinfo>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element minOccurs="0" name="MyCustomErrMsg" nillable="true" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element xmlns:q24="http://www.canadapost.ca/ws/address/2015/01" name="CustomFaultMsg" nillable="true" type="q24:CustomFaultMsg" />
  <xsd:complexType name="AddressInput">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Addressee" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="AddressLine1" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="AddressLine2" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="MunicipalityName" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="ProvinceCode" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="PostalCode" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ArrayOfMunicipality">
    <xsd:sequence>
      <xsd:element xmlns:q25="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="unbounded" name="Municipality" nillable="true" type="q25:Municipality" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element xmlns:q26="http://www.canadapost.ca/ws/address/2015/01" name="ArrayOfMunicipality" nillable="true" type="q26:ArrayOfMunicipality" />
  <xsd:complexType name="Municipality">
    <xsd:sequence>
      <xsd:element xmlns:q27="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects" minOccurs="0" name="GeneralDeliveries" nillable="true" type="q27:ArrayOfGeneralDelivery" />
      <xsd:element minOccurs="0" name="Name" nillable="true" type="xsd:string" />
      <xsd:element minOccurs="0" name="OldName" nillable="true" type="xsd:string" />
      <xsd:element xmlns:q28="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects" minOccurs="0" name="POBoxes" nillable="true" type="q28:POBoxes" />
      <xsd:element xmlns:q29="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects" minOccurs="0" name="RuralRoutes" nillable="true" type="q29:RuralRoutes" />
      <xsd:element xmlns:q30="http://schemas.datacontract.org/2004/07/CanadaPost.Address.Lookup.AddressObjects" minOccurs="0" name="Streets" nillable="true" type="q30:ArrayOfStreet" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element xmlns:q31="http://www.canadapost.ca/ws/address/2015/01" name="Municipality" nillable="true" type="q31:Municipality" />
  <xsd:complexType name="FSADetailsType">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="FSA" type="xsd:string" />
      <xsd:element minOccurs="1" maxOccurs="1" name="CMBImplementationDate" nillable="true" type="xsd:date" />
      <xsd:element minOccurs="0" maxOccurs="1" name="CMBImplementationState" type="xsd:string" />
      <xsd:element xmlns:q32="http://www.canadapost.ca/ws/address/2015/01" minOccurs="1" maxOccurs="1" name="FSAStatus" type="q32:FSAStatus" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:simpleType name="FSAStatus">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="Unknown" />
      <xsd:enumeration value="Invalid" />
      <xsd:enumeration value="OK" />
      <xsd:enumeration value="NotFound" />
    </xsd:restriction>
  </xsd:simpleType>
  <xsd:complexType name="MunicipalityNames">
    <xsd:sequence>
      <xsd:element xmlns:q33="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="1" name="Provinces" type="q33:ArrayOfProvince" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ArrayOfProvince">
    <xsd:sequence>
      <xsd:element xmlns:q34="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="unbounded" name="Province" nillable="true" type="q34:Province" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="Province">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Code" type="xsd:string" />
      <xsd:element xmlns:q35="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="1" name="MunicipalityNames" type="q35:ArrayOfMunicipalityName" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ArrayOfMunicipalityName">
    <xsd:sequence>
      <xsd:element xmlns:q36="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="unbounded" name="MunicipalityName" nillable="true" type="q36:MunicipalityName" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="MunicipalityName">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Value" type="xsd:string" />
      <xsd:element xmlns:q37="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="1" name="AlternateMunicipalityNames" type="q37:ArrayOfAlternateMunicipalityName" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ArrayOfAlternateMunicipalityName">
    <xsd:sequence>
      <xsd:element xmlns:q38="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="unbounded" name="AlternateMunicipalityName" nillable="true" type="q38:AlternateMunicipalityName" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AlternateMunicipalityName">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Value" type="xsd:string" />
      <xsd:element minOccurs="1" maxOccurs="1" name="IsValid" type="xsd:boolean" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="StreetTypeLists">
    <xsd:sequence>
      <xsd:element xmlns:q39="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="unbounded" name="StreetTypes" type="q39:StreetTypesLocalized" />
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="StreetTypesLocalized">
    <xsd:sequence>
      <xsd:element xmlns:q40="http://www.canadapost.ca/ws/address/2015/01" minOccurs="0" maxOccurs="unbounded" name="StreetType" type="q40:StreetType" />
    </xsd:sequence>
    <xsd:attribute ref="xml:lang" use="optional" />
  </xsd:complexType>
  <xsd:complexType name="StreetType">
    <xsd:sequence>
      <xsd:element minOccurs="0" maxOccurs="1" name="Code" type="xsd:string" />
      <xsd:element minOccurs="0" maxOccurs="1" name="Description" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
</xsd:schema>