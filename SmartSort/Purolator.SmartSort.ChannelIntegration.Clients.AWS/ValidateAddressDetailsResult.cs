﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.ChannelIntegration.Clients.AWS
{
    public class ValidateAddressDetailsResult
    {
        private bool _isParseable = false;
        private bool _isValid = false;
        private bool _alternativeAddressInd = false;
        private SuggestedAddress _matchingSuggestedAddress = null;
        
        public AWSStatusCode AWSStatusCode { get; set; }

        public bool IsParseable
        {
            get { return _isParseable; }
            set { _isParseable = value; }
        }

        public bool IsValid
        {
            get { return _isValid; }
            set { _isValid = value; }
        }

        public bool AlternativeAddressInd
        {
            get { return _alternativeAddressInd; }
            set { _alternativeAddressInd = value; }
        }

        public SuggestedAddress MatchingSuggestedAddress
        {
            get { return _matchingSuggestedAddress; }
            set { _matchingSuggestedAddress = value; }
        }
    }
}
