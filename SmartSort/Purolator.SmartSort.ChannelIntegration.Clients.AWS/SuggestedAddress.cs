﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.ChannelIntegration.Clients.AWS
{
    [Serializable]
    public class SuggestedAddress : Address
    {
        public enum StreetNumberSequence
        {
            Odd,
            Even,
            All
        }

        public enum AddressTypes
        {
            Unknown,
            Street,
            PostOfficeBox,
            RuralRoute,
            GeneralDelivery
        }

        public new SuggestedAddress Clone()
        {
            SuggestedAddress newAddress = new SuggestedAddress();
            newAddress.StreetName = StreetName;
            newAddress.StreetNumber = StreetNumber;
            newAddress.StreetNumberSuffix = StreetNumberSuffix;
            newAddress.StreetDirection = StreetDirection;
            newAddress.StreetType = StreetType;
            newAddress.Floor = Floor;
            newAddress.Suite = Suite;
            newAddress.StreetAddress2 = StreetAddress2;
            newAddress.StreetAddress3 = StreetAddress3;
            newAddress.PostalCode = PostalCode;
            newAddress.ProvinceCode = ProvinceCode;
            newAddress.City = City;

            //Range Data
            newAddress.StreetNumberSequenceType = StreetNumberSequenceType;
            newAddress.FromStreetNumber = FromStreetNumber;
            newAddress.ToStreetNumber = ToStreetNumber;
            newAddress.FromSuiteNumber = FromSuiteNumber;
            newAddress.ToSuiteNumber = ToSuiteNumber;
            newAddress.FromStreetNumberSuffix = FromStreetNumberSuffix;
            newAddress.ToStreetNumberSuffix = ToStreetNumberSuffix;
            newAddress.FromSuiteNumber = FromSuiteNumber;
            newAddress.ToSuiteNumber = ToSuiteNumber;

            newAddress.AddressType = AddressType;
            return newAddress;
        }

        private AddressTypes _addressType = AddressTypes.Unknown;
        public AddressTypes AddressType
        {
            get { return _addressType; }
            set { _addressType = value; }
        }

        private StreetNumberSequence _streetNumberSequenceType;
        public StreetNumberSequence StreetNumberSequenceType
        {
            get { return _streetNumberSequenceType; }
            set { _streetNumberSequenceType = value; }
        }

        private string _fromStreetNumber;
        public string FromStreetNumber
        {
            get { return _fromStreetNumber; }
            set { _fromStreetNumber = value; }
        }

        private string _toStreetNumber;
        public string ToStreetNumber
        {
            get { return _toStreetNumber; }
            set { _toStreetNumber = value; }
        }

        private string _fromStreetNumberSuffix;
        public string FromStreetNumberSuffix
        {
            get { return _fromStreetNumberSuffix; }
            set { _fromStreetNumberSuffix = value; }
        }

        private string _toStreetNumberSuffix;
        public string ToStreetNumberSuffix
        {
            get { return _toStreetNumberSuffix; }
            set { _toStreetNumberSuffix = value; }
        }

        private string _fromSuiteNumber;
        public string FromSuiteNumber
        {
            get { return _fromSuiteNumber; }
            set { _fromSuiteNumber = value; }
        }

        private string _toSuiteNumber;
        public string ToSuiteNumber
        {
            get { return _toSuiteNumber; }
            set { _toSuiteNumber = value; }
        }

    }

}
