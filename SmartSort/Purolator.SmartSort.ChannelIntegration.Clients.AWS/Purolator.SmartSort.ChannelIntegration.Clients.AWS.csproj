﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{37718613-EC5C-4C0C-BC48-7626506BA000}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Purolator.SmartSort.ChannelIntegration.Clients.AWS</RootNamespace>
    <AssemblyName>Purolator.SmartSort.ChannelIntegration.Clients.AWS</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>..\SmartSortRuleSet.ruleset</CodeAnalysisRuleSet>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AWS.cs" />
    <Compile Include="AWSConstants.cs" />
    <Compile Include="AWSTranslate.cs" />
    <Compile Include="MunicipalitiesResult.cs" />
    <Compile Include="GetSuggestedAddressesResult.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Service References\AWSServiceProxy\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="SuggestedAddress.cs" />
    <Compile Include="ValidateAddressDetailsResult.cs" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.Address.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.CMBDetailsType.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.FSADetailsType.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetPostalCodeDetailsResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.GetStreetTypeListResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupGeneralDeliveryAddressResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupPOBoxAddressResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupRuralAddressResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.LookupStreetAddressResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.MunicipalityNames.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.PostalCode1.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWSServiceProxy.StreetTypesLocalized.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\AWSServiceProxy\Service.wsdl" />
    <None Include="Service References\AWSServiceProxy\Service.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\AWSServiceProxy\Service1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\AWSServiceProxy\Service2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\AWSServiceProxy\Service3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\AWSServiceProxy\Service4.xsd">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Service References\AWSServiceProxy\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\AWSServiceProxy\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\AWSServiceProxy\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\AWSServiceProxy\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Purolator.SmartSort.Business.Common\Purolator.SmartSort.Business.Common.csproj">
      <Project>{7b3557d3-dc6a-484d-bbf3-e48b5832208e}</Project>
      <Name>Purolator.SmartSort.Business.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Business.Entities\Purolator.SmartSort.Business.Entities.csproj">
      <Project>{862103a3-8dab-46ee-8890-2f192a71ed66}</Project>
      <Name>Purolator.SmartSort.Business.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Common\Purolator.SmartSort.Common.csproj">
      <Project>{93213bc1-5510-457f-b09b-94acb33a232f}</Project>
      <Name>Purolator.SmartSort.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>