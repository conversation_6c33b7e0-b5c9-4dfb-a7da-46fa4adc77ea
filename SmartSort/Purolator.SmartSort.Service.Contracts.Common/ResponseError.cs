﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Business.Common.Exception;
using Purolator.SmartSort.Business.Components.Common;
//using Purolator.SmartSort.Business.
using System.Runtime.Serialization;
using Purolator.SmartSort.Service.Common;

namespace Purolator.SmartSort.Service.Contracts.Common
{
    [DataContract(Name = "ResponseError", Namespace = ServiceNamespace.DataType, IsReference = false)]
    public class ResponseError
    {
        private long _errorCode;
        private string _errorDescription;

        /// <summary>
        /// Initializes a new instance of ResponseError class.
        /// </summary>
        public ResponseError()
        {
            _errorCode = 0;
            _errorDescription = "";
        }
        
        
        /// <summary>
        /// Get the error code.
        /// </summary>
        [DataMember(Name = "ErrorCode", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 0)]  
        public long ErrorCode
        {
            get { return _errorCode; }
            set { _errorCode = value; }
        }

        /// <summary>
        /// Get the exception description.
        /// </summary>
        [DataMember(Name = "ErrorMessage", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]  
        public string ErrorMessage
        {
            get { return _errorDescription; }
            set { _errorDescription = value; }
        }
    }
}
