﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Business.Common.Exception;
using Purolator.SmartSort.Business.Components.Common;
using System.Xml.Serialization;
using System.Runtime.Serialization;
using Purolator.SmartSort.Service.Common;


namespace Purolator.SmartSort.Service.Contracts.Common
{
    [DataContract(Name = "SmartSortResponseError", Namespace = ServiceNamespace.DataType, IsReference = false)]
    public class SmartSortResponseError
    {        

        /// <summary>
        /// Initializes a new instance of ResponseError class.
        /// </summary>
        public SmartSortResponseError()
        {

            /*_pin = ""; 
            _errorCode = "0";
            _errorMessage = "";*/
        }

       
        

        private string _pin;
        /// <summary>
        /// Get the exception description.
        /// </summary>
        ///         
        [DataMember(Name = "Pin", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 0)]        
        public string Pin
        {
            get { return _pin; }
            set { _pin = value; }
        }


        private string _errorCode;
        /// <summary>
        /// Get the error code.
        /// </summary>
        ///         
        [DataMember(Name = "ErrorCode", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]        
        public string ErrorCode
        {
            get { return _errorCode; }
            set { _errorCode = value; }
        }


        private string _errorMessage;
        /// <summary>
        /// Get the exception description.
        /// </summary>
        [DataMember(Name = "ErrorMessage", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 2)]        
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }
    }
}
