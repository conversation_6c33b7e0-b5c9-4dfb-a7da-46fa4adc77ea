﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.34209
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Purolator.SmartSort.Windows.Service.RoutePrinter {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class RoutePlanRenderResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal RoutePlanRenderResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Purolator.SmartSort.Windows.Service.RoutePrinter.RoutePlanRenderResource", typeof(RoutePlanRenderResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City/Municipality.
        /// </summary>
        public static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Name.
        /// </summary>
        public static string CustomerName {
            get {
                return ResourceManager.GetString("CustomerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generated At: {0}.
        /// </summary>
        public static string DisplayDate {
            get {
                return ResourceManager.GetString("DisplayDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FSA/CITY/POSTAL CODE.
        /// </summary>
        public static string Facility {
            get {
                return ResourceManager.GetString("Facility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High.
        /// </summary>
        public static string High {
            get {
                return ResourceManager.GetString("High", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low.
        /// </summary>
        public static string Low {
            get {
                return ResourceManager.GetString("Low", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No data available.
        /// </summary>
        public static string NoData {
            get {
                return ResourceManager.GetString("NoData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Postal Code.
        /// </summary>
        public static string PostalCode {
            get {
                return ResourceManager.GetString("PostalCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PUDRO.
        /// </summary>
        public static string Pudro {
            get {
                return ResourceManager.GetString("Pudro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Route Plan: {0}.
        /// </summary>
        public static string RoutePlanVersion {
            get {
                return ResourceManager.GetString("RoutePlanVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shelf No..
        /// </summary>
        public static string ShelfNo {
            get {
                return ResourceManager.GetString("ShelfNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Split Route Plan.
        /// </summary>
        public static string SplitTitle {
            get {
                return ResourceManager.GetString("SplitTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Street Name.
        /// </summary>
        public static string StreetName {
            get {
                return ResourceManager.GetString("StreetName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Street Number.
        /// </summary>
        public static string StreetNumber {
            get {
                return ResourceManager.GetString("StreetNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        public static string StreetNumberType {
            get {
                return ResourceManager.GetString("StreetNumberType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Number.
        /// </summary>
        public static string UnitNumber {
            get {
                return ResourceManager.GetString("UnitNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Route Plan.
        /// </summary>
        public static string V2Title {
            get {
                return ResourceManager.GetString("V2Title", resourceCulture);
            }
        }
    }
}
