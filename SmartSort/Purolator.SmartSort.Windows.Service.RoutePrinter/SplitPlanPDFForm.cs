﻿using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Shapes;
using MigraDoc.DocumentObjectModel.Tables;
using Purolator.SmartSort.Business.Entities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Web;
//using Purolator.SmartSort.RoutePlanRendering.Resources;
using System.Linq;

namespace Purolator.SmartSort.Windows.Service.RoutePrinter
{
   
    public class SplitPlanPDFForm
    {

        private readonly static Color DefaultTableColor = new Color(235, 240, 249);
        private readonly static Color DefaultTableBorderColor = new Color(81, 125, 192);
        private readonly static Color DefaultAltCellColor = new Color(196, 196, 196);


        private readonly List<TableHeaderAttributes> AddressTableHeaderAttributes = new List<TableHeaderAttributes>()
        {
             { new TableHeaderAttributes() { ParagraphAlignment = ParagraphAlignment.Center, Width = "1.8cm"   } },  //Pudro          
             { new TableHeaderAttributes() { ParagraphAlignment = ParagraphAlignment.Center, Width = "1.8cm" } },  //FSA
             { new TableHeaderAttributes() { ParagraphAlignment = ParagraphAlignment.Center, Width = "10cm" } },  //Postal Code
             { new TableHeaderAttributes() { ParagraphAlignment = ParagraphAlignment.Center, Width = "5cm" } }  //City
        };


        private readonly List<List<TableColumnAttributes>> AddressTableColumnAttributes = new List<List<TableColumnAttributes>>()
        {
            new List<TableColumnAttributes>() 
            {
                { new TableColumnAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Text = RoutePlanRenderResource.Pudro, CellIndex = 0 } },             
                { new TableColumnAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Text = "FSA", CellIndex = 1 } },                             
                { new TableColumnAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Text = "Postal Code", CellIndex = 2 } },             
                { new TableColumnAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Text = "City", CellIndex = 3 } },                             }                                         
        };

        private Table AddTable(Section section)
        {            
            Table table = null;       
            table = section.AddTable();
            table.Style = "Table";
            table.Borders.Color = DefaultTableBorderColor;
            table.Borders.Width = 0.25;
            table.Borders.Left.Width = 0.5;
            table.Borders.Right.Width = 0.5;
            table.Rows.LeftIndent = 0;
            return table;
        }

        private Row AddTableColumn(Table table)
        {
            Row row = table.AddRow();
            row.HeadingFormat = true;
            row.Format.Alignment = ParagraphAlignment.Center;
            row.Format.Font.Bold = true;
            row.Shading.Color = DefaultTableColor;
            row.TopPadding = 2;
            row.BottomPadding = 2;
            return row;
        }

        private void AddTableColumn(Table table, TableHeaderAttributes columnAttributes)
        {
            Column column = table.AddColumn(columnAttributes.Width);
            column.Format.Alignment = columnAttributes.ParagraphAlignment;
        }

        private void FillTableColumn(Row row, TableColumnAttributes tableAttributes)
        {
            row.Cells[tableAttributes.CellIndex].AddParagraph(tableAttributes.Text);
            row.Cells[tableAttributes.CellIndex].Format.Alignment = tableAttributes.ParagraphAlignment;
            row.Cells[tableAttributes.CellIndex].VerticalAlignment = tableAttributes.VerticalAlignment;
            if (tableAttributes.MergeDown > 0)
            {
                row.Cells[tableAttributes.CellIndex].MergeDown = tableAttributes.MergeDown;
            }
            if (tableAttributes.MergeRight > 0)
            {
                row.Cells[tableAttributes.CellIndex].MergeRight = tableAttributes.MergeRight;
            }
        }

        private Row AddTableRow(Table table)
        {
            Row row = table.AddRow();
            row.TopPadding = 1.5;
            row.Format.Font.Size = 12;
            row.TopPadding = 2;
            row.BottomPadding = 2;
            return row;
        }

        private void ApplyDocumentStyles(Document document)
        {
            Style style = document.Styles["Normal"];
            style.Font.Name = "Arial";
            style = document.Styles[StyleNames.Header];
            style.ParagraphFormat.AddTabStop("16cm", TabAlignment.Right);

            style = document.Styles[StyleNames.Footer];
            style.ParagraphFormat.AddTabStop("8cm", TabAlignment.Center);

            style = document.Styles.AddStyle("Table", "Normal");
            style.Font.Name = "Arial";
            style.Font.Size = 12;

            style = document.Styles.AddStyle("Reference", "Normal");
            style.ParagraphFormat.SpaceBefore = "5mm";
            style.ParagraphFormat.SpaceAfter = "5mm";
            style.ParagraphFormat.TabStops.AddTabStop("16cm", TabAlignment.Right);
        }


        private Section AddDocumentSection(Document document)
        {
            Section section = document.AddSection();
            section.Document.DefaultPageSetup.LeftMargin = "1.0cm";
            section.Document.DefaultPageSetup.RightMargin = "1.0cm";

            return section;
        }


        private Section AddDocumentSection(string logoPath, Document document, string terminal, string planVersion)
        {
            string creationDate = DateTime.Now.ToString(@"MM-dd-yyyy h:mm:ss tt", CultureInfo.InvariantCulture);

            Section section = document.AddSection();
            section.Document.DefaultPageSetup.LeftMargin = "1.0cm";
            section.Document.DefaultPageSetup.RightMargin = "1.0cm";

            if (!string.IsNullOrWhiteSpace(logoPath))
            {
                Image image = section.Headers.Primary.AddImage(logoPath);
                image.Height = "0.5cm";
                image.LockAspectRatio = true;
                image.RelativeVertical = RelativeVertical.Line;
                image.RelativeHorizontal = RelativeHorizontal.Margin;
                image.Top = ShapePosition.Top;
                image.Left = ShapePosition.Right;
                image.WrapFormat.Style = WrapStyle.Through;
            }


            Paragraph paragraph = section.Headers.Primary.AddParagraph();
            paragraph.Format.Alignment = ParagraphAlignment.Center;
            paragraph.AddFormattedText(terminal, new Font() { Size = 16, Bold = true });


            TextFrame textFrame = section.Headers.Primary.AddTextFrame();
            textFrame.Height = "3.0cm";
            textFrame.Width = "7.0cm";
            textFrame.Left = ShapePosition.Left;
            textFrame.RelativeHorizontal = RelativeHorizontal.Margin;
            textFrame.Top = "0.8cm";
            textFrame.RelativeVertical = RelativeVertical.Page;

            paragraph = textFrame.AddParagraph();   
            paragraph.Format.Alignment = ParagraphAlignment.Left;
            paragraph.AddFormattedText(string.Format(RoutePlanRenderResource.DisplayDate, creationDate), new Font() { Size = 10, Bold = false });

            textFrame = section.Headers.Primary.AddTextFrame();
            textFrame.Height = "3.0cm";
            textFrame.Width = "8.0cm";
            textFrame.Left = ShapePosition.Right;
            textFrame.RelativeHorizontal = RelativeHorizontal.Margin;
            textFrame.Top = "0.8cm";
            textFrame.RelativeVertical = RelativeVertical.Page;
            paragraph = textFrame.AddParagraph();            
            paragraph.Format.Alignment = ParagraphAlignment.Left;
            paragraph.AddFormattedText(string.Format(RoutePlanRenderResource.RoutePlanVersion, planVersion), new Font() { Size = 10, Bold = false });
                       


            paragraph = section.Footers.Primary.AddParagraph();
            paragraph.Format.Font.Size = 10;
            paragraph.Format.Alignment = ParagraphAlignment.Right;
            paragraph.AddText("Page ");
            paragraph.AddPageField();
            paragraph.AddText(" of ");
            paragraph.AddNumPagesField();

            return section;
        }


        private void AddDataNotAvailableRegion(Document document)
        {
            Section section = document.LastSection;
            TextFrame addressFrame = section.AddTextFrame();
            addressFrame.Top = "5.9cm";
            addressFrame.Width = " 10cm";
            addressFrame.Left = "9cm";
            Paragraph paragraph = addressFrame.AddParagraph();
            paragraph.Format.Font.Size = 16;
            paragraph.Format.Alignment = ParagraphAlignment.Center;
            paragraph.AddText(RoutePlanRenderResource.NoData);
        }

        private void AddAddressTable(Section section, SplitPlanPrint item)
        {
            Table table = AddTable(section);

            foreach (var tableAttributes in AddressTableHeaderAttributes)
            {
                AddTableColumn(table, tableAttributes);
            }


            foreach (var tableRows in AddressTableColumnAttributes)
            {

                Row row = AddTableColumn(table);
                foreach (var attr in tableRows)
                {
                    FillTableColumn(row, attr);
                }
            }
            

            FillAddressTable(item, table);
        }

        private void FillAddressTable(SplitPlanPrint printItem, Table table)
        {      
            const int MAX_FSA = 20;
            const int MAX_PC = 100;
            const int MAX_CITY = 20;

                             
            foreach (var item in printItem.Pudros)
            {                
                Row row = null;
                
                int iFSA = 0;
                int iPC = 0;
                int iCity = 0;
                                        

                while (iFSA < item.FSA.Count || iPC < item.PostalCodes.Count || iCity < item.Cities.Count)
                {
                    row = AddTableRow(table);
                    row.Cells[0].VerticalAlignment = VerticalAlignment.Center;
                    AddCellParagraph(row.Cells[0], item.Pudro, false, false);

                    int rFSA = MAX_FSA;
                    int rPC = MAX_PC;
                    int rCity = MAX_CITY;

                    if (iFSA < item.FSA.Count)
                    {
                        if(iFSA + rFSA > item.FSA.Count) 
                        {
                             rFSA = item.FSA.Count - iFSA;                        
                        }
                        else
                        {
                            rFSA = MAX_FSA;
                        }

                    
                        var fsas = item.FSA.GetRange(iFSA, rFSA);
                        iFSA += rFSA;

                        foreach (var fsa in fsas)
                        {
                            AddCellParagraphMonospaced(row.Cells[1], fsa);
                        }

                    }
                    else
                    {
                        AddCellParagraphMonospaced(row.Cells[1], "");
                    }

                    //
                    if (iPC < item.PostalCodes.Count)
                    {
                        if (iPC + rPC > item.PostalCodes.Count)
                        {
                            rPC = item.PostalCodes.Count - iPC;
                        }
                        else
                        {
                            rPC = MAX_PC;
                        }

                        var postalCodes = item.PostalCodes.GetRange(iPC, rPC);
                        iPC += rPC;

                        int pcIndex = -1;
                        string pcCurrentValue = string.Empty;
                        foreach (var pc in postalCodes)
                        {
                            pcIndex++;
                            if (pcIndex < 5)
                            {
                                if (!string.IsNullOrWhiteSpace(pcCurrentValue))
                                {
                                    pcCurrentValue += "/";
                                }
                                pcCurrentValue += pc.Trim();
                            }
                            else
                            {
                                AddCellParagraphMonospaced(row.Cells[2], pcCurrentValue);
                                pcIndex = 0;
                                pcCurrentValue = pc.Trim();
                            }                            
                        }
                        // add last one
                        AddCellParagraphMonospaced(row.Cells[2], pcCurrentValue);
                    }
                    else
                    {
                        AddCellParagraphMonospaced(row.Cells[2], "");
                    }

                   //
                    if (iCity < item.Cities.Count)
                    {
                        if (iCity + rCity > item.Cities.Count)
                        {
                            rCity = item.Cities.Count - iCity;
                        }
                        else
                        {
                            rCity = MAX_CITY;
                        }

                        var cities = item.Cities.GetRange(iCity, rCity);
                        iCity += rCity;

                        foreach (var city in cities)
                        {
                            AddCellParagraphMonospaced(row.Cells[3], city);
                        }

                    }
                    else
                    {
                        AddCellParagraphMonospaced(row.Cells[3], "");
                    }
                }
                
            }            
        }

        private Paragraph AddCellParagraph(Cell cell, string text, bool isBold, bool isUseAltCellColor = false, ParagraphAlignment alignment = ParagraphAlignment.Center)
        {
            Paragraph paragraph = cell.AddParagraph(text);
            paragraph.Format.Font.Bold = isBold;
            paragraph.Format.Alignment = alignment;

            if (isUseAltCellColor)
            {
                cell.Shading.Color = DefaultAltCellColor;
            }
            return paragraph;
        }

        private Paragraph AddCellParagraphMonospaced(Cell cell, string text)
        {

            Paragraph paragraph = cell.AddParagraph(text);                        
            paragraph.Format.Font.Name = "Lucida Sans Typewriter";
            paragraph.Format.Alignment = ParagraphAlignment.Center;
        
            return paragraph;
        }

        


        public Document CreateDocument(string logoPath, SplitPlanPrint routePlan)
        {
            Document document = new Document();
            document.Info.Title = RoutePlanRenderResource.SplitTitle;
            document.Info.Subject = RoutePlanRenderResource.SplitTitle;
            document.Info.Author = "Purolator";
            document.DefaultPageSetup.PageFormat = PageFormat.Letter;
            document.DefaultPageSetup.Orientation = Orientation.Portrait;


            ApplyDocumentStyles(document);

            if (routePlan != null && routePlan.Pudros != null)
            {
                Section section = AddDocumentSection(logoPath, document,routePlan.Terminal, routePlan.VersionNumber);
                AddAddressTable(section, routePlan);                
            }
            else
            {
                document.AddSection();
                AddDataNotAvailableRegion(document);
            }

            return document;
        }       
    }
}
