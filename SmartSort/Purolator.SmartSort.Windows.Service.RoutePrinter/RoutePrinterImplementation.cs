﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Configuration;
using Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Common;
using Purolator.SmartSort.Data.Access.RouteManagement;
using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Tables;
using MigraDoc.DocumentObjectModel.Shapes;
using MigraDoc.Rendering;
using System.Diagnostics;
using PdfSharp.Pdf;


namespace Purolator.SmartSort.Windows.Service.RoutePrinter
{
    public class RoutePrinterImplementation
    {

        private readonly RoutePlanServiceRepository serviceRepo = new RoutePlanServiceRepository(Constants.DB_CONNECTION_NAME);
        private readonly RoutePlanPrintRepository printRepo = new RoutePlanPrintRepository(Constants.DB_CONNECTION_NAME);
        private readonly SplitPlanPrintRepository splitRepo = new SplitPlanPrintRepository(Constants.DB_CONNECTION_NAME);

        public void ResetDatabaseQueue()
        {
            try
            {
                serviceRepo.ResetWorkItems(Environment.MachineName);
            }
            catch (Exception ex)
            {
                Logger.Error("Error resetting preprint queue", LogCategories.PRINT_SERVICE, ex);
            }
        }

        

        public List<RoutePrintWorkItem> GetWorkItems(int fetchAmount)
        {
            List<RoutePrintWorkItem> result = new List<RoutePrintWorkItem>();            
                                   
            try
            {
                result = serviceRepo.GetWorkItems(Environment.MachineName, fetchAmount);
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading preprint records", LogCategories.PRINT_SERVICE, ex);
            }            

            return result;
        }


        private void SetLanguage(string lang)
        {
            System.Globalization.CultureInfo ci = new System.Globalization.CultureInfo(lang);
            System.Threading.Thread.CurrentThread.CurrentCulture = ci;
            RoutePlanRenderResource.Culture = ci;
        }

        public void Process(RoutePrintWorkItem item)
        {
            bool success = false;
            var startTime = DateTime.Now;
            try
            {
                
                Logger.Info("Processing route plan request:" + item.RequestID, LogCategories.PRINT_SERVICE);

                AppSettingsReader settings = new AppSettingsReader();
                string uncpath = (string)settings.GetValue("UNCSavePath", typeof(string));
                string user = (string)settings.GetValue("UNCUser", typeof(string));
                string domain = (string)settings.GetValue("UNCDomain", typeof(string));
                string password = (string)settings.GetValue("UNCPassword", typeof(string));

                var printRecord = printRepo.GetRoutePlan(item.RoutePlanVersionID);
                var splitRecord = splitRepo.GetSplitPlan(item.RoutePlanVersionID);

                SetLanguage("en");

                // English
                RoutePlanPDFForm formEn = new RoutePlanPDFForm();
                var documentEn = formEn.CreateDocument(null, printRecord);
                PdfDocumentRenderer pdfRendererEn = new PdfDocumentRenderer(false, PdfFontEmbedding.Always)
                {
                    Document = documentEn
                };

                pdfRendererEn.RenderDocument();

                SplitPlanPDFForm sformEn = new SplitPlanPDFForm();
                var sdocumentEn = sformEn.CreateDocument(null, splitRecord);
                PdfDocumentRenderer spdfRendererEn = new PdfDocumentRenderer(false, PdfFontEmbedding.Always)
                {
                    Document = sdocumentEn
                };

                spdfRendererEn.RenderDocument();

                //French
                SetLanguage("fr-ca");

                RoutePlanPDFForm formFr = new RoutePlanPDFForm();
                var documentFr = formFr.CreateDocument(null, printRecord);
                PdfDocumentRenderer pdfRendererFr = new PdfDocumentRenderer(false, PdfFontEmbedding.Always)
                {
                    Document = documentFr
                };

                pdfRendererFr.RenderDocument();

                SplitPlanPDFForm sformFr = new SplitPlanPDFForm();
                var sdocumentFr = sformFr.CreateDocument(null, splitRecord);
                PdfDocumentRenderer spdfRendererFr = new PdfDocumentRenderer(false, PdfFontEmbedding.Always)
                {
                    Document = sdocumentFr
                };

                spdfRendererFr.RenderDocument();
                               
                string pdfRoutePlanFileName = "RoutePlan_" + item.RoutePlanVersionID;
                string pdfSplitPlanFileName = "SplitPlan_" + item.RoutePlanVersionID;

                using (UNCAccessWithCredentials unc = new UNCAccessWithCredentials())
                {
                    if (unc.NetUseWithCredentials(uncpath, user, domain, password))
                    {
                        using (var writer = File.Create(uncpath + "\\" + pdfRoutePlanFileName + "_en.pdf"))
                        {
                            pdfRendererEn.Save(writer, true);
                        }

                        using (var writer = File.Create(uncpath + "\\" + pdfSplitPlanFileName + "_en.pdf"))
                        {
                            spdfRendererEn.Save(writer, true);
                        }


                        using (var writer = File.Create(uncpath + "\\" + pdfRoutePlanFileName + "_fr-ca.pdf"))
                        {
                            pdfRendererFr.Save(writer, true);
                        }

                        using (var writer = File.Create(uncpath + "\\" + pdfSplitPlanFileName + "_fr-ca.pdf"))
                        {
                            spdfRendererFr.Save(writer, true);
                        }

                        item.RoutePlanPDF = pdfRoutePlanFileName;
                        item.SplitPlanPDF = pdfSplitPlanFileName;

                        success = true;
                    }
                    else
                    {                       
                        Logger.Error("Failed to connect to " + uncpath +
                                        "\r\nLastError = " + unc.LastError.ToString(), LogCategories.PRINT_SERVICE);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error processing route plan request:" + item.RequestID, LogCategories.PRINT_SERVICE, ex);
            }
            finally
            {               
                try
                {
                    item.Status = success ? "C" : "E";
                    serviceRepo.UpdateWorkItem(item);
                }
                catch (Exception ex)
                {
                    Logger.Error("Error updating route plan request:" + item.RequestID, LogCategories.PRINT_SERVICE, ex);
                }
                TimeSpan duration = DateTime.Now - startTime;
                Logger.Info("Done processing route plan request:" + item.RequestID + " in " + duration.Seconds + "s", LogCategories.PRINT_SERVICE);
            }
        }

    }    
         
}
