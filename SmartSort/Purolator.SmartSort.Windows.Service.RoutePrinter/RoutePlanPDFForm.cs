﻿using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Shapes;
using MigraDoc.DocumentObjectModel.Tables;
using Purolator.SmartSort.Business.Entities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Web;
//using Purolator.SmartSort.RoutePlanRendering.Resources;
using System.Linq;

namespace Purolator.SmartSort.Windows.Service.RoutePrinter
{

    public struct TableColumnAttributes
    {
        #region Public Properties
        public string Text { get; set; }
        public ParagraphAlignment ParagraphAlignment { get; set; }  
        public VerticalAlignment VerticalAlignment { get; set; }
        public int MergeDown { get; set; }
        public int MergeRight { get; set; }
        public int CellIndex { get; set; }
        #endregion
    }

    public struct TableHeaderAttributes
    {
        #region Public Properties        
        public ParagraphAlignment ParagraphAlignment { get; set; }                
        public Unit Width { get; set; }
        #endregion
    }


    public class RoutePlanPDFForm
    {

        private readonly static Color DefaultTableColor = new Color(235, 240, 249);
        private readonly static Color DefaultTableBorderColor = new Color(81, 125, 192);
        private readonly static Color DefaultAltCellColor = new Color(196, 196, 196);


        private readonly List<TableHeaderAttributes> AddressTableHeaderAttributes = new List<TableHeaderAttributes>()
        {
             { new TableHeaderAttributes() { ParagraphAlignment = ParagraphAlignment.Center, Width = "6cm"   } },  //RoutePlanRenderResource.StreetName          
             { new TableHeaderAttributes() { ParagraphAlignment = ParagraphAlignment.Center, Width = "2cm" } },  //RoutePlanRenderResource.Low
             { new TableHeaderAttributes() { ParagraphAlignment = ParagraphAlignment.Center, Width = "2cm" } },  //RoutePlanRenderResource.High            
             { new TableHeaderAttributes() { ParagraphAlignment = ParagraphAlignment.Center, Width = "2cm"   } },  //RoutePlanRenderResource.StreetNumberType           
             { new TableHeaderAttributes() { ParagraphAlignment = ParagraphAlignment.Center, Width = "4cm"   } },  //RoutePlanRenderResource.City          
             { new TableHeaderAttributes() { ParagraphAlignment = ParagraphAlignment.Center, Width = "2cm" } },  //RoutePlanRenderResource.ShelfNo
            
        };


        private readonly List<List<TableColumnAttributes>> AddressTableColumnAttributes = new List<List<TableColumnAttributes>>()
        {
            new List<TableColumnAttributes>() 
            {
                { new TableColumnAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Text = RoutePlanRenderResource.StreetName, CellIndex = 0, MergeDown = 1 } },             
                { new TableColumnAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Text = RoutePlanRenderResource.StreetNumber, CellIndex = 1, MergeRight = 2 } },             
                { new TableColumnAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Text = RoutePlanRenderResource.City, CellIndex = 4, MergeDown = 1 } },             
                { new TableColumnAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Text = RoutePlanRenderResource.ShelfNo, CellIndex = 5, MergeDown = 1 } },
            },
            new List<TableColumnAttributes>() 
            {
                { new TableColumnAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Text = RoutePlanRenderResource.Low, CellIndex = 1 } },
                { new TableColumnAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Text = RoutePlanRenderResource.High, CellIndex = 2 } },             
                { new TableColumnAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Text = RoutePlanRenderResource.StreetNumberType, CellIndex = 3 } },                           
            }                                     
        };

        private Table AddTable(Section section)
        {            
            Table table = null;       
            table = section.AddTable();
            table.Style = "Table";
            table.Borders.Color = DefaultTableBorderColor;
            table.Borders.Width = 0.25;
            table.Borders.Left.Width = 0.5;
            table.Borders.Right.Width = 0.5;
            table.Rows.LeftIndent = 0;
            return table;
        }

        private Row AddTableColumn(Table table)
        {
            Row row = table.AddRow();
            row.HeadingFormat = true;
            row.Format.Alignment = ParagraphAlignment.Center;
            row.Format.Font.Bold = true;
            row.Shading.Color = DefaultTableColor;
            row.TopPadding = 2;
            row.BottomPadding = 2;
            return row;
        }

        private void AddTableColumn(Table table, TableHeaderAttributes columnAttributes)
        {
            Column column = table.AddColumn(columnAttributes.Width);
            column.Format.Alignment = columnAttributes.ParagraphAlignment;
        }

        private void FillTableColumn(Row row, TableColumnAttributes tableAttributes)
        {
            row.Cells[tableAttributes.CellIndex].AddParagraph(tableAttributes.Text);
            row.Cells[tableAttributes.CellIndex].Format.Alignment = tableAttributes.ParagraphAlignment;
            row.Cells[tableAttributes.CellIndex].VerticalAlignment = tableAttributes.VerticalAlignment;
            if (tableAttributes.MergeDown > 0)
            {
                row.Cells[tableAttributes.CellIndex].MergeDown = tableAttributes.MergeDown;
            }
            if (tableAttributes.MergeRight > 0)
            {
                row.Cells[tableAttributes.CellIndex].MergeRight = tableAttributes.MergeRight;
            }
        }

        private Row AddTableRow(Table table)
        {
            Row row = table.AddRow();
            row.TopPadding = 1.5;
            row.Format.Font.Size = 12;
            row.TopPadding = 2;
            row.BottomPadding = 2;
            return row;
        }

        private void ApplyDocumentStyles(Document document)
        {
            Style style = document.Styles["Normal"];
            style.Font.Name = "Arial";
            style = document.Styles[StyleNames.Header];
            style.ParagraphFormat.AddTabStop("16cm", TabAlignment.Right);

            style = document.Styles[StyleNames.Footer];
            style.ParagraphFormat.AddTabStop("8cm", TabAlignment.Center);

            style = document.Styles.AddStyle("Table", "Normal");
            style.Font.Name = "Arial";
            style.Font.Size = 12;

            style = document.Styles.AddStyle("Reference", "Normal");
            style.ParagraphFormat.SpaceBefore = "5mm";
            style.ParagraphFormat.SpaceAfter = "5mm";
            style.ParagraphFormat.TabStops.AddTabStop("16cm", TabAlignment.Right);
        }


        private Section AddDocumentSection(Document document)
        {
            Section section = document.AddSection();
            section.Document.DefaultPageSetup.LeftMargin = "1.0cm";
            section.Document.DefaultPageSetup.RightMargin = "1.0cm";

            return section;
        }


        private Section AddDocumentSection(string logoPath, Document document, string route, string terminal, string planVersion)
        {
            string creationDate = DateTime.Now.ToString(@"MM-dd-yyyy h:mm:ss tt", CultureInfo.InvariantCulture);

            Section section = document.AddSection();
            section.Document.DefaultPageSetup.LeftMargin = "1.0cm";
            section.Document.DefaultPageSetup.RightMargin = "1.0cm";

            if (!string.IsNullOrWhiteSpace(logoPath))
            {
                Image image = section.Headers.Primary.AddImage(logoPath);
                image.Height = "0.5cm";
                image.LockAspectRatio = true;
                image.RelativeVertical = RelativeVertical.Line;
                image.RelativeHorizontal = RelativeHorizontal.Margin;
                image.Top = ShapePosition.Top;
                image.Left = ShapePosition.Right;
                image.WrapFormat.Style = WrapStyle.Through;
            }


            Paragraph paragraph = section.Headers.Primary.AddParagraph();
            paragraph.Format.Alignment = ParagraphAlignment.Center;
            paragraph.AddFormattedText(terminal + " - " + route, new Font() { Size = 16, Bold = true });


            TextFrame textFrame = section.Headers.Primary.AddTextFrame();
            textFrame.Height = "3.0cm";
            textFrame.Width = "7.0cm";
            textFrame.Left = ShapePosition.Left;
            textFrame.RelativeHorizontal = RelativeHorizontal.Margin;
            textFrame.Top = "0.8cm";
            textFrame.RelativeVertical = RelativeVertical.Page;           

            paragraph = textFrame.AddParagraph();
            paragraph.Format.Alignment = ParagraphAlignment.Left;
            paragraph.AddFormattedText(string.Format(RoutePlanRenderResource.DisplayDate, creationDate), new Font() { Size = 10, Bold = false });

            textFrame = section.Headers.Primary.AddTextFrame();
            textFrame.Height = "3.0cm";
            textFrame.Width = "9.0cm";
            textFrame.Left = ShapePosition.Right;
            textFrame.RelativeHorizontal = RelativeHorizontal.Margin;
            textFrame.Top = "0.8cm";
            textFrame.RelativeVertical = RelativeVertical.Page;
            paragraph = textFrame.AddParagraph();
            paragraph.Format.Alignment = ParagraphAlignment.Right;
            paragraph.AddFormattedText(string.Format(RoutePlanRenderResource.RoutePlanVersion, planVersion), new Font() { Size = 10, Bold = false });
                       


            paragraph = section.Footers.Primary.AddParagraph();
            paragraph.Format.Font.Size = 10;
            paragraph.Format.Alignment = ParagraphAlignment.Right;
            paragraph.AddText("Page ");
            paragraph.AddPageField();
            paragraph.AddText(" of ");
            paragraph.AddNumPagesField();

            return section;
        }


        private void AddDataNotAvailableRegion(Document document)
        {
            Section section = document.LastSection;
            TextFrame addressFrame = section.AddTextFrame();
            addressFrame.Top = "5.9cm";
            addressFrame.Width = " 10cm";
            addressFrame.Left = "9cm";
            Paragraph paragraph = addressFrame.AddParagraph();
            paragraph.Format.Font.Size = 16;
            paragraph.Format.Alignment = ParagraphAlignment.Center;
            paragraph.AddText(RoutePlanRenderResource.NoData);
        }

        private void AddAddressTable(Section section, RoutePlanPrintItem item)
        {
            Table table = AddTable(section);

            foreach (var tableAttributes in AddressTableHeaderAttributes)
            {
                AddTableColumn(table, tableAttributes);
            }


            foreach (var tableRows in AddressTableColumnAttributes)
            {

                Row row = AddTableColumn(table);
                foreach (var attr in tableRows)
                {
                    FillTableColumn(row, attr);
                }
            }
            

            FillAddressTable(item, table);
        }

        private void FillAddressTable(RoutePlanPrintItem printItem, Table table)
        {
            bool isUseAltCellColor = false;
            string lastShelf = String.Empty;

            int index = 0;
            foreach (var item in printItem.Addresses)
            {
                index++;
                isUseAltCellColor = index % 2 == 0;

                Row row = AddTableRow(table);
                string streetName = !String.IsNullOrWhiteSpace(item.StreetName) ? CultureInfo.CurrentCulture.TextInfo.ToTitleCase(item.StreetName.ToLower()) : String.Empty;
                string city = !String.IsNullOrWhiteSpace(item.City) ? CultureInfo.CurrentCulture.TextInfo.ToTitleCase(item.City.ToLower()) : String.Empty;


                AddCellParagraph(row.Cells[0], streetName, false, isUseAltCellColor);
                AddCellParagraph(row.Cells[1], item.FromStreetNum, false, isUseAltCellColor, ParagraphAlignment.Right);
                AddCellParagraph(row.Cells[2], item.ToStreetNum, false, isUseAltCellColor, ParagraphAlignment.Right);
                AddCellParagraph(row.Cells[3], String.IsNullOrWhiteSpace(item.StreetNumberType) ? String.Empty : item.StreetNumberType, false, isUseAltCellColor);
                AddCellParagraph(row.Cells[4], city, false, isUseAltCellColor);
                AddCellParagraph(row.Cells[5], item.Shelf, false, isUseAltCellColor);
            }
        }

        private Paragraph AddCellParagraph(Cell cell, string text, bool isBold, bool isUseAltCellColor = false, ParagraphAlignment alignment = ParagraphAlignment.Center)
        {
            Paragraph paragraph = cell.AddParagraph(text);
            paragraph.Format.Font.Bold = isBold;
            paragraph.Format.Alignment = alignment;

            if (isUseAltCellColor)
            {
                cell.Shading.Color = DefaultAltCellColor;
            }
            return paragraph;
        }



        public Document CreateDocument(string logoPath, RoutePlanPrint routePlan)
        {
            Document document = new Document();
            document.Info.Title = RoutePlanRenderResource.V2Title;
            document.Info.Subject = RoutePlanRenderResource.V2Title;
            document.Info.Author = "Purolator";
            document.DefaultPageSetup.PageFormat = PageFormat.Legal;
            document.DefaultPageSetup.Orientation = Orientation.Portrait;


            ApplyDocumentStyles(document);

            if (routePlan != null && routePlan.Routes != null)
            {
                foreach (var route in routePlan.Routes)
                {
                    Section section = AddDocumentSection(logoPath, document, route.Route, routePlan.Terminal, routePlan.VersionNumber);
                    AddAddressTable(section, route);
                }
            }
            else
            {
                document.AddSection();
                AddDataNotAvailableRegion(document);
            }

            return document;
        }       
    }
}
