﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="E1001" xml:space="preserve">
    <value>Valeur de temps invalide pour la date {0}</value>
  </data>
  <data name="E1002" xml:space="preserve">
    <value>Une erreur est survenue lors du déchargement du NIC</value>
  </data>
  <data name="E1003" xml:space="preserve">
    <value>Une erreur est survenue lors du repositionnement du NIC</value>
  </data>
  <data name="E1004" xml:space="preserve">
    <value>Code d'appareil manquant</value>
  </data>
  <data name="E1005" xml:space="preserve">
    <value>Numéro de dépôt manquant</value>
  </data>
  <data name="E1006" xml:space="preserve">
    <value>Numéro de route manquant</value>
  </data>
  <data name="E1007" xml:space="preserve">
    <value>NIC manquant</value>
  </data>
  <data name="E1008" xml:space="preserve">
    <value>Étagère invalide {0}</value>
  </data>
  <data name="E1010" xml:space="preserve">
    <value>Une erreur est survenue lors du déchargement des NIC</value>
  </data>
  <data name="E1011" xml:space="preserve">
    <value>Une erreur est survenue lors du repositionnement des NIC</value>
  </data>
  <data name="E1012" xml:space="preserve">
    <value>Une erreur est survenue lors de la fermeture du camion</value>
  </data>
  <data name="E1013" xml:space="preserve">
    <value>Une erreur est survenue lors de la récupération du manifeste du courrier</value>
  </data>
  <data name="E1014" xml:space="preserve">
    <value>Une erreur est survenue lors du repositionnement du NIC, déjà assigné à la route {0}</value>
  </data>
  <data name="E1015" xml:space="preserve">
    <value>L'étagère doit être {0}</value>
  </data>
  <data name="E1016" xml:space="preserve">
    <value>L'étagère ne peut être {0}</value>
  </data>
  <data name="E1017" xml:space="preserve">
    <value>Vous devez d'abord assigner la route.</value>
  </data>
  <data name="E2001" xml:space="preserve">
    <value>Une erreur est survenue avec le registre des lectures du tri intelligent</value>
  </data>
  <data name="E2002" xml:space="preserve">
    <value>Une erreur est survenue : le nombre d'articles dans la liste de lecture ne correspond pas au nombre d'éléments attendu {0}</value>
  </data>
  <data name="E2003" xml:space="preserve">
    <value>Une erreur est survenue avec le service du registre du tri intelligent</value>
  </data>
  <data name="E2004" xml:space="preserve">
    <value>Une erreur est survenue dans le système de lecture du tri intelligent</value>
  </data>
  <data name="E3000" xml:space="preserve">
    <value>Une erreur est survenue dans les paramètres du service de recherche du tri intelligent</value>
  </data>
  <data name="E3001" xml:space="preserve">
    <value>Une erreur est survenue avec la recherche du tri intelligent</value>
  </data>
  <data name="E4000" xml:space="preserve">
    <value>Une erreur est survenue avec les paramètres du service de fuseau horaire du tri intelligent</value>
  </data>
  <data name="E4001" xml:space="preserve">
    <value>Une erreur est survenue avec le service du fuseau horaire du tri intelligent</value>
  </data>
  <data name="TRK0" xml:space="preserve">
    <value>Route est pas fermée</value>
  </data>
</root>