﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Purolator.SmartSort.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ErrorResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ErrorResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Purolator.SmartSort.Resources.ErrorResources", typeof(ErrorResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid date time value {0}.
        /// </summary>
        public static string E1001 {
            get {
                return ResourceManager.GetString("E1001", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error unloading pin.
        /// </summary>
        public static string E1002 {
            get {
                return ResourceManager.GetString("E1002", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error reposition pin.
        /// </summary>
        public static string E1003 {
            get {
                return ResourceManager.GetString("E1003", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Missing device id.
        /// </summary>
        public static string E1004 {
            get {
                return ResourceManager.GetString("E1004", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Missing terminal number.
        /// </summary>
        public static string E1005 {
            get {
                return ResourceManager.GetString("E1005", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Missing route number.
        /// </summary>
        public static string E1006 {
            get {
                return ResourceManager.GetString("E1006", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Missing pin.
        /// </summary>
        public static string E1007 {
            get {
                return ResourceManager.GetString("E1007", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid shelf {0}.
        /// </summary>
        public static string E1008 {
            get {
                return ResourceManager.GetString("E1008", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error unloading pins.
        /// </summary>
        public static string E1010 {
            get {
                return ResourceManager.GetString("E1010", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error reposition pins.
        /// </summary>
        public static string E1011 {
            get {
                return ResourceManager.GetString("E1011", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error closing truck.
        /// </summary>
        public static string E1012 {
            get {
                return ResourceManager.GetString("E1012", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error getting courier manifest.
        /// </summary>
        public static string E1013 {
            get {
                return ResourceManager.GetString("E1013", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error reposition pin, already assigned to route {0}.
        /// </summary>
        public static string E1014 {
            get {
                return ResourceManager.GetString("E1014", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shelf must be {0}.
        /// </summary>
        public static string E1015 {
            get {
                return ResourceManager.GetString("E1015", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shelf cannot be {0}.
        /// </summary>
        public static string E1016 {
            get {
                return ResourceManager.GetString("E1016", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Route must be assigned first..
        /// </summary>
        public static string E1017 {
            get {
                return ResourceManager.GetString("E1017", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error with Smart Sort Scan Log.
        /// </summary>
        public static string E2001 {
            get {
                return ResourceManager.GetString("E2001", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error with number of items in scan list do not match correct number of elements expecting {0}.
        /// </summary>
        public static string E2002 {
            get {
                return ResourceManager.GetString("E2002", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error processing Smart Sort Scan Log.
        /// </summary>
        public static string E2003 {
            get {
                return ResourceManager.GetString("E2003", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error in Smart Sort Scan worker.
        /// </summary>
        public static string E2004 {
            get {
                return ResourceManager.GetString("E2004", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error with Paramaters in Smart Sort Lookup Service.
        /// </summary>
        public static string E3000 {
            get {
                return ResourceManager.GetString("E3000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error with Smart Sort Lookup.
        /// </summary>
        public static string E3001 {
            get {
                return ResourceManager.GetString("E3001", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error with Paramaters in Smart Sort Time Zone Service.
        /// </summary>
        public static string E4000 {
            get {
                return ResourceManager.GetString("E4000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error with Smart Sort Time Zone Service.
        /// </summary>
        public static string E4001 {
            get {
                return ResourceManager.GetString("E4001", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Route is not closed.
        /// </summary>
        public static string TRK0 {
            get {
                return ResourceManager.GetString("TRK0", resourceCulture);
            }
        }
    }
}
