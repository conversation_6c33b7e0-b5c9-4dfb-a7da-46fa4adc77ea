﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Purolator.SmartSort.Service.Contracts;
using Purolator.SmartSort.Service.MessageContracts;
using Purolator.SmartSort.Service.DataContracts;
using System.Data.SqlClient;
using System.Configuration;
using System.ServiceModel;
using Purolator.SmartSort.Common;
using Purolator.SmartSort.Service.Common;
using Entities = Purolator.SmartSort.Business.Entities;
using Translators = Purolator.SmartSort.Service.Translators;
using Purolator.SmartSort.Data.Access;
using Purolator.SmartSort.Service.Contracts.Common;
using Purolator.SmartSort.Resources;
using DevTrends.WCFDataAnnotations;

namespace Purolator.SmartSort.Service.Implementation.CourierManifest
{
  
    //[ValidateDataAnnotationsBehavior]
    public class CourierManifestService : ICourierManifestService
    {

        public static string[] SHELFS = {
            "01", "02", "03", "04" ,"05", "06" , "07" , "08", "09", "10"
            ,"11", "12", "13", "14", "15", "16", "17", "18", "19", "20"
            ,"21", "22", "A", "B", "C", "D", "PR", "DG"
          };


        private void SetLanguage(Purolator.SmartSort.Service.MessageContracts.Request req)
        {
            string lang = Language.en.ToString();
            if (req != null && req.RequestContext != null)
            {
                lang = req.RequestContext.Language.ToString();
            }
            System.Globalization.CultureInfo ci = new System.Globalization.CultureInfo(lang);
            System.Threading.Thread.CurrentThread.CurrentCulture = ci;
            ErrorResources.Culture = ci;
        }

        public UnloadFromTruckResponse UnloadFromTruck(UnloadFromTruckRequest unloadFromTruckRequest)
        {            
            UnloadFromTruckResponse response = new UnloadFromTruckResponse();            

            try
            {

                SetLanguage(unloadFromTruckRequest);

                string deviceId = unloadFromTruckRequest.DeviceID;
                string terminal = unloadFromTruckRequest.TerminalID;

                if (!validateFields(response, deviceId, terminal))
                {
                    return response;
                }

                LoadDeviationRepository repo = new LoadDeviationRepository(Constants.DB_CONNECTION_NAME);

                foreach (PinEvent ev in unloadFromTruckRequest.PinEvents)
                {
                    string pin = ev.Pin;
                    try
                    {
                        Entities.LoadDeviationEvent deviationEvent = new Entities.LoadDeviationEvent();
                        deviationEvent.Terminal = terminal;
                        deviationEvent.DeviceID = deviceId;
                        deviationEvent.Route = ev.Route;
                        deviationEvent.PIN = pin;

                        try
                        {
                            deviationEvent.ScanDateTime = DateTimeOffset.Parse(ev.EventTimestamp);
                        }
                        catch (Exception ex)
                        {
                            Logger.Error("Error parsing date in unload for pin:" + pin, LogCategories.COURIER_MANIFEST, ex);
                            PinResponseError error = new PinResponseError();
                            error.Pin = pin;
                            error.ErrorCode = "1001";
                            error.ErrorMessage = string.Format(ErrorResources.E1001, ev.EventTimestamp);
                            response.AddPinError(error);
                            continue;
                        }

                        if(validateRoute(response, ev.Route, pin))
                        {                      
                            repo.UnloadFromTruck(deviationEvent);
                        }
                    }
                    catch (Exception pinEx)
                    {
                        Logger.Error("Error unloading pin:" + pin , LogCategories.COURIER_MANIFEST, pinEx);
                        PinResponseError error = new PinResponseError();
                        error.Pin = pin;
                        error.ErrorCode = "1002";
                        error.ErrorMessage = ErrorResources.E1002;
                        response.AddPinError(error);
                    }
                    
                }                                                          
            }
            catch (Exception ex)
            {
               Logger.Error("Error unloading pins", LogCategories.COURIER_MANIFEST, ex);
               response.ErrorCode = "1010";
               response.ErrorMessage = ErrorResources.E1010;
            }
            finally 
            {
                response.InferStatusCode();
            }

            
            return response;
        }

        public RepositionPackageResponse RepositionPackage(RepositionPackageRequest repositionPackageRequest)
        {
            RepositionPackageResponse response = new RepositionPackageResponse();

            try
            {
                SetLanguage(repositionPackageRequest);

                string deviceId = repositionPackageRequest.DeviceID;
                string terminal = repositionPackageRequest.TerminalID;                

                
                if (!validateFields(response, deviceId, terminal))
                {
                    return response;
                }

                LoadDeviationRepository repo = new LoadDeviationRepository(Constants.DB_CONNECTION_NAME);

                foreach (PinRepositioningEvent ev in repositionPackageRequest.PinRepositioningEvents)
                {
                    string pin = ev.Pin;
                    try
                    {
                        Entities.LoadDeviationEvent deviationEvent = new Entities.LoadDeviationEvent();
                        deviationEvent.Terminal = terminal;
                        deviationEvent.DeviceID = deviceId;
                        deviationEvent.Route = ev.Route;
                        deviationEvent.Shelf = ev.Shelf;
                        deviationEvent.PIN = pin;
                        deviationEvent.Action = (Entities.Enums.RepositioningAction)ev.Action;

                        if(!validateShelf(deviationEvent.Shelf))
                        {
                            Logger.Error("Invalid shelf" + deviationEvent.Shelf + " in load deviation for pin:" + pin + " - action:" + deviationEvent.Action, LogCategories.COURIER_MANIFEST);
                            PinResponseError error = new PinResponseError();
                            error.Pin = pin;
                            error.ErrorCode = "1008";
                            error.ErrorMessage = string.Format(ErrorResources.E1008, deviationEvent.Shelf);
                            response.AddPinError(error);
                            continue;
                        }

                        try
                        {
                            deviationEvent.ScanDateTime = DateTimeOffset.Parse(ev.EventTimestamp);
                        }
                        catch (Exception ex)
                        {
                            Logger.Error("Error parsing date in load deviation for pin:" + pin + " - action:" + deviationEvent.Action, LogCategories.COURIER_MANIFEST, ex);
                            PinResponseError error = new PinResponseError();
                            error.Pin = pin;
                            error.ErrorCode = "1001";
                            error.ErrorMessage = string.Format(ErrorResources.E1001, ev.EventTimestamp);
                            response.AddPinError(error);
                            continue;
                        }

                        if (validateRoute(response, ev.Route, pin))
                        {
                            try
                            {
                                switch (deviationEvent.Action)
                                {
                                    case Entities.Enums.RepositioningAction.AlternateTruck:
                                        repo.LoadToAlternateTruck(deviationEvent);
                                        break;
                                    case Entities.Enums.RepositioningAction.Reposition:
                                        repo.RepositionPackage(deviationEvent);
                                        break;
                                    default: throw new Exception();
                                }
                            }
                            catch (SqlException ex)
                            {
                                if (ex.Number == 50000)
                                {
                                    PinResponseError error = new PinResponseError();
                                    error.Pin = pin;
                                   
                                    try
                                    {
                                        string[] messageParts = ex.Errors[0].Message.Split(':');

                                        switch (ex.Class)
                                        {
                                            case 12: // Requested by Hitesh
                                                error.ErrorCode = "1017";
                                                error.ErrorMessage = ErrorResources.E1017;
                                                break;
                                            case 13:
                                            case 14:
                                                error.ErrorCode = "1016";
                                                error.ErrorMessage = string.Format(ErrorResources.E1016, messageParts[1]);
                                                break;
                                            case 16:
                                                error.ErrorCode = "1014";
                                                error.ErrorMessage = string.Format(ErrorResources.E1014, messageParts[1]);
                                                break;
                                            case 17:
                                            case 18:
                                                error.ErrorCode = "1015";
                                                error.ErrorMessage = string.Format(ErrorResources.E1015, messageParts[1]);
                                                break;
                                            default:
                                                throw ex;
                                        }
                                    }
                                    catch (Exception e)
                                    {
                                        if(string.IsNullOrWhiteSpace(error.ErrorCode))
                                        {                                            
                                            error.ErrorCode = "1003";
                                        }
                                        error.ErrorMessage = ex.Errors[0].Message;
                                    }

                                    Logger.Info("Error repositioning pin:" + pin + " - " + error.ErrorMessage, LogCategories.COURIER_MANIFEST);
                                    response.AddPinError(error);
                                }
                                else
                                {
                                    throw ex;
                                }
                            }
                        }                        
                    }                        
                    catch (Exception pinEx)
                    {
                        Logger.Error("Error load deviation for pin:" + pin, LogCategories.COURIER_MANIFEST, pinEx);
                        PinResponseError error = new PinResponseError();
                        error.Pin = pin;
                        error.ErrorCode = "1003";
                        error.ErrorMessage = ErrorResources.E1003;
                        response.AddPinError(error);
                    }

                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error reposition pins", LogCategories.COURIER_MANIFEST, ex);
                response.ErrorCode = "1011";
                response.ErrorMessage = ErrorResources.E1011;
            }
            finally
            {
                response.InferStatusCode();
            }

            return response;
            
        }

        public CloseTruckResponse CloseTruck(CloseTruckRequest closeTruckRequest)
        {
            CloseTruckResponse response = new CloseTruckResponse();
            response.IsSuccess = false;
            try
            {
                SetLanguage(closeTruckRequest);

                LoadDeviationRepository repo = new LoadDeviationRepository(Constants.DB_CONNECTION_NAME);
                Entities.LoadDeviationEvent deviationEvent = new Entities.LoadDeviationEvent();
                deviationEvent.Terminal = closeTruckRequest.TerminalID;
                deviationEvent.DeviceID = closeTruckRequest.DeviceID;
                deviationEvent.Route = closeTruckRequest.Route;
                
                if (!validateFields(response, deviationEvent.DeviceID, deviationEvent.Terminal, deviationEvent.Route))
                {
                    return response;
                }

                try
                {
                    deviationEvent.ScanDateTime = DateTimeOffset.Parse(closeTruckRequest.EventTimestamp);
                }
                catch (Exception ex)
                {
                    Logger.Error("Error parsing date in close truck", LogCategories.COURIER_MANIFEST, ex);
                    Logger.Error("Error closing truck" + closeTruckRequest.TerminalID + " route:" + closeTruckRequest.Route, LogCategories.COURIER_MANIFEST, ex);
                    response.ErrorCode = "1001";
                    response.ErrorMessage = string.Format(ErrorResources.E1001, closeTruckRequest.EventTimestamp);

                    return response;
                }
               
                repo.CloseRoute(deviationEvent);
                response.IsSuccess = true;
            }
            catch (Exception ex)
            {
                Logger.Error("Error closing truck" + closeTruckRequest.TerminalID + " route:" + closeTruckRequest.Route, LogCategories.COURIER_MANIFEST, ex);
                response.ErrorCode = "1012";
                response.ErrorMessage = ErrorResources.E1012;
            }            

            return response;
        }

        public CourierManifestResponse GetCourierManifest(CourierManifestRequest courierManifestRequest)
        {
            CourierManifestResponse response =  new CourierManifestResponse();
            response.IsSuccess = false;

            try
            {
                SetLanguage(courierManifestRequest);


                if (!validateFields(response, courierManifestRequest.DeviceID, courierManifestRequest.TerminalID, courierManifestRequest.Route))
                {
                    return response;
                }

                DateTime manifestDate = DateTime.Now;

                try
                {
                    manifestDate = DateTime.Parse(courierManifestRequest.Date);
                }
                catch (Exception ex)
                {
                    Logger.Error("Error parsing date in get courier manifest", LogCategories.COURIER_MANIFEST, ex);
                    response.ErrorCode = "1001";
                    response.ErrorMessage = string.Format(ErrorResources.E1001, courierManifestRequest.Date);

                    return response;
                }

                CourierManifestRepository repo = new CourierManifestRepository(Constants.DB_CONNECTION_NAME);

                Entities.CourierManifest cm = repo.GetCourierManifest(courierManifestRequest.TerminalID, manifestDate, courierManifestRequest.Route);
                response = Translators.TranslateCourierManifest.Translate(cm);
                response.IsSuccess = true;

            }
            catch (SqlException ex)
            {
                if (ex.Number == 50000)
                {                    
                    response.ErrorCode = "TRK0";
                    response.ErrorMessage = ErrorResources.TRK0;
                }
                else
                {
                    Logger.Error("Error getting courier manifest for terminal:" + courierManifestRequest.TerminalID + " route:" + courierManifestRequest.Route, LogCategories.COURIER_MANIFEST, ex);
                    response.ErrorCode = "1013";
                    response.ErrorMessage = ErrorResources.E1013;
                }
            }              
            catch (Exception ex)
            {
                Logger.Error("Error getting courier manifest for terminal:" + courierManifestRequest.TerminalID + " route:" + courierManifestRequest.Route, LogCategories.COURIER_MANIFEST, ex);
                response.ErrorCode = "1013";
                response.ErrorMessage = ErrorResources.E1013;
            }

            response.TerminalID = courierManifestRequest.TerminalID;
            response.Route = courierManifestRequest.Route;

            return response;
        }

        public IsServiceUpResponse IsServiceUp()
        {
            IsServiceUpResponse response = new IsServiceUpResponse();
            try
            {
                CourierManifestRepository repo = new CourierManifestRepository(Constants.DB_CONNECTION_NAME);                
                response.IsServiceUp = repo.isServiceUp();                
            }
            catch (Exception ex)
            {
                Logger.Error("Error IsServiceUp:", LogCategories.COURIER_MANIFEST, ex);
            }

            return response;
        }


        private bool validateFields(DeviceResponseBase response, string deviceId, string terminal)
        {

            if (string.IsNullOrWhiteSpace(deviceId))
            {
                response.ErrorCode = "1004";
                response.ErrorMessage = ErrorResources.E1004;
                return false;
            }
            if (string.IsNullOrWhiteSpace(terminal))
            {
                response.ErrorCode = "1005";
                response.ErrorMessage = ErrorResources.E1005;
                return false;
            }            

            return true;
        }

        private bool validateFields(CourierManifestResponse response, string deviceId, string terminal, string route)
        {
            if (string.IsNullOrWhiteSpace(deviceId))
            {
                response.ErrorCode = "1004";
                response.ErrorMessage = ErrorResources.E1004;
                return false;
            }
            if (string.IsNullOrWhiteSpace(terminal))
            {
                response.ErrorCode = "1005";
                response.ErrorMessage = ErrorResources.E1005;
                return false;
            }
            if (string.IsNullOrWhiteSpace(route))
            {
                response.ErrorCode = "1006";
                response.ErrorMessage = ErrorResources.E1006;
                return false;
            }
            return true;
        }

        private bool validateFields(CloseTruckResponse response, string deviceId, string terminal, string route)
        {
            if (string.IsNullOrWhiteSpace(deviceId))
            {
                response.ErrorCode = "1004";
                response.ErrorMessage = ErrorResources.E1004;
                return false;
            }
            if (string.IsNullOrWhiteSpace(terminal))
            {
                response.ErrorCode = "1005";
                response.ErrorMessage = ErrorResources.E1005;
                return false;
            }
            if (string.IsNullOrWhiteSpace(route))
            {
                response.ErrorCode = "1006";
                response.ErrorMessage = ErrorResources.E1006;
                return false;
            }
            return true;
        }


        private bool validateShelf(string shelf)
        {
            if(SHELFS.Contains(shelf)) return true;                        
            return false;
        }

        private bool validateRoute(DeviceResponseBase response, string route, string pin)
        {
            PinResponseError error = new PinResponseError();
            error.Pin = pin;

            if (string.IsNullOrWhiteSpace(route))
            {
                error.ErrorCode = "1006";
                error.ErrorMessage = ErrorResources.E1006;
                response.AddPinError(error);
                return false;
            }

            if (string.IsNullOrWhiteSpace(pin))
            {
                error.ErrorCode = "1007";
                error.ErrorMessage = ErrorResources.E1007;
                response.AddPinError(error);
                return false;
            }


            return true;
        }

    }
}
