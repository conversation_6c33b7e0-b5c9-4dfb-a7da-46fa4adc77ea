﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
  </startup>
  <connectionStrings>
    <add name="SqlConnection" connectionString="Data Source= DEVESODB.purolator.com\DEVESODB,1433;Initial Catalog= SSBE;User ID= smartsortapp;Password=!Spring15;Pooling=true;Max Pool Size=100;Min Pool Size=0;Connection Lifetime=600" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <system.data>
    <configSections>
      <section name="dataConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings, Microsoft.Practices.EnterpriseLibrary.Data, Version=5.0.414.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="true" />
    </configSections>
    <dataConfiguration defaultDatabase="SqlConnection" />
  </system.data>
</configuration>