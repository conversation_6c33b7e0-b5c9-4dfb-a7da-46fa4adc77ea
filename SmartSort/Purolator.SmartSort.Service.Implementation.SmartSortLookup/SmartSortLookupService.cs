﻿using System;
using System.ServiceModel;
using System.ServiceModel.Activation;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Purolator.SmartSort.Business.Common.Exception;
using System.ServiceModel.Web;
using Purolator.SmartSort.Common;
using Purolator.SmartSort.Service.MessageContracts;
using Purolator.SmartSort.Service.DataContracts;
using Purolator.SmartSort.Business.Common;
using System.Xml.Linq;
using System.IO;
using System.Diagnostics;

using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data.Sql;
using System.Configuration;
using System.Data;
using System.Reflection;
using Purolator.SmartSort.Resources;

using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Data.Access;
using System.ServiceModel.Description;
using System.ServiceModel.Dispatcher;
using System.Runtime.Serialization.Json;
using System.ServiceModel.Channels;
using System.Net;
using DevTrends.WCFDataAnnotations;

namespace Purolator.SmartSort.Service.Implementation.SmartSortLookup
{
    // NOTE: You can use the "Rename" command on the "Refactor" menu to change the class name "SmartSortScanService" in code, svc and config file together.
    // NOTE: In order to launch WCF Test Client for testing this service, please select SmartSortScanService.svc or SmartSortScanService.svc.cs at the Solution Explorer and start debugging.
    //[ValidateDataAnnotationsBehavior]
    [AspNetCompatibilityRequirements(RequirementsMode = AspNetCompatibilityRequirementsMode.Allowed)]
    [ValidateDataAnnotationsBehavior]   
    public class SmartSortLookupService : ISmartSortLookupService
    {                       
        public SmartSortError SmartSortScanError(String scansReceived)
        {
            SmartSortError smartError = new SmartSortError("Error Recevied");
            return smartError;

        }




        [WebInvoke(Method = "POST",
        RequestFormat = WebMessageFormat.Json,
        ResponseFormat = WebMessageFormat.Json,
        BodyStyle = WebMessageBodyStyle.Bare,
        UriTemplate = "getrouteshelf")]
        public SmartSortGetRouteShelfResponse GetRouteShelf(SmartSortGetRouteShelfRequest smartSortGetRouteShelfRequest)
        {
            SmartSortGetRouteShelfResponse resp = new SmartSortGetRouteShelfResponse();

            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            try
            {
                bool isPinFound = false;
                
                XDocument doc = new XDocument();
                //Lookup PIN in SSBE          
                resp.Errors = new List<Contracts.Common.ResponseError>();

                //Error first then prove otherwise
                resp.StatusCode = StatusCodeSmartSortLookup.Error.ToString();
                resp.PIN = smartSortGetRouteShelfRequest.PIN;
                resp.TerminalID = smartSortGetRouteShelfRequest.TerminalID.ToString();

                Entities.SmartSortLookup ssLookup = new Entities.SmartSortLookup();

                ssLookup = Translators.TranslateSmartSortLog.Translate(smartSortGetRouteShelfRequest);

                SmartSortLookupRepository sslRepo = new SmartSortLookupRepository(Purolator.SmartSort.Common.Constants.DB_CONNECTION_NAME);


                try
                {
                    List<Entities.RouteDetails> ssl = sslRepo.GetRouteDetails(ssLookup);

                    resp.StatusCode = ssLookup.Status;

                    if (resp.StatusCode == "Resolution Required")
                    {
                        // only return SRR if no address present, otherwise try to correct and lookup
                        if (string.IsNullOrWhiteSpace(smartSortGetRouteShelfRequest.AddressLine1))
                        {
                            resp.StatusCode = StatusCodeSmartSortLookup.SRR.ToString();
                            isPinFound = true;
                        }                        
                    }
                    else if (resp.StatusCode == "Resolved")
                    {
                        resp.StatusCode = StatusCodeSmartSortLookup.Resolved.ToString();
                        isPinFound = true;
                    }
                    else if (resp.StatusCode == "Miss Direct")
                    {
                        resp.StatusCode = StatusCodeSmartSortLookup.MDR.ToString();
                        isPinFound = true;
                    }

                    if (ssl != null)
                    {
                        if (ssl.Count > 0)
                        {
                            resp.RouteNumber = ssl[0].Route;
                            resp.ShelfNumber = ssl[0].Shelf;
                            resp.TruckShelfOverride = ssl[0].TruckShelfOverride;
                            resp.DeliverySequenceID = ssl[0].DeliverySeqID;
                            resp.PrimarySort = ssl[0].PrimarySort;
                            resp.ConveyerSideIdentifier = ssl[0].BeltSide;

                        }
                    }

                }
                catch (Exception ex)
                {

                    Logger.Error("SmartSortLookupService error", LogCategories.LOOKUP_SERVICE, ex);

                    Contracts.Common.ResponseError smartSortLookupError = new Contracts.Common.ResponseError();
                    smartSortLookupError.ErrorMessage = ErrorResources.E3001 + ' ' + ex.Message;
                    smartSortLookupError.ErrorCode = 3001;
                    resp.Errors.Add(smartSortLookupError);
                    return resp;
                }

                if (!isPinFound)                
                {

                    if (smartSortGetRouteShelfRequest.AddressLine1 != null && smartSortGetRouteShelfRequest.AddressLine1.ValueExists())
                    {
                        //Call AWS W/S to parse Address                                       
                        Purolator.SmartSort.ChannelIntegration.Clients.AWS.AWS aws = new ChannelIntegration.Clients.AWS.AWS();

                        Purolator.SmartSort.Business.Entities.Address add = new Business.Entities.Address();
                        add.CustomerName = smartSortGetRouteShelfRequest.CustomerName;
                        add.StreetNumber = smartSortGetRouteShelfRequest.StreetNumber;
                        add.StreetName = smartSortGetRouteShelfRequest.AddressLine1;
                        add.StreetAddress2 = smartSortGetRouteShelfRequest.AddressLine2;
                        add.StreetAddress3 = string.Empty;
                        add.ProvinceCode = smartSortGetRouteShelfRequest.ProvinceCode;  //AWS SEEMS TO NEED THESE SEPERATED..OOPS
                        add.City = smartSortGetRouteShelfRequest.MunicipalityName; //AWS SEEMS TO NEED THESE SEPERATED..OOPS
                        add.PostalCode = smartSortGetRouteShelfRequest.PostalCode;
                        Purolator.SmartSort.Business.Common.BOResult<Purolator.SmartSort.ChannelIntegration.Clients.AWS.ValidateAddressDetailsResult> result = new BOResult<ChannelIntegration.Clients.AWS.ValidateAddressDetailsResult>();

                        result = aws.ValidateAddress(add);

                        //If IsParsable and Civic Address || UnParsable
                        if ((result.ReturnObject.IsParseable == true && result.ReturnObject.MatchingSuggestedAddress.AddressType == ChannelIntegration.Clients.AWS.SuggestedAddress.AddressTypes.Street) || (result.ReturnObject.IsParseable == false))
                        {
                            Entities.SmartSortLookupAddressValidation ssLookupAV = new Entities.SmartSortLookupAddressValidation();

                            ssLookupAV = Translators.TranslateSmartSortLookupAddressValidation.Translate(smartSortGetRouteShelfRequest, result.ReturnObject);

                            SmartSortLookupAddressValidationRepository sslAVRepo = new SmartSortLookupAddressValidationRepository(Purolator.SmartSort.Common.Constants.DB_CONNECTION_NAME);

                            try
                            {
                                List<Entities.RouteDetailsAddressValidation> sslAV = sslAVRepo.GetRouteDetailsAddressValidation(ssLookupAV);//sslRepo.GetRouteDetails(ssLookup);    

                                resp.StatusCode = ssLookupAV.Status;
                                if (resp.StatusCode == "Remediation Required")
                                {
                                    resp.StatusCode = StatusCodeSmartSortLookup.REM.ToString();
                                }
                                else if (resp.StatusCode == "Resolution Required")
                                {
                                    resp.StatusCode = StatusCodeSmartSortLookup.SRR.ToString();
                                }

                                if (sslAV != null && sslAV.Count > 0)
                                {
                                    resp.RouteNumber = sslAV[0].Route;
                                    resp.ShelfNumber = sslAV[0].Shelf;
                                    resp.TruckShelfOverride = sslAV[0].TruckShelfOverride;
                                    resp.DeliverySequenceID = sslAV[0].DeliverySeqID;
                                    resp.PrimarySort = sslAV[0].PrimarySort;
                                    resp.ConveyerSideIdentifier = sslAV[0].BeltSide;

                                }
                            }
                            catch (Exception ex)
                            {
                                Logger.Error("SmartSortLookupService error", LogCategories.LOOKUP_SERVICE, ex);

                                Contracts.Common.ResponseError smartSortLookupError = new Contracts.Common.ResponseError();
                                smartSortLookupError.ErrorMessage = ErrorResources.E3001 + ':' + ex.Message;
                                smartSortLookupError.ErrorCode = 3001;
                                resp.Errors.Add(smartSortLookupError);

                            }
                        }
                        else
                        {
                            //Parsable && Non Civic
                            resp.StatusCode = resp.StatusCode = StatusCodeSmartSortLookup.SRR.ToString();
                        }

                    }
                    else
                    {
                        //Input does not have Address Elements
                        resp.StatusCode = StatusCodeSmartSortLookup.REM.ToString();

                    }
                }
                return resp;
            }
            catch (Exception ex)
            {
                Logger.Error("SmartSortLookupService error", LogCategories.LOOKUP_SERVICE, ex);

                Contracts.Common.ResponseError smartSortLookupError = new Contracts.Common.ResponseError();
                smartSortLookupError.ErrorMessage = ErrorResources.E3001 + ':' + ex.Message;
                smartSortLookupError.ErrorCode = 3001;
                resp.Errors.Add(smartSortLookupError);
                return resp;
            }
            finally
            {
                try
                {
                    // request
                    DataContractJsonSerializer js = new DataContractJsonSerializer(typeof(SmartSortGetRouteShelfRequest));  
                    MemoryStream msObj = new MemoryStream();  
                    js.WriteObject(msObj, smartSortGetRouteShelfRequest);  
                    msObj.Position = 0;  
                    StreamReader sr = new StreamReader(msObj);   
                    string request = sr.ReadToEnd();    
                    sr.Close();  
                    msObj.Close();


                    // response
                    js = new DataContractJsonSerializer(typeof(SmartSortGetRouteShelfResponse));
                    msObj = new MemoryStream();
                    js.WriteObject(msObj, resp);
                    msObj.Position = 0;
                    sr = new StreamReader(msObj);
                    string response = sr.ReadToEnd();
                    sr.Close();
                    msObj.Close();
 
                    // log to DB
                    string pin = smartSortGetRouteShelfRequest.PIN != null ? smartSortGetRouteShelfRequest.PIN : string.Empty;
                    string deviceId = smartSortGetRouteShelfRequest.DeviceID != null ? smartSortGetRouteShelfRequest.DeviceID : string.Empty;

                    Entities.LookupWSLog log = new Entities.LookupWSLog();
                    log.RequestId = deviceId;
                    log.PIN = pin;
                    log.Request = request;
                    log.Response = response;

                    stopwatch.Stop();
                    log.CallDuration = (int) stopwatch.ElapsedMilliseconds;

                    SmartSortLookupLogFactory logRepo = new SmartSortLookupLogFactory(Purolator.SmartSort.Common.Constants.DB_CONNECTION_NAME);
                    logRepo.InsertSmartSortScan(log);

                }
                catch (Exception ex)
                {
                    Logger.Error("SmartSortLookupService logging to DB error", LogCategories.LOOKUP_SERVICE, ex);
                }
            }

         
        }
       

    }



    public class WebHttpBehaviorEx : WebHttpBehavior
    {

        protected override void AddServerErrorHandlers(ServiceEndpoint endpoint, EndpointDispatcher endpointDispatcher)
        {

            // clear default erro handlers.

            endpointDispatcher.ChannelDispatcher.ErrorHandlers.Clear();

            // add our own error handler.

            endpointDispatcher.ChannelDispatcher.ErrorHandlers.Add(new ErrorHandlerEx());

        }

    }

    public class ErrorHandlerEx : IErrorHandler
    {

        public bool HandleError(Exception error)
        {

            return true;

        }
        
        public void ProvideFault(Exception error, System.ServiceModel.Channels.MessageVersion version, ref System.ServiceModel.Channels.Message fault)
        {
          
                    // extract the our FaultContract object from the exception object.
                    var detail = error.GetType().GetProperty("Message").GetGetMethod().Invoke(error, null);
                    
                    Contracts.Common.ResponseError smartSortLookupError = new Contracts.Common.ResponseError();
                    smartSortLookupError.ErrorMessage = ErrorResources.E3000 + ':' + error.Message;
                    smartSortLookupError.ErrorCode = 3000;
                 
                    Logger.Error("SmartSortLookupService error", LogCategories.LOOKUP_SERVICE, error);

                    // create a fault message containing our FaultContract object
                    fault = System.ServiceModel.Channels.Message.CreateMessage(version, "", smartSortLookupError, new DataContractJsonSerializer(smartSortLookupError.GetType()));

                    // tell WCF to use JSON encoding rather than default XML
                    var wbf = new WebBodyFormatMessageProperty(WebContentFormat.Json);
                    fault.Properties.Add(WebBodyFormatMessageProperty.Name, wbf);

                    var rmp = new HttpResponseMessageProperty();

                    rmp.Headers[HttpResponseHeader.ContentType] = "application/json";
                    rmp.Headers["jsonerror"] = "true";

                    rmp.StatusCode = System.Net.HttpStatusCode.BadRequest;

                    // put appropraite description here..
                    rmp.StatusDescription = "See fault object for more information.";

                    fault.Properties.Add(HttpResponseMessageProperty.Name, rmp);            
        }

    }

    public class MyFactory : WebServiceHostFactory
    {

        public override ServiceHostBase CreateServiceHost(string constructorString, Uri[] baseAddresses)
        {

            var sh = new ServiceHost(typeof(SmartSortLookupService), baseAddresses);

            sh.Description.Endpoints[0].Behaviors.Add(new WebHttpBehaviorEx());

            return sh;

        }

        protected override ServiceHost CreateServiceHost(Type serviceType, Uri[] baseAddresses)
        {

            return base.CreateServiceHost(serviceType, baseAddresses);

        }


    }

}
