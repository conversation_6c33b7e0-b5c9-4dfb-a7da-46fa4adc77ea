﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{CD36F7DE-A423-4A5E-BD45-113D91B28911}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Purolator.SmartSort.Service.Implementation.ShipmentEvent</RootNamespace>
    <AssemblyName>Purolator.SmartSort.Service.Implementation.ShipmentEvent</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>..\SmartSortRuleSet.ruleset</CodeAnalysisRuleSet>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Common, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.Common.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ShipmentEventService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DevTrends.WCFDataAnnotations\DevTrends.WCFDataAnnotations.csproj">
      <Project>{5bd4eaa0-4782-4e75-ad6c-5753ba2d0a91}</Project>
      <Name>DevTrends.WCFDataAnnotations</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Business.Common\Purolator.SmartSort.Business.Common.csproj">
      <Project>{7b3557d3-dc6a-484d-bbf3-e48b5832208e}</Project>
      <Name>Purolator.SmartSort.Business.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Business.Entities\Purolator.SmartSort.Business.Entities.csproj">
      <Project>{862103a3-8dab-46ee-8890-2f192a71ed66}</Project>
      <Name>Purolator.SmartSort.Business.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Common\Purolator.SmartSort.Common.csproj">
      <Project>{93213bc1-5510-457f-b09b-94acb33a232f}</Project>
      <Name>Purolator.SmartSort.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Data.Access\Purolator.SmartSort.Data.Access.csproj">
      <Project>{1fd9cdb9-e4b1-4aa5-ab52-2d3c6e03262d}</Project>
      <Name>Purolator.SmartSort.Data.Access</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Service.Common\Purolator.SmartSort.Service.Common.csproj">
      <Project>{57588481-3bfa-4a35-a1ab-09e202ab6da6}</Project>
      <Name>Purolator.SmartSort.Service.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Service.Contracts\Purolator.SmartSort.Service.Contracts.csproj">
      <Project>{1ac49b5c-7f91-4e55-9701-68ab8ad35170}</Project>
      <Name>Purolator.SmartSort.Service.Contracts</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Service.DataContracts\Purolator.SmartSort.Service.DataContracts.csproj">
      <Project>{1807d0b9-d240-4adc-a5b6-d8fa290f1171}</Project>
      <Name>Purolator.SmartSort.Service.DataContracts</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.Smartsort.Service.MessageContracts\Purolator.SmartSort.Service.MessageContracts.csproj">
      <Project>{c89aff74-2601-488a-af90-b6b890de9b8f}</Project>
      <Name>Purolator.SmartSort.Service.MessageContracts</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.Service.Translators\Purolator.SmartSort.Service.Translators.csproj">
      <Project>{0d2fcb2c-e293-4deb-9c88-f2dd6f850b2a}</Project>
      <Name>Purolator.SmartSort.Service.Translators</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>