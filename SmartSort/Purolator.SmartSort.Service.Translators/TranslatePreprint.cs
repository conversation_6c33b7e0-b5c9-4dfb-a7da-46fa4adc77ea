﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Entities = Purolator.SmartSort.Business.Entities;

namespace Purolator.SmartSort.Service.Translators
{
    public class TranslatePreprint
    {
        public static Entities.Address Translate(Entities.PrePrintDetails from)
        {
            Entities.Address to = new Entities.Address();
            to.StreetName = from.Line1;
            to.StreetAddress2 = from.Line2;
            to.City = from.City;
            to.PostalCode = from.PostalCode;
            to.ProvinceCode = from.Province;
            //to.C = from.Country;

            return to;
        }

        public static Entities.PrePrintMaster Translate(Entities.PrePrintDetails from, Entities.Address address, string statusCode)
        {
            Entities.PrePrintMaster to = new Entities.PrePrintMaster();
            to.PrePrintStgId = from.PrePrintStgId;
            to.DestinationCity = address.City;
            to.DestinationCompanyName = address.CustomerName;
            to.DestinationPostalCode = address.PostalCode;
            to.DestinationProvince = address.ProvinceCode;
            to.DestinationStreetDirection = address.StreetDirection;
            to.DestinationStreetName = address.StreetName;
            to.DestinationStreetNumber = address.StreetNumber;
            if (!string.IsNullOrWhiteSpace(address.StreetNumberSuffix))
            {
                to.DestinationStreetNumSuf = address.StreetNumberSuffix.Substring(0,1);
            }
            to.DestinationStreetType = address.StreetType;
            to.DestinationUnitNumber = address.Suite;
            to.StatusCd = statusCode;

            return to;
        }
    }
}
