﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Service.MessageContracts;
using Purolator.SmartSort.Service.DataContracts;
using System.Numerics;

namespace Purolator.SmartSort.Service.Translators
{
    public class TranslateShipmentEvent
    {
        public static Entities.ShipmentEventStaging Translate(ShipmentEventRequest from)
        {
            Entities.ShipmentEventStaging to = new Entities.ShipmentEventStaging();
            Entities.ShipmentEventStagingMessage m = new Entities.ShipmentEventStagingMessage();

            m.MessageGUID = from.TrackingInfo.MessageGUID;
            m.FWODocumentNo = from.TrackingInfo.FWONumber;
            m.EventExternalCode = from.TrackingInfo.EventID;            
            m.EventDateTimeTZ = DateTimeOffset.Parse(from.TrackingInfo.EventDate);
            m.SenderCodeSet = from.TrackingInfo.SenderCode;
            m.SenderID = from.TrackingInfo.SenderID;
            m.SenderName = from.TrackingInfo.SenderName;
            m.FreightUnit = from.TrackingInfo.FreightUnit;
            m.PiecePin = from.TrackingInfo.PiecePIN;
            m.ShipmentPin = from.TrackingInfo.ShipmentPIN;
            m.ServicePriority = from.ProductInfo.ServiceTime;
            m.ServiceLevel = from.ProductInfo.ServiceLevel;
            m.TransportMode = from.ProductInfo.TransportationMode;  // Fix by J.E.
            m.PackageType = from.ProductInfo.PackageType;            
            m.MaterialHandlingType = from.ProductInfo.MaterialHandlingType;            
            m.ReturnType = from.ProductInfo.ReturnType;            
            m.ProductNumber = from.ProductInfo.ProductNumber;
            m.ShipmentDate = from.TrackingInfo.ShipmentDate;
            m.Weight = from.ProductInfo.Weight;
            m.WeightUnit = from.ProductInfo.WeightUnit.ToString();
            m.OriginTerminal = from.LocationInfo.OriginTerminal;
            m.OriginSiteID = from.LocationInfo.OriginSiteID;
            m.DestinationTerminal = from.LocationInfo.DestinationTerminal;
            m.DestinationSiteID = from.LocationInfo.DestinationSiteID;
            m.ExpectedDeliveryDate = from.ProductInfo.ExpectedDeliveryDate;
            m.DeclareAddressLine1 = from.DeclaredReceiverParty.Address.StreetAddress1;
            m.DeclareAddressLine2 = from.DeclaredReceiverParty.Address.StreetAddress2;
            m.DeclareAddressLine3 = from.DeclaredReceiverParty.Address.StreetAddress3;
            m.DeclareCompanyName = from.DeclaredReceiverParty.OrganizationFormattedName;
            m.DeclareReceiverAttnName = from.DeclaredReceiverParty.PersonFormattedName;
            m.DeclareCity = from.DeclaredReceiverParty.Address.City;
            m.DeclareProvince = from.DeclaredReceiverParty.Address.Province;
            m.DeclarePostalCode = from.DeclaredReceiverParty.Address.PostalCode;
            m.DeclareCountry = from.DeclaredReceiverParty.Address.Country;
            m.CurrentSuiteNum = from.CurrentReceiverPart.Address.Suite;
            m.CurrentStreetNum = from.CurrentReceiverPart.Address.StreetNumber;
            m.CurrentStreetNumSuf = from.CurrentReceiverPart.Address.StreetSuffix;
            m.CurrentStreetName = from.CurrentReceiverPart.Address.StreetName;
            m.CurrentStreetType = from.CurrentReceiverPart.Address.StreetType;
            if (from.CurrentReceiverPart.Address.StreetDirection != null)
            {
                m.CurrentStreetDir = from.CurrentReceiverPart.Address.StreetDirection.ToString();
            }
            m.CurrentCity = from.CurrentReceiverPart.Address.City;
            m.CurrentProvince = from.CurrentReceiverPart.Address.Province;
            m.CurrentPostalCode = from.CurrentReceiverPart.Address.PostalCode;
            m.CurrentCountry = from.CurrentReceiverPart.Address.Country;            
            m.CurrentAddressLine2 = from.CurrentReceiverPart.Address.StreetAddress2;
            m.CurrentAddressLine3 = from.CurrentReceiverPart.Address.StreetAddress3;
            m.AddressType = from.CurrentReceiverPart.Address.AddressType;
            m.PurolatorAVInfo = from.CurrentReceiverPart.PurolatorAVSInfo;
            m.PurolatorAVStatus = from.CurrentReceiverPart.AddresssValidationStatus;
            
            to.Message = m;

            List<Entities.ShipmentService> services = new List<Entities.ShipmentService>();
            if (from.ServiceInfo != null)
            {
                foreach (var service in from.ServiceInfo)
                {
                    var s = new Entities.ShipmentService();
                    s.ServiceScopeInd = service.ServiceScopeIndicator.ToString().Substring(0, 1);
                    s.ServiceType = service.ServiceType;
                    s.ServiceValue = service.ServiceValue;
                    services.Add(s);
                }
            }

            to.ShipmentServices = services;

            return to;
        }
    }
}
