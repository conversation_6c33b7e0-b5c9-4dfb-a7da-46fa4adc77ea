﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Service.MessageContracts;
using Purolator.SmartSort.Service.DataContracts;
using System.Numerics;

namespace Purolator.SmartSort.Service.Translators
{
    public class TranslateSmartSortScan
    {
        private static int deviceIdIndex,userLogginIdIndex, routePlanVersionIDIndex, parkingPlanMasterVersionIdIndex, terminalIdIndexIndex, pinIndex, prePrintIdIndex, primarySortIndex, beltSideIndex, routeIndex, ssStatusReasonIndex, shelfIndex, deliverySequenceIdIndex, truckShelfOverrideIndex, smartSortModeIndex, scanDateTimeIndex, postalCodeIndex, provinceCodeIndex, municipalityNameIndex, streetNumberIndex, streetNumberSuffixIndex, streetNameIndex, streetTypeIndex, streetDirectionIndex, unitNumberIndex, customerNameIndex, printDateTimeIndex, deliveryTimeIndex, shipmentTypeIndex, deliveryTypeIndex, diversionCodeIndex, handlingClassTypeIndex, packageTypeIndex, barcodeTypeIndex, resolvedByIndex, alternateAddressFlagIndex;

        //TODO - Create SmartSortScanRequest accepting String Value
        private static string[] scanItems;

        public static Entities.SmartSortScan Translate(String deviceId, String userLogginId, String terminalId, String scan)
        {

            
            String routePlanVersionID, parkingPlanMasterVersionId, pin, prePrintId, primarySort, beltSide, route, ssStatusReason, shelf, deliverySequenceId, truckShelfOverride, smartSortMode, scanDateTime, postalCode, provinceCode, municipalityName, streetNumber, streetNumberSuffix, streetName, streetType, streetDirection, unitNumber, customerName, printDateTime, deliveryTime, shipmentType, deliveryType, diversionCode, handlingClassType, packageType, barcodeType, resolvedBy, alternateAddressFlag;
            setIndexes();

            scanItems = scan.Split('|');

            routePlanVersionID = getScanItem(routePlanVersionIDIndex);
            parkingPlanMasterVersionId = getScanItem(parkingPlanMasterVersionIdIndex);
            pin = getScanItem(pinIndex);
            prePrintId = getScanItem(prePrintIdIndex);
            primarySort = getScanItem(primarySortIndex);
            beltSide = getScanItem(beltSideIndex);
            route = getScanItem(routeIndex);
            ssStatusReason = getScanItem(ssStatusReasonIndex);
            shelf = getScanItem(shelfIndex);
            deliverySequenceId = getScanItem(deliverySequenceIdIndex);
            truckShelfOverride = getScanItem(truckShelfOverrideIndex);
            smartSortMode = getScanItem(smartSortModeIndex);
            scanDateTime = getScanItem(scanDateTimeIndex);
            postalCode = getScanItem(postalCodeIndex);
            provinceCode = getScanItem(provinceCodeIndex);
            municipalityName = getScanItem(municipalityNameIndex);
            streetNumber = getScanItem(streetNumberIndex);
            streetNumberSuffix = getScanItem(streetNumberSuffixIndex);
            streetName = getScanItem(streetNameIndex);
            streetType = getScanItem(streetTypeIndex);
            streetDirection = getScanItem(streetDirectionIndex);
            unitNumber = getScanItem(unitNumberIndex);
            customerName = getScanItem(customerNameIndex);
            printDateTime = getScanItem(printDateTimeIndex);
            deliveryTime = getScanItem(deliveryTimeIndex);
            shipmentType = getScanItem(shipmentTypeIndex);
            deliveryType = getScanItem(deliveryTypeIndex);
            diversionCode = getScanItem(diversionCodeIndex);
            handlingClassType = getScanItem(handlingClassTypeIndex);
            packageType = getScanItem(packageTypeIndex);
            barcodeType = getScanItem(barcodeTypeIndex);
            resolvedBy = getScanItem(resolvedByIndex);
            alternateAddressFlag = getScanItem(alternateAddressFlagIndex);
        
            Entities.SmartSortScan ssScan = new Entities.SmartSortScan();
            ssScan.SSLog = new Entities.SmartSortScanLog();
            ssScan.SSLog.Terminal = terminalId;
            ssScan.SSLog.PiecePin = pin;
            ssScan.SSLog.SmartSortDeviceID = deviceId;
            ssScan.SSLog.UserLoginID = userLogginId;
            ssScan.SSLog.RoutePlanVersionID = routePlanVersionID;
            ssScan.SSLog.ParkingPlanCodeID = parkingPlanMasterVersionId;
            ssScan.SSLog.PrimarySort = primarySort;
            ssScan.SSLog.BeltSide = beltSide;
            ssScan.SSLog.Route = route;
            ssScan.SSLog.SSStatusReason = ssStatusReason;
            ssScan.SSLog.Shelf = shelf;
            ssScan.SSLog.DeliverySeqID = deliverySequenceId;
            ssScan.SSLog.TruckShelfOverride = truckShelfOverride;
            ssScan.SSLog.SmartSortMode = smartSortMode;
            ssScan.SSLog.ScanDateTimeTZ = scanDateTime;
            ssScan.SSLog.PostalCode = postalCode;
            ssScan.SSLog.Province = provinceCode;
            ssScan.SSLog.City = municipalityName;
            ssScan.SSLog.StreetNumber = streetNumber;
            ssScan.SSLog.StreetNumSuf = streetNumberSuffix;
            ssScan.SSLog.StreetName = streetName;
            ssScan.SSLog.StreetType = streetType;
            ssScan.SSLog.StreetDirection = streetDirection;
            ssScan.SSLog.UnitNumber = unitNumber;
            ssScan.SSLog.CustomerName = customerName;
            ssScan.SSLog.BarcodeType = barcodeType;
            ssScan.SSLog.DeliveryTime = deliveryTime;
            ssScan.SSLog.DiversionCode = diversionCode;
            ssScan.SSLog.PackageType = packageType;
            ssScan.SSLog.PreprintID = prePrintId;
            ssScan.SSLog.PrintDateTimeTZ = printDateTime;
            ssScan.SSLog.ShipmentType = shipmentType;
            ssScan.SSLog.HandlingClassType = handlingClassType;
            ssScan.SSLog.DeliveryType = deliveryType;
            ssScan.SSLog.ResolvedBy = resolvedBy;
            ssScan.SSLog.AlternateAddressFlag = alternateAddressFlag;


            /*Entities.ShipmentEventStaging to = new Entities.ShipmentEventStaging();
            Entities.ShipmentEventStagingMessage m = new Entities.ShipmentEventStagingMessage();
            

            l.PiecePin = from.CurrentReceiverPart;



            m.MessageGUID = from.TrackingInfo.MessageGUID;
            m.FWODocumentNo = from.TrackingInfo.FWONumber;
            m.EventExternalCode = from.TrackingInfo.EventID;            
            m.EventDateTimeTZ = DateTimeOffset.Parse(from.TrackingInfo.EventDate);
            m.SenderCodeSet = from.TrackingInfo.SenderCode;
            m.SenderID = from.TrackingInfo.SenderID;
            m.SenderName = from.TrackingInfo.SenderName;
            m.FreightUnit = from.TrackingInfo.FreightUnit;
            m.PiecePin = from.TrackingInfo.PiecePIN;
            m.ShipmentPin = from.TrackingInfo.ShipmentPIN;
            m.ServiceLevel = from.ProductInfo.ServiceLevel.ToString();
            m.TransportMode = from.ProductInfo.TransportationMode.ToString();
            m.PackageType = from.ProductInfo.PackageType.ToString();
            m.MaterialHandlingType = from.ProductInfo.MaterialHandlingType.ToString();
            m.ReturnType = from.ProductInfo.ReturnType.ToString();
            m.ProductNumber = from.ProductInfo.ProductNumber;
            m.ShipmentDate = from.TrackingInfo.ShipmentDate;
            m.Weight = from.ProductInfo.Weight;
            m.WeightUnit = from.ProductInfo.WeightUnit.ToString();
            m.OriginTerminal = from.LocationInfo.OriginTerminal;
            m.OriginSiteID = from.LocationInfo.OriginSiteID;
            m.DestinationTerminal = from.LocationInfo.DestinationTerminal;
            m.DestinationSiteID = from.LocationInfo.DestinationSiteID;
            m.ExpectedDeliveryDate = from.ProductInfo.ExpectedDeliveryDate;
            m.DeclareAddressLine1 = from.DeclaredReceiverParty.Address.StreetAddress1;
            m.DeclareAddressLine2 = from.DeclaredReceiverParty.Address.StreetAddress2;
            m.DeclareAddressLine3 = from.DeclaredReceiverParty.Address.StreetAddress3;
            m.DeclareCompanyName = from.DeclaredReceiverParty.OrganizationFormattedName;
            m.DeclareReceiverAttnName = from.DeclaredReceiverParty.PersonFormattedName;
            m.DeclareCity = from.DeclaredReceiverParty.Address.City;
            m.DeclareProvince = from.DeclaredReceiverParty.Address.Province;
            m.DeclarePostalCode = from.DeclaredReceiverParty.Address.PostalCode;
            m.DeclareCountry = from.DeclaredReceiverParty.Address.Country;
            m.CurrentSuiteNum = from.CurrentReceiverPart.Address.Suite;
            m.CurrentStreetNum = from.CurrentReceiverPart.Address.StreetNumber;
            m.CurrentStreetNumSuf = from.CurrentReceiverPart.Address.StreetSuffix;
            m.CurrentStreetName = from.CurrentReceiverPart.Address.StreetName;
            m.CurrentStreetType = from.CurrentReceiverPart.Address.StreetType;
            m.CurrentStreetDir = from.CurrentReceiverPart.Address.StreetDirection.ToString();
            m.CurrentCity = from.CurrentReceiverPart.Address.City;
            m.CurrentProvince = from.CurrentReceiverPart.Address.Province;
            m.CurrentPostalCode = from.CurrentReceiverPart.Address.PostalCode;
            m.CurrentCountry = from.CurrentReceiverPart.Address.Country;
            //m.CurrentCompanyName = from.CurrentReceiverPart.
            m.CurrentAddressLine2 = from.CurrentReceiverPart.Address.StreetAddress2;
            m.CurrentAddressLine3 = from.CurrentReceiverPart.Address.StreetAddress3;
            m.AddressType = from.CurrentReceiverPart.Address.AddressType;
            m.PurolatorAVInfo = from.CurrentReceiverPart.PurolatorAVSInfo;
            m.PurolatorAVStatus = from.CurrentReceiverPart.AddresssValidationStatus.ToString();
            
            to.Message = m;
            */
            return ssScan;
        }

        private static String getScanItem(int Index)
        {
            if (!String.IsNullOrEmpty(scanItems[Index]))
            {
                return Uri.UnescapeDataString(scanItems[Index].ToString());
            }
            else
            {
                return null;
            }
        }

        private static void setIndexes()
        {
            deviceIdIndex = 0;
            userLogginIdIndex = 1;
            terminalIdIndexIndex = 2;
            routePlanVersionIDIndex = 0;
            parkingPlanMasterVersionIdIndex = 1;
            pinIndex = 2;
            prePrintIdIndex = 3;
            primarySortIndex = 4;
            beltSideIndex = 5;
            routeIndex = 6;
            ssStatusReasonIndex = 7;
            shelfIndex = 8;
            deliverySequenceIdIndex = 9;
            truckShelfOverrideIndex = 10;
            smartSortModeIndex = 11;
            scanDateTimeIndex = 12;
            postalCodeIndex = 13;
            provinceCodeIndex = 14;
            municipalityNameIndex = 15;
            streetNumberIndex = 16;
            streetNumberSuffixIndex = 17;
            streetNameIndex = 18;
            streetTypeIndex = 19;
            streetDirectionIndex = 20;
            unitNumberIndex = 21;
            customerNameIndex = 22;
            printDateTimeIndex = 23;
            deliveryTimeIndex = 24;
            shipmentTypeIndex = 25;
            deliveryTypeIndex = 26;
            diversionCodeIndex = 27;
            handlingClassTypeIndex = 28;
            packageTypeIndex = 29;
            barcodeTypeIndex = 30;
            resolvedByIndex = 31;
            alternateAddressFlagIndex = 32;
        }



    }
}
