﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Service.MessageContracts;
using Purolator.SmartSort.Service.DataContracts;
using System.Numerics;

namespace Purolator.SmartSort.Service.Translators
{
    public class TranslateSmartSortLog
    {
        public static Entities.SmartSortLookup Translate(MessageContracts.SmartSortGetRouteShelfRequest from)
        {
        
            Entities.SmartSortLookup to = new Entities.SmartSortLookup();
            to.AddressLine1 = from.AddressLine1;
            to.AddressLine2 = from.AddressLine2;
            to.City = from.MunicipalityName;
            to.CustomerName = from.CustomerName;
            to.DeliveryType = from.DeliveryType.ToString();
            to.PIN = from.PIN;
            to.PostalCode = from.PostalCode;
            to.Province = from.ProvinceCode;
            to.StreetNumber = from.StreetNumber;
            to.TerminalID = from.TerminalID.ToString();
            to.UnitSuiteNumber = from.UnitNumber;
           
            return to;
        }
    }
}
