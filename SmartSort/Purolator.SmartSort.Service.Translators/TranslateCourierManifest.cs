﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Entities = Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Service.MessageContracts;
using Purolator.SmartSort.Service.DataContracts;

namespace Purolator.SmartSort.Service.Translators
{
    public class TranslateCourierManifest
    {
        public static CourierManifestResponse Translate(Entities.CourierManifest from)
        {
            CourierManifestResponse to = new CourierManifestResponse();

            if (from != null)
            {
                to.VehicleStops = Translate(from.ManifestRows);
            }

            
            to.IsSuccess = true;

            return to;
        }

        private static List<VehicleStop> Translate(List<Entities.CourierManifestRow> rows)
        {
            List<VehicleStop> stops = new List<VehicleStop>();

            if (rows != null)
            {
                foreach (var row in rows)
                {
                    VehicleStop stop = new VehicleStop();
                    stop.StopID = row.Stopid;
                    stop.City = row.City;
                    stop.DeliverySequenceID = row.DeliverySeqId;
                    stop.PostalCode = row.PostalCode;
                    stop.StreetName = row.StreetName;
                    stop.StreetNumber = row.StreetNumber;
                    stop.StreetType = row.StreetType;
                    stop.StreetDirection = row.StreetDirection;
                    stop.StreetNumberSuffix = row.StreetNumberSuffix;
                    stop.Packages = Translate(row.Packages);
                    stops.Add(stop);
                }

            }
            return stops;
        }

        private static List<PackageInformation> Translate(List<Entities.CourierManifestPackage> packages)
        {
            List<PackageInformation> pkgs = new List<PackageInformation>();

            if (packages != null)
            {
                foreach (var pk in packages)
                {
                    PackageInformation p = new PackageInformation();
                    p.AlternateAddressFlag = IsTrue(pk.AlternateAddressFlag);
                    p.ChainofSignature = IsTrue(pk.ChainOfSignature);
                    p.CustomerName = pk.CustomerName;
                    if(string.IsNullOrEmpty(p.CustomerName))
                    {
                        p.CustomerName = "Not Available";
                    }
                    p.DangerousGoods = IsTrue(pk.DangerousGoods);                    
                    p.HeavyWeight = IsTrue(pk.HeavyWeight);
                    p.HoldForPickup = IsTrue(pk.HoldForPickup);
                    if(string.IsNullOrWhiteSpace(pk.PackageLocation))
                    {
                        p.PackageLocation = pk.ShelfNumber;
                    }
                    else
                    {
                        p.PackageLocation = pk.PackageLocation;
                    }



                    switch (pk.PackageType)
                    {
                        case "0":
                        case "1":
                        case "2":
                            p.PackageType = (PackageType)Enum.Parse(typeof(PackageType), pk.PackageType);
                            break;
                        default:
                            p.PackageType = PackageType.Unclassified;
                            break;
                            
                    }

                    p.PIN = pk.PiecePin;
                    
                    switch (pk.ServiceTime)
                    {
                        case "0900":
                            p.PremiumService = PremiumService.PREMIUM_9AM;
                            break;
                        case "1030":
                            p.PremiumService = PremiumService.PREMIUM_1030AM;
                            break;
                        case "EVN":
                            p.PremiumService = PremiumService.Evening;
                            break;
                        case "NOON":                            
                                 p.PremiumService = PremiumService.Noon;
                            break;
                        default:
                            p.PremiumService = PremiumService.None;
                            break;
                    }
               
                    p.ShelfNumber = pk.ShelfNumber;
                    p.UnitNumber = pk.UnitNumber;

                    pkgs.Add(p);
                }
            }

            return pkgs;
        }

        private static bool IsTrue(string val)
        {
            return !string.IsNullOrWhiteSpace(val) && (val.Trim().Equals("1") || val.Trim().Equals("Y"));
        }
        
    }
}
