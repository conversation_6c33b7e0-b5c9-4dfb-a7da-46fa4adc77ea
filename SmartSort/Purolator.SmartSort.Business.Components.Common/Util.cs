﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using Purolator.SmartSort.Business.Entities;
using Purolator.SmartSort.Business.Common.Exception;

namespace Purolator.SmartSort.Business.Components.Common
{
    public class Util
    {
        public static string GetDatabaseConnectionName()
        {
            return ConfigurationManager.AppSettings["DATABASE"];
        }
    }
}
