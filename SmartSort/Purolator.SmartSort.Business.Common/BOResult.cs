﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Purolator.SmartSort.Business.Common
{
    public class BOResult<TDomainObject> : IBOResult
    {
        /// <summary>
        /// Indicates if the call succeed.
        /// </summary>
        private bool _success;
        /// <summary>
        /// The generic type of the return object.
        /// </summary>
        private TDomainObject _returnObject;
        /// <summary>
        /// The list of the errors.
        /// </summary>
        private List<Exception.SmartSortError> _errorList;
        /// <summary>
        /// The included result set.
        /// </summary>
        private List<IBOResult> _includedResults;
        /// <summary>
        /// Indicate if log the error
        /// </summary>
        private bool _log;

        /// <summary>
        /// Initializes a new instance of the BOResult class.
        /// </summary>
        public BOResult()
        {
            _success = false;
            _errorList = new List<Exception.SmartSortError>();
            _log = true;

            _includedResults = new List<IBOResult>();
        }

        /// <summary>
        /// Add a new instance of the IBOResult class result object to the included result.
        /// </summary>
        /// <param name="includedResult">Another result object that will be associated in the included result.</param>
        public void Add(IBOResult includedResult)
        {
            _includedResults.Add(includedResult);
        }

        /// <summary>
        /// Indicates if the call succeeds. The default value is false.
        /// </summary>
        public bool IsSuccess
        {
            get
            {
                if (_success)
                {
                    foreach (IBOResult result in _includedResults)
                    {
                        if (!result.IsSuccess)
                        {
                            _success = false;
                            break;
                        }
                    }
                }
                return _success;
            }
            set { _success = value; }
        }

        /// <summary>
        /// The return object returned from the call. It is a generic type.
        /// </summary>
        public TDomainObject ReturnObject
        {
            get { return _returnObject; }
            set { _returnObject = value; }
        }

        /// <summary>
        /// Get the error list.
        /// </summary>
        /// <returns></returns>
        public List<Exception.SmartSortError> GetErrors()
        {
            List<Purolator.SmartSort.Business.Common.Exception.SmartSortError> retList = new List<Purolator.SmartSort.Business.Common.Exception.SmartSortError>();
            retList.AddRange(_errorList);

            foreach (IBOResult result in _includedResults)
            {
                retList.AddRange(result.GetErrors());
            }
            return retList;
        }

        /// <summary>
        /// Get the error list based on the type.
        /// </summary>
        /// <param name="typeName">The type in the error list</param>
        /// <returns>Returns the errors with the same type in the error list.</returns>
        public List<Exception.SmartSortError> GetErrors(Type type)
        {
            List<Exception.SmartSortError> retList = new List<Exception.SmartSortError>();
            foreach (Exception.SmartSortError ex in _errorList)
            {
                if (ex.GetType() == type)
                    retList.Add(ex);
            }
            foreach (IBOResult result in _includedResults)
            {
                retList.AddRange(result.GetErrors(type));
            }

            return retList;
        }

        /// <summary>
        /// Indicates if there is any error in the return.
        /// </summary>
        /// <returns>if there is error, return ture; otherwise, return false.</returns>
        public bool HasError()
        {
            return (GetErrors().Count > 0);
        }

        /// <summary>
        /// Indicates if there is any error with the type in the error list.
        /// </summary>
        /// <param name="typeName">The type in the error list.</param>
        /// <returns>The errors with the same type in the error list.</returns>
        public bool HasError(Type type)
        {
            return (GetErrors(type).Count > 0);
        }

        /// <summary>
        /// Add an error in the error list.
        /// </summary>
        /// <param name="error">The error object.</param>
        public void AddError(Exception.SmartSortError error)
        {
            if (_log)
            {
                //SmartSortLogger.Write(error.Message, Constants.LOGGING_CATEGORY_TRACE, LogPriority.Medium, System.Diagnostics.TraceEventType.Error);
            }
            if (_errorList == null) _errorList = new List<Purolator.SmartSort.Business.Common.Exception.SmartSortError>();
            _errorList.Add(error);
        }
        /// <summary>
        /// Add a list of errors to the error list.
        /// </summary>
        /// <param name="error">The error object.</param>
        public void AddErrors(List<Exception.SmartSortError> errList)
        {
            if (_log)
            {
                foreach (Exception.SmartSortError err in errList)
                {
                    //SmartSortLogger.Write(err.Message, Constants.LOGGING_CATEGORY_TRACE, LogPriority.Medium, System.Diagnostics.TraceEventType.Error);
                }
                if (_errorList == null) _errorList = new List<Purolator.SmartSort.Business.Common.Exception.SmartSortError>();
                _errorList.AddRange(errList);
            }
        }
        /// <summary>
        /// Add an array of errors to the error list.
        /// </summary>
        /// <param name="errors">Array of SmartSort errors to add</param>
        public void AddErrors(Exception.SmartSortError[] errors)
        {
            foreach (Exception.SmartSortError err in errors)
            {
                AddError(err);
            }
        }
        /// <summary>
        /// Get included results.
        /// </summary>
        public List<IBOResult> IncludedResults
        {
            get { return _includedResults; }
        }

        /// <summary>
        /// Indicate if log the error when adding an error. The default value is true.
        /// </summary>
        public bool LogError
        {
            get { return _log; }
            set { _log = value; }
        }


        public void AddErrors(IBOResult includedResult)
        {
            throw new NotImplementedException();
        }
    }
}
