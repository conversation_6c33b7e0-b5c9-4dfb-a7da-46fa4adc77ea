﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace Purolator.SmartSort.Business.Common
{
    public sealed class CallerContext : IDisposable
    {       
        private string _language;

        public string RequestXML { get; set; }
        public string ResponseXML { get; set; }
        public bool ShouldLog { get; set; }
        
        public CallerContext(string language)
        {            
            if (language == null || language.Length == 0)
                _language = Constants.LANGUAGE_ENGLISH;
            else
                _language = language;

            ShouldLog = false;

            ContextManager.Instance.Add(this);
        }
      
        /// <summary>
        /// Return the language
        /// </summary>
        public string Language
        {
            get { return _language; }
        }      

        public void Dispose()
        {
            ContextManager.Instance.Remove();
        }
    }
}
