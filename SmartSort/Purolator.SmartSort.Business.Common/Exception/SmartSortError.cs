﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Purolator.SmartSort.Business.Common.Exception
{
    [Serializable]
    public class SmartSortError
    {
        protected System.Collections.Generic.List<System.String> _subName;
        private System.Collections.Specialized.StringDictionary _parameters;
        protected string _message;
        private SmartSortError _innerError;

        /// <summary>
        /// Initializes a new instance of a business error class.
        /// </summary>
        public SmartSortError()
        {
            _message = "SmartSort Error";
            _innerError = null;
            _subName = new List<string>();
            _parameters = new System.Collections.Specialized.StringDictionary();
        }

        /// <summary>
        /// Initializes a new instance of a business error class with a specified error message. 
        /// </summary>
        /// <param name="message"></param>
        public SmartSortError(string message) : this()
        {
            _message = message;
        }
        /// <summary>
        /// Initializes a new instance of the SmartSortError class with a specified 
        /// error message and a reference to the inner error that is the cause 
        /// of this error. 
        /// </summary>
        /// <param name="message">The message that describes the error.</param>
        /// <param name="innerError">The error that is the cause of the current error.</param>
        public SmartSortError(string message, SmartSortError innerError)
            : this()
        {
            _message = message;
            _innerError = innerError;
        }
        /// <summary>
        /// The business error message.
        /// </summary>
        public string Message
        {
            get { return _message; }
        }
        /// <summary>
        /// The inner error object.
        /// </summary>
        public SmartSortError InnerError
        {
            get { return _innerError; }
            set { _innerError = value; }
        }
        /// <summary>
        /// The parameters associated with the error. The parameters carry additional information
        /// of the error. The usage of the parameters is for 
        /// displaying the description of the error.
        /// </summary>
        public System.Collections.Specialized.StringDictionary Parameters
        {
            get { return _parameters; }
            set { _parameters = value; }
        }
        /// <summary>
        /// The sub name of the business error. It can be used to distinct the same typed 
        /// business error with different description. 
        /// <see>FiledError</see>
        /// </summary>
        public string SubClassName
        {
            get
            {
                string name = "";
                for (int i = 0; i < _subName.Count; i++)
                {
                    name += "." + _subName[i];
                }
                return name;
            }
        }

        public virtual string MessageKey
        {
            get
            {
                string key = this.GetType().ToString().Replace("Purolator.SmartSort.Business.Common.Exception", "SmartSortError").Replace(".", "_");
                if (this.SubClassName != "")
                    key = key + this.SubClassName.Replace(".", "_");

                return key;
            }
        }
    }
}
