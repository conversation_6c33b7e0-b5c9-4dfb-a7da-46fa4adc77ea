﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace Purolator.SmartSort.Business.Common.Exception
{
    [Serializable]
    public class SmartSortException : System.Exception
    {
        private SmartSortError _error;

        /// <summary>
        /// Initializes a new instance of the SmartSortException class
        /// </summary>
        public SmartSortException() : base() { }
        /// <summary>
        /// Initializes a new instance of the SmartSortException class with a specified error message. 
        /// </summary>
        /// <param name="message">Exception message</param>
        public SmartSortException(string message) : base(message) { }
        /// <summary>
        /// Initializes a new instance of the SmartSortException class with a specified 
        /// error message and a reference to the inner exception that is the cause 
        /// of this exception. 
        /// </summary>
        /// <param name="message">The message that describes the error. </param>
        /// <param name="innerException">The exception that is the cause of the current exception.</param>
        public SmartSortException(string message, System.Exception innerException)
            : base(message, innerException) { }

        /// <summary>
        /// Initializes a new instance of the SmartSortException class with the 
        /// apecified SmartSortError
        /// </summary>
        /// <param name="error">SmartSortError instance</param>
        public SmartSortException(SmartSortError error)
            : base()
        {
            _error = error;
        }

        public virtual string MessageKey
        {
            get
            {
                return this.GetType().ToString().Replace("Purolator.SmartSort.Business.Common.Exception", "SmartSortException").Replace(".", "_");
            }
        }

        /// <summary>
        /// Gets or sets the optional associated SmartSortError instance
        /// </summary>
        public SmartSortError SmartSortError
        {
            get { return _error; }
            set { _error = value; }
        }

        public override void GetObjectData(SerializationInfo info, StreamingContext context)
        {
            base.GetObjectData(info, context);
        }
    }
}
