﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;

namespace Purolator.SmartSort.Business.Common
{
    public sealed class ContextManager
    {
        public static readonly ContextManager Instance = new ContextManager();
        private const string DATA_SLOT_NAME = "SmartSort-CallerContext";        

        private ContextManager()
        {
            Thread.AllocateNamedDataSlot(DATA_SLOT_NAME);
        }

        public CallerContext Get()
        {
            return Thread.GetData(Thread.GetNamedDataSlot(DATA_SLOT_NAME)) as CallerContext;
        }

        public void Add(CallerContext context)
        {
            Thread.SetData(Thread.GetNamedDataSlot(DATA_SLOT_NAME), context);
        }

        public void Remove()
        {
            Thread.SetData(Thread.GetNamedDataSlot(DATA_SLOT_NAME), null);            
        }
    }
}
