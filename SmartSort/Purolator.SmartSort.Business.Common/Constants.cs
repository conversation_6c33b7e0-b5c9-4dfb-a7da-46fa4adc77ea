﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Purolator.SmartSort.Business.Common
{
    public class Constants
    {
        public const string LANGUAGE_ENGLISH = "E";
        public const string LANGUAGE_FRENCH = "F";
        public const string EXCEPTION_POLICY_DATA_ACCESS = "Data Access Policy";
        public const string EXCEPTION_POLICY_BUSINESS = "Business Policy";
        public const String CACHE_DEPENDENCY_FILE = "CACHE_DEPENDENCY_FILE";
        public const string LOGGING_CATEGORY_BUSINESS = "Business";
        public const string LOGGING_CATEGORY_WEB_SERVICES = "Web Services";
        public const string EXCEPTION_POLICY_WEB_SERVICES = "Web Services Policy";
        public const string LOGGING_CATEGORY_TRACE = "Trace";
        public const string Address_Traiged_Eventcode = "4010";
        public const string Smart_Sort_Scan_Eventcode = "4020";
        public const string Address_Remediation_Required_Eventcode = "4030";
        public const string Package_Resolution_Required_Eventcode = "4040";
        public const string Package_Misdirected_Eventcode = "4050";
        public const string Send_DirectTo_SRR_Eventcode = "4055";
        public const string Load_ToAlternate_PandD_Truck_Eventcode = "4060";
        public const string UNload_From_PandD_Truck_Eventcode = "4070";
        public const string Reposition_On_PandD_Truck_Eventcode = "4080";
        public const string PandD_Truck_Closure_Eventcode = "4090";

        public const string DB_CONNECTION_NAME = "SSDB";

    }
}
