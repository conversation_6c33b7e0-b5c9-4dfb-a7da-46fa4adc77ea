﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace Purolator.SmartSort.Business.Common
{
    public interface IBOResult
    {
        bool IsSuccess { get; set; }
        List<Exception.SmartSortError> GetErrors();
        List<Exception.SmartSortError> GetErrors(Type type);
        bool HasError();
        bool HasError(Type type);
        void AddError(Exception.SmartSortError error);
        void AddErrors(List<Exception.SmartSortError> errList);
        bool LogError { get; set; }
        void AddErrors(IBOResult includedResult);
    }
}
