﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Purolator.SmartSort.Service.DataContracts;
using System.ServiceModel;
using Purolator.SmartSort.Service.Common;
using System.Runtime.Serialization;
using System.ComponentModel.DataAnnotations;

namespace Purolator.SmartSort.Service.MessageContracts
{    

    [DataContract(Name = "SmartSortGetRouteShelfRequest", Namespace = ServiceNamespace.DataType, IsReference = false)]  
    public class SmartSortGetRouteShelfRequest 
    {       
        [DataMember(Name = "TerminalID", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]        
        public int TerminalID  { get; set; }
     
        [DataMember(Name = "PIN", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 1)]        
        public String PIN{ get; set; }
       
        [DataMember(Name = "DeviceID", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]      
        public String DeviceID{ get; set; }

        [DataMember(Name = "CustomerName", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 3)]               
        public String CustomerName{get; set; }

        [DataMember(Name = "UnitNumber", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 4)]               
        public String UnitNumber{get; set; }
        
        [DataMember(Name = "StreetNumber", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 5)]
        public String StreetNumber { get; set; }
 
        [DataMember(Name = "AddressLine1", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 6)]
        public String AddressLine1 { get; set; }

        [DataMember(Name = "AddressLine2", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 7)]      
        public String AddressLine2 { get; set; }

        [DataMember(Name = "MunicipalityName", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 8)]
        public String MunicipalityName { get; set; }

        [DataMember(Name = "ProvinceCode", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 9)]
        public String ProvinceCode { get; set; }

        [DataMember(Name = "PostalCode", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 10)]
        public String PostalCode { get; set; }


        [DataMember(Name = "PremiumService", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 11)]
        [ValidEnumValue]
        public PremiumService PremiumService { get; set; }

        [DataMember(Name = "ShipmentType", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 12)]
        [ValidEnumValue]
        public ShipmentType ShipmentType { get; set; }

        [DataMember(Name = "DeliveryType", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 13)]
        [ValidEnumValue]
        public DeliveryType DeliveryType { get; set; }

        [DataMember(Name = "DiversionCode", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 14)]
        [ValidEnumValue]
        public DiversionCode DiversionCode { get; set; }

        [DataMember(Name = "HandlingClassType", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 15)]        
        public string HandlingClassType { get; set; }

    }

    public class ValidEnumValueAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            Type enumType = value.GetType();
            bool valid = Enum.IsDefined(enumType, value);
            if (!valid)
            {
                return new ValidationResult(String.Format("{0} is not a valid value for type {1}", value, enumType.Name));
            }
            return ValidationResult.Success;
        }
    }

}
