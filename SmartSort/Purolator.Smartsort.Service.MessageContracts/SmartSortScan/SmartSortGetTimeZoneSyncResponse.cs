﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Business.Common.Exception;
using Purolator.SmartSort.Service.Contracts.Common;
using Purolator.SmartSort.Service.Common;
using Purolator.SmartSort.Service.DataContracts;

namespace Purolator.SmartSort.Service.MessageContracts
{
    
    [Serializable]
    public class SmartSortGetTimeZoneSyncResponse
    {
        private string terminal;
        [MessageBodyMember(Name = "Terminal", Order = ServiceDataMember.MemberStartNumber + 0)]
        public String Terminal
        {
            get { return terminal; }
            set { terminal = value; }
        }

        private string timeZoneOffset;
        [MessageBodyMember(Name = "TimeZoneOffset", Order = ServiceDataMember.MemberStartNumber + 0)]
        public String TimeZoneOffset
        {
            get { return timeZoneOffset; }
            set { timeZoneOffset = value; }
        }


        private List<ResponseError> errorList;
        /// <summary>
        /// Get the ResponseError list.
        /// </summary>
        [MessageBodyMember(Name = "Errors", Order = ServiceDataMember.MemberStartNumber + 3)]
        public List<ResponseError> Errors
        {
            get { return errorList; }
            set { errorList = value; }
        }

        

    }
}




