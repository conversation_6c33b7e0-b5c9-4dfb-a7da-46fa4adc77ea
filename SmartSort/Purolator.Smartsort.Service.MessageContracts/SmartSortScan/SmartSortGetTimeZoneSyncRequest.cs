﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Purolator.SmartSort.Service.DataContracts;
using System.ServiceModel;
using Purolator.SmartSort.Service.Common;
using System.Runtime.Serialization;
using System.ComponentModel.DataAnnotations;

namespace Purolator.SmartSort.Service.MessageContracts
{

    [DataContract(Name = "SmartSortGetTimeZoneSyncRequest", Namespace = ServiceNamespace.DataType, IsReference = false)]
    public class SmartSortGetTimeZoneSyncRequest 
    {       
        [DataMember(Name = "Terminal", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]        
        public string terminal  { get; set; }          
    }


}
