﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using Purolator.SmartSort.Business.Common;
using Purolator.SmartSort.Business.Common.Exception;
using Purolator.SmartSort.Service.Contracts.Common;
using Purolator.SmartSort.Service.Common;
using Purolator.SmartSort.Service.DataContracts;

namespace Purolator.SmartSort.Service.MessageContracts
{
    
    [Serializable]
    public class SmartSortScanResponse
    {
        private String statusCode;
        private List<SmartSortResponseError> errorList;
        

        /// <summary>
        /// Initializes a new instance of ResponseResultBase. 
        /// </summary>
        public SmartSortScanResponse()
        {
           
        }

        /// <summary>
        /// Indicates if the call succeeded. The default value is false.
        /// </summary>
        [MessageBodyMember(Name = "StatusCode", Order = ServiceDataMember.MemberStartNumber + 0)]
        public String StatusCode
        {
            get { return statusCode; }
            set { statusCode = value; }
        }
       
        /// <summary>
        /// Get the ResponseError list.
        /// </summary>
       [MessageBodyMember(Name = "Errors", Order = ServiceDataMember.MemberStartNumber + 1)]
        public List<SmartSortResponseError> Errors
        {
            get { return errorList; }
            set { errorList = value; }
        }
            
      
     
        public void AddEventError(SmartSortResponseError error)
        {
            errorList.Add(error);
        }

    
        private string GetLanguage()
        {
            if (ContextManager.Instance.Get() == null)
                return Constants.LANGUAGE_ENGLISH;

            if (ContextManager.Instance.Get().Language == Constants.LANGUAGE_FRENCH)
                return Constants.LANGUAGE_FRENCH;
            else
                return Constants.LANGUAGE_ENGLISH;
        }

    }
}




