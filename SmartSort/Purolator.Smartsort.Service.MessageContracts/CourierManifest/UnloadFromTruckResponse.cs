﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Xml.Serialization;
using System.Runtime.Serialization;

namespace Purolator.SmartSort.Service.MessageContracts
{
    [DataContract(Name = "UnloadFromTruckResponse", Namespace = ServiceNamespace.DataType, IsReference = false)]
    public class UnloadFromTruckResponse : DeviceResponseBase
    {
    }
}
