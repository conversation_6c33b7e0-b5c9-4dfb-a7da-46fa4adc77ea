﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;
using Purolator.SmartSort.Service.DataContracts;
using Purolator.SmartSort.Service.Common;


namespace Purolator.SmartSort.Service.MessageContracts
{
    /// <summary>
    /// Message Contract Class - Request
    /// </summary>
   //[MessageContract(IsWrapped = false)]
   [DataContract]
    public class Request
    {       
        private RequestContext _RequestContext;

        /// <summary>
        /// RequestContext - RequestContext
        /// </summary>
        /// 
        //[MessageHeader(Name = "RequestContext", MustUnderstand = true)]
       [DataMember(Name = "RequestContext", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]    
        public RequestContext RequestContext
        {
            get { return this._RequestContext; }
            set { this._RequestContext = value; }
        }
    }
}
