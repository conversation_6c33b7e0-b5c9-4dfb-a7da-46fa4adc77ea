﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Xml;
using System.Xml.Linq;

namespace Purolator.SmartSort.Service.DataContracts
{
    /// <summary>
    /// RequestContext
    /// </summary>
    [DataContract(Name = "RequestContext", IsReference = false)]    
    public class RequestContext
    {
        #region RequestContext name constants
        /// <summary>
        /// RequestContextName
        /// </summary>
        [IgnoreDataMember]
        public const string RequestContextName = "RequestContext";

        /// <summary>
        /// VersionName
        /// </summary>
        [IgnoreDataMember]
        public const string VersionName = "Version";

        /// <summary>
        /// LanguageName
        /// </summary>
        [IgnoreDataMember]
        public const string LanguageName = "Language";
      
        /// <summary>
        /// RequestReferenceName
        /// </summary>
        [IgnoreDataMember]
        public const string RequestReferenceName = "RequestReference";

        #endregion RequestContext name constants


        private string _Version;

        /// <summary>
        /// Version - string
        /// </summary>
        [DataMember(Name = VersionName, IsRequired = true)]
        public string Version
        {
            get { return this._Version; }
            set { this._Version = value; }
        }

        private Language _Language;

        /// <summary>
        /// Language - string
        /// </summary>
        [DataMember(Name = LanguageName, IsRequired = true)]
        public Language Language
        {
            get { return this._Language; }
            set { this._Language = value; }
        }
       
        private string _RequestReference;

        /// <summary>
        /// RequestReference - string
        /// </summary>
        [DataMember(Name = RequestReferenceName, IsRequired = true)]
        public string RequestReference
        {
            get
            {
                if (string.IsNullOrEmpty(_RequestReference))
                {
                    _RequestReference = Guid.NewGuid().ToString();
                }

                return this._RequestReference;
            }
            set { this._RequestReference = value; }
        }

       

        /// <summary>
        /// Create the RequestContext object from XML
        /// </summary>
        /// <param name="reader">XML reader</param>
        /// <returns>RequestContext object</returns>
        public static RequestContext ReadContext(XmlDictionaryReader reader)
        {
            RequestContext requestContext = new RequestContext();

            XElement contextElement = XElement.Load(reader.ReadSubtree(), LoadOptions.None);

            // <Version>
            XElement foundElement = contextElement.Element(XName.Get(RequestContext.VersionName));
            if (foundElement != null && !foundElement.IsEmpty)
                requestContext.Version = foundElement.Value;

            // <Language>
            foundElement = contextElement.Element(XName.Get(RequestContext.LanguageName));
            if (foundElement != null && !foundElement.IsEmpty)
            {
                try
                {
                    requestContext.Language = (Language)Enum.Parse(typeof(Language), foundElement.Value, true);
                }
                catch
                {
                    requestContext.Language = Language.en;
                }
            }

           
            // <RequestReference>
            foundElement = contextElement.Element(XName.Get(RequestContext.RequestReferenceName));
            if (foundElement != null && !foundElement.IsEmpty)
                requestContext.RequestReference = foundElement.Value;
         

            return requestContext;
        }

        /// <summary>
        /// Create a name to be set on the thread, mainly for logging purpose
        /// </summary>
        /// <returns>Thread name</returns>
        public string GetThreadName()
        {
            string name = string.Empty;
          
            if (!string.IsNullOrEmpty(RequestReference))
            {
                name += RequestReference;
            }
           
            return name;
        }
    }
}
