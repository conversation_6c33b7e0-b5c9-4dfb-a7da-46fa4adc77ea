﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.ComponentModel.DataAnnotations;

namespace Purolator.SmartSort.Service.DataContracts
{
    [DataContract(Name = "Product", Namespace = ServiceNamespace.DataType, IsReference = false)]
    public class ProductInfo
    {
        [DataMember(Name = "ProductNbr", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 0)]
        [StringLength(18)]
        public string ProductNumber { get; set; }

        [DataMember(Name = "ServiceDeliveryTime", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]
        [StringLength(3)]
        public string ServiceTime { get; set; }

        [DataMember(Name = "ServiceLevelId", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 2)]
        [StringLength(3)]
        public string ServiceLevel { get; set; }

        [DataMember(Name = "PackageTypeId", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 3)]
        [StringLength(3)]
        public string PackageType { get; set; }
        
        [DataMember(Name = "MaterialHandlingType", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 4)]
        [StringLength(4)]
        public string MaterialHandlingType { get; set; }

        [DataMember(Name = "TransportPriority", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 5)]
        [StringLength(3)]
        public string TransportPriority { get; set; }

        [DataMember(Name = "TransportationMode", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 6)]
        [StringLength(2)]
        public string TransportationMode { get; set; }
        
        [DataMember(Name = "ExpectedDeliveryDate", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 7)]
        [StringLength(10)]
        [RegularExpression("^\\d{4,4}-\\d{2,2}-\\d{2,2}$")]
        public string ExpectedDeliveryDate { get; set; }
        
        [DataMember(Name = "ReturnType", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 8)]
        [StringLength(1)]
        public string ReturnType { get; set; }

        [DataMember(Name = "GrossWeight", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 9)]
        public Decimal Weight { get; set; }
        
        [DataMember(Name = "WeightUnit", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 10)]
        public WeightUnit WeightUnit { get; set; }
    }
}

