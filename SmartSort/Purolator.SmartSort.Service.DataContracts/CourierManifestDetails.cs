﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;


namespace Purolator.SmartSort.Service.DataContracts
{
   [DataContract(Name = "CourierManifestDetails", Namespace = ServiceNamespace.DataType, IsReference = false)]
   public class CourierManifestDetails
    {
        #region Private Members
        private int _TerminalID;
        private int[] _PINS;
        private string _Action;
        private int _RouteNumber;
        private int _ShelfNumber;
        #endregion

        #region Public Members

        [DataMember(Name = "TerminalID", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]
        public int TerminalID
        {
            get { return _TerminalID; }
            set { _TerminalID = value; }
        }

        [DataMember(Name = "PINS", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]
        public int[] PINS
        {
            get { return _PINS; }
            set { _PINS = value; }
        }

        [DataMember(Name = "Action", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 4)]
        public string Action
        {
            get { return _Action; }
            set { _Action = value; }
        }

        [DataMember(Name = "RouteNumber", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 1)]
        public int RouteNumber
        {
            get { return _RouteNumber; }
            set { _RouteNumber = value; }
        }

        [DataMember(Name = "ShelfNumber", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 3)]
        public int ShelfNumber
        {
            get { return _ShelfNumber; }
            set { _ShelfNumber = value; }
        }
        #endregion
    }
}
