﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Service.DataContracts
{
    [DataContract(Name = "VehicleStop", Namespace = ServiceNamespace.DataType, IsReference = false)]
    public class VehicleStop
    {                       
        //[XmlElement, DataMember(Name = "StopID", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 0)]
        [DataMember(Name = "StopID", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]
        public int StopID { get; set; }

        //[XmlElement, DataMember(Name = "StreetNumber", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]
        [DataMember(Name = "StreetNumber", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 1)]
        public string StreetNumber { get; set; }
        
        //[XmlElement, DataMember(Name = "StreetNumberSuffix", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 2)]
        [DataMember(Name = "StreetNumberSuffix", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]
        public string StreetNumberSuffix { get; set; }
        
        //[XmlElement, DataMember(Name = "StreetName", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 3)]
        [DataMember(Name = "StreetName", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 3)]
        public string StreetName { get; set; }
        
        //[XmlElement, DataMember(Name = "StreetType", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 4)]
        [DataMember(Name = "StreetType", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 4)]
        public string StreetType { get; set; }
        
        //[XmlElement, DataMember(Name = "StreetDirection", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 5)]
        [DataMember(Name = "StreetDirection", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 5)]
        public string StreetDirection { get; set; }
        
        //[XmlElement, DataMember(Name = "City", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 6)]
        [DataMember(Name = "City", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 6)]
        public string City { get; set; }
        
        //[XmlElement, DataMember(Name = "PostalCode", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 7)]
        [DataMember(Name = "PostalCode", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 7)]
        public string PostalCode { get; set; }
        
        //[XmlElement, DataMember(Name = "DeliverySequenceID", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 8)]
        [DataMember(Name = "DeliverySequenceID", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 8)]
        public int DeliverySequenceID { get; set; }
                
        //[XmlElement, DataMember(Name = "Packages", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 9)]
        [DataMember(Name = "Packages", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 9)]
        public List<PackageInformation> Packages { get; set; }        
    }
}
