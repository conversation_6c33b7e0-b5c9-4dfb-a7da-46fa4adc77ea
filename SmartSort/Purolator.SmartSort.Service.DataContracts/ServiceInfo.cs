﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.ComponentModel.DataAnnotations;

namespace Purolator.SmartSort.Service.DataContracts
{
    [DataContract(Name = "Service", Namespace = ServiceNamespace.DataType, IsReference = false)]
    public class ServiceInfo
    {
        [DataMember(Name = "ServiceScopeIndicator", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 0)]
        public ServiceScopeIndicator ServiceScopeIndicator { get; set; }

        [DataMember(Name = "ServiceType", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]
        [StringLength(30)]
        public string ServiceType { get; set; }

        [DataMember(Name = "ServiceValue", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 2)]
        [StringLength(30)]
        public string ServiceValue { get; set; }    
    }
}
