﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.ComponentModel.DataAnnotations;

namespace Purolator.SmartSort.Service.DataContracts
{
    [DataContract(Name = "LocationInfo", Namespace = ServiceNamespace.DataType, IsReference = false )]
    public class Locations
    {
        [DataMember(Name = "OriginTerminalId", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 0)]
        [StringLength(3)]
        public string OriginTerminal  { get; set; }

        [DataMember(Name = "OriginSiteId", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]
        [StringLength(4)]
        public string OriginSiteID  { get; set; }

        [DataMember(Name = "DestinationTerminalId", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 2)]
        [StringLength(3)]
        public string DestinationTerminal  { get; set; }

        [DataMember(Name = "DestinationSiteId", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 3)]
        [StringLength(4)]
        public string DestinationSiteID  { get; set; }        
    }
}
