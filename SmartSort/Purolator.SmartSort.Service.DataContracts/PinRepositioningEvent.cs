﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.ComponentModel.DataAnnotations;

namespace Purolator.SmartSort.Service.DataContracts
{
    [DataContract(Name = "PinRepositioningEvent", Namespace = ServiceNamespace.DataType, IsReference = false)]
    public class PinRepositioningEvent
    {
                
        [DataMember(Name = "Pin", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0 )]
        //[StringLength(15, MinimumLength = 1)]
        public string Pin  { get; set; }
        
        [DataMember(Name = "Action", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 1)]
        public RepositioningAction Action  { get; set; }
        
        [DataMember(Name = "Route", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]
        //[StringLength(4, MinimumLength = 1)]
        public string Route  { get; set; }
        
        [DataMember(Name = "Shelf", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 3)]
        //[StringLength(3, MinimumLength = 1)]
        public string Shelf  { get; set; }
        
        [DataMember(Name = "EventTimestamp", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 4)]
        //[StringLength(25, MinimumLength = 1)]
        public string EventTimestamp { get; set; }        
    }
}
