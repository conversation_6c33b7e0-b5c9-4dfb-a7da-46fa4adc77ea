﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.ComponentModel.DataAnnotations;

namespace Purolator.SmartSort.Service.DataContracts
{
    [DataContract(Name = "DeclaredReceiverParty" , Namespace = ServiceNamespace.DataType, IsReference = false )]
    public class DeclaredReceiverParty
    {        
        [DataMember(Name = "CompanyOrPersonName", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 0)]
        [StringLength(100)]
        public string OrganizationFormattedName  { get; set; }

        [DataMember(Name = "AttentionToName", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]
        [StringLength(100)]
        public string PersonFormattedName  { get; set; }

        [DataMember(Name = "Address", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]
        public Address Address { get; set; }
    }
}
