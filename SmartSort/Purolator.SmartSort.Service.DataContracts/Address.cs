﻿using System;
using System.Data;
using System.Configuration;
using System.Xml.Linq;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.Serialization;
using Purolator.SmartSort.Service.Common;
using System.ComponentModel.DataAnnotations;

namespace Purolator.SmartSort.Service.DataContracts
{
  
    [DataContract(Name = "Address", IsReference = false, Namespace = ServiceNamespace.DataType)]
    public class Address
    {                
        [DataMember(Name = "AddressLine1", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 0)]
        [StringLength(100)]
        public string StreetAddress1 { get; set; }

        [DataMember(Name = "AddressLine2", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]
        [StringLength(100)]
        public string StreetAddress2 { get; set; }

        [DataMember(Name = "AddressLine3", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 2)]
        [StringLength(100)]
        public string StreetAddress3 { get; set; }

        [DataMember(Name = "MunicipalityName", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 3)]
        [StringLength(60)]
        public string City { get; set; }

        [DataMember(Name = "RegionCode", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 4)]
        [StringLength(20)]
        public string Province { get; set; }

        [DataMember(Name = "PostCode", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 5)]
        [StringLength(10)]
        public string PostalCode { get; set; }

        [DataMember(Name = "CountryCode", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 6)]
        [StringLength(2)]
        public string Country { get; set; }

        [DataMember(Name = "Suite", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 7)]
        [StringLength(10)]
        public string Suite { get; set; }

        [DataMember(Name = "StreetName", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 8)]
        [StringLength(60)]
        public string StreetName { get; set; }

        [DataMember(Name = "StreetNumber", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 9)]
        [StringLength(10)]
        public string StreetNumber { get; set; }
        
        [DataMember(Name = "StreetSuffix", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 10)]
        [StringLength(10)]
        public string StreetSuffix { get; set; }

        [DataMember(Name = "StreetTypeCode", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 11)]
        [StringLength(10)]
        public string StreetType { get; set; }

        [DataMember(Name = "StreetDirectionCode", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 12)]
        public StreetDirectionCode? StreetDirection { get; set; }

        [DataMember(Name = "FloorNbr", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 13)]
        [StringLength(10)]
        public string Floor { get; set; }

        [DataMember(Name = "AddressType", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 14)]
        [StringLength(10)]
        public string AddressType { get; set; }        
    }
}
