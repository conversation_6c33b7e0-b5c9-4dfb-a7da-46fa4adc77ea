﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.ComponentModel.DataAnnotations;

namespace Purolator.SmartSort.Service.DataContracts
{
    [DataContract(Name = "CurrentReceiverParty",Namespace = ServiceNamespace.DataType,IsReference = false)]
    public class CurrentReceiverParty
    {
        [DataMember(Name = "AddresssValidationStatus", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 0)]
        [StringLength(50)]
        public string AddresssValidationStatus { get; set; }

        [DataMember(Name = "AddresssValidationStatusInfo", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]
        [StringLength(255)]
        public string PurolatorAVSInfo { get; set; }

        [DataMember(Name = "Address", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]
        public Address Address { get; set; }
    }
}
