﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.ComponentModel.DataAnnotations;
using System.Numerics;


namespace Purolator.SmartSort.Service.DataContracts
{
    [DataContract(Name = "Tracking", Namespace = ServiceNamespace.DataType, IsReference = false)]    
    public class TrackingInfo
    {        
        [DataMember(Name = "MessageId", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]        
        [StringLength(22, MinimumLength = 1)]
        public string MessageGUID  { get; set; }

        [DataMember(Name = "EventTypeCode", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 1)]        
        [StringLength(20, MinimumLength = 4)]
        public string EventID  { get; set; }

        [DataMember(Name = "SourceSystemId", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 2)]
        [StringLength(5)]
        public string SenderCode  { get; set; }

        [DataMember(Name = "SubProcessId", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 3)]
        [StringLength(4)]
        public string SenderID  { get; set; }

        [DataMember(Name = "SubSubProcessId", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 4)]
        [StringLength(20)]
        public string SenderName  { get; set; }

        [DataMember(Name = "EventTimeStamp", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 5)]
        [StringLength(25)]
        [RegularExpression("^\\d{4,4}-\\d{2,2}-\\d{2,2}T\\d{2,2}:\\d{2,2}:\\d{2,2}[+-]\\d{2,2}:\\d{2,2}$")]
        public String EventDate  { get; set; }

        [DataMember(Name = "ShippingDate", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 6)]
        [StringLength(10)]
        [RegularExpression("^\\d{4,4}-\\d{2,2}-\\d{2,2}$")]
        public string ShipmentDate  { get; set; }

        [DataMember(Name = "PiecePin", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 7)]
        [StringLength(35)]
        public string PiecePIN { get; set; }

        [DataMember(Name = "ShipmentPin", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 8)]
        [StringLength(35)]
        public string ShipmentPIN  { get; set; }        

        [DataMember(Name = "FreightUnitNbr", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 9)]
        public long FreightUnit  { get; set; }

        [DataMember(Name = "ForwardingOrderNbr", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 10)]
        public long FWONumber { get; set; }
    }
}
