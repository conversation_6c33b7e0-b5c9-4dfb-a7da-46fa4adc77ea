﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace Purolator.SmartSort.Service.DataContracts
{
    [DataContract(Name = "Event", Namespace = ServiceNamespace.DataType, IsReference = false)]
    public class ShipmentInformation
    {        
        [DataMember(Name = "TrackingInfo", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 0)]
        public TrackingInfo TrackingInfo  { get; set; }

        [DataMember(Name = "ProductInfo", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]
        public ProductInfo ProductInfo  { get; set; }

        [DataMember(Name = "Services" , IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 2)]
        public List<ServiceInfo> ServiceInfo  { get; set; }
       
        [DataMember(Name = "LocationInfo",IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 3)]
        public Locations LocationInfo  { get; set; }
               
        [DataMember(Name = "DeclaredReceiverParty", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 4)]
        public DeclaredReceiverParty DeclaredReceiverParty  { get; set; }

        [DataMember(Name = "CurrentReceiverParty" , IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 5)]
        public CurrentReceiverParty CurrentReceiverPart  { get; set; }
    }
}
