﻿using Purolator.SmartSort.Service.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Xml.Serialization;

namespace Purolator.SmartSort.Service.DataContracts
{
    [DataContract(Name = "Package", Namespace = ServiceNamespace.DataType, IsReference = false)]
    public class PackageInformation
    {               
        //[XmlElement, DataMember(Name = "PIN", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 0)]
        [DataMember(Name = "PIN", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 0)]
        public string PIN { get; set; }
        
        //[XmlElement, DataMember(Name = "CustomerName", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]
        [DataMember(Name = "CustomerName", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 1)]
        public string CustomerName { get; set; }

        //[XmlElement, DataMember(Name = "UnitNumber", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 1)]
        [DataMember(Name = "UnitNumber", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]
        public string UnitNumber { get; set; }        
        
        //[XmlElement, DataMember(Name = "PremiumService", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 2)]
        [DataMember(Name = "PremiumService", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 2)]
        public PremiumService PremiumService { get; set; }
        
        //[XmlElement, DataMember(Name = "DangerousGoods", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 3)]
        [DataMember(Name = "DangerousGoods", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 3)]
        public bool DangerousGoods { get; set; }
        
        //[XmlElement, DataMember(Name = "ChainofSignature", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 4)]
        [DataMember(Name = "ChainofSignature", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 4)]
        public bool ChainofSignature { get; set; }
        
        //[XmlElement, DataMember(Name = "HoldForPickup", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 6)]
        [DataMember(Name = "HoldForPickup", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 6)]
        public bool HoldForPickup { get; set; }
        
        //[XmlElement, DataMember(Name = "HeavyWeight", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 7)]
        [DataMember(Name = "HeavyWeight", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 7)]
        public bool HeavyWeight { get; set; }
        
        //[XmlElement, DataMember(Name = "PackageType", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 8)]
        [DataMember(Name = "PackageType", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 8)]
        public PackageType PackageType { get; set; }
        
        //[XmlElement, DataMember(Name = "AlternateAddressFlag", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 9)]
        [DataMember(Name = "AlternateAddressFlag", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 9)]
        public bool AlternateAddressFlag { get; set; }
        
        //[XmlElement, DataMember(Name = "ShelfNumber", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 10)]
        [DataMember(Name = "ShelfNumber", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 10)]
        public string ShelfNumber { get; set; }
                
        //[XmlElement, DataMember(Name = "PackageLocation", IsRequired = false, Order = ServiceDataMember.MemberStartNumber + 11)]
        [DataMember(Name = "PackageLocation", IsRequired = true, Order = ServiceDataMember.MemberStartNumber + 11)]
        public string PackageLocation { get; set; }
    }
}
