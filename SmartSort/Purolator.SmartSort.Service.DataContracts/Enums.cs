﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;

namespace Purolator.SmartSort.Service.DataContracts
{


    /// <summary>
    /// Language - enum
    /// </summary>
    [DataContract(Name = "Language", IsReference = false)]
    public enum Language
    {
        /// <summary>
        /// en
        /// </summary>
        [EnumMember(Value = "en")]
        en,

        /// <summary>
        /// fr
        /// </summary>
        [EnumMember(Value = "fr")]
        fr
    }

    [DataContract(Name = "ServiceTime", IsReference = false)]    
    public enum ServiceTime
    {
        [EnumMember(Value = "Standard")]
        None = 3,
        [EnumMember(Value = "9AM")]
        Morning900AM = 1,
        [EnumMember(Value = "1030AM")]
        Morning1030AM = 2,
        [EnumMember(Value = "12PM")]
        Noon12 = 4,
        [EnumMember(Value = "Evening")]
        Evening = 5

    }



    [DataContract(Name = "ServiceLevel", IsReference = false)]
    public enum ServiceLevel
    {
        [EnumMember(Value = "Outbound")]
        Outbound = 1,
        [EnumMember(Value = "SameDay")]
        SameDay = 2,
        [EnumMember(Value = "Express")]
        Express = 3,
        [EnumMember(Value = "Extended")]
        Extended = 4,
        [EnumMember(Value = "MultiWeight")]
        MultiWeight = 5
    }


    [DataContract(Name = "PackageType", IsReference = false)]
    public enum SAPPackageType
    {
        [EnumMember(Value = "ExpressEnvelope")]
        ExpressEnvelope = 1,
        [EnumMember(Value = "ExpressPack")]
        ExpressPack = 2,
        [EnumMember(Value = "CustomerPackaging")]
        CustomerPackaging = 3,
        [EnumMember(Value = "ExpressBox")]
        ExpressBox = 4
    }

    [DataContract(Name = "PackageType", IsReference = false)]
    public enum PackageType
    {
        [EnumMember(Value = "Unclassified")]
        Unclassified = 0,
        [EnumMember(Value = "Mail")]
        Mail = 1,
        [EnumMember(Value = "Box")]
        Box = 2
    }

    [DataContract(Name = "DeliveryType", IsReference = false)]
    public enum DeliveryType
    {
        [EnumMember(Value = "Regular")]
        Regular = 0,
        [EnumMember(Value = "OSNR")]
        OSNR = 1,
        [EnumMember(Value = "Hold For Pickup")]
        HFPU = 2
    }

    [DataContract(Name = "ShipmentType", IsReference = false)]
    public enum ShipmentType
    {
        [EnumMember(Value = "Regular")]
        Regular = 0,
        [EnumMember(Value = "ExpressCheque")]
        ExpressCheque = 1,
        [EnumMember(Value = "Returns")]
        Returns = 2
    }

    
    [DataContract(Name = "PremiumService", IsReference = false)]
    public enum PremiumService
    {
        [EnumMember(Value = "None")]
        None = 0,
        [EnumMember(Value = "AM900")]
        PREMIUM_9AM = 25,
        [EnumMember(Value = "AM1030")]
        PREMIUM_1030AM = 28,
        [EnumMember(Value = "Evening")]
        Evening = 29,
        [EnumMember(Value = "Noon")]
        Noon = 12
    }

    [DataContract(Name = "MaterialHandlingType", IsReference = false)]
    public enum MaterialHandlingType
    {
        [EnumMember(Value = "ChainOfSignature")]
        ChainOfSignature = 0,
        [EnumMember(Value = "Conveyable")]
        Conveyable = 1,
        [EnumMember(Value = "DangerousGoods")]
        DangerousGoods = 2,
        [EnumMember(Value = "Mail")]
        Mail = 3,
        [EnumMember(Value = "NonConveyable")]
        NonConveyable = 4
    }


    [DataContract(Name = "TransportationMode", IsReference = false)]
    public enum TransportationMode
    {
        [EnumMember(Value = "Ground")]
        Ground = 0,
        [EnumMember(Value = "Air")]
        Air = 1        
    }

    [DataContract(Name = "ReturnType", IsReference = false)]
    public enum ReturnType
    {
        [EnumMember(Value = "ReturnManagement")]
        ReturnManagement = 0,
        [EnumMember(Value = "OutboundReturnShipment")]
        OutboundReturnShipment = 1,
        [EnumMember(Value = "DangerousGoods")]
        DangerousGoods = 2,
        [EnumMember(Value = "ReturnTypeFromScan")]
        ReturnTypeFromScan = 3        
    }

     [DataContract(Name = "WeightUnit", IsReference = false)]
    public enum WeightUnit
    {
        [EnumMember(Value = "LB")]
        LB = 0,
        [EnumMember(Value = "KG")]
        KG = 1       
    }

     [DataContract(Name = "ServiceScopeIndicator", IsReference = false)]
     public enum ServiceScopeIndicator
     {
         [EnumMember(Value = "ShipmentLevel")]
         ShipmentLevel = 0,
         [EnumMember(Value = "PieceLevel")]
         PieceLevel = 1
     }

     [DataContract(Name = "ServiceType", IsReference = false)]
     public enum ServiceType
     {
         [EnumMember(Value = "DangerousGoods")]
         DangerousGoods = 0,
         [EnumMember(Value = "DeliveryType")]
         DeliveryType = 1,
         [EnumMember(Value = "TimeDefinitive")]
         TimeDefinitive = 2,
         [EnumMember(Value = "SpecialHandling ")]
         SpecialHandling = 3
     }

     [DataContract(Name = "ServiceSubType", IsReference = false)]
     public enum ServiceSubType
     {
         [EnumMember(Value = "Generic")]
         Generic = 0,
         [EnumMember(Value = "FullyRegulated")]
         FullyRegulated = 1,
         [EnumMember(Value = "BiologicalSubstance")]
         BiologicalSubstance = 2,
         [EnumMember(Value = "DryIce")]
         DryIce = 3,
         [EnumMember(Value = "Greater500KGExemption")]
         Greater500KGExemption = 4,
         [EnumMember(Value = "LimitedQuantitiesOrConsumerCommodity")]
         LimitedQuantitiesOrConsumerCommodity = 5,
         [EnumMember(Value = "HoldForPickup")]
         HoldForPickup = 6,
         [EnumMember(Value = "OriginSignatureNotRequired")]
         OriginSignatureNotRequired = 7,
         [EnumMember(Value = "ResidentialSignatureRequired")]
         ResidentialSignatureRequired = 8,
         [EnumMember(Value = "ChainOfSignature")]
         ChainOfSignature = 9,
         [EnumMember(Value = "SaturdayDelivery")]
         SaturdayDelivery = 10,
         [EnumMember(Value = "SaturdayPickup")]
         SaturdayPickup = 11,
         [EnumMember(Value = "Default")]
         Default = 12,
         [EnumMember(Value = "Oversize")]
         Oversize = 13,
         [EnumMember(Value = "Liquid")]
         Liquid = 14,
         [EnumMember(Value = "NonPackaged")]
         NonPackaged = 15,
         [EnumMember(Value = "ManagementDiscretion")]
         ManagementDiscretion = 16
     }


    [DataContract(Name = "AddresssValidationStatus", IsReference = false)]
    public enum AddresssValidationStatus
    {
        [EnumMember(Value = "Valid")]
        Valid = 0,
        [EnumMember(Value = "Invalid")]
        Invalid = 1,
        [EnumMember(Value = "ValidRangeOnly")]
        ValidRangeOnly = 2,
        [EnumMember(Value = "Unparsable")]
        Unparsable = 3,
        [EnumMember(Value = "IncompleteInput")]
        IncompleteInput = 4,
        [EnumMember(Value = "NotCanadian")]
        NotCanadian = 5,
        [EnumMember(Value = "AWSUnavailable")]
        AWSUnavailable = 6     
    }


    [DataContract(Name = "DiversionCode", IsReference = false)]
    public enum DiversionCode
    {
        [EnumMember(Value = "None_Default")]
        None_Default = 0,
        [EnumMember(Value = "DangerousGoods")]
        DangerousGoods = 1,
        [EnumMember(Value = "ChainOfSignature_COS")]
        ChainOfSignature_COS = 2,
        [EnumMember(Value = "HeavyWeight")]
        HeavyWeight = 3,
        [EnumMember(Value = "FutureUse")]
        FutureUse = 4,
        [EnumMember(Value = "SpecialHandling_SHS")]
        SpecialHandling_SHS = 5,
        [EnumMember(Value = "Saturday")]
        Saturday = 6
    }

    [DataContract(Name = "StreetDirectionCode", IsReference = false)]
    public enum StreetDirectionCode
    {
        [EnumMember(Value = "N")]
        N,
        [EnumMember(Value = "NE")]
        NE,
        [EnumMember(Value = "E")]
        E,
        [EnumMember(Value = "SE")]
        SE,
        [EnumMember(Value = "S")]
        S,
        [EnumMember(Value = "SW")]
        SW,
        [EnumMember(Value = "W")]        
        W,
        [EnumMember(Value = "NW")]
        NW,
        [EnumMember(Value = "O")]
        O,
        [EnumMember(Value = "NO")]
        NO,
        [EnumMember(Value = "SO")]
        SO
    }
     
    

    [DataContract(Name = "HandlingClassType", IsReference = false)]
    public enum HandlingClassType
    {
        [EnumMember(Value = "NoHandlingClass")]
        NoHandlingClass = 00,
        [EnumMember(Value = "FullyRegulated")]
        FullyRegulated = 01,
        [EnumMember(Value = "UN3373DG")]
        UN3373DG = 02,
        [EnumMember(Value = "UN1845DG")]
        UN1845DG = 03,
        [EnumMember(Value = "KG500Exempt")]
        KG500Exempt = 04,
        [EnumMember(Value = "Default")]
        Default = 10,
        [EnumMember(Value = "Oversized")]
        Oversized = 11,
        [EnumMember(Value = "Liquid")]
        Liquid = 12,
        [EnumMember(Value = "NonPackages")]
        NonPackages = 13,
        [EnumMember(Value = "ManagementDiscretion")]
        ManagementDiscretion = 14
    }

    [DataContract(Name = "StatusCode", IsReference = false)]
    public enum StatusCode
    {
        [EnumMember(Value = "Error")]
        Error = 0,
        [EnumMember(Value = "Success")]
        Success = 1,
        [EnumMember(Value = "Partial")]
        Partial = 2
    }
    
    [DataContract(Name = "StatusCodeSmartSortLookup", IsReference = false)]
    public enum StatusCodeSmartSortLookup        
    {
        [EnumMember(Value = "Error")]
        Error ,
        [EnumMember(Value = "Resolved")]
        Resolved,
        [EnumMember(Value = "ResolutionRequired")]
        SRR,
        [EnumMember(Value = "Misrect")]
        MDR,
        [EnumMember(Value = "RemediationRequired")]
        REM
    }

    [DataContract(Name = "RepositioningAction", IsReference = false)]
    public enum RepositioningAction
    {
        [EnumMember(Value = "Reposition")]
        Reposition = 0,
        [EnumMember(Value = "AlternateTruck")]
        AlternateTruck = 1        
    }
   
}
