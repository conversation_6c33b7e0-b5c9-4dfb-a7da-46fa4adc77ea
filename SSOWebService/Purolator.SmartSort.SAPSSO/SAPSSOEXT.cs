﻿using System;
using System.Runtime.InteropServices;
using System.Text;
using System.IO;
using System.ComponentModel;
using System.Collections.Generic;
using System.Security.Permissions;
using System.Security;
using System.Threading;
using System.Configuration;
using System.Diagnostics;
using System.Configuration;
using Microsoft.Practices.EnterpriseLibrary.Logging;
using Microsoft.Practices.EnterpriseLibrary.Logging.Formatters;
using Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners;
using Microsoft.Practices.EnterpriseLibrary.Common.Configuration;


namespace Purolator.SmartSort.SAPSSO
{
    public enum TICKET_TYPE
    {
        LOGON_TICKET = 0,
        ASSERTION_TICKET = 1
    };

    public struct TICKET_INFO
    {
        public string sapUser;
        public string sapSysID;
        public string sapClient;
        public byte[] sapCert;
        public TICKET_TYPE ticketType;
    }

    public struct TICKET_PORTAL_INFO
    {
        public string sapUser;
        public string sapPortalUser;
        public string sapSysID;
        public string sapClient;
        public byte[] sapCert;
    }

    public struct TICKET_PORTAL_INFO_LONG
    {
        public string sapUser;
        public string sapPortalUser;
        public string sapSysID;
        public string sapClient;
        public string sapAuthSchema;
        public Int64 validity;
        public byte[] sapCert;
        public TICKET_TYPE ticketType;
    }

    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
    internal struct TICKET_INFO_LONG
    {
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr struct_size;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)]
        public byte[] sSAPCodepage;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pSysID;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pIntSysIDL;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pSAPUser;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pIntSAPUserL;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pSAPClient;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pIntSAPClientL;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pSAPLangu;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pIntSAPLanguL;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr ptmCreTime;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr ptmValidity;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pPrtUser;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pIntPrtUserL;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pAuthScheme;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pIntAuthSchemeL;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pAsn1Certificate;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr pIntAsn1CertificateL;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 1)]
        public byte[] flags;
        [MarshalAs(UnmanagedType.SysInt)]
        public IntPtr tTicketType;
    }

    [SuppressUnmanagedCodeSecurity()]
    internal class NativeMethods
    {

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool SetDllDirectory(string lpPathName);

        /********************* process global methods ******************************/
        /************** INVOKE THESE METHODS ONLY ONCE PER PROCESS *****************/
        /* initialize SAPSSOEXT and SSF library */
        [DllImport("sapssoext", CharSet = CharSet.Ansi, SetLastError = true)]
        internal static extern Int32 MySapInitialize(String pszSsfLib);

        /* shutdown SAPSSEXT and SSF library */
        [DllImport("sapssoext", CharSet = CharSet.Ansi, SetLastError = true)]
        internal static extern Int32 MySapShutdown();

        /* get version of SAPSSOEXT */
        [DllImport("sapssoext", CharSet = CharSet.Ansi, SetLastError = true)]
        internal static extern Int32 MySapGetVersion();

        /* load a key file into the memory, you can omit then pszAddrBook and pszPassword */
        [DllImport("sapssoext", CharSet = CharSet.Ansi, SetLastError = true)]
        internal static extern Int32 MySapLoadTicketKey(byte[] pKey,
                                                           Int32 pKeyLen,
                                                           String pszPassword,
                                                           Int32 iIndex,
                                                           Int32 tType);
        /************** INVOKE THESE METHODS ONLY ONCE PER PROCESS *****************/
        /********************* process global methods ******************************/

        /* set property for SAPSSOEXT runtime */
        [DllImport("sapssoext", CharSet = CharSet.Ansi, SetLastError = true)]
        internal static extern Int32 SsoExtSetProperty(String pParameter, String pValue);

        /* get property from SAPSSOEXT runtime, if Null, then retrieve a list of all properties */
        [DllImport("sapssoext", CharSet = CharSet.Ansi, SetLastError = true)]
        internal static extern IntPtr SsoExtGetProperty(String pParameter);

        /********************* thread local methods *********************************/
        /* create a SAP assertion ticket */
        [DllImport("sapssoext", CharSet = CharSet.Ansi, SetLastError = true)]
        internal static extern Int32 MySapCreateAssertionTicket(String pszMySysId,
                                                             String pszMySysClient,
                                                             String pszAdrBook,
                                                             String pszPassword,
                                               ref TICKET_INFO_LONG ticket_info,
                          [MarshalAs(UnmanagedType.LPArray)] byte[] pTicketBuffer,
                       [MarshalAs(UnmanagedType.LPArray)]   Int32[] pTicketLen);

        /* validate a ticket */
        [DllImport("sapssoext", CharSet = CharSet.Ansi, SetLastError = true)]
        internal static extern Int32 MySapEvalLogonTicket(String pszTicket,
                                                             String pszAdrBook,
                                                             String pszPassword,
                        [MarshalAs(UnmanagedType.LPArray)]   byte[] putf8User,
                      [MarshalAs(UnmanagedType.LPArray)]   UInt32[] putf8UserLen,
                        [MarshalAs(UnmanagedType.LPArray)]   byte[] pASN1Cert,
                      [MarshalAs(UnmanagedType.LPArray)]   UInt32[] pASN1CertLen,
                        [MarshalAs(UnmanagedType.LPArray)]   byte[] putf8SysID,
                      [MarshalAs(UnmanagedType.LPArray)]   UInt32[] putf8SysIDLen,
                        [MarshalAs(UnmanagedType.LPArray)]   byte[] putf8Client,
                      [MarshalAs(UnmanagedType.LPArray)]   UInt32[] putf8ClientLen,
                                                             IntPtr pvReserverd);

        [DllImport("sapssoext", CharSet = CharSet.Ansi, SetLastError = true)]
        internal static extern Int32 MySapEvalLogonTicketEx(String pszTicket,
                                                             String pszAdrBook,
                                                             String pszPassword,
                        [MarshalAs(UnmanagedType.LPArray)]   byte[] pASN1Cert,
                      [MarshalAs(UnmanagedType.LPArray)]   UInt32[] pASN1CertLen,
                                               ref TICKET_INFO_LONG ticket_info);

        [DllImport("sapssoext", CharSet = CharSet.Ansi, SetLastError = true)]
        internal static extern Int32 MySapEvalAssertionTicket(String pszTicket,
                                                             String pszAdrBook,
                                                             String pszPassword,
                                                             String pszMySysId,
                                                             String pszMySysClient,
                                               ref TICKET_INFO_LONG ticket_info);

        [DllImport("sapssoext", CharSet = CharSet.Ansi, SetLastError = true)]
        internal static extern Int32 MySapEvalTicket(String pszTicket,
            [MarshalAs(UnmanagedType.LPArray)]   IntPtr[] lTicketSize,
                                                 ref TICKET_INFO_LONG ticket_info);

        /* parse certificate information */
        [DllImport("sapssoext", CharSet = CharSet.Ansi, SetLastError = true)]
        internal static extern Int32 MySapParseCertificate(byte[] pASN1Cert,
                                                           UInt32 pASN1CertLen,
                                                           String pszType,
                        [MarshalAs(UnmanagedType.LPArray)] byte[] pBuffer,
                     [MarshalAs(UnmanagedType.LPArray)]   Int32[] pBufferLen);
        /********************* thread local methods *********************************/
    }

    public class SAPSSOEXT
    {
        private TICKET_INFO_LONG m_ticket_info = new TICKET_INFO_LONG();
        private System.Text.UTF8Encoding u8enc = new System.Text.UTF8Encoding();
        byte[] m_blankArray = new byte[256];


        static SAPSSOEXT() 
        {
            NativeMethods.SetDllDirectory("C:\\SAPSSO");

            // initialize logging
            IConfigurationSource configurationSource = ConfigurationSourceFactory.Create();
            LogWriterFactory logWriterFactory = new LogWriterFactory(configurationSource);
            Logger.SetLogWriter(logWriterFactory.Create());     
        }

        public SAPSSOEXT()
        {
            for (int i = 0; i < m_blankArray.Length; i++)
                m_blankArray[i] = 0x20;
            /* init */
            initLongInfo();
        }

        public SAPSSOEXT(String ssfLibrary)
        {
            for (int i = 0; i < m_blankArray.Length; i++)
                m_blankArray[i] = 0x20;
            initLongInfo();
            int result = StartSSOExt(ssfLibrary);
            Console.WriteLine(result);
        }

        // finalize
        ~SAPSSOEXT()
        {
            if (m_ticket_info.tTicketType != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.tTicketType);
                m_ticket_info.tTicketType = IntPtr.Zero;
            }
            if (m_ticket_info.pIntAsn1CertificateL != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pIntAsn1CertificateL);
                m_ticket_info.pIntAsn1CertificateL = IntPtr.Zero;
            }
            if (m_ticket_info.pIntAuthSchemeL != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pIntAuthSchemeL);
                m_ticket_info.pIntAuthSchemeL = IntPtr.Zero;
            }
            if (m_ticket_info.pIntPrtUserL != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pIntPrtUserL);
                m_ticket_info.pIntPrtUserL = IntPtr.Zero;
            }
            if (m_ticket_info.pIntSAPClientL != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pIntSAPClientL);
                m_ticket_info.pIntSAPClientL = IntPtr.Zero;
            }
            if (m_ticket_info.pIntSAPLanguL != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pIntSAPLanguL);
                m_ticket_info.pIntSAPLanguL = IntPtr.Zero;
            }
            if (m_ticket_info.pIntSAPUserL != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pIntSAPUserL);
                m_ticket_info.pIntSAPUserL = IntPtr.Zero;
            }
            if (m_ticket_info.pIntSysIDL != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pIntSysIDL);
                m_ticket_info.pIntSysIDL = IntPtr.Zero;
            }
            if (m_ticket_info.ptmValidity != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.ptmValidity);
                m_ticket_info.ptmValidity = IntPtr.Zero;
            }
            if (m_ticket_info.ptmCreTime != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.ptmCreTime);
                m_ticket_info.ptmCreTime = IntPtr.Zero;
            }
            if (m_ticket_info.pSAPUser != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pSAPUser);
                m_ticket_info.pSAPUser = IntPtr.Zero;
            }
            if (m_ticket_info.pSysID != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pSysID);
                m_ticket_info.pSysID = IntPtr.Zero;
            }
            if (m_ticket_info.pSAPClient != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pSAPClient);
                m_ticket_info.pSAPClient = IntPtr.Zero;
            }
            if (m_ticket_info.pSAPLangu != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pSAPLangu);
                m_ticket_info.pSAPLangu = IntPtr.Zero;
            }
            if (m_ticket_info.pPrtUser != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pPrtUser);
                m_ticket_info.pPrtUser = IntPtr.Zero;
            }
            if (m_ticket_info.pAuthScheme != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pAuthScheme);
                m_ticket_info.pAuthScheme = IntPtr.Zero;
            }
            if (m_ticket_info.pAsn1Certificate != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(m_ticket_info.pAsn1Certificate);
                m_ticket_info.pAsn1Certificate = IntPtr.Zero;
            }
        }

        // load and start external SSF library
        public int StartSSOExt(String ssfLibrary)
        {
            return NativeMethods.MySapInitialize(ssfLibrary);
        }

        // stop and unload external SSF library
        public int StopSSOExt()
        {
            return NativeMethods.MySapShutdown();
        }

        // initializse structure
        private void initLongInfo()
        {
            if (IntPtr.Size == 8)
            {
                m_ticket_info.struct_size = (IntPtr)152; /* sizeof struct on 64 bit platform */
            }
            else if (IntPtr.Size == 4)
            {
                m_ticket_info.struct_size = (IntPtr)80;  /* sizeof struct on 32 bit platform */
            }
            m_ticket_info.sSAPCodepage = new byte[4];
            m_ticket_info.sSAPCodepage[0] = (byte)'4';
            m_ticket_info.sSAPCodepage[1] = (byte)'1';
            m_ticket_info.sSAPCodepage[2] = (byte)'1';
            m_ticket_info.sSAPCodepage[3] = (byte)'0';
            m_ticket_info.flags = new byte[1];
            if (m_ticket_info.pSysID == IntPtr.Zero)
            {
                m_ticket_info.pSysID = Marshal.AllocHGlobal(256);
            }
            else
            {
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pSysID, m_blankArray.Length);
            }
            if (m_ticket_info.pIntSysIDL == IntPtr.Zero)
            {
                m_ticket_info.pIntSysIDL = Marshal.AllocHGlobal(IntPtr.Size);
            }
            else
            {
                Marshal.WriteIntPtr(m_ticket_info.pIntSysIDL, new IntPtr(0));
            }
            if (m_ticket_info.pSAPUser == IntPtr.Zero)
            {
                m_ticket_info.pSAPUser = Marshal.AllocHGlobal(256);
            }
            else
            {
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pSAPUser, m_blankArray.Length);
            }
            if (m_ticket_info.pIntSAPUserL == IntPtr.Zero)
            {
                m_ticket_info.pIntSAPUserL = Marshal.AllocHGlobal(IntPtr.Size);
            }
            else
            {
                Marshal.WriteIntPtr(m_ticket_info.pIntSAPUserL, new IntPtr(0));
            }
            if (m_ticket_info.pSAPClient == IntPtr.Zero)
            {
                m_ticket_info.pSAPClient = Marshal.AllocHGlobal(256);
            }
            else
            {
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pSAPClient, m_blankArray.Length);
            }
            if (m_ticket_info.pIntSAPClientL == IntPtr.Zero)
            {
                m_ticket_info.pIntSAPClientL = Marshal.AllocHGlobal(IntPtr.Size);
            }
            else
            {
                Marshal.WriteIntPtr(m_ticket_info.pIntSAPClientL, new IntPtr(0));
            }
            if (m_ticket_info.pSAPLangu == IntPtr.Zero)
            {
                m_ticket_info.pSAPLangu = Marshal.AllocHGlobal(256);
            }
            else
            {
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pSAPLangu, m_blankArray.Length);
            }
            if (m_ticket_info.pIntSAPLanguL == IntPtr.Zero)
            {
                m_ticket_info.pIntSAPLanguL = Marshal.AllocHGlobal(IntPtr.Size);
            }
            else
            {
                Marshal.WriteIntPtr(m_ticket_info.pIntSAPLanguL, new IntPtr(0));
            }
            if (m_ticket_info.pPrtUser == IntPtr.Zero)
            {
                m_ticket_info.pPrtUser = Marshal.AllocHGlobal(256);
            }
            else
            {
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pPrtUser, m_blankArray.Length);
            }
            if (m_ticket_info.pIntPrtUserL == IntPtr.Zero)
            {
                m_ticket_info.pIntPrtUserL = Marshal.AllocHGlobal(IntPtr.Size);
            }
            else
            {
                Marshal.WriteIntPtr(m_ticket_info.pIntPrtUserL, new IntPtr(0));
            }
            if (m_ticket_info.pAuthScheme == IntPtr.Zero)
            {
                m_ticket_info.pAuthScheme = Marshal.AllocHGlobal(256);
            }
            else
            {
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pAuthScheme, m_blankArray.Length);
            }
            if (m_ticket_info.pIntAuthSchemeL == IntPtr.Zero)
            {
                m_ticket_info.pIntAuthSchemeL = Marshal.AllocHGlobal(IntPtr.Size);
            }
            else
            {
                Marshal.WriteIntPtr(m_ticket_info.pIntAuthSchemeL, new IntPtr(0));
            }
            if (m_ticket_info.ptmCreTime == IntPtr.Zero)
            {
                m_ticket_info.ptmCreTime = Marshal.AllocHGlobal(IntPtr.Size);
            }
            else
            {
                Marshal.WriteIntPtr(m_ticket_info.ptmCreTime, new IntPtr(0));
            }
            if (m_ticket_info.ptmValidity == IntPtr.Zero)
            {
                m_ticket_info.ptmValidity = Marshal.AllocHGlobal(IntPtr.Size);
            }
            else
            {
                Marshal.WriteIntPtr(m_ticket_info.ptmValidity, new IntPtr(0));
            }
            if (m_ticket_info.pAsn1Certificate == IntPtr.Zero)
            {
                m_ticket_info.pAsn1Certificate = Marshal.AllocHGlobal(8196);
            }
            else
            {
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pAsn1Certificate, m_blankArray.Length);
            }
            if (m_ticket_info.pIntAsn1CertificateL == IntPtr.Zero)
            {
                m_ticket_info.pIntAsn1CertificateL = Marshal.AllocHGlobal(IntPtr.Size);
            }
            else
            {
                Marshal.WriteIntPtr(m_ticket_info.pIntAsn1CertificateL, new IntPtr(0));
            }
            if (m_ticket_info.tTicketType == IntPtr.Zero)
            {
                m_ticket_info.tTicketType = Marshal.AllocHGlobal(IntPtr.Size);
            }
            else
            {
                Marshal.WriteIntPtr(m_ticket_info.tTicketType, new IntPtr(0));
            }
        }

        // reset structure
        private void resetLongInfo()
        {
            if (m_ticket_info.tTicketType != IntPtr.Zero)
                Marshal.WriteIntPtr(m_ticket_info.tTicketType, new IntPtr(m_blankArray.Length));
            if (m_ticket_info.pAsn1Certificate != IntPtr.Zero)
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pAsn1Certificate, m_blankArray.Length);
            if (m_ticket_info.pIntAsn1CertificateL != IntPtr.Zero)
                Marshal.WriteIntPtr(m_ticket_info.pIntAsn1CertificateL, new IntPtr(8192));
            if (m_ticket_info.pAuthScheme != IntPtr.Zero)
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pAuthScheme, m_blankArray.Length);
            if (m_ticket_info.pIntAuthSchemeL != IntPtr.Zero)
                Marshal.WriteIntPtr(m_ticket_info.pIntAuthSchemeL, new IntPtr(m_blankArray.Length));
            if (m_ticket_info.pPrtUser != IntPtr.Zero)
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pPrtUser, m_blankArray.Length);
            if (m_ticket_info.pIntPrtUserL != IntPtr.Zero)
                Marshal.WriteIntPtr(m_ticket_info.pIntPrtUserL, new IntPtr(m_blankArray.Length));
            if (m_ticket_info.pSAPClient != IntPtr.Zero)
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pSAPClient, m_blankArray.Length);
            if (m_ticket_info.pIntSAPClientL != IntPtr.Zero)
                Marshal.WriteIntPtr(m_ticket_info.pIntSAPClientL, new IntPtr(m_blankArray.Length));
            if (m_ticket_info.pSAPLangu != IntPtr.Zero)
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pSAPLangu, m_blankArray.Length);
            if (m_ticket_info.pIntSAPLanguL != IntPtr.Zero)
                Marshal.WriteIntPtr(m_ticket_info.pIntSAPLanguL, new IntPtr(m_blankArray.Length));
            if (m_ticket_info.pSAPUser != IntPtr.Zero)
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pSAPUser, m_blankArray.Length);
            if (m_ticket_info.pIntSAPUserL != IntPtr.Zero)
                Marshal.WriteIntPtr(m_ticket_info.pIntSAPUserL, new IntPtr(m_blankArray.Length));
            if (m_ticket_info.pSysID != IntPtr.Zero)
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pSysID, m_blankArray.Length);
            if (m_ticket_info.pIntSysIDL != IntPtr.Zero)
                Marshal.WriteIntPtr(m_ticket_info.pIntSysIDL, new IntPtr(m_blankArray.Length));
            if (m_ticket_info.ptmCreTime != IntPtr.Zero)
                Marshal.WriteIntPtr(m_ticket_info.ptmCreTime, new IntPtr(m_blankArray.Length));
            if (m_ticket_info.ptmValidity != IntPtr.Zero)
                Marshal.WriteIntPtr(m_ticket_info.ptmValidity, new IntPtr(m_blankArray.Length));
        }

        // evaluate a SAP logon ticket from Portal
        public int EvalPortalTicket(String ticket,
                                 String addBook,
                                 String passWord,
                             ref TICKET_PORTAL_INFO ticket_info)
        {
            int sso_rc = 0;
            byte[] cert = new byte[8400];
            byte[] buffer = new byte[256];
            int bufferlen = 0;
            UInt32[] certlen = new UInt32[1];
            certlen[0] = 8400;
            if (m_ticket_info.pIntPrtUserL == IntPtr.Zero)
            {
                m_ticket_info.pIntPrtUserL = Marshal.AllocHGlobal(new IntPtr(256));
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pPrtUser, m_blankArray.Length);
            }
            resetLongInfo();
            sso_rc = NativeMethods.MySapEvalLogonTicketEx(ticket,
                                               getFullFilePath(addBook),
                                               passWord,
                                               cert,
                                               certlen,
                                               ref m_ticket_info);

            if (sso_rc != 0)
            {
                ticket_info.sapClient = "";
                ticket_info.sapPortalUser = "";
                ticket_info.sapSysID = "";
                ticket_info.sapUser = "";
                return sso_rc;
            }
            else
            {
                Marshal.Copy(m_ticket_info.pSAPUser, buffer, 0, bufferlen = (int)Marshal.ReadIntPtr(m_ticket_info.pIntSAPUserL));
                ticket_info.sapUser = u8enc.GetString(buffer, 0, bufferlen);
                Marshal.Copy(m_ticket_info.pSysID, buffer, 0, bufferlen = (int)Marshal.ReadIntPtr(m_ticket_info.pIntSysIDL));
                ticket_info.sapSysID = u8enc.GetString(buffer, 0, bufferlen);
                Marshal.Copy(m_ticket_info.pSAPClient, buffer, 0, bufferlen = (int)Marshal.ReadIntPtr(m_ticket_info.pIntSAPClientL));
                ticket_info.sapClient = u8enc.GetString(buffer, 0, bufferlen);
                Marshal.Copy(m_ticket_info.pPrtUser, buffer, 0, bufferlen = (int)Marshal.ReadIntPtr(m_ticket_info.pIntPrtUserL));
                ticket_info.sapPortalUser = u8enc.GetString(buffer, 0, bufferlen);
                ticket_info.sapCert = new byte[certlen[0]];
                Array.Copy(cert, ticket_info.sapCert, certlen[0]);
                return 0;
            }
        }

        // evaluate a SAP logon ticket from Portal with all possibly values
        public int EvalPortalTicket(String ticket,
                                 String addBook,
                                 String passWord,
                             ref TICKET_PORTAL_INFO_LONG ticket_info)
        {
            int sso_rc = 0;
            byte[] cert = new byte[8400];
            byte[] buffer = new byte[256];
            int bufferlen = 0;
            UInt32[] certlen = new UInt32[1];
            certlen[0] = 8400;
            if (m_ticket_info.pIntPrtUserL == IntPtr.Zero)
            {
                m_ticket_info.pIntPrtUserL = Marshal.AllocHGlobal(256);
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pPrtUser, m_blankArray.Length);
            }
            if (m_ticket_info.pIntAuthSchemeL == IntPtr.Zero)
            {
                m_ticket_info.pIntAuthSchemeL = Marshal.AllocHGlobal(256);
                Marshal.Copy(m_blankArray, 0, m_ticket_info.pAuthScheme, m_blankArray.Length);
            }
            if (m_ticket_info.ptmValidity == IntPtr.Zero)
            {
                m_ticket_info.ptmValidity = Marshal.AllocHGlobal(IntPtr.Size);
            }
            resetLongInfo();
            sso_rc = NativeMethods.MySapEvalLogonTicketEx(ticket,
                                               getFullFilePath(addBook),
                                               passWord,
                                               cert,
                                               certlen,
                                               ref m_ticket_info);
            if (sso_rc != 0)
            {
                ticket_info.sapAuthSchema = "";
                ticket_info.sapClient = "";
                ticket_info.sapPortalUser = "";
                ticket_info.sapSysID = "";
                ticket_info.sapUser = "";
                ticket_info.validity = 0;
                return sso_rc;
            }
            else
            {
                Marshal.Copy(m_ticket_info.pSAPUser, buffer, 0, bufferlen = (int)Marshal.ReadIntPtr(m_ticket_info.pIntSAPUserL));
                ticket_info.sapUser = u8enc.GetString(buffer, 0, bufferlen);
                Marshal.Copy(m_ticket_info.pSysID, buffer, 0, bufferlen = (int)Marshal.ReadIntPtr(m_ticket_info.pIntSysIDL));
                ticket_info.sapSysID = u8enc.GetString(buffer, 0, bufferlen);
                Marshal.Copy(m_ticket_info.pSAPClient, buffer, 0, bufferlen = (int)Marshal.ReadIntPtr(m_ticket_info.pIntSAPClientL));
                ticket_info.sapClient = u8enc.GetString(buffer, 0, bufferlen);
                Marshal.Copy(m_ticket_info.pAuthScheme, buffer, 0, bufferlen = (int)Marshal.ReadIntPtr(m_ticket_info.pIntAuthSchemeL));
                ticket_info.sapAuthSchema = u8enc.GetString(buffer, 0, bufferlen);
                Marshal.Copy(m_ticket_info.pPrtUser, buffer, 0, bufferlen = (int)Marshal.ReadIntPtr(m_ticket_info.pIntPrtUserL));
                ticket_info.sapPortalUser = u8enc.GetString(buffer, 0, bufferlen);
                if (IntPtr.Size == 8)
                {   /* time_t is int64 */
                    ticket_info.validity = (Int64)Marshal.ReadInt64(m_ticket_info.ptmValidity);
                }
                else if (IntPtr.Size == 4)
                {   /* time_t is int32 */
                    ticket_info.validity = (Int64)Marshal.ReadInt32(m_ticket_info.ptmValidity);
                }
                ticket_info.sapCert = new byte[certlen[0]];
                Array.Copy(cert, ticket_info.sapCert, certlen[0]);
                return 0;
            }
        }

     

        // set property in SAPSSOEXT
        public int SetProperty(String pName, String pValue)
        {
            return NativeMethods.SsoExtSetProperty(pName, pValue);
        }

        // get property from SAPSSOEXT
        public String GetProperty(String pName)
        {
            return Marshal.PtrToStringAnsi(NativeMethods.SsoExtGetProperty(pName));
        }

        // load a PSE file into the memory of SAPSSOEXT
        public int LoadPSE(byte[] pseArray)
        {
            return NativeMethods.MySapLoadTicketKey(pseArray, pseArray.Length, null, 0, 0);
        }

        // load a X509 certificate (for ticket verification) into the memory of SAPSSOEXT
        public int LoadX509Certificate(byte[] xCertificate)
        {
            return NativeMethods.MySapLoadTicketKey(xCertificate, xCertificate.Length, null, 0, 1);
        }

        // parse a X.509 certificate
        // see sapssoext.h for possibly values of "info"
        public static String ParseCertificate(byte[] certArray,
                                            int certLength,
                                            String info)
        {
            int sso_rc = 0;
            byte[] buffer = new byte[8600];
            Int32[] bufferlen = new Int32[1];
            bufferlen[0] = 8600;

            sso_rc = NativeMethods.MySapParseCertificate(certArray,
                                                         (uint)certLength,
                                                         info, /* see sapssoext.h */
                                                         buffer,
                                                         bufferlen);
            if (sso_rc != 0)
                return "";
            else
                return System.Text.Encoding.UTF8.GetString(buffer, 0, (int)bufferlen[0]);
        }

        
        // get the full path to a file
        private static String getFullFilePath(string filename)
        {
            String path;

            if (filename == null)
                return null;
            if (Path.HasExtension(filename))
            {
                path = Path.GetFullPath(filename);
            }
            else
            {
                path = Path.GetFullPath(filename + ".pse");
            }
            if (!File.Exists(path))
                throw new FileNotFoundException("File " + filename + " does not exists", filename);
            return path;
        }
        // read the ticket string from a File
        private static String getTicket(string filename)
        {
            if (filename == null)
                return null;
            try
            {
                // Create an instance of StreamReader to read from a file.
                // The using statement also closes the StreamReader.
                using (StreamReader sr = new StreamReader(filename))
                {
                    String line = sr.ReadToEnd();
                    sr.Close();
                    return line;
                }
            }
            catch (Exception e)
            {
                // Let the user know what went wrong.
                Console.WriteLine("The file could not be read:");
                Console.WriteLine(e.Message);
                throw new FieldAccessException("File " + filename + " could not be read", e);
            }

        }

           // main method, used for testing purposes
        static void Main(string[] args)
        {
            String ticket = "AjExMDAgAA9wb3J0YWw6VC5FVkhBTkyIABNiYXNpY2F1dGhlbnRpY2F0aW9uAQAIVC5FVkhBTkwCAAMwMDADAANEUDcEAAwyMDE1MDgwNDE3MTkFAAQAAAAICgAIVC5FVkhBTkz/AQQwggEABgkqhkiG9w0BBwKggfIwge8CAQExCzAJBgUrDgMCGgUAMAsGCSqGSIb3DQEHATGBzzCBzAIBATAiMB0xDDAKBgNVBAMTA0RQNzENMAsGA1UECxMESjJFRQIBADAJBgUrDgMCGgUAoF0wGAYJKoZIhvcNAQkDMQsGCSqGSIb3DQEHATAcBgkqhkiG9w0BCQUxDxcNMTUwODA0MTcxOTE0WjAjBgkqhkiG9w0BCQQxFgQUUuc3bZu1AWLL/iO9GKvT05G6mSQwCQYHKoZIzjgEAwQuMCwCFC/rZCja8Vf8lv0Bwfz8zRYuEe0aAhQDieEfI/61!zfCdTYZgam5KepXQw==";
            string username = SAPLogin(ticket);
            Console.WriteLine("SAP UserID=" + username);
        }
        

        public static string SAPLogin(string ticket)
        {
            string username = null;
            try
            {
                int sso_rc = 1;
                TICKET_PORTAL_INFO_LONG ticketinfo = new TICKET_PORTAL_INFO_LONG();
                String pseFile = ConfigurationManager.AppSettings["SAPCertificate"];
                SAPSSOEXT ssoext = new SAPSSOEXT(null);


                byte[] buffer = new byte[8192];
                int l;
                using (BinaryReader br = new BinaryReader(new FileStream(pseFile, FileMode.Open,
                              FileAccess.Read, FileShare.ReadWrite)))
                {
                    l = br.Read(buffer, 0, 8192);
                    br.Close();
                }
                byte[] bybuffer = new byte[l];
                for (int i = 0; i < l; i++)
                {
                    bybuffer[i] = buffer[i];
                }

                ssoext.LoadPSE(bybuffer);

                

                if (0 == (sso_rc = ssoext.EvalPortalTicket(ticket,
                                                           null,
                                                           null,
                                                           ref ticketinfo)))
                {
                    username = ticketinfo.sapUser;
                }
                else
                {
                    Error("EvalPortalTicket returned with error code: " + sso_rc);
                }
            }
            catch (Exception ex)
            {
                Error("Error SSO login", ex);
            }
            Info("SAPSSO WS called, returned SAP username=" + username);
            return username;
        }

        public static void Error(string message, System.Exception ex = null)
        {
            LogEntry log = new LogEntry();
            log.Message = message;
            log.Categories.Add("WebPortal");
            log.Priority = 10;
            log.Severity = TraceEventType.Error;
            if (ex != null)
            {
                Dictionary<string, object> exProperties = new Dictionary<string, object>();
                exProperties.Add("ExceptionMessage", ex.Message);
                exProperties.Add("ExceptionStackTrace", ex.StackTrace);
                log.ExtendedProperties = exProperties;
            }
            Logger.Write(log);
        }

        public static void Info(string message)
        {
            LogEntry log = new LogEntry();
            log.Message = message;
            log.Categories.Add("WebPortal");
            log.Priority = 10;
            log.Severity = TraceEventType.Information;            
            Logger.Write(log);
        }

    }

}
